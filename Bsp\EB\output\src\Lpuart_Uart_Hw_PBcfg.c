/**
 * file    Lpuart_Uart_Hw_PBcfg.c
 * brief   UART driver for LES14XX
 * author  xiali
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
*                                        INCLUDE FILESt
==================================================================================================*/

#include "Uart_Hw_Types.h"
#include "Uart_Hw_Cfg.h"
#include "Std_Types.h"
#if (LPUART_UART_HW_HAS_DMA_ENABLED == STD_ON)
#include "Dma_Hw.h"
#endif

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LPUART_UART_HW_PBCFG_VENDOR_ID_C                     (110u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define LPUART_UART_HW_PBCFG_SW_MINOR_VERSION_C              (0u)
#define LPUART_UART_HW_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Lpuart_Uart_Hw_Types.h */
#if (LPUART_UART_HW_PBCFG_VENDOR_ID_C != LPUART_UART_HW_TYPES_VENDOR_ID)
    #error "Lpuart_Uart_Hw_PBcfg.c and Lpuart_Uart_Hw_Types.h have different vendor ids"
#endif
#if ((LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LPUART_UART_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LPUART_UART_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LPUART_UART_HW_TYPES_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Lpuart_Uart_Hw_Types.h are different"
#endif
#if ((LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION_C != LPUART_UART_HW_TYPES_SW_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_MINOR_VERSION_C != LPUART_UART_HW_TYPES_SW_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_PATCH_VERSION_C != LPUART_UART_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Lpuart_Uart_Hw_Types.h are different"
#endif

/* Checks against Uart_Hw_Cfg.h */
#if (LPUART_UART_HW_PBCFG_VENDOR_ID_C != LPUART_UART_HW_CFG_VENDOR_ID)
    #error "Lpuart_Uart_Hw_PBcfg.c and Uart_Hw_Cfg.h have different vendor ids"
#endif
#if ((LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LPUART_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LPUART_UART_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LPUART_UART_HW_CFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Uart_Hw_Cfg.h are different"
#endif
#if ((LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION_C != LPUART_UART_HW_CFG_SW_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_MINOR_VERSION_C != LPUART_UART_HW_CFG_SW_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_PATCH_VERSION_C != LPUART_UART_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Uart_Hw_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Std_Types.h are different"
    #endif
    /* Checks against Dma_Hw.h */
    #if (LPUART_UART_HW_HAS_DMA_ENABLED == STD_ON)
        #if ((LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DMA_HW_AR_RELEASE_MAJOR_VERSION) || \
             (LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != DMA_HW_AR_RELEASE_MINOR_VERSION))
            #error "AutoSar Version Numbers of Lpuart_Uart_Hw_PBcfg.c and Dma_Hw.h are different"
        #endif
    #endif
#endif
/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                   STATE STRUCTURE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

#define UART_START_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Uart_MemMap.h"
extern Lpuart_Uart_Hw_StateStructure_t g_astLpuart_Uart_Hw_apStateStructure[LPUART_UART_HW_NUMBER_OF_INSTANCES];
#define UART_STOP_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Uart_MemMap.h"

#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"




/**
* @brief          Hardware configuration for Uart Hardware - Configuration:
*
* @api
*/
const Lpuart_Uart_Hw_UserConfig_t Lpuart_Uart_Hw_xHwConfigPB_0 =
{

    /*!< Baud rate in hertz */
    116279U,
    /* Baud clock divisor*/
    43U,
    /* Over sampling ratio*/
    16U,
    /* Frame format */
    UART_FRAME_FORMAT_IDLELINE,
    /* Uart mode */
    UART_FULL_DUPLEX_MODE,
    /* RS485 mode direction */
    UART_RS485_DIR_TX,
    /* RS485 mode direction polarity */
    UART_RS485_DEPOL_SAME,
    /* RS485 mode rx frame address */
    0x0U,
    /* RS485 mode rx frame address match enable */
    FALSE,
    /* RS485 sleep mode enable */
    FALSE,
    /* Parity type */
    LPUART_UART_HW_PARITY_DISABLED,
    /* Number of stop bits, 1 stop bit (default) or 2 stop bits */
    UART_HW_ONE_STOP_BIT,
    /* Number of bits per transmitted/received word */
    LPUART_UART_HW_8_BITS_PER_CHAR,
    /* Uart Tx FIFO waterline */
    (uint8)UART_TXFIFO_WATERLINE_3,
    /* Uart Rx FIFO waterline */
    (uint8)UART_RXFIFO_WATERLINE_0,
    /* Uart Tx Endian */
    (uint8)UART_FRAME_ENDIAN_LSB,
    /* Uart Rx Endian */
    (uint8)UART_FRAME_ENDIAN_LSB,
    /* Auto Flow Control Enable */
    FALSE,
    /* CTS RTS valid polarity */
    STD_LOW,
    /* Type of UART transfer (interrupt/dma based) */
    LPUART_UART_HW_USING_INTERRUPTS,
    /* Callback to invoke for Uart event.*/
    (Lpuart_Uart_Hw_Callback_t)Uart_Ipw_LpuartCallback,
    /* User callback parameter pointer.*/
    NULL_PTR,
#if (LPUART_UART_HW_HAS_DMA_ENABLED == STD_ON)
    /* DMA channel number for DMA-based rx. */
    DMA_LOGIC_CH_0,
    /* DMA channel number for DMA-based tx. */
    DMA_LOGIC_CH_2,
#endif
    /* Runtime state structure refference */
    &g_astLpuart_Uart_Hw_apStateStructure[0]

};

#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"
/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}

/** @} */

#endif
