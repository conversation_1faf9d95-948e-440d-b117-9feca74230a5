/**
 * @file    Gpt_Hw_PBcfg.h
 * @brief   Gpt Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 */

#ifndef GPT_HW_PBCFG_H
#define GPT_HW_PBCFG_H




#include "Gpt_Hw.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* Inclusion of incompatible header files shall be avoided */


/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
#define GPT_HW_VENDOR_ID_PBCFG_H                    (110U)
#define GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H     (4U)
#define GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H     (4U)
#define GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H  (0U)
#define GPT_HW_SW_MAJOR_VERSION_PBCFG_H             (1U)
#define GPT_HW_SW_MINOR_VERSION_PBCFG_H             (0U)
#define GPT_HW_SW_PATCH_VERSION_PBCFG_H             (0U)
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/
#if (GPT_HW_VENDOR_ID_PBCFG_H != GPT_HW_VENDOR_ID)
    #error "Gpt_HW_PBcfg.h and Gpt_Ipw.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H != GPT_HW_AR_RELEASE_MAJOR_VERSION) || \
     (GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H != GPT_HW_AR_RELEASE_MINOR_VERSION) || \
     (GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H != GPT_HW_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Gpt_HW_PBcfg.h and Gpt_Hw.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_H != GPT_HW_SW_MAJOR_VERSION) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_H != GPT_HW_SW_MINOR_VERSION) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_H != GPT_HW_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Gpt_HW_PBcfg.h and Gpt_Hw.h are different"
#endif
/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
/* 
 * @brief   Gpt HwInstanceConfig configuration array
 */
extern const Gpt_HwInstanceConfig_t Gpt_HwInstanceConfig_PB[1U];

/* 
 * @brief   Gpt channels IP related configuration array
 */
extern const Gpt_HwChannelConfig_t Gpt_ChannelConfig_PB[1U];


#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"


/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
/* } */
#endif /* GPT_HW_PBCFG_H*/

#endif

