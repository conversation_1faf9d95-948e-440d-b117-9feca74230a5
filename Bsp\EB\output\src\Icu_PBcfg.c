/**
 * 
 * @file Icu_PBcfg.c
 * @brief 
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 
#include "Std_Types.h"
#include "Icu.h"
#include "Icu_Hw_Types.h"
#include "Icu_Hw_PBcfg.h"

#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
#include "Dma.h"
#endif

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define ICU_PBCFG_VENDOR_ID_C                    (110u)
#define ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C     (4u)
#define ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C     (4u)
#define ICU_PBCFG_AR_RELEASE_REVISION_VERSION_C  (0u)
#define ICU_PBCFG_SW_MAJOR_VERSION_C             (1u)
#define ICU_PBCFG_SW_MINOR_VERSION_C             (0u)
#define ICU_PBCFG_SW_PATCH_VERSION_C             (0u)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
/* Check if header file and Std_Types.h file are of the same Autosar version */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Icu_PBcfg.c and Std_Types.h are different"
    #endif

    #if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        #if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DMA_AR_RELEASE_MAJOR_VERSION) || \
             (ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C != DMA_AR_RELEASE_MINOR_VERSION))
            #error "AutoSar Version Numbers of Icu_PBcfg.c and CDD_Dma.h are different"
        #endif
    #endif
#endif

/* Check if source file and ICU header file are of the same vendor */
#if (ICU_PBCFG_VENDOR_ID_C != ICU_VENDOR_ID)
    #error "Icu_PBcfg.c and Icu.h have different vendor IDs"
#endif
/* Check if source file and ICU header file are of the same AutoSar version */
#if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != ICU_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C != ICU_AR_RELEASE_MINOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_REVISION_VERSION_C != ICU_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_PBcfg.c and Icu.h are different"
#endif
/* Check if source file and ICU header file are of the same Software version */
#if ((ICU_PBCFG_SW_MAJOR_VERSION_C != ICU_SW_MAJOR_VERSION) || \
     (ICU_PBCFG_SW_MINOR_VERSION_C != ICU_SW_MINOR_VERSION) || \
     (ICU_PBCFG_SW_PATCH_VERSION_C != ICU_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_PBcfg.c and Icu.h are different"
#endif

/* Check if source file and ICU header file are of the same vendor */
#if (ICU_PBCFG_VENDOR_ID_C != ICU_HW_TYPES_VENDOR_ID)
    #error "Icu_PBcfg.c and Icu_Hw_Types.h have different vendor IDs"
#endif
/* Check if source file and ICU header file are of the same AutoSar version */
#if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != ICU_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C != ICU_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_REVISION_VERSION_C != ICU_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_PBcfg.c and Icu_Hw_Types.h are different"
#endif
/* Check if source file and ICU header file are of the same Software version */
#if ((ICU_PBCFG_SW_MAJOR_VERSION_C != ICU_HW_TYPES_SW_MAJOR_VERSION) || \
     (ICU_PBCFG_SW_MINOR_VERSION_C != ICU_HW_TYPES_SW_MINOR_VERSION) || \
     (ICU_PBCFG_SW_PATCH_VERSION_C != ICU_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_PBcfg.c and Icu_Hw_Types.h are different"
#endif

/* Check if source file and ICU header file are of the same vendor */
#if (ICU_PBCFG_VENDOR_ID_C != ICU_HW_PBCFG_VENDOR_ID)
    #error "Icu_PBcfg.c and Icu_Hw_PBcfg.h have different vendor IDs"
#endif
/* Check if source file and ICU header file are of the same AutoSar version */
#if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_MINOR_VERSION_C != ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_REVISION_VERSION_C != ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_PBcfg.c and Icu_Hw_PBcfg.h are different"
#endif
/* Check if source file and ICU header file are of the same Software version */
#if ((ICU_PBCFG_SW_MAJOR_VERSION_C != ICU_HW_PBCFG_SW_MAJOR_VERSION) || \
     (ICU_PBCFG_SW_MINOR_VERSION_C != ICU_HW_PBCFG_SW_MINOR_VERSION) || \
     (ICU_PBCFG_SW_PATCH_VERSION_C != ICU_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_PBcfg.c and Icu_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

#define ICU_START_SEC_CODE
#include "Icu_MemMap.h"


#define ICU_STOP_SEC_CODE
#include "Icu_MemMap.h"

#define ICU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"

/*
*   @brief Translation LUT for Logical channel number to Partition Configuration indexed location
*/
static const uint8 Icu_ChIndexMap_PB[12U] = 
{
    0U,    /* IcuConfigSet has an index of 0 */
    1U,    /* IcuConfigSet has an index of 1 */
    2U,    /* IcuConfigSet has an index of 2 */
    3U,    /* IcuConfigSet has an index of 3 */
    4U,    /* IcuConfigSet has an index of 4 */
    5U,    /* IcuConfigSet has an index of 5 */
    6U,    /* IcuConfigSet has an index of 6 */
    7U,    /* IcuConfigSet has an index of 7 */
    8U,    /* IcuConfigSet has an index of 8 */
    9U,    /* IcuConfigSet has an index of 9 */
    10U,    /* IcuConfigSet has an index of 10 */
    11U    /* IcuConfigSet has an index of 11 */
};

/*
*  @brief    PB Configuration
*/
static const Icu_ChannelConfig_t Icu_ChannelConfig_PB[12U]=
{
    /* IcuChannel_HALL1 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[0U] /* Ipw channel pointer */
    },
    /* IcuChannel_HALL2 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[1U] /* Ipw channel pointer */
    },
    /* IcuChannel_HALL3 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[2U] /* Ipw channel pointer */
    },
    /* IcuChannel_HALL4 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[3U] /* Ipw channel pointer */
    },
    /* IcuChannel_HALL5 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[4U] /* Ipw channel pointer */
    },
    /* IcuChannel_HALL6 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[5U] /* Ipw channel pointer */
    },
    /* IcuChannel_0 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[6U] /* Ipw channel pointer */
    },
    /* IcuChannel_1 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[7U] /* Ipw channel pointer */
    },
    /* IcuChannel_2 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[8U] /* Ipw channel pointer */
    },
    /* IcuChannel_3 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[9U] /* Ipw channel pointer */
    },
    /* IcuChannel_4 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[10U] /* Ipw channel pointer */
    },
    /* IcuChannel_5 */
    {
        (uint8)FALSE,    /* Wakeup capability */
        ICU_RISING_EDGE,    /* Edge used */
        ICU_MODE_SIGNAL_MEASUREMENT,    /* Measurement mode */
        (Icu_MeasurementSubModeType)ICU_DUTY_CYCLE,    /* Icu_MeasurementSubModeType */
        NULL_PTR,    /* Icu_Channel_Notification */
#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
        (Icu_DmaChannelType)NoDmaChannel,    /* DmaChannel */
#endif
#if (ICU_REPORT_WAKEUP_SOURCE == STD_ON)
        (Icu_WakeupValueType)0U,    /* Icu_Channel_WakeupValue */
#endif /* (ICU_REPORT_WAKEUP_SOURCE == STD_ON). */
        &Icu_Hw_IpChannelConfig_PB[11U] /* Ipw channel pointer */
    }
};

    


const Icu_ConfigType Icu_Config=
{
    (uint8)12, 
    /** @brief The number of channels configured*/
    &Icu_ChannelConfig_PB, 
    /** @brief Index of channel in each partition map table*/
    &Icu_ChIndexMap_PB,
    /** @brief core index*/
    (uint8)0U
};


#define ICU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"

/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

#ifdef __cplusplus
}
#endif


