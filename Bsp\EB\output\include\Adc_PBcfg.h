/**
 * file    Adc_PBcfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_PBCFG_H
#define ADC_PBCFG_H

/**
*   @file
*
*   @addtogroup adc_driver_config Adc Driver Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#include "Adc_HwType.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_VENDOR_ID_PBCFG                      (110u)
#define ADC_AR_RELEASE_MAJOR_VERSION_PBCFG       (4u)
#define ADC_AR_RELEASE_MINOR_VERSION_PBCFG       (4u)
#define ADC_AR_RELEASE_REVISION_VERSION_PBCFG    (0u)
#define ADC_SW_MAJOR_VERSION_PBCFG               (1u)
#define ADC_SW_MINOR_VERSION_PBCFG               (0u)
#define ADC_SW_PATCH_VERSION_PBCFG               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Check if Adc Configuration header file and Adc_HwType.h are of the same vendor */
#if (ADC_VENDOR_ID_PBCFG != ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_PBcfg.h and Adc_HwType.h have different vendor ids"
#endif

/* Check if Adc Configuration header file and Adc_HwType.h are of the same Autosar version */
#if ((ADC_AR_RELEASE_MAJOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_AR_RELEASE_MINOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_AR_RELEASE_REVISION_VERSION_PBCFG != ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_PBcfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc Configuration header file and Adc_HwType.h are of the same Software version */
#if ((ADC_SW_MAJOR_VERSION_PBCFG != ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_SW_MINOR_VERSION_PBCFG != ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_SW_PATCH_VERSION_PBCFG != ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_PBcfg.h and Adc_HwType.h are different"
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

#define ADC_CONFIG_PB \
 extern const Adc_ConfigType Adc_Config;\
/**
* @brief           Number of channels configured for each group.
*
*/

#define ADC_CFGSET_GROUP_0_CHANNELS      (10U)
#define ADC_CFGSET_GROUP_1_CHANNELS      (9U)

/**
* @brief          Total number of groups in Config.
*
*/
#define ADC_GROUPS                       (2U)


/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#define ADC_START_SEC_CODE
#include "Adc_MemMap.h"


#define ADC_STOP_SEC_CODE
#include "Adc_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_PBCFG_H */

