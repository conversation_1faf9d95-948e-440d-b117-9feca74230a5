/**
 * *************************************************************************
 * @file Clock_PBcfg.h
 * @brief This file is used for dynamic code generation
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

#ifndef MCU_PBCFG_H
#define MCU_PBCFG_H

/**
 * *************************************************************************
 * @file   Mcu_PBcfg.h
 * @brief This file is used for Data structures for the Mcu driver.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
*                                          INCLUDE FILES
==================================================================================================*/


#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define MCU_PBCFG_VENDOR_ID                     (110U)
#define MCU_PBCFG_AR_RELEASE_MAJOR_VERSION      (4U)
#define MCU_PBCFG_AR_RELEASE_MINOR_VERSION      (4U)
#define MCU_PBCFG_AR_RELEASE_REVISION_VERSION   (0U)
#define MCU_PBCFG_SW_MAJOR_VERSION              (1U)
#define MCU_PBCFG_SW_MINOR_VERSION              (0U)
#define MCU_PBCFG_SW_PATCH_VERSION              (0U)

/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/


/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL VARIABLES
==================================================================================================*/

#define MCU_CONFIG_PB \
 extern const Mcu_ConfigType Mcu_Config;

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */
#endif /* #ifndef MCU_PBCFG_H */


