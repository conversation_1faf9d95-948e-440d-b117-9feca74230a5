/**
 * @file    Gpt_PBcfg.c
 * @brief   Gpt PBcfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 20.1, #include directives should only be preceded by preprocessor directives or comments.
 * REASON: Variables and text need to be placed in the specified location
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 8.9, An object should be defined at block scope if its identifier only appears in a single function.
 * REASON: this is generated dynamic code, it cannot be placed inside static code functions.Code functions are not affected
 */

#include "Gpt.h"
#include "Gpt_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_PBCFG_C                      (110U)
#define GPT_RELEASE_MAJOR_VERSION_PBCFG_C          (4U)
#define GPT_RELEASE_MINOR_VERSION_PBCFG_C          (4U)
#define GPT_RELEASE_REVISION_VERSION_PBCFG_C       (0U)
#define GPT_SW_MAJOR_VERSION_PBCFG_C               (1U)
#define GPT_SW_MINOR_VERSION_PBCFG_C               (0U)
#define GPT_SW_PATCH_VERSION_PBCFG_C               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Check if Gpt_PBcfg.c file and Gpt.h header file are of the same vendor */
#if (GPT_VENDOR_ID_PBCFG_C != GPT_VENDOR_ID)
    #error "Gpt_PBcfg.c and Gpt.h have different vendor ids"
#endif
/* Check if Gpt_PBcfg.c file and Gpt.h header file are of the same Autosar version */
#if ((GPT_RELEASE_MAJOR_VERSION_PBCFG_C != GPT_AR_RELEASE_MAJOR_VERSION) || \
     (GPT_RELEASE_MINOR_VERSION_PBCFG_C != GPT_AR_RELEASE_MINOR_VERSION) || \
     (GPT_RELEASE_REVISION_VERSION_PBCFG_C != GPT_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Gpt_PBcfg.c and Gpt.h are different"
#endif
/* Check if Gpt_PBcfg.c file and Gpt.h header file are of the same Software version */
#if ((GPT_SW_MAJOR_VERSION_PBCFG_C != GPT_SW_MAJOR_VERSION) || \
     (GPT_SW_MINOR_VERSION_PBCFG_C != GPT_SW_MINOR_VERSION) || \
     (GPT_SW_PATCH_VERSION_PBCFG_C != GPT_SW_PATCH_VERSION))
    #error "Software Version Numbers of Gpt_PBcfg.c and Gpt.h are different"
#endif

#if (GPT_HW_VENDOR_ID_PBCFG_H != GPT_VENDOR_ID_PBCFG_C)
    #error "Gpt_Hw_PBcfg.h and Gpt_PBcfg.c have different vendor ids"
#endif
/* Check if the header files are of the same Autosar version */
#if ((GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H != GPT_RELEASE_MAJOR_VERSION_PBCFG_C) || \
     (GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H != GPT_RELEASE_MINOR_VERSION_PBCFG_C) || \
     (GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H != GPT_RELEASE_REVISION_VERSION_PBCFG_C) \
    )
    #error "AutoSar Version Numbers of Gpt_Hw_PBcfg.h and Gpt_PBcfg.c are different"
#endif
/* Check if the header files are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_H != GPT_SW_MAJOR_VERSION_PBCFG_C) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_H != GPT_SW_MINOR_VERSION_PBCFG_C) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_H != GPT_SW_PATCH_VERSION_PBCFG_C) \
    )
    #error "Software Version Numbers of Gpt_Hw_PBcfg.h and Gpt_PBcfg.c are different"
#endif
/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/
/**
 * @brief The callback functions defined by the user to be called as channel notifications
 *
 */
extern FUNC(void, GPT_CODE) Gpt_5msCycleCallback(VAR(uint8, AUTOMATIC) IsrType);

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/
#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"

#define GPT_CONF_CHANNELS_PB    1U
#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"

#define GPT_START_SEC_CONST_UNSPECIFIED
#include "Gpt_MemMap.h"

#define GPT_STOP_SEC_CONST_UNSPECIFIED
#include "Gpt_MemMap.h" 

#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
static const Gpt_ChannelConfig_t Gpt_InitChannelPB[GPT_CONF_CHANNELS_PB] =
{
    {   /*GptChannelConfiguration_0 configuration data*/
        (boolean)FALSE, /* Wakeup capability */
        &Gpt_5msCycleCallback, /* Channel notification */
#if ((GPT_WAKEUP_FUNCTIONALITY_API == STD_ON) && (GPT_REPORT_WAKEUP_SOURCE == STD_ON))
        (uint32)0U, /* Wakeup information */
#endif
        (uint32)(4294967295U), /* Maximum ticks value*/
        (GPT_CH_MODE_CONTINUOUS), /* Timer mode:continous/one-shot */
        &Gpt_ChannelConfig_PB[0U]
    }
};

    

/* VariantPostBuild or more than 1 configured variant */
/**
 * @brief        Gpt configuration type does not existing GptEcucPartitionRef and VariantPostBuild or more than 1 configured variant.
 * @details      This is the type of the data structure including the configuration
 *               set required for initializing the GPT driver.
 *
 */
const Gpt_ConfigType Gpt_Config =
{
    /* @brief Number of GPT channels (configured in tresos plugin builder)*/
    (uint8)1U,
    /* @brief Pointer to the GPT channel configuration */
    &Gpt_InitChannelPB,
    /* @brief Number of GPT instances (configured in tresos plugin builder)*/
    1U,
    /* @brief Pointer to the GPT instance configuration */
    &Gpt_HwInstanceConfig_PB
};
    

#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h" 
/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif /* GPT_PBCFG_C */
/** } */

