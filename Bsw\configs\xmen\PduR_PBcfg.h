/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : PduR_PBcfg.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-22 16:30:34 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef  PDUR_PBCFG_H
#define  PDUR_PBCFG_H
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/
/*******************************************************************************
**                      Includes                                              **
*******************************************************************************/
/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/
#define PDUR_PBCFG_H_AR_MAJOR_VERSION  4u
#define PDUR_PBCFG_H_AR_MINOR_VERSION  2u
#define PDUR_PBCFG_H_AR_PATCH_VERSION  2u
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/
#define PDUR_SRC_PDU_SUM           13u

/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define PDUR_SRCPDU_CCU_1          0u
#define PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1          1u
#define PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1          2u
#define PDUR_SRCPDU_IDC_1          3u
#define PDUR_SRCPDU_CanTP_Func_Diag_Request          4u
#define PDUR_SRCPDU_CanTP_Phys_Diag_Request          5u
#define PDUR_SRCPDU_COM_CD_1          6u
#define PDUR_SRCPDU_DCM_Diag_Resp          7u
#define PDUR_SRCPDU_Com_DSM_LIN1          8u
#define PDUR_SRCPDU_MLCU_DSCU_1          9u
#define PDUR_SRCPDU_MLCU_DSCU_2          10u
#define PDUR_SRCPDU_DSS_DSCU          11u
#define PDUR_SRCPDU_PSS_DSCU          12u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define PDUR_DEST_PDU_SUM              13u

#define PDUR_DEST_GATEWAY_TP_PDU_SUM   0u

/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define PDUR_DESTPDU_COM_CCU_1          0u
#define PDUR_DESTPDU_COM_CCU_ZCUL_HVSPRL_1          1u
#define PDUR_DESTPDU_COM_CCU_ZCUR_HVSPRR_1          2u
#define PDUR_DESTPDU_COM_IDC_1          3u
#define PDUR_DESTPDU_DCM_Func_Diag_Request          4u
#define PDUR_DESTPDU_DCM_Phys_Diag_Request          5u
#define PDUR_DESTPDU_CD_1          6u
#define PDUR_DESTPDU_CanTP_Diag_Resp          7u
#define PDUR_DESTPDU_DSM_LIN1          8u
#define PDUR_DESTPDU_Com_MLCU_DSCU_1          9u
#define PDUR_DESTPDU_Com_MLCU_DSCU_2          10u
#define PDUR_DESTPDU_Com_DSS_DSCU          11u
#define PDUR_DESTPDU_Com_PSS_DSCU          12u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define PDUR_ROUTING_PATH_GROUP_SUM        0u

#define PDUR_SRC_UP_MULTICAST_TX_IF_SUM              0u
#define PDUR_GATEWAY_DIRECT_BUFFER_PDU_SUM           0u

#define PDUR_DEFAULT_VALUE_LENGTH           0u
#define PDUR_DEFAULT_VALUE_PDU                  0u
#endif  /* end of PDUR_CFG_H */

/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/

