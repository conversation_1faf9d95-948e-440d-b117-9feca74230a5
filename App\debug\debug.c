#include "debug.h"
#include "motor.h"
#include "log.h"
#include "mcu.h"
#include "cmd.h"

#ifdef USE_DEBUG_FUNCS

static const tDebugOption DEBUG_OPTION_DEFAULT = {0};
static tDebugData debug_data = {
#ifdef SEAT_SIDE_RIGHT
    .mid = MOTOR_2_SEAT_LIFT,
#else
    .mid = MOTOR_4_FRONT_BACK,
#endif
};
static tDebugOption debug_option = {0};

static void debug_clear(void)
{
    debug_option = DEBUG_OPTION_DEFAULT;
}

/// @brief  Check debug status, and call related debug functions
/// @param  
void check_debug_funcs(void)
{
    if (debug_option.motor.calib_one) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        motor_calib(m);
    }

    if (debug_option.motor.clear) {
        motors_clear();
    }

    if (debug_option.motor.forward) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        // motor_go(m, OPTION_POS);
        motor_force(m, OPTION_POS);
    }

    if (debug_option.motor.backward) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        // motor_go(m, OPTION_NEG);
        motor_force(m, OPTION_NEG);
    }

    if (debug_option.motor.shrink) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        motor_move_to(m, 0);
    }

    if (debug_option.motor.move_to) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        motor_move_to(m, debug_data.pos);
    }

    if (debug_option.motor.expand) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        motor_move_to(m, m->config.range);
    }

    if (debug_option.motor.reset) {
        // TODO: reset
        Mcu_PerformReset();
    }

    if (debug_option.motor.waist_off) {
        waist_off();
    }

    if (debug_option.motor.stop) {
        u8 mid = debug_data.mid;
        if (mid >= MOTOR_MAX) {
            println(LogInfo, "check_debug_funcs: invalid motor id %d", mid);
            return;
        }
        tMotor *m = &motors[mid];
        motor_stop(m);
    }

    if (debug_option.massage) {
        do_massage((tCmd){CMD_MASSAGE, ACTION_POS, 0});
    }

    if (debug_option.fan) {
        do_fan_all((tCmd){CMD_FAN, ACTION_POS, 0});
    }

    if (debug_option.clean_mem) {
        mem_clean();
    }

    if (debug_option.calib.s1_back) {
        motors_calib_s1_back();
    }

    if (debug_option.calib.s2_lift) {
        motors_calib_s2_lift();
    }

    if (debug_option.calib.s3_front) {
        motors_calib_s3_front();
    }

    if (debug_option.calib.s4_legs) {
        motors_calib_s4_legs();
    }

    if (debug_option.calib.s5_slide) {
        motors_calib_s5_slide();
    }

    if (debug_option.seat.calib_all) {
        motors_calib_batch();
    }

    if (debug_option.seat.dft) {
        motors_move_default();
    }

    if (debug_option.seat.shrink) {
        motors_shrink_fast();
    }

    if (debug_option.seat.force_shrink) {
        motors_shrink_force();
    }

    if (debug_option.seat.max) {
        motors_expand();
    }

    if (debug_option.seat.zero) {
        motors_zero_gravity();
    }

    if (debug_option.seat.recover) {
        motors_zero_recover();
    }

    debug_clear();
}

#endif