<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Uart" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Uart" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Uart"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="GeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="UartDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DisableUartRuntimeErrorDetect" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartTimeoutDuration" type="INTEGER" value="1000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartDmaEnable" type="BOOLEAN" value="true"/>
                <d:var name="UartVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="UartCallbackCapability" type="BOOLEAN" value="true"/>
                <d:var name="UartCallback" type="FUNCTION-NAME" 
                       value="BluetoothUartCallback">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:lst name="UartEcucPartitionRef"/>
              </d:ctr>
              <d:ctr name="UartGlobalConfig" type="IDENTIFIABLE">
                <d:lst name="UartChannel" type="MAP">
                  <d:ctr name="UartChannel_0" type="IDENTIFIABLE">
                    <d:var name="UartHwUsing" type="ENUMERATION" 
                           value="LPUART_HW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="UartChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:ref name="UartClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Uart"/>
                    <d:ref name="UartChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="DetailModuleConfiguration" type="IDENTIFIABLE">
                      <d:var name="UartHwChannel" type="ENUMERATION" 
                             value="LPUART_0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="DesireBaudrate" type="ENUMERATION" 
                             value="LPUART_UART_BAUDRATE_115200"/>
                      <d:var name="OverSamplingRatio" type="INTEGER" value="16">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CustomBaudrateValue" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartFrameFormat" type="ENUMERATION" 
                             value="UART_FRAME_FORMAT_IDLELINE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartMode" type="ENUMERATION" 
                             value="UART_FULL_DUPLEX_MODE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRS485ModeDirection" type="ENUMERATION" 
                             value="UART_RS485_DIR_TX">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRS485ModeDePol" type="ENUMERATION" 
                             value="UART_RS485_DEPOL_SAME">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRS485ModeRxFrameAddr" type="INTEGER" 
                             value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRS485ModeRxFrameAddrMatchEn" 
                             type="BOOLEAN" value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRS485SleepModeEn" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartInteruptDmaMethod" type="ENUMERATION" 
                             value="LPUART_UART_HW_USING_INTERRUPTS"/>
                      <d:ref name="UartDmaTxChannelRef" type="REFERENCE" 
                             value="ASPath:/Dma/Dma/DmaConfig/dmaLogicChannel_uart0_tx">
                        <a:a name="ENABLE" value="true"/>
                      </d:ref>
                      <d:ref name="UartDmaRxChannelRef" type="REFERENCE" 
                             value="ASPath:/Dma/Dma/DmaConfig/dmaLogicChannel_Type_0">
                        <a:a name="ENABLE" value="true"/>
                      </d:ref>
                      <d:var name="UartParityType" type="ENUMERATION" 
                             value="LPUART_UART_HW_PARITY_DISABLED">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartStopBitNumber" type="ENUMERATION" 
                             value="UART_HW_ONE_STOP_BIT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartWordLength" type="ENUMERATION" 
                             value="LPUART_UART_HW_8_BITS_PER_CHAR"/>
                      <d:var name="UartTxFifoWaterLine" type="ENUMERATION" 
                             value="UART_TXFIFO_WATERLINE_3">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRxFifoWaterLine" type="ENUMERATION" 
                             value="UART_RXFIFO_WATERLINE_0"/>
                      <d:var name="UartTxEndian" type="ENUMERATION" 
                             value="UART_FRAME_ENDIAN_LSB">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartRxEndian" type="ENUMERATION" 
                             value="UART_FRAME_ENDIAN_LSB">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartAutoFlowControlEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartFlowControlCTSRTSValidPolarity" 
                             type="ENUMERATION" value="STD_LOW">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="UartInternalLoopbackEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="FlexioModuleConfiguration" type="IDENTIFIABLE">
                      <d:ref name="UartHwChannelRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="FlexioUartInteruptDmaMethod" 
                             type="ENUMERATION" 
                             value="FLEXIO_UART_HW_DRIVER_TYPE_INTERRUPTS">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="FlexioDmaChannelRef" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="DesireBaudrate" type="ENUMERATION" 
                             value="FLEXIO_UART_BAUDRATE_9600">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="Instance" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="bitCount" type="ENUMERATION" 
                             value="FLEXIO_UART_HW_8_BITS_PER_CHAR">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="driverDirection" type="ENUMERATION" 
                             value="FLEXIO_UART_HW_DIRECTION_TX">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="FlexioIrqTxFunctionCallback" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="FlexioIrqRxFunctionCallback" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
