/**
 * 
 * @file Pwm_Hw_PBcfg.h
 * @brief PWM config file
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef PWM_HW_PBCFG_H
#define PWM_HW_PBCFG_H

#include "Pwm_Ipw_Types.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_HW_PBCFG_VENDOR_ID                    (110U)
#define PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION     (4U)
#define PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION     (4U)
#define PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION  (0U)
#define PWM_HW_PBCFG_SW_MAJOR_VERSION             (1U)
#define PWM_HW_PBCFG_SW_MINOR_VERSION             (0U)
#define PWM_HW_PBCFG_SW_PATCH_VERSION             (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/

/* Check if source file and PWM header file are of the same vendor */
#if (PWM_HW_PBCFG_VENDOR_ID != PWM_IPW_TYPES_VENDOR_ID)
    #error "Pwm_Hw_PBcfg.c and Pwm_Ipw_Types.h have different vendor IDs"
#endif
/* Check if source file and PWM header file are of the same AutoSar version */
#if ((PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != PWM_IPW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION != PWM_IPW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION != PWM_IPW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_Hw_PBcfg.c and Pwm_Ipw_Types.h are different"
#endif
/* Check if source file and PWM header file are of the same Software version */
#if ((PWM_HW_PBCFG_SW_MAJOR_VERSION != PWM_IPW_TYPES_SW_MAJOR_VERSION) || \
     (PWM_HW_PBCFG_SW_MINOR_VERSION != PWM_IPW_TYPES_SW_MINOR_VERSION) || \
     (PWM_HW_PBCFG_SW_PATCH_VERSION != PWM_IPW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_Hw_PBcfg.c and Pwm_Ipw_Types.h are different"
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"
extern const PwmIpwChannelConfig_t Pwm_Hw_IpChannelConfig_PB[16U];

#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"


#ifdef __cplusplus
}
#endif

#endif /* PWM_HW_PBCFG_H */
