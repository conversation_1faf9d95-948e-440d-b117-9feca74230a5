#ifndef LINSM_CFG_H
#define LINSM_CFG_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "Std_Types.h"

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
/*Switches the Default Error Tracer (Det) detection and notification ON or OFF.*/
#define LINSM_DEV_ERROR_DETECT             STD_OFF

/*Fixed period that the MainFunction shall be called.*/
#define LINSM_MAIN_PROCESSING_PERIOD       2u

/*Switches the LinSM_GetVersionInfo function ON or OFF.*/
#define LINSM_VERSION_INFO_API             STD_OFF

/* channel number */
#define LINSM_CHANNEL_NUM                  1u

/* switch for LinSMTransceiverPassiveMode */
/* The APIs LinIf_SetTrcvMode() will only be called when this parameter is enabled.*/
#define LINSM_TRANSCEIVER_PASSIVER_MODE_SUPPORT    STD_OFF

/* Indiacate wheater support MASTER/SLAVE node */    
#define LINSM_MASTER_NODE_SUPPORT                   STD_ON
#define LINSM_SLAVE_NODE_SUPPORT                    STD_OFF

/* Null-schedule index */
#define LINSM_NULL_SCHEDULE_INDEX           0u
#define LINSM_LinIfScheduleTable_schedule_1  LinIfScheduleTable_schedule_1

/* LinSM ScheduleTable Index define for BswM */
#define LinSMSchedule_0 LINSM_LinIfScheduleTable_schedule_1

#endif /* LINSM_CFG_H */

