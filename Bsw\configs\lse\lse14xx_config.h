﻿/**
 * @file config.h
 * @brief 用户根据实际情况进行配置，SDK包根据EVB板提供一份默认的配置
 * @version  1.0
 * <AUTHOR> <PERSON> (<EMAIL>)
 * @date 2024-06-17
 *
 * @copyright Copyright (c) 2023 LANSHAN. All rights reserved
 *
 */

#ifndef LSE14XX_CHIP_CONFIG
#define LSE14XX_CHIP_CONFIG

/* 设置芯片类型配置， LSE14XX 芯片必须配置该项 */
#define CONFIG_CHIP_LSE14X0

/* 关闭 systemInit（）函数调用 */
//#define CONFIG_NO_SYSTEM_INIT

/* 关闭看门狗去使能，如果配置该项，看门狗将持续运行，客户程序需要进行喂狗，否则会产生狗超时 */
//#define CONFIG_NO_WDT_DISABLE

/* 从flash启动 */
#define CONFIG_BOOT_FROM_FLASH

/*将OTP trim值写入寄存器通路*/
//#define CONFIG_FLASH_TRIM_REG

/*抬压设置*/
#define CONFIG_FLASH_LIFT_PRESSURE
#ifndef CONFIG_SYSTEM_FREQUENCY
#define CONFIG_SYSTEM_FREQUENCY 160000000
#endif 

#endif /* LSE14XX_CHIP_CONFIG */












