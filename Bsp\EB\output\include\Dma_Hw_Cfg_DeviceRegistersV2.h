/**
 * file    Dma_Hw_Cfg_DeviceRegistersV2.h
 * brief   Dma Hw Cfg Device Register V2 header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/* Prevention from multiple including the same header */
#ifndef DMA_HW_CFG_DEVICE_REGISTERS_V2_H
#define DMA_HW_CFG_DEVICE_REGISTERS_V2_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Mcal.h"
#include "Std_Types.h"
#include "dma_reg.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define DMA_HW_CFG_DEVICEREGISTERSV2_VENDOR_ID                       (110U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_SW_MAJOR_VERSION                (1U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_SW_MINOR_VERSION                (0U)
#define DMA_HW_CFG_DEVICEREGISTERSV2_SW_PATCH_VERSION                (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types header file are of the same Autosar version */
#if ((DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Dma_Hw_Cfg_DeviceRegistersV2.h and Std_Types.h are different"
#endif
#endif

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
/*-----------------------------------------------/
/  SOC SPECIFIC DMA INSTANCES                    /
/-----------------------------------------------*/
/** Number of instances of the DMA module. */
#define DMA_INSTANCE_COUNT               ((uint32)1U)

#define DMA_HW_BASE                      (DMA_BASE)
#define DMA_HW_PTR                       ((DmaHwInstReg_t *)DMA_HW_BASE)
#define DMA_HW_DMA_HW_BASE_PTRS          { DMA_HW_PTR }
#define DMA_HW_DMA_NOF_INST              DMA_INSTANCE_COUNT       /* Total number of hardware instances */

/*-----------------------------------------------/
/  SOC SPECIFIC DMA CHANNELS                     /
/-----------------------------------------------*/
#define DMA_HW_TCD_BASE                  (DMA_BASE + 8192U)
#define DMA_HW_TCD_PTR                   ((DmaHwTcdArray_t *)DMA_HW_TCD_BASE)
#define DMA_HW_TCD_BASE_PTRS             { DMA_HW_TCD_PTR }

#define DMA_HW_TCD_NOF_CH                (16U)                    /* Number of hardware channels */

#define DMA_HW_TCD_NOF_INST              DMA_INSTANCE_COUNT       /* Number of hardware TCD instances */


/*-----------------------------------------------/
/  SOC SPECIFIC DMA TCD ALIGNMENT                /
/-----------------------------------------------*/
#define DMA_HW_TCD_NOT_ALIGNED               STD_OFF


/*==================================================================================================
                                        DMA MP STRUCTURE
==================================================================================================*/
typedef struct
{
	DmaType_t tCommReg;
	uint8 res[3004U];
	EdmacIntrType_t tIntrReg;
}DmaHwInstReg_t;

/*==================================================================================================
                                         TCD STRUCTURE
==================================================================================================*/

typedef  EdmacChannelType_t DmaHwTcdRegType;
typedef  EdmacChannelType_t DmaHwSwTcdRegType;

typedef struct {
    DmaHwTcdRegType tTcdReg;
	uint32 res[3U]; /*alignment 0x80*/
} DmaHwChTcdReg_t;

#if (DMA_HW_TCD_NOT_ALIGNED == STD_OFF)
typedef struct {
    struct {
        DmaHwChTcdReg_t tChTcdReg;
    } TCD_RSV[DMA_HW_TCD_NOF_CH];
} DmaHwTcdArray_t;
#endif

#ifdef __cplusplus
}
#endif

/** @} */

#endif  /* #ifndef DMA_HW_CFG_DEVICE_REGISTERS_V2_H. */

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
