/**
 * file    Fls_Hw_PBcfg.c
 * brief   Flash driver for LES14XX
 * author  huhl
 * date    2025.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */



/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fls_Hw_Types.h"



#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_HW_VENDOR_ID_CFG_C                          (110u)
#define FLS_HW_AR_RELEASE_MAJOR_VERSION_CFG_C           (4u)
#define FLS_HW_AR_RELEASE_MINOR_VERSION_CFG_C           (4u)
#define FLS_HW_AR_RELEASE_REVISION_VERSION_CFG_C        (0u)
#define FLS_HW_SW_MAJOR_VERSION_CFG_C                   (1u)
#define FLS_HW_SW_MINOR_VERSION_CFG_C                   (0u)
#define FLS_HW_SW_PATCH_VERSION_CFG_C                   (0u)


/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Fls_Hw_Types header file are of the same vendor */
#if (FLS_HW_VENDOR_ID_CFG_C != FLS_HW_TYPES_VENDOR_ID)
    #error "Fls_Hw_Cfg.c and Fls_Hw_Types.h have different vendor ids"
#endif
/* Check if current file and Fls_Hw_Types header file are of the same Autosar version */
#if ((FLS_HW_AR_RELEASE_MAJOR_VERSION_CFG_C    != FLS_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (FLS_HW_AR_RELEASE_MINOR_VERSION_CFG_C    != FLS_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (FLS_HW_AR_RELEASE_REVISION_VERSION_CFG_C != FLS_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Fls_Hw_Cfg.c and Fls_Hw_Types.h are different"
#endif
/* Check if current file and Fls_Hw_Types header file are of the same Software version */
#if ((FLS_HW_SW_MAJOR_VERSION_CFG_C != FLS_HW_TYPES_SW_MAJOR_VERSION) || \
     (FLS_HW_SW_MINOR_VERSION_CFG_C != FLS_HW_TYPES_SW_MINOR_VERSION) || \
     (FLS_HW_SW_PATCH_VERSION_CFG_C != FLS_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Fls_Hw_Cfg.c and Fls_Hw_Types.h are different"
#endif


/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/


/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/
#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

const FlsHwConfig_t FlsConfigSet_InitCfg =
{
    NULL_PTR,  /* FlsStartFlashAccessNotif */
    NULL_PTR,  /* FlsFinishedFlashAccessNotif */
    /*hardware time configure parameter*/
    {
         /* When FLASH page is erased, the NVSTR is created from top edge to bottom edge of ERASE */
        (uint32) 80800UL,
        /* When FALSH MASS and BLOCK are erased, the NVSTR is created from top edge to bottom edge of ERASE */
        (uint32) 1616000UL,
        /* When FLASH MASS erases, ERASE the hold time from bottom edge to bottom edge of NVSTR */
        (uint16) 8080U,
        /* FLASH recovery time */
        (uint16) 808U,
         /* Retention time from lower edge of FLASH PROG to lower edge of NVSTR */
        (uint16) 647U,
        /* FLASH programming hold time, that is, the hold time from SE bottom edge to PROG bottom edge */
        (uint16) 2U,
        /* FLASH YE programming time */
        (uint16) 647U,
         /* FLASH NVSTR Top edge to YE top edge hold time */
        (uint16) 162U,
        /* FLASH PROG/ERASE Creation time of NVSTR */
        (uint16) 647U,
        /* FLASH read cycle time */
        (uint8)  3U,
        /* FLASH access time. That is, the time from SE top edge to data validity */
        (uint8)  2U,
        /* FLASH SE is configured along the width */
        (uint8)  2U,
        /* FLASH recalls the XE/YE hold time */
        (uint8)  2U,
        /* FLASH RECALL lower edge hold time of SE */
        (uint8)  2U,
        /* FLASH RECALL High level hold time */
        (uint8)  9U,
         /* FLASH RECALL the creation time of SE rising edge */
        (uint8)  2UL,
    }

};

/* Fls module initialization data sFlsHwPartitionConfig*/
const FlsHWCfgPartition_t Fls_HwPartitionConfig  =
{
	FLS_NO_PARTITION,
	FALSE,
	TRUE,
	15U,
};

/* Fls module initialization data sFlsMacPartitionConfig*/
const FlsMacPartitionCfg_t Fls_MacPartitionConfig  =
{
	4294967295UL,
	{
		4294967295UL,	/* CMAC0 value */
		4294967295UL,	/* CMAC1 value */
		4294967295UL,	/* CMAC2 value */
		4294967295UL,	/* CMAC3 value */
	},
	4294967295UL,
	{
		4294967295UL,	/* Backup CMAC0 value */
		4294967295UL,	/* Backup CMAC1 value */
		4294967295UL,	/* Backup CMAC2 value */
		4294967295UL,	/* Backup CMAC3 value */
	},
	FALSE,
};

/* Fls module initialization data sFlsSecKeyPartitionConfig*/
const FlsSecPartitionCfg_t Fls_SecKeyPartitionConfig  =
{
	FALSE,
	FALSE,
	FALSE,
	FALSE,
	FLASH_BOOT,
	FLS_JTAG_ACCESS_NOVERIFY,
	{
		4294967295UL,	/* FlsJtagKey0 value */
		4294967295UL,	/* FlsJtagKey0 value */
		4294967295UL,	/* FlsJtagKey0 value */
		4294967295UL,	/* FlsJtagKey0 value */
	},

};

#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


#ifdef __cplusplus
}
#endif

/** @}*/
