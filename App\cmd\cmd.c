#include "cmd.h"
#include "motor.h"
#include "log.h"
#include "Pwm.h"
#include "Pwm_Cfg.h"
#include "Pwm_Types.h"
#include "os_module.h"
#include "pod_lin.h"
#include "mem.h"

static tMotorType cmd_to_motor_type(tCmdKind kind)
{
    switch (kind) {
    case CMD_BACK_REST:
        return MOTOR_1_BACK_REST;
#ifdef SEAT_SIDE_RIGHT
    case CMD_SEAT_LIFT:
        return MOTOR_2_SEAT_LIFT;
    case CMD_SEAT_SLIDE:
        return MOTOR_3_SEAT_SLIDE;
#endif
    case CMD_FRONT_BACK:
        return MOTOR_4_FRONT_BACK;
    case CMD_LEG_LIFT:
        return MOTOR_5_LEG_LIFT;
    case CMD_LEG_STRETCH:
        return MOTOR_6_LEG_STRETCH;
    default:
        return MOTOR_MAX;
    }
}

static tMotorOption action_to_option(tCmdAction action)
{
    switch (action) {
    case ACTION_POS:
        return OPTION_POS;
    case ACTION_NEG:
        return OPTION_NEG;
    case ACTION_OFF:
        return OPTION_OFF;
    }
}

/*****************************************
* Simple Motor Controls
******************************************/

void do_simple_motor(tCmd cmd)
{
    // simple commands
    tMotorType motor_type = cmd_to_motor_type(cmd.kind);
    if (MOTOR_MAX == motor_type)
    {
        println(LogError, "OS_MOTOR: motor type not found");
        return;
    }
    tMotor *motor = motor_find(motor_type);
    if (NULL == motor)
    {
        println(LogError, "OS_MOTOR: motor not found");
        return;
    }
    if (cmd.action != ACTION_NONE) {
        motor_go(motor, action_to_option(cmd.action));
    }
}

/*****************************************
* Composite Motor Controls
******************************************/
void do_zero_gravity(tCmd cmd)
{
    // zero gravity
    switch (cmd.action)
    {
    case ACTION_POS:
        motors_zero_gravity();
        break;
    case ACTION_NEG:
        motors_zero_recover();
        break;
    case ACTION_OFF:
    case ACTION_NONE:
        // do nothing
        break;
    }
}

/*****************************************
* Lin Controls
******************************************/
void do_massage(tCmd cmd)
{
    // Lin
    switch (cmd.action)
    {
    case ACTION_POS: // open massage
        os_submit_event_u16(EV(Lin, MassageEV), LIN_MASSAGE_SWITCH, LIN_MASSAGE_ON);
        break;
    default: // close massage
        os_submit_event_u16(EV(Lin, MassageEV), LIN_MASSAGE_SWITCH, LIN_MASSAGE_OFF);
        break;
    }
}

void do_massage_mode(tCmd cmd)
{
    // Lin
    os_submit_event_u16(EV(Lin, MassageEV), LIN_MASSAGE_MODE, cmd.level);
}

void do_massage_intensity(tCmd cmd)
{
    // Lin
    os_submit_event_u16(EV(Lin, MassageEV), LIN_MASSAGE_INTENSITY, cmd.level);
}




/*****************************************
* Single Chanel PWM Controls
******************************************/

/// @brief Set Single Channel PWM
/// @param channel Channel ID
/// @param pwm PWM value, max is MAX_PWM
/// @param action action::Pos -> On; Others -> Off
// static void do_single_pwm(Pwm_ChannelType channel, u32 pwm, tCmdAction action)
// {
//     if (pwm >= MAX_PWM) {
//         pwm = MAX_PWM;
//     }
//     switch (action)
//     {
//     case ACTION_POS:
//         // 设定固定PWM值
//         // TODO: 需要分档位吗？
//         Pwm_SetDutyCycle(channel, pwm);//设置A通道PWM占空比
//         break;
//     default:
//         // 关闭PWM输出
//         // z_PWM_SetGpcmCtrl(channel, 0, 4);
//         break;
//     }
// }

/// Heater related
static u32 heater_pwm_levels[4] = {0x0000, 0x2000, 0x3000, 0x4000};
// static u32 heater_pwm_levels[4] = {0x0000, 0x0B85, 0x170A, 0x228F};
void do_heater(bool on, u8 level, Pwm_ChannelType channel)
{
    u32 pwm = 0x3000;
    if (level < 4) {
        pwm = heater_pwm_levels[level];
    }
    if (on)
    {
        Pwm_SetDutyCycle(channel, pwm);
    } else {
        // 关闭PWM输出
        Pwm_SetDutyCycle(channel, 0x0000);
    }
}


static bool heater_back_on = false;
static u8 heater_back_level = 1;
void do_heater_back(tCmd cmd)
{
    heater_back_on = cmd.action == ACTION_POS;
    do_heater(heater_back_on, heater_back_level, PwmConf_PwmChannel_PwmChannel_Heat1);
}

static bool heater_seat_on = false;
static u8 heater_seat_level = 1;
void do_heater_seat(tCmd cmd)
{
    heater_seat_on = cmd.action == ACTION_POS;
    do_heater(heater_seat_on, heater_seat_level, PwmConf_PwmChannel_PwmChannel_Heat2);
}

void do_heater_back_level(tCmd cmd)
{
    heater_back_level = cmd.level;
    do_heater(heater_back_on, heater_back_level, PwmConf_PwmChannel_PwmChannel_Heat1);
}

void do_heater_seat_level(tCmd cmd)
{
    heater_seat_level = cmd.level;
    do_heater(heater_seat_on, heater_seat_level, PwmConf_PwmChannel_PwmChannel_Heat2);
}

void do_heater_all(tCmd cmd)
{
    do_heater_back(cmd);
    do_heater_seat(cmd);
}

// Fan related

// static u32 fan_pwm_levels_std[4] = {0x0000, 0x2CCC, 0x2000, 0x1333};
static u32 fan_pwm_levels[4] = {0x0000, 0x5333, 0x6000, 0x6CCC};
// static u32 fan_pwm_levels[4] = {0x0000, 0x2500, 0x4000, 0x6000};
static u32 fan_period = 10000;
static void do_fan(bool on, u8 level, Pwm_ChannelType channel)
{
    u32 fan_pwm = 0x4000;
    if (level < 4) {
        fan_pwm = fan_pwm_levels[level];
    }
    if (on)
    {
        println(LogInfo, "Fan [%d] turned on!", channel);
        Pwm_SetDutyCycle(channel, fan_pwm);
        // Pwm_SetPeriodAndDuty(channel, fan_period, fan_pwm);
    } else {
        // 关闭PWM输出
        println(LogInfo, "Fan [%d] turned off!", channel);
        Pwm_SetDutyCycle(channel, 0x0000);
    }
}

static bool fan_back_on = false;
static u8 fan_back_level = 1;
void do_fan_back(tCmd cmd)
{
    fan_back_on = cmd.action == ACTION_POS;
    do_fan(fan_back_on, fan_back_level, PwmConf_PwmChannel_PwmChannel_Fan1);
}

static bool fan_seat_on = false;
static u8 fan_seat_level = 1;
void do_fan_seat(tCmd cmd)
{
    fan_seat_on = cmd.action == ACTION_POS;
    do_fan(fan_seat_on, fan_seat_level, PwmConf_PwmChannel_PwmChannel_Fan2);
}

void do_fan_back_level(tCmd cmd)
{
    fan_back_level = cmd.level;
    do_fan(fan_back_on, fan_back_level, PwmConf_PwmChannel_PwmChannel_Fan1);
}

void do_fan_seat_level(tCmd cmd)
{
    fan_seat_level = cmd.level;
    do_fan(fan_seat_on, fan_seat_level, PwmConf_PwmChannel_PwmChannel_Fan2);
}

void do_fan_all(tCmd cmd)
{
    do_fan_back(cmd);
    do_fan_seat(cmd);
}

void do_waist(tCmd cmd)
{
    u8 action = 0;
    switch (cmd.action) {
    case WAIST_UP:
        //do_single_pwm(PwmConf_PwmChannel_PwmChannel_Waist_Left, DEFAULT_LEFT_WAIST_PWM, cmd.action);
        action = 1;
        break;
    case WAIST_DOWN:
        action = 2;
        //do_single_pwm(PwmConf_PwmChannel_PwmChannel_Waist_Left, DEFAULT_LEFT_WAIST_PWM, cmd.action);
        break;
    case WAIST_FRONT:
        action = 3;
        break;
    case WAIST_BACK:
        action = 4;
        break;
    }
    os_submit_event_u16_from_isr(EV(Lin, MassageEV), LIN_WAIST, action);
}

void waist_off(void)
{
    do_waist((tCmd){CMD_WAIST, WAIST_NONE, 0});
}
