/**
 * file    Adc_Ipw_PBcfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_IPW_PBCFG_H
#define ADC_IPW_PBCFG_H

/**
*   @file
*
*   @addtogroup adc_ipw_config Adc Ipw Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#include "Adc_HwType.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_IPW_VENDOR_ID_PBCFG                       (110u)
#define ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG        (4u)
#define ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG        (4u)
#define ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG     (0u)
#define ADC_IPW_SW_MAJOR_VERSION_PBCFG                (1u)
#define ADC_IPW_SW_MINOR_VERSION_PBCFG                (0u)
#define ADC_IPW_SW_PATCH_VERSION_PBCFG                (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check vendor id */
#if (ADC_IPW_VENDOR_ID_PBCFG != ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_Ipw_PBcfg.h and Adc_HwType.h have different vendor ids"
#endif

/* Check Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG != ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.h and Adc_HwType.h are different"
#endif

/* Check Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_PBCFG != ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_IPW_SW_MINOR_VERSION_PBCFG != ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_IPW_SW_PATCH_VERSION_PBCFG != ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_Ipw_PBcfg.h and Adc_HwType.h are different"
#endif

/*==================================================================================================
*                              STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/**
* @brief          Adc Ipw Config configuration variant .
*/
extern const Adc_Ipw_Config_t AdcIpwCfg;

/**
* @brief          ADC Ipw Group 0 Config .
*/
extern const Adc_Ipw_GroupConfig_t AdcIpwGroupConfig_0;

/**
* @brief          ADC Ipw Group 1 Config .
*/
extern const Adc_Ipw_GroupConfig_t AdcIpwGroupConfig_1;


#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_IPW_PBCFG_H */

