/**
 * file    Pdb_Adc_Hw_Cfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef PDB_ADC_HW_CFG_H
#define PDB_ADC_HW_CFG_H

/**
*   @file
*
*   @addtogroup pdb_adc_hw_config Pdb Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/
#include "Pdb_Adc_Hw_Types.h"
#include "Pdb_Adc_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define PDB_ADC_HW_VENDOR_ID_CFG                      (110u)
#define PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_CFG       (4u)
#define PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_CFG       (4u)
#define PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_CFG    (0u)
#define PDB_ADC_HW_SW_MAJOR_VERSION_CFG               (1u)
#define PDB_ADC_HW_SW_MINOR_VERSION_CFG               (0u)
#define PDB_ADC_HW_SW_PATCH_VERSION_CFG               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_Types.h file are of the same vendor */
#if (PDB_ADC_HW_VENDOR_ID_CFG != PDB_ADC_HW_VENDOR_ID_TYPES)
    #error "Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_Types.h have different vendor ids"
#endif

/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_Types.h file are of the same Autosar version */
#if ((PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_Types.h file are of the same Software version */
#if ((PDB_ADC_HW_SW_MAJOR_VERSION_CFG != PDB_ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (PDB_ADC_HW_SW_MINOR_VERSION_CFG != PDB_ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (PDB_ADC_HW_SW_PATCH_VERSION_CFG != PDB_ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_PBcfg.h file are of the same vendor */
#if (PDB_ADC_HW_VENDOR_ID_CFG != PDB_ADC_HW_VENDOR_ID_PBCFG)
    #error "Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_PBcfg.h file are of the same Autosar version */
#if ((PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_CFG != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_PBcfg.h are different"
#endif

/* Check if Pdb_Adc_Hw_Cfg.h file and Pdb_Adc_Hw_PBcfg.h file are of the same Software version */
#if ((PDB_ADC_HW_SW_MAJOR_VERSION_CFG != PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_SW_MINOR_VERSION_CFG != PDB_ADC_HW_SW_MINOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_SW_PATCH_VERSION_CFG != PDB_ADC_HW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Pdb_Adc_Hw_Cfg.h and Pdb_Adc_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PDB_ADC_HW_CFG_H */

