/**
 * file    Flexio_Pwm_Hw_CfgDefines.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_PWM_HW_CFGDEFINES_H
#define FLEXIO_PWM_HW_CFGDEFINES_H

/*==================================================================================================
*                                          INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_PWM_HW_CFGDEFINES_VENDOR_ID                    (110U)
#define FLEXIO_PWM_HW_CFGDEFINES_MODULE_ID                    (121U)
#define FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MAJOR_VERSION     (4U)
#define FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MINOR_VERSION     (4U)
#define FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_REVISION_VERSION  (0U)
#define FLEXIO_PWM_HW_CFGDEFINES_SW_MAJOR_VERSION             (1U)
#define FLEXIO_PWM_HW_CFGDEFINES_SW_MINOR_VERSION             (0U)
#define FLEXIO_PWM_HW_CFGDEFINES_SW_PATCH_VERSION             (0U)

/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Std_Types.h are of the same AUTOSAR version */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
         (FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR version numbers of Flexio_Pwm_HW_CfgDefines.h and Std_Types.h are different."
    #endif
#endif

/*==================================================================================================
*                                            CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       DEFINES AND MACROS
==================================================================================================*/
/** @brief      Flexio Timer has 8bit pwm low mode */
#define FLEXIO_PWM_HW_HAS_LOW_MODE              (STD_ON)



/* Defines for use with MCL Flexio common part */

/*==================================================================================================
*                                              ENUMS
==================================================================================================*/

/*==================================================================================================
*                                  STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                  GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                       FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_PWM_HW_CFGDEFINES_H */

