/**
 * 
 * @file Pwm_PBcfg.c
 * @brief 
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * @section[blobal]
 * Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
 * Reason: The macro definition names are similar, the version number definition prefixes are the same, 
 * and the keywords are different.
 *
 * @section[blobal]
 * Violations MISRA C 2012 Advisory Rule 10.5 , The value of an expression should not 
 * be cast to an inappropriate essential type.
 * Reason: When configuring the mask event, because some of them fire more than one event 
 * at the same time, this mask event is represented by int, and because the structure is 
 * defined as an enumeration type, it needs to be cast.
 *
 */
 
#include "Std_Types.h"
#include "Pwm_Types.h"
#include "Pwm_Hw_Types.h"
#include "Pwm_Hw_PBcfg.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define PWM_PBCFG_VENDOR_ID_C                    (110U)
#define PWM_PBCFG_AR_RELEASE_MAJOR_VERSION_C     (4U)     
#define PWM_PBCFG_AR_RELEASE_MINOR_VERSION_C     (4U)     
#define PWM_PBCFG_AR_RELEASE_REVISION_VERSION_C  (0U)     
#define PWM_PBCFG_SW_MAJOR_VERSION_C             (1U)
#define PWM_PBCFG_SW_MINOR_VERSION_C             (0U)
#define PWM_PBCFG_SW_PATCH_VERSION_C             (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
/* Check if header file and Std_Types.h file are of the same Autosar version */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((PWM_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (PWM_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Pwm_PBcfg.c and Std_Types.h are different"
    #endif
#endif

/* Check if source file and PWM header file are of the same vendor */
#if (PWM_PBCFG_VENDOR_ID_C != PWM_HW_TYPES_VENDOR_ID)
    #error "Pwm_PBcfg.c and Pwm_Hw_Types.h have different vendor IDs"
#endif
/* Check if source file and PWM header file are of the same AutoSar version */
#if ((PWM_PBCFG_AR_RELEASE_MAJOR_VERSION_C != PWM_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_MINOR_VERSION_C != PWM_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_REVISION_VERSION_C != PWM_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_PBcfg.c and Pwm_Hw_Types.h are different"
#endif
/* Check if source file and PWM header file are of the same Software version */
#if ((PWM_PBCFG_SW_MAJOR_VERSION_C != PWM_HW_TYPES_SW_MAJOR_VERSION) || \
     (PWM_PBCFG_SW_MINOR_VERSION_C != PWM_HW_TYPES_SW_MINOR_VERSION) || \
     (PWM_PBCFG_SW_PATCH_VERSION_C != PWM_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_PBcfg.c and Pwm_Hw_Types.h are different"
#endif

/* Check if source file and PWM header file are of the same vendor */
#if (PWM_PBCFG_VENDOR_ID_C != PWM_HW_PBCFG_VENDOR_ID)
    #error "Pwm_PBcfg.c and Pwm_Hw_PBcfg.h have different vendor IDs"
#endif
/* Check if source file and PWM header file are of the same AutoSar version */
#if ((PWM_PBCFG_AR_RELEASE_MAJOR_VERSION_C != PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_MINOR_VERSION_C != PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_REVISION_VERSION_C != PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_PBcfg.c and Pwm_Hw_PBcfg.h are different"
#endif
/* Check if source file and PWM header file are of the same Software version */
#if ((PWM_PBCFG_SW_MAJOR_VERSION_C != PWM_HW_PBCFG_SW_MAJOR_VERSION) || \
     (PWM_PBCFG_SW_MINOR_VERSION_C != PWM_HW_PBCFG_SW_MINOR_VERSION) || \
     (PWM_PBCFG_SW_PATCH_VERSION_C != PWM_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_PBcfg.c and Pwm_Hw_PBcfg.h are different"
#endif

/* Check if source file and PWM header file are of the same vendor */
#if (PWM_PBCFG_VENDOR_ID_C != PWM_TYPES_VENDOR_ID)
    #error "Pwm_PBcfg.c and Pwm_Types.h have different vendor IDs"
#endif
/* Check if source file and PWM header file are of the same AutoSar version */
#if ((PWM_PBCFG_AR_RELEASE_MAJOR_VERSION_C != PWM_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_MINOR_VERSION_C != PWM_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_REVISION_VERSION_C != PWM_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_PBcfg.c and Pwm_Types.h are different"
#endif
/* Check if source file and PWM header file are of the same Software version */
#if ((PWM_PBCFG_SW_MAJOR_VERSION_C != PWM_TYPES_SW_MAJOR_VERSION) || \
     (PWM_PBCFG_SW_MINOR_VERSION_C != PWM_TYPES_SW_MINOR_VERSION) || \
     (PWM_PBCFG_SW_PATCH_VERSION_C != PWM_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_PBcfg.c and Pwm_Types.h are different"
#endif


/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"


static const uint8 Pwm_ChIndexMap_PB[2U] = 
{
    0U,    /* PwmConfigSet has an index of 0 */
    1U    /* PwmConfigSet has an index of 1 */
};

/*
*  @brief    PB Configuration
*/
static const PwmChannelConfig_t Pwm_ChannelConfig_PB[16U]=
{
		{
        (uint8)0U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[0U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)1U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[1U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)2U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[2U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)3U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[3U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)4U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[4U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)5U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[5U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)6U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[6U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)7U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[7U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)8U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[8U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)9U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[9U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)10U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[10U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)11U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[11U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)12U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[12U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)13U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[13U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)14U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[14U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
		{
        (uint8)15U,
	        PWM_VARIABLE_PERIOD,
        &Pwm_Hw_IpChannelConfig_PB[15U], /* Ipw channel pointer */
        (Pwm_OutputStateType)PWM_LOW,
        (Pwm_NotifyType)NULL_PTR,
			},
};
		
const Pwm_ConfigType Pwm_Config=
{
    (uint8)16U, 
    /** @brief The number of channels configured*/
    &Pwm_ChannelConfig_PB, 
    /** @brief Index of channel in each partition map table*/
    &Pwm_ChIndexMap_PB,
    /** @brief  Index table to translate HW channels to logical used to process interrupts for notifications */
    {0U},
    /** @brief core index*/
    0U
};


#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

#ifdef __cplusplus
}
#endif


