/**
 * 
 * @file Icu_Cfg.h
 * @brief AUTOSAR Icu - contains the configuration data of the ICU driver
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef ICU_CFG_H
#define ICU_CFG_H

#include "Std_Types.h"
#include "Icu_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ICU_CFG_VENDOR_ID                       (110u)
#define ICU_CFG_AR_RELEASE_MAJOR_VERSION        (4u)
#define ICU_CFG_AR_RELEASE_MINOR_VERSION        (4u)
#define ICU_CFG_AR_RELEASE_REVISION_VERSION     (0u)
#define ICU_CFG_SW_MAJOR_VERSION                (1u)
#define ICU_CFG_SW_MINOR_VERSION                (0u)
#define ICU_CFG_SW_PATCH_VERSION                (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((ICU_CFG_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
         (ICU_CFG_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Icu_Cfg.h and Std_Types.h are different"
    #endif
#endif

/* Check if source file and Icu configuration header file are of the same vendor */
#if (ICU_PBCFG_VENDOR_ID != ICU_CFG_VENDOR_ID)
    #error "Icu_PBcfg.h and Icu_Cfg.h have different vendor IDs"
#endif
    /* Check if header file and Icu configuration header file are of the same Autosar version */
#if ((ICU_PBCFG_AR_RELEASE_MAJOR_VERSION != ICU_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_MINOR_VERSION != ICU_CFG_AR_RELEASE_MINOR_VERSION) || \
     (ICU_PBCFG_AR_RELEASE_REVISION_VERSION != ICU_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_PBcfg.h and Icu_Cfg.h are different"
#endif
/* Check if header file and Icu configuration header file are of the same software version */
#if ((ICU_PBCFG_SW_MAJOR_VERSION != ICU_CFG_SW_MAJOR_VERSION) || \
     (ICU_PBCFG_SW_MINOR_VERSION != ICU_CFG_SW_MINOR_VERSION) || \
     (ICU_PBCFG_SW_PATCH_VERSION != ICU_CFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_PBcfg.h and Icu_Cfg.h are different"
#endif

/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
#define ICU_CONFIG_EXT \
        ICU_CONFIG_PB


/** @brief Maximum number of ICU channels configured. */
#define ICU_MAX_CHANNEL                 ((Icu_ChannelType)12U)

/**
*   @brief  Switches the Development Error Detection and Notification on or off.
*        STD_ON: Enabled.
*        STD_OFF: Disabled.
*   @implements ICU_DEV_ERROR_DETECT_define
*/
#define ICU_DEV_ERROR_DETECT            (STD_OFF)

/**
*   @brief  Switch for enabling Wakeup source reporting.
*        STD_ON: Report Wakeup source.
*        STD_OFF: Do not report Wakeup source.
*
*/
#define ICU_REPORT_WAKEUP_SOURCE        (STD_OFF)

/** @brief Configuration of Optional APIs. */
/**
*   @brief  Adds or removes the service Icu_GetVersionInfo() from the code.
*        STD_ON: Icu_GetVersionInfo() can be used.
*        STD_OFF: Icu_GetVersionInfo() can not be used.
*
*/
#define ICU_GET_VERSION_INFO_API          (STD_OFF)

/**
*   @brief  Adds or removes the service Icu_DeInit() from the code.
*        STD_ON: Icu_DeInit() can be used. STD_OFF: Icu_DeInit() can not be used.
*   @implements ICU_DE_INIT_API_define
*/
#define ICU_DE_INIT_API                   (STD_ON)

/**
*   @brief  Adds or removes the service Icu_SetMode() from the code.
*        STD_ON: Icu_SetMode() can be used.
*        STD_OFF: Icu_SetMode() can not be used.
*   @implements ICU_SET_MODE_API_define
*/
#define ICU_SET_MODE_API                  (STD_OFF)

/**
*   @brief  Adds or removes the service Icu_DisableWakeup() from the code.
*        STD_ON: Icu_DisableWakeup() can be used.
*        STD_OFF: Icu_DisableWakeup() can not be used.
*   @implements ICU_DISABLE_WAKEUP_API_define
*/
#define ICU_DISABLE_WAKEUP_API            (STD_OFF)

/**
*   @brief  Adds or removes the service Icu_EnableWakeup() from the code.
*        STD_ON: Icu_EnableWakeup() can be used.
*        STD_OFF: Icu_EnableWakeup() can not be used.
*   @implements ICU_ENABLE_WAKEUP_API_define
*/
#define ICU_ENABLE_WAKEUP_API             (STD_OFF)

/**
*   @brief  Adds or removes all services related to the timestamping functionality as listed
*        below from the code: Icu_StartTimestamp(), Icu_StopTimestamp(), Icu_GetTimestampIndex().
*        STD_ON: The services listed above can be used.
*        STD_OFF: The services listed above can not be used.
*   @implements ICU_TIMESTAMP_API_define
*/
#define ICU_TIMESTAMP_API                 (STD_ON)

/**
*   @brief  Adds or removes all services related to the edge counting functionality as listed below,
*           from the code: Icu_ResetEdgeCount(), Icu_EnableEdgeCount(), Icu_DisableEdgeCount(),
*           Icu_GetEdgeNumbers().
*        STD_ON: The services listed above can be used.
*        STD_OFF: The services listed above can not be used.
*   @implements ICU_EDGE_COUNT_API_define
*/
#define ICU_EDGE_COUNT_API                (STD_OFF)

/**
*   @brief  Adds or removes the service Icu_GetTimeElapsed() from the code.
*        STD_ON: Icu_GetTimeElapsed() can be used.
*        STD_OFF: Icu_GetTimeElapsed() can not be used.
*   @implements ICU_GET_TIME_ELAPSED_API_define
*/
#define ICU_GET_TIME_ELAPSED_API          (STD_OFF)

/**
*   @brief  Adds or removes the service Icu_GetDutyCycleValues() from the code.
*        STD_ON: Icu_GetDutyCycleValues() can be used.
*        STD_OFF: Icu_GetDutyCycleValues() can not be used.
*   @implements ICU_GET_DUTY_CYCLE_VALUES_API_define
*/
#define ICU_GET_DUTY_CYCLE_VALUES_API     (STD_ON)

/**
*   @brief  Adds or removes the service Icu_GetInputState() from the code.
*        STD_ON: Icu_GetInputState() can be used.
*        STD_OFF: Icu_GetInputState() can not be used.
*   @implements ICU_GET_INPUT_STATE_API_define
*/
#define ICU_GET_INPUT_STATE_API           (STD_OFF)

/**
*   @brief  Adds or removes the services Icu_StartSignalMeasurement() and
*          Icu_StopSignalMeasurement() from the code.
*        STD_ON: Icu_StartSignalMeasurement() and Icu_StopSignalMeasurement() can be used.
*        STD_OFF: Icu_StartSignalMeasurement() and Icu_StopSignalMeasurement() can not be used.
*   @implements ICU_SIGNAL_MEASUREMENT_API_define
*/
#define ICU_SIGNAL_MEASUREMENT_API        (STD_ON)

/**
*   @brief  Adds or removes the service Icu_CheckWakeup() from the code.
*        STD_ON: Icu_CheckWakeup() can be used.
*        STD_OFF: Icu_CheckWakeup() can not be used.
*   @implements ICU_WAKEUP_FUNCTIONALITY_API_define
*/
#define ICU_WAKEUP_FUNCTIONALITY_API      (STD_OFF)

/**
*   @brief  Adds or removes the services Icu_EnableEdgeDetection() and Icu_DisableEdgeDetection()
*           from the code.
*        STD_ON: Icu_EnableEdgeDetection() and Icu_DisableEdgeDetection() can be used.
*        STD_OFF: Icu_EnableEdgeDetection() and Icu_DisableEdgeDetection() can not be used.
*
*   @implements  ICU_EDGE_DETECT_API_define
*/
#define ICU_EDGE_DETECT_API               (STD_ON)



/**
 * @brief Adds or removes the support for TimeStamp Measurement with DMA.
 *        STD_ON:  DMA in TimeStamp Measurement can be used.
 *        STD_OFF: DMA in TimeStamp Measurement can not be used.
 */
#define ICU_TIMESTAMP_USES_DMA            (STD_OFF)

#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
/**
 * @brief Implementation specific.
 *        Each channel provides a DMA resolution of 32 bits.
 */
#define ICU_DMA_SIZE                (4u)

/**
 * @brief Implementation specific.
 *        DMA number of bytes transfer on a minor loop is 4 bytes - for 32 bits hw registers.
 */
#define ICU_DMA_NUM_BYTES           (4U)

/**
 * @brief Implementation specific.
 *        DMA offset is 32 bits HW registers.
 */
#define ICU_DMA_OFFSET              (4U)

/** @brief Define when no MCL DMA channel is used. */
#define NoDmaChannel                ((Icu_DmaChannelType)0xFFu)

#endif /* (ICU_TIMESTAMP_USES_DMA == STD_ON). */

#define IcuConf_IcuChannel_IcuChannel_HALL1        ((Icu_ChannelType)0U)
#define IcuConf_IcuChannel_IcuChannel_HALL2        ((Icu_ChannelType)1U)
#define IcuConf_IcuChannel_IcuChannel_HALL3        ((Icu_ChannelType)2U)
#define IcuConf_IcuChannel_IcuChannel_HALL4        ((Icu_ChannelType)3U)
#define IcuConf_IcuChannel_IcuChannel_HALL5        ((Icu_ChannelType)4U)
#define IcuConf_IcuChannel_IcuChannel_HALL6        ((Icu_ChannelType)5U)
#define IcuConf_IcuChannel_IcuChannel_0        ((Icu_ChannelType)6U)
#define IcuConf_IcuChannel_IcuChannel_1        ((Icu_ChannelType)7U)
#define IcuConf_IcuChannel_IcuChannel_2        ((Icu_ChannelType)8U)
#define IcuConf_IcuChannel_IcuChannel_3        ((Icu_ChannelType)9U)
#define IcuConf_IcuChannel_IcuChannel_4        ((Icu_ChannelType)10U)
#define IcuConf_IcuChannel_IcuChannel_5        ((Icu_ChannelType)11U)

/**
* @brief          Number of configured partitions.
*/
#define ICU_MAX_PARTITIONS                    (1U)

#define ICU_MULTICORE_SUPPORT             (STD_OFF)

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/
/**
*   @brief Implementation specific. This type shall be chosen in order to have the most efficient
*       implementation on a specific micro-controller platform.
*       Range: 0  to width of the timer register.
*       Description: Width of the buffer for timestamps ticks and measured elapsed time ticks
*/
typedef uint32 Icu_TimerRegisterWidthType;

#if (STD_ON == ICU_TIMESTAMP_API)
/**
*   @brief Implementation specific. This type shall be chosen in order to have the most efficient
*       implementation on a specific micro-controller platform.
*       Description: Type, to abstract the return value of the service Icu_GetTimestampIndex().
*/
typedef uint16 Icu_HwSpecificIndexType;
#endif /* (STD_ON == ICU_TIMESTAMP_API). */

#if (STD_ON == ICU_EDGE_COUNT_API)
/**
*   @brief Implementation specific. This type shall be chosen in order to have the most efficient
*       implementation on a specific micro-controller platform.
*       Description: Type, to abstract the return value of the service Icu_GetEdgeNumbers().
*/
typedef uint32 Icu_HwSpecificEdgeNumberType;
#endif /* (STD_ON == ICU_EDGE_COUNT_API). */

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif    /* ICU_CFG_H */


