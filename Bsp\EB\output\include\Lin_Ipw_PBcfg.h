/**
 * @file     Lin_Ipw_PBcfg.h
 * @brief    Lin_Ipw_PBcfg Software h files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 **/


#ifndef LIN_IPW_PBCFG_H
#define LIN_IPW_PBCFG_H


/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/
#include "Lin_Ipw_Types.h"


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LIN_IPW_PBCFG_VENDOR_ID                     (110u)
#define LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION      (4u)
#define LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION      (4u)
#define LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION   (0u)
#define LIN_IPW_PBCFG_SW_MAJOR_VERSION              (1u)
#define LIN_IPW_PBCFG_SW_MINOR_VERSION              (0u)
#define LIN_IPW_PBCFG_SW_PATCH_VERSION              (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Lin_Ipw_Types.h */
#if (LIN_IPW_PBCFG_VENDOR_ID != LIN_IPW_TYPES_VENDOR_ID)
    #error "Lin_Ipw_PBcfg.h and Lin_Ipw_Types.h have different vendor ids"
#endif
#if ((LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION    != LIN_IPW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION    != LIN_IPW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION != LIN_IPW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Lin_Ipw_PBcfg.h and Lin_Ipw_Types.h are different"
#endif
/* Check if current file and Lin_Ipw_Types.h header file are of the same Software version */
#if ((LIN_IPW_PBCFG_SW_MAJOR_VERSION != LIN_IPW_TYPES_SW_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_MINOR_VERSION != LIN_IPW_TYPES_SW_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_PATCH_VERSION != LIN_IPW_TYPES_SW_PATCH_VERSION) )
    #error "Software Version Numbers of Lin_Ipw_PBcfg.h and Lin_Ipw_Types.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

#define LIN_IPW_CONFIG_PB \

    extern const LinHwConfig_t Lin_Ipw_pHwConfigPB_0;\
    
    extern const LinHwConfig_t Lin_Ipw_pHwConfigPB_1;\
    

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_IPW_PBCFG_H */
