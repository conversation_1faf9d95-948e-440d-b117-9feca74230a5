#include "os_pod.h"
#include "pod_lin.h"
/// ---------- begin of guard: <includes> -------------------------------------
#include "log.h"
#include "types.h"

#include "Lin.h"

#include "Com.h"
#include "ComM.h"
#include "ComM_PBCfg.h"
#include "ComM_Gent.h"

#include "LinIf.h"
#include "LinSM.h"
#include "PduR.h"
#include "SchM_Com.h"
#include "SchM_Dcm.h"
#include "SchM_Dem.h"
#include "SchM_LinIf.h"
#include "SchM_LinSM.h"

#include "lin_events.h"
/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------
#define LIN_MASTER_SEND LIN_FRAMERESPONSE_TX
#define LIN_MASTER_RECEIVE  LIN_FRAMERESPONSE_RX
#define T_LIN_TEST_FRM_NUM      (2U)
#define LIN_FRAME_LEN           (8U)
#define T_LIN_TIME_OUT          40000

static uint32 tick_ctr = 0;
uint8 g_Lin_MasterSendFrameIdx = 0U;
uint8 receive_signal_array[COM_RXSIGNAL_NUMBER];

uint8 Com_ReceiveSignalWithoutDup(uint8 signalId, uint8* signalDataPtr)
{
    uint8 ret = Com_ReceiveSignal(signalId, signalDataPtr);
    if (ret == E_OK )
    {
        if (*signalDataPtr != receive_signal_array[signalId])
        {
            receive_signal_array[signalId] = *signalDataPtr;
            ret = E_OK;
        }
        else
        {
            ret = E_NOT_OK;
        }
    }
    return ret;
}

/* volatile  */uint8 T_Lin_Data[LIN_FRAME_LEN] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
/* volatile  */uint8 R_Lin_DATA[LIN_FRAME_LEN] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
const Lin_PduType Lin_FrameData[T_LIN_TEST_FRM_NUM] =
{
    {0x32U, LIN_CLASSIC_CS, LIN_MASTER_SEND, 8U, T_Lin_Data},
    {0x30U, LIN_CLASSIC_CS, LIN_MASTER_RECEIVE, 8U, R_Lin_DATA}
};

Lin_Seat_Events lin_Seat_Events;

void SlaveCallBack(Lin_PduType * PduPtr)
{

}

static void receive_cmd(uint8 signalId)
{
    uint8 signal = 0;
    if (E_OK == Com_ReceiveSignalWithoutDup(signalId, &signal))
    {
        log_printf_blocking("lin receive signalId %d signal %d\n",signalId,signal);
        os_submit_event_u16(EV(Cmd, LinEV), signalId, signal);
    }
}

static uint8 cmd_signals[CMD_SIGNALS_NUM] =
{
#ifdef DRIVER_8_WAY_CONTROLLER
    DSMDiverReqMasStFeedback_MLCU_DSCU_1,
    DsmParkMassageModSts_MLCU_DSCU_1    ,
    DsmParkMassageStrSts_MLCU_DSCU_1    ,
    DrvPmpFailr_MLCU_DSCU_1             ,
    DrvSysVoltagefault_MLCU_DSCU_1      ,
    DrvMLCUResponseErr_MLCU_DSCU_1      ,
    DrvSoftversion_MLCU_DSCU_1          ,
    DrvLumbsts_MLCU_DSCU_1             ,
#endif
// #ifdef SEAT_SIDE_LEFT
    DSS_ForwardBack_SwitchActiveSts_DSS_DSCU,          
    DSS_SeatBack_SwitchActiveSts_DSS_DSCU,             
    DSS_LegRest_SwitchActiveSts_DSS_DSCU,             
    DSS_LegRest_SwitchActiveSts_FB_DSS_DSCU,             
// #endif
// #ifdef SEAT_SIDE_RIGHT
//     PSS_ForwardBack_SwitchActiveSts_PSS_DSCU,        
//     PSS_SeatBack_SwitchActiveSts_PSS_DSCU,             
//     PSS_LegRest_SwitchActiveSts_PSS_DSCU,             
//     PSS_LegRest_SwitchActiveSts_FB_PSS_DSCU,             
// #endif
};

static void lin_receive_cmds(void)
{
    for (uint8 i = 0; i < CMD_SIGNALS_NUM; ++i)
    {
        receive_cmd(cmd_signals[i]);
    }
}
/// ---------- end of guard:   <header> ---------------------------------------

void pod_lin_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_lin_on_init> -----------------------------------------
    memset(&lin_Seat_Events, 0, sizeof(lin_Seat_Events));
    os_timer_start(TmLin2ms);
    log_printf_blocking("os lin root entry\n");
/// ---------- end of guard:   <pod_lin_on_init> -----------------------------------------
}

void pod_lin_on_massage_event(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_lin_on_massage_event> -----------------------------------------
    // TODO: use a struct to handle more signals
    u8 massage_cmd = (u8) ev->as.u16[0];
    u8 massage_level = (u8) ev->as.u16[1];
    switch (massage_cmd) {
    case LIN_MASSAGE_SWITCH:
        Com_SendSignal(CDCDiverMassReqSet_DSM_LIN1, &massage_level);
        break;
    case LIN_MASSAGE_MODE:
        Com_SendSignal(CdcDsmMssgModSet_DSM_LIN1, &massage_level);
        break;
    case LIN_MASSAGE_INTENSITY:
        Com_SendSignal(CdcDsmMssgStrngthSet_DSM_LIN1, &massage_level);
        break;
    case LIN_WAIST:
        Com_SendSignal(CdcDsmLumbSet_DSM_LIN1, &massage_level);
        break;
    }
/// ---------- end of guard:   <pod_lin_on_massage_event> -----------------------------------------
}

void pod_lin_on_tm2ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_lin_on_tm2ms> -----------------------------------------
    uint32 count = 0;
    count += 1;

/*----------------------------lin mainfunction start----------------------------*/
    //lin_task();

    LinIf_MainFunction();
    LinSM_MainFunction();
    ComM_MainFunction_ComMChannel_DSCU();
    Com_MainFunctionRx();
    Com_MainFunctionTx();

    
    if(tick_ctr++ == 50){//100ms
        LinIf_ScheduleRequest(LIN_CHANNEL_ID_0,LinIfScheduleTable_schedule_1);
        // os_submit_event_ptr(EV(Motor, SeatEvents),4,&lin_Seat_Events);
        tick_ctr=0;
        lin_receive_cmds();
    }

/*----------------------------lin mainfunction end----------------------------*/
/// ---------- end of guard:   <pod_lin_on_tm2ms> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------