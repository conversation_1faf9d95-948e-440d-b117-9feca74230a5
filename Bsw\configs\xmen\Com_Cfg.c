/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Com_Cfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-09-01 09:26:30 
*  @date               : 2025-06-03 11:13:57 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "Com.h"
#include "Com_Callout.h"
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/
#define COM_CFG_C_AR_MAJOR_VERSION  4u
#define COM_CFG_C_AR_MINOR_VERSION  2u
#define COM_CFG_C_AR_PATCH_VERSION  2u
#define COM_CFG_C_SW_MAJOR_VERSION  2u
#define COM_CFG_C_SW_MINOR_VERSION  0u
#define COM_CFG_C_SW_PATCH_VERSION  0u

/*******************************************************************************
**                      Version Check                                         **
*******************************************************************************/
#if (COM_CFG_C_AR_MAJOR_VERSION != COM_CFG_H_AR_MAJOR_VERSION)
    #error "Com.c:Mismatch in Specification Major Version"
#endif

#if (COM_CFG_C_AR_MINOR_VERSION != COM_CFG_H_AR_MINOR_VERSION)
    #error "Com.c:Mismatch in Specification Minor Version"
#endif

#if (COM_CFG_C_AR_PATCH_VERSION != COM_CFG_H_AR_PATCH_VERSION)
    #error "Com.c:Mismatch in Specification Patch Version"
#endif

#if (COM_CFG_C_SW_MAJOR_VERSION != COM_CFG_H_SW_MAJOR_VERSION)
    #error "Com.c:Mismatch in Specification Major Version"
#endif

#if (COM_CFG_C_SW_MINOR_VERSION != COM_CFG_H_SW_MINOR_VERSION)
    #error "Com.c:Mismatch in Specification Minor Version"
#endif

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
/* PRQA S 1531,3408,1533 ++ */ /* MISRA Rule 8.7,8.4,8.9 */
#define COM_START_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"
VAR(uint8, COM_VAR) Com_TxIPduRuntimeBuff[COM_TXIPDUBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint8,COM_CONST_PBCFG) Com_TxIPduInitValue[COM_TXIPDUBUFF_SIZE] = {
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"
VAR(uint8, COM_VAR) Com_RxIPduRuntimeBuff[COM_RXIPDUBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint8,COM_CONST_PBCFG) Com_RxIPduInitValue[COM_RXIPDUBUFF_SIZE]= {
    0x00,0x00,0x00,0x07,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_BOOLEAN
#include "Com_MemMap.h"
VAR(boolean, COM_VAR) Com_SignalBoolRuntimeBuff[COM_SIGNAL_BOOLBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_BOOLEAN
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_BOOLEAN
#include "Com_MemMap.h"
CONST(boolean,COM_CONST_PBCFG) Com_SignalBoolInitValue[COM_SIGNAL_BOOLBUFF_SIZE] = {
    /* DrvPmpFailr_MLCU_DSCU_1*/
    FALSE,
    /* DrvMLCUResponseErr_MLCU_DSCU_1*/
    FALSE,
    /* PsgRPmpFailr_MLCU_DSCU_2*/
    FALSE,
    /* PsgRMLCUResponseErr_MLCU_DSCU_2*/
    FALSE,
    /* DSS_ResponseErr_DSS_DSCU*/
    FALSE,
    /* PSS_ResponseErr_PSS_DSCU*/
    FALSE,
};
#define COM_STOP_SEC_PBCONFIG_DATA_BOOLEAN
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"
VAR(uint8, COM_VAR) Com_Signal8BitRuntimeBuff[COM_SIGNAL_8BITBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint8,COM_CONST_PBCFG) Com_Signal8BitInitValue[COM_SIGNAL_8BITBUFF_SIZE] = {
    0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_16
#include "Com_MemMap.h"
VAR(uint16, COM_VAR) Com_Signal16BitRuntimeBuff[COM_SIGNAL_16BITBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_16
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"
CONST(uint16,COM_CONST_PBCFG) Com_Signal16BitInitValue[COM_SIGNAL_16BITBUFF_SIZE]={
    0x0u,0x0u,0x0u,0x0u,0x0u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_64
#include "Com_MemMap.h"
VAR(uint64, COM_VAR) Com_Signal64BitRuntimeBuff[COM_SIGNAL_64BITBUFF_SIZE];
#define COM_STOP_SEC_VAR_NO_INIT_64
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_64
#include "Com_MemMap.h"
CONST(uint64,COM_CONST_PBCFG) Com_Signal64BitInitValue[COM_SIGNAL_64BITBUFF_SIZE]={
    0x0u,0x0u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_64
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint8,COM_CONST_PBCFG) Com_Signal8BitInvalidValue[COM_SIGNAL_8BIT_INVALID_SIZE]={
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    0x00,0x00,0x00,0x00,0x00,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"
CONST(uint16,COM_CONST_PBCFG) Com_Signal16BitInvalidValue[COM_SIGNAL_16BIT_INVALID_SIZE]={
    0x0u,0x0u,0x0u,0x0u,0x0u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_64
#include "Com_MemMap.h"
CONST(uint64,COM_CONST_PBCFG) Com_Signal64BitInvalidValue[COM_SIGNAL_64BIT_INVALID_SIZE]={
    0x0u,0x0u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_64
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_UNSPECIFIED
#include "Com_MemMap.h"
VAR(Com_RxIPduRunTimeStateType, COM_VAR) Com_RxIPduRunTimeState[COM_RXIPDU_NUMBER];
#define COM_STOP_SEC_VAR_NO_INIT_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
CONST(Com_RxIPduRunTimeStateType, COM_CONST) Com_RxIPduInitState[COM_RXIPDU_NUMBER]=
{
    {
        /* IPDU_COM_CCU_1 */
        64u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_CCU_ZCUL_HVSPRL_1 */
        32u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_CCU_ZCUR_HVSPRR_1 */
        32u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_IDC_1 */
        64u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_MLCU_DSCU_1 */
        8u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_MLCU_DSCU_2 */
        8u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_DSS_DSCU */
        8u,
        0u,

        0u,

    },
    {
        /* IPDU_COM_PSS_DSCU */
        8u,
        0u,

        0u,

    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"
CONST(Com_RxIpduGroupIdType, COM_CONST) Com_RxIPduGroupsRef[COM_RXIPDUGROUP_NUMBER] =
{

    Com_RxPduGroup_CanController_0,
    Com_RxPduGroup_CanController_0,
    Com_RxPduGroup_CanController_0,
    Com_RxPduGroup_CanController_0,
    ComIPduGroup_DSCU_Rx,
    ComIPduGroup_DSCU_Rx,
    ComIPduGroup_DSCU_Rx,
    ComIPduGroup_DSCU_Rx,
};
#define COM_STOP_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"

#define COM_START_SEC_VAR_NO_INIT_UNSPECIFIED
#include "Com_MemMap.h"
VAR(Com_TxIPduRunTimeStateType, COM_VAR) Com_TxIPduRunTimeState[COM_TXIPDU_NUMBER];
#define COM_STOP_SEC_VAR_NO_INIT_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
CONST(Com_TxIPduRunTimeStateType, COM_CONST) Com_TxIPduInitState[COM_TXIPDU_NUMBER]=
{
    {
        /* IPDU_COM_CD_1*/
        8u,

        0u,
        0u,
        0u,
        0u,
        0u,

        4u,
        COM_TX_MODE_NONE,
    },
    {
        /* IPDU_COM_DSM_LIN1*/
        8u,

        0u,
        0u,
        0u,
        0u,
        0u,

        4u,
        COM_TX_MODE_NONE,
    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
static CONST(Com_RxIPduType, COM_CONST) Com_RxIPdu[COM_RXIPDU_NUMBER]=
{
    {
        /* IPDU_COM_CCU_1*/

        &COM_RX_CCU_1_Callout,
        64u,
        0u,
        0u,

        0u,
        1u,

        0u,
        1u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_CCU_ZCUL_HVSPRL_1*/

        &COM_RX_CCU_ZCUL_HVSPRL_1_Callout,
        32u,
        0u,
        64u,

        1u,
        5u,

        1u,
        2u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_CCU_ZCUR_HVSPRR_1*/

        &COM_RX_CCU_ZCUR_HVSPRR_1_Callout,
        32u,
        0u,
        96u,

        5u,
        9u,

        2u,
        3u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_IDC_1*/

        &COM_RX_IDC_1_Callout,
        64u,
        0u,
        128u,

        9u,
        11u,

        3u,
        4u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_MLCU_DSCU_1*/

        NULL_PTR,
        8u,
        0u,
        192u,

        11u,
        19u,

        4u,
        5u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_MLCU_DSCU_2*/

        NULL_PTR,
        8u,
        0u,
        200u,

        19u,
        27u,

        5u,
        6u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_DSS_DSCU*/

        NULL_PTR,
        8u,
        0u,
        208u,

        27u,
        32u,

        6u,
        7u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {
        /* IPDU_COM_PSS_DSCU*/

        NULL_PTR,
        8u,
        0u,
        216u,

        32u,
        37u,

        7u,
        8u,

        COM_UNUSED_RXSIGNALGROUPID,0u,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"
CONST(Com_TxIpduGroupIdType, COM_CONST) Com_TxIPduGroupsRef[COM_TXIPDUGROUP_NUMBER] =
{

    Com_TxPduGroup_CanController_0,
    ComIPduGroup_DSCU_Tx,
};
#define COM_STOP_SEC_PBCONFIG_DATA_16
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
static CONST(Com_TxIPduType, COM_CONST) Com_TxIPdu[COM_TXIPDU_NUMBER]=
{
    {   /* IPDU_COM_CD_1*/

        8u,
        0u,

        0u,
        0u,
        0u,
        PDUR_SRCPDU_COM_CD_1,
        COM_UNUSED_UINT16,
        COM_UNUSED_UINT16,

        0u,5u,

       COM_UNUSED_TXSIGNALGROUPID,0u,

        0u,1u,

        COM_TX_MODE_NONE,
        COM_TX_MODE_NONE,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
    {   /* IPDU_COM_DSM_LIN1*/

        8u,
        0u,

        0u,
        0u,
        8u,
        PDUR_SRCPDU_Com_DSM_LIN1,
        COM_UNUSED_UINT16,
        COM_UNUSED_UINT16,

        5u,13u,

       COM_UNUSED_TXSIGNALGROUPID,0u,

        1u,2u,

        COM_TX_MODE_NONE,
        COM_TX_MODE_NONE,

        0u,

        COM_DEFERRED,
        COM_PDU_NORMAL,
    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint16, COM_CONST) Com_RxSignalDataInvalidValueIdRef[COM_RXSIGNAL_INVALID_NUMBER] =
{
    /* UMM_UsageModeSt_IPDU_COM_CCU_1*/
    0u,
    /* HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    1u,
    /* HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    2u,
    /* HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    3u,
    /* HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    4u,
    /* HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    5u,
    /* HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    6u,
    /* HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    7u,
    /* HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    8u,
    /* ACU_CDSwSt_IPDU_COM_IDC_1*/
    9u,
    /* ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1*/
    10u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(Com_DataInvalidActionType, COM_CONST) Com_RxSignalDataInvalidActionRef[COM_RXSIGNAL_INVALID_NUMBER] =
{
    /* UMM_UsageModeSt_IPDU_COM_CCU_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* ACU_CDSwSt_IPDU_COM_IDC_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1*/
    COM_INVALID_ACTION_NOTIFY,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_32
#include "Com_MemMap.h"
CONST(Com_RxInvalidNotificationType, COM_CONST) Com_RxInvalidNotificationCfg[COM_RXSIGNAL_INVALID_NUMBER] =
{
    /* UMM_UsageModeSt_IPDU_COM_CCU_1*/
    &Rte_COMCbkInv_UMM_UsageModeSt_IPDU_COM_CCU_1,
    /* HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    &Rte_COMCbkInv_HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1,
    /* HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    &Rte_COMCbkInv_HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1,
    /* HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    &Rte_COMCbkInv_HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1,
    /* HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/
    &Rte_COMCbkInv_HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1,
    /* HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    &Rte_COMCbkInv_HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1,
    /* HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    &Rte_COMCbkInv_HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1,
    /* HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    &Rte_COMCbkInv_HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1,
    /* HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/
    &Rte_COMCbkInv_HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1,
    /* ACU_CDSwSt_IPDU_COM_IDC_1*/
    &Rte_COMCbkInv_ACU_CDSwSt_IPDU_COM_IDC_1,
    /* ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1*/
    &Rte_COMCbkInv_ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1,
};
#define COM_STOP_SEC_PBCONFIG_DATA_32
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
static CONST(Com_RxSignalType, COM_CONST) Com_RxSignal[COM_RXSIGNAL_NUMBER]=
{
    {
        /* UMM_UsageModeSt_IPDU_COM_CCU_1*/

        0u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        3u,

        0u,
        0u,

        0u,
        3u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/

        1u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        9u,

        1u,
        1u,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/

        2u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        9u,

        1u,
        2u,

        6u,
        2u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/

        3u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        5u,

        1u,
        3u,

        4u,
        3u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1*/

        4u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        5u,

        1u,
        4u,

        7u,
        7u,
        2u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/

        5u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        9u,

        2u,
        5u,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/

        6u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        5u,

        2u,
        6u,

        4u,
        3u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/

        7u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        5u,

        2u,
        7u,

        7u,
        7u,
        2u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1*/

        8u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        9u,

        2u,
        8u,

        6u,
        2u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* ACU_CDSwSt_IPDU_COM_IDC_1*/

        9u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        27u,

        3u,
        9u,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1*/

        10u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        27u,

        3u,
        10u,

        2u,
        3u,
        1u,
        COM_UINT8,
        COM_BIG_ENDIAN,

    },
    {
        /* DSMDiverReqMasStFeedback_MLCU_DSCU_1*/

        155u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DsmParkMassageModSts_MLCU_DSCU_1*/

        156u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        4u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DsmParkMassageStrSts_MLCU_DSCU_1*/

        157u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DrvPmpFailr_MLCU_DSCU_1*/

        0u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        3u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DrvSysVoltagefault_MLCU_DSCU_1*/

        158u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DrvMLCUResponseErr_MLCU_DSCU_1*/

        1u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        7u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DrvSoftversion_MLCU_DSCU_1*/

        159u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        2u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        8u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DrvLumbsts_MLCU_DSCU_1*/

        167u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        3u,

        4u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        3u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSMpassReqMassFeedback_MLCU_DSCU_2*/

        160u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsmParkMassageModSts_MLCU_DSCU_2*/

        161u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        4u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DsmPassMasStrSts_MLCU_DSCU_2*/

        162u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsgRPmpFailr_MLCU_DSCU_2*/

        2u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        3u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsgRSysVoltagefault_MLCU_DSCU_2*/

        163u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsgRMLCUResponseErr_MLCU_DSCU_2*/

        3u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        7u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsgRSoftversion_MLCU_DSCU_2*/

        164u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        2u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        8u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PsgRLumbsts_MLCU_DSCU_2*/

        168u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        3u,

        5u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        3u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSS_ForwardBack_SwitchActiveSts_DSS_DSCU*/

        169u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        6u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSS_LegRest_SwitchActiveSts_FB_DSS_DSCU*/

        170u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        6u,
        COM_UNUSED_RXSIGNALINVALIDID,

        2u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSS_SeatBack_SwitchActiveSts_DSS_DSCU*/

        171u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        6u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSS_LegRest_SwitchActiveSts_DSS_DSCU*/

        172u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        6u,
        COM_UNUSED_RXSIGNALINVALIDID,

        6u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* DSS_ResponseErr_DSS_DSCU*/

        4u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        6u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PSS_ForwardBack_SwitchActiveSts_PSS_DSCU*/

        173u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        7u,
        COM_UNUSED_RXSIGNALINVALIDID,

        0u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PSS_LegRest_SwitchActiveSts_FB_PSS_DSCU*/

        174u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        7u,
        COM_UNUSED_RXSIGNALINVALIDID,

        2u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PSS_SeatBack_SwitchActiveSts_PSS_DSCU*/

        175u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        7u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PSS_LegRest_SwitchActiveSts_PSS_DSCU*/

        176u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        0u,

        7u,
        COM_UNUSED_RXSIGNALINVALIDID,

        6u,
        2u,
        1u,
        COM_UINT8,
        COM_LITTLE_ENDIAN,

    },
    {
        /* PSS_ResponseErr_PSS_DSCU*/

        5u,
        0u,
        0u,
        COM_UNUSED_UINT16,
        1u,

        7u,
        COM_UNUSED_RXSIGNALINVALIDID,

        4u,
        1u,
        1u,
        COM_BOOLEAN,
        COM_LITTLE_ENDIAN,

    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(uint16, COM_CONST) Com_TxSignalDataInvalidValueIdRef[COM_TXSIGNAL_INVALID_NUMBER] =
{
    /* CD_CellingHostMotSt_IPDU_COM_CD_1*/
    75u,
    /* CD_CellingLockMotSt_IPDU_COM_CD_1*/
    76u,
    /* CD_CellingSwitchCnt_IPDU_COM_CD_1*/
    0u,
    /* CD_CellingDisplaySt_IPDU_COM_CD_1*/
    77u,
    /* CD_CellingDisplayAngle_IPDU_COM_CD_1*/
    78u,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"
CONST(Com_DataInvalidActionType, COM_CONST) Com_TxSignalDataInvalidActionRef[COM_TXSIGNAL_INVALID_NUMBER] =
{
    /* CD_CellingHostMotSt_IPDU_COM_CD_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* CD_CellingLockMotSt_IPDU_COM_CD_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* CD_CellingSwitchCnt_IPDU_COM_CD_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* CD_CellingDisplaySt_IPDU_COM_CD_1*/
    COM_INVALID_ACTION_NOTIFY,
    /* CD_CellingDisplayAngle_IPDU_COM_CD_1*/
    COM_INVALID_ACTION_NOTIFY,
};
#define COM_STOP_SEC_PBCONFIG_DATA_8
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
static CONST(Com_TxSignalType, COM_CONST) Com_TxSignal[COM_TXSIGNAL_NUMBER]=
{
    {
        /* CD_CellingHostMotSt_IPDU_COM_CD_1*/
        75u,
        0u,
        0u,

        0u,

        0u,

        0u,

        0u,
        2u,
        1u,

        FALSE,
        COM_BIG_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CD_CellingLockMotSt_IPDU_COM_CD_1*/
        76u,
        0u,
        0u,

        0u,

        0u,

        1u,

        2u,
        2u,
        1u,

        FALSE,
        COM_BIG_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CD_CellingSwitchCnt_IPDU_COM_CD_1*/
        0u,
        0u,
        0u,

        2u,

        0u,

        2u,

        0u,
        16u,
        2u,

        FALSE,
        COM_BIG_ENDIAN,
        COM_UINT16,
        COM_PENDING,

    },
    {
        /* CD_CellingDisplaySt_IPDU_COM_CD_1*/
        77u,
        0u,
        0u,

        3u,

        0u,

        3u,

        5u,
        3u,
        1u,

        FALSE,
        COM_BIG_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CD_CellingDisplayAngle_IPDU_COM_CD_1*/
        78u,
        0u,
        0u,

        4u,

        0u,

        4u,

        0u,
        8u,
        1u,

        FALSE,
        COM_BIG_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CDCDiverMassReqSet_DSM_LIN1*/
        149u,
        0u,
        0u,

        0u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        0u,
        2u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcDsmMssgModSet_DSM_LIN1*/
        150u,
        0u,
        0u,

        0u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        4u,
        4u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcDsmMssgStrngthSet_DSM_LIN1*/
        151u,
        0u,
        0u,

        1u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        0u,
        3u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CDCpassMassReqset_DSM_LIN1*/
        152u,
        0u,
        0u,

        2u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        0u,
        2u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcPsmMssgModSet_DSM_LIN1*/
        153u,
        0u,
        0u,

        2u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        4u,
        4u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcPsmMssgStrngthSet_DSM_LIN1*/
        154u,
        0u,
        0u,

        3u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        0u,
        3u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcDsmLumbSet_DSM_LIN1*/
        165u,
        0u,
        0u,

        1u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        4u,
        3u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
    {
        /* CdcPsmLumbSet_DSM_LIN1*/
        166u,
        0u,
        0u,

        3u,

        1u,

        COM_UNUSED_TXSIGNALINVALIDID,

        4u,
        3u,
        1u,

        FALSE,
        COM_LITTLE_ENDIAN,
        COM_UINT8,
        COM_PENDING,

    },
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"

#define COM_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
CONST(Com_ConfigType, COM_CONST_PBCFG) Com_PBConfigData =
{
    &Com_RxIPdu[0],
    &Com_TxIPdu[0],
    &Com_RxSignal[0],
    &Com_TxSignal[0],
    NULL_PTR,
    NULL_PTR,
    NULL_PTR,
    NULL_PTR,
    NULL_PTR,
    NULL_PTR,
    NULL_PTR,
};
#define COM_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "Com_MemMap.h"
/* PRQA S 1531,3408,1533 -- */ /* MISRA Rule 8.7,8.4,8.9 */

