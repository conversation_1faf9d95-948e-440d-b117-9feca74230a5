/**
 * file    CDD_Uart_PBcfg.c
 * brief   UART driver for LES14XX
 * author  xiali
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "CDD_Uart_Cfg.h"
#include "Uart_Ipw_Cfg.h"
#include "CDD_Uart_Defines.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define UART_PBCFG_VENDOR_ID_C                     (110u)
#define UART_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define UART_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define UART_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define UART_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define UART_PBCFG_SW_MINOR_VERSION_C              (0u)
#define UART_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against CDD_Uart_Cfg.h */
#if (UART_PBCFG_VENDOR_ID_C != UART_CFG_VENDOR_ID)
    #error "Uart_PBcfg.c and CDD_Uart_Cfg.h have different vendor ids"
#endif
#if ((UART_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != UART_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_MINOR_VERSION_C    != UART_CFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_REVISION_VERSION_C != UART_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Uart_PBcfg.c and CDD_Uart_Cfg.h are different"
#endif
#if ((UART_PBCFG_SW_MAJOR_VERSION_C != UART_CFG_SW_MAJOR_VERSION) || \
     (UART_PBCFG_SW_MINOR_VERSION_C != UART_CFG_SW_MINOR_VERSION) || \
     (UART_PBCFG_SW_PATCH_VERSION_C != UART_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_PBcfg.c and CDD_Uart_Cfg.h are different"
#endif

/* Checks against Uart_Ipw_Cfg.h */
#if (UART_PBCFG_VENDOR_ID_C != UART_IPW_CFG_VENDOR_ID)
    #error "Uart_PBcfg.c and Uart_Ipw_Cfg.h have different vendor ids"
#endif
#if ((UART_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != UART_IPW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_MINOR_VERSION_C    != UART_IPW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_REVISION_VERSION_C != UART_IPW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Uart_PBcfg.c and Uart_Ipw_Cfg.h are different"
#endif
#if ((UART_PBCFG_SW_MAJOR_VERSION_C != UART_IPW_CFG_SW_MAJOR_VERSION) || \
     (UART_PBCFG_SW_MINOR_VERSION_C != UART_IPW_CFG_SW_MINOR_VERSION) || \
     (UART_PBCFG_SW_PATCH_VERSION_C != UART_IPW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_PBcfg.c and Uart_Ipw_Cfg.h are different"
#endif

/* Checks against CDD_Uart_Defines.h */
#if (UART_PBCFG_VENDOR_ID_C != UART_DEFINES_VENDOR_ID_CFG)
    #error "Uart_PBcfg.c and CDD_Uart_Defines.h have different vendor ids"
#endif
#if ((UART_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != UART_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (UART_PBCFG_AR_RELEASE_MINOR_VERSION_C    != UART_DEFINES_AR_RELEASE_MINOR_VERSION_CFG) || \
     (UART_PBCFG_AR_RELEASE_REVISION_VERSION_C != UART_DEFINES_AR_RELEASE_REVISION_VERSION_CFG))
    #error "AUTOSAR Version Numbers of Uart_PBcfg.c and CDD_Uart_Defines.h are different"
#endif
#if ((UART_PBCFG_SW_MAJOR_VERSION_C != UART_DEFINES_SW_MAJOR_VERSION_CFG) || \
     (UART_PBCFG_SW_MINOR_VERSION_C != UART_DEFINES_SW_MINOR_VERSION_CFG) || \
     (UART_PBCFG_SW_PATCH_VERSION_C != UART_DEFINES_SW_PATCH_VERSION_CFG) \
    )
    #error "Software Version Numbers of Uart_PBcfg.c and CDD_Uart_Defines.h are different"
#endif
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK

/* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((UART_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
            (UART_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Uart_PBcfg.c and Std_Types.h are different"
    #endif
#endif

/*==================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"

/**
* @brief   Export IPW configurations.
*/
UART_IPW_CONFIG_EXT

#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

#if (UART_MULTICORE_SUPPORT == STD_ON)
#define UART_CORE_ID     ((uint32)0U)
#endif


/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"


/**
* @brief          Configuration for Uart Channel 0
*
*
*/
static const Uart_ChannelConfigType Uart_ChannelConfig_0 =
{
    0, /*Uart Channel Id*/

#if (UART_MULTICORE_SUPPORT == STD_ON)
    UART_CORE_ID, /*Uart Channel Core Id*/
#endif
    80000000U, /* Clock frequency */
    &Uart_Ipw_xHwConfigPB_0 /* Uart Hardware config */
};




                                        
/**
* @brief          Configuration for Uart driver
*
*
*/
const Uart_ConfigType Uart_xConfig =
{

    
#if (UART_MULTICORE_SUPPORT == STD_ON)
    UART_CORE_ID,
#endif
    {


        &Uart_ChannelConfig_0




    }
};




#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
/**
* @violates @ref Uart_PBcfg_c_REF_1 <MA>_MemMap.h is included
                 after each section define in order to set the current memory section as defined by AUTOSAR.
* @violates @ref Uart_PBcfg_c_REF_2 This violation is not fixed
                  since the inclusion of <MA>_MemMap.h is as per AUTOSAR requirement [SWS_MemMap_00003].
*/
#include "Uart_MemMap.h"

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}

/** @} */

#endif
