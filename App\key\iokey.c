#include "iokey.h"
#include "os_module.h"

static tIOKey io_keys[IO_KEY_NUM] = {
    {.key = 0, .last = 0, .count = 0, .pin = DioConf_DioChannel_PTB11_KEY1, .state = false, .debounce = false},
    {.key = 0, .last = 0, .count = 0, .pin = DioConf_DioChannel_PTB10_KEY2, .state = false, .debounce = false},
    {.key = 0, .last = 0, .count = 0, .pin = DioConf_DioChannel_PTA0_KEY3,  .state = false, .debounce = false},
};

static tU8 io_key_stats[IO_KEY_NUM] = {0};

void check_io_keys(void)
{
    // TODO: debounce
    for (int i = 0; i < IO_KEY_NUM; i++)
    {
        tIOKey* key = &io_keys[i];
        key->key = Dio_ReadChannel(io_keys[i].pin);
        
        // 上一次是低电平，本次是高电平，说明按键从松开到按下
        if (key->last == 0 && key->key == 1)
        {
            key->debounce = true;
        }

        
        if (key->key == 0)
        {
            key->debounce = false;
            key->last = 0;
        }

        if (key->debounce) 
        {
            if (key->key == 1)
            {
                key->count++;
                if (key->count > 30)
                {
                    key->debounce = false;
                    io_keys[i].state = !io_keys[i].state;
                    os_submit_event_u16(EV(Cmd, IOKeyEV), i, io_keys[i].state);
                    io_keys[i].last = io_keys[i].key;
                    key->count = 0;
                }
            }
            else
            {
                key->debounce = false;
                key->count = 0;
            }
        }
    }
}
