/**
 * @file    Gpt_PBcfg.h
 * @brief   Gpt PBcfg file.
 * <AUTHOR>
 * @date    2024.6.10
 * @@version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef GPT_PBCFG_H
#define GPT_PBCFG_H

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_PBCFG_H                    (110U)
#define GPT_AR_RELEASE_MAJOR_VERSION_PBCFG_H     (4U)
#define GPT_AR_RELEASE_MINOR_VERSION_PBCFG_H     (4U)
#define GPT_AR_RELEASE_REVISION_VERSION_PBCFG_H  (0U)
#define GPT_SW_MAJOR_VERSION_PBCFG_H             (1U)
#define GPT_SW_MINOR_VERSION_PBCFG_H             (0U)
#define GPT_SW_PATCH_VERSION_PBCFG_H             (0U)
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define GPT_CONFIG_PB \
        extern const Gpt_ConfigType Gpt_Config;


#ifdef __cplusplus
}
#endif
/** } */
#endif  /* GPT_PBCFG_H */
