/**
 * 
 * @file Pwm_Hw_Cfg.h
 * @brief 
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>H<PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef PWM_HW_CFG_H
#define PWM_HW_CFG_H



	



#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_HW_CFG_VENDOR_ID                   (110U)
#define PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION    (4U)
#define PWM_HW_CFG_AR_RELEASE_MINOR_VERSION    (4U)
#define PWM_HW_CFG_AR_RELEASE_REVISION_VERSION (0U)
#define PWM_HW_CFG_SW_MAJOR_VERSION            (1U)
#define PWM_HW_CFG_SW_MINOR_VERSION            (0U)
#define PWM_HW_CFG_SW_PATCH_VERSION            (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
                                             ENUMS
==================================================================================================*/

/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/

/** @brief        Switch to indicate that the notifications are supported */
/* [cover SWSID = SWS_Pwm_20112]
The function Pwm_DisableNotification shall be configurable On/Off by the configuration parameter: PwmNotificationSupported {PWM_NOTIFICATION_SUPPORTED}.*/
/* [cover SWSID = SWS_Pwm_20113]
The function Pwm_EnableNotification shall be configurable On/Off by the configuration parameter: PwmNotificationSupported {PWM_NOTIFICATION_SUPPORTED}.  
Regarding error detection, the requirements SWS_Pwm_00117, SWS_Pwm_00047, SWS_Pwm_10051 and SWS_Pwm_20051 are applicable to the function Pwm_EnableNotification.*/
#define PWM_NOTIFICATION_SUPPORTED              (STD_ON)

/** @brief        Switch to enable that power state mode is supported */
#define PWM_POWER_STATE_SUPPORTED               (STD_ON)

/** @brief        Switch to enable that power state mode is supported */
#define PWM_POWER_STATE_ASYNCH_MODE_SUPPORTED   (STD_OFF)

/** @brief        Switch to enable the dual clock feature (Pwm_SetClockMode API) */
#define PWM_SET_CLOCK_MODE_API                  (STD_ON)

/** @brief        Switch to indicate that Pwm_SetPeriodAndDuty API is supported */
/* [cover SWSID = SWS_Pwm_20083]
The function Pwm_SetPeriodAndDuty shall be configurable On/Off by the configuration parameter: PwmSetPeriodAndDuty {PWM_SET_PERIOD_AND_DUTY_API}.*/
#define PWM_SET_PERIOD_AND_DUTY_API             (STD_ON)

/** @brief        Switch to indicate that Pwm_SetChannelOutput API is supported */
#define PWM_SET_CHANNEL_OUTPUT_API              (STD_OFF)

/** @brief        Switch to indicate that Pwm_SetCounterBus API is supported */
#define PWM_SET_COUNTER_BUS_API                 (STD_OFF)

/** @brief        Switch to indicate that Pwm_SetTriggerDelay API is supported */
#define PWM_SET_TRIGGER_DELAY_API               (STD_OFF)

/** @brief        Switch to indicate that Hw instance common configuration done by the PWM driver */
#define PWM_HW_INSTANCE_USED                    (STD_OFF)

/** @brief        Switch to indicate that Flexio is used */
#define PWM_FLEXIO_USED                         (STD_OFF)

/** @brief        Switch to indicate that Pwm is used */
#define PWM_USED                                (STD_ON)



#define PWM_PORT_USED                       (STD_OFF)

#define PWM_HW_PWM_USED                        (STD_ON)
/** @brief        Switch to indicate that PwmEmiosFastUpdate API is supported */
#define PWM_FAST_UPDATE_API                     (STD_OFF)
/** @brief Switches the Development Error Detection and Notification on or off.  */
#define PWM_HW_PORT_DEV_ERROR_DETECT           (STD_ON)

/** @brief The number of PORT_CI instances available on platform */
#define PWM_NUM_PORT_CI_HW_MODULE_U8           ((uint8)5U)

/** @brief Adds or removes all services related to the deinitialization functionality. */
#define PWM_HW_PORT_DEINIT_API                 (STD_ON)

/** @brief Support for User mode. If this parameter has been configured to STD_ON, the PORT driver 
 *         code can be executed from both supervisor and user mode. */
#define PWM_HW_PORT_ENABLE_USER_MODE_SUPPORT   (STD_OFF)

/* Verification for user mode support. */
#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
    #if (defined (PWM_HW_PORT_ENABLE_USER_MODE_SUPPORT) && (STD_ON == PWM_HW_PORT_ENABLE_USER_MODE_SUPPORT))
        #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Pwm in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined
    #endif
#endif


#ifdef __cplusplus
}
#endif

#endif /* PWM_HW_CFG_H */
