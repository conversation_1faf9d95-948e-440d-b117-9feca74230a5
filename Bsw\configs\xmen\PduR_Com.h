/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : PduR_Com.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-03-01 14:41:59 
*  @date               : 2024-12-17 19:05:04 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef  PDUR_COM_H
#define  PDUR_COM_H
/*******************************************************************************
**                      Includes                                              **
*******************************************************************************/
#include "PduR.h"
/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
#define PDUR_COM_H_AR_MAJOR_VERSION  4u
#define PDUR_COM_H_AR_MINOR_VERSION  2u
#define PDUR_COM_H_AR_PATCH_VERSION  2u

/*******************************************************************************
**                      Global Functions                                      **
*******************************************************************************/
#define PduR_ComCancelReceive            PduR_CancelReceive
#define PduR_ComCancelTransmit            PduR_CancelTransmit
#define PduR_ComChangeParameter            PduR_ChangeParameter
#define PduR_ComTransmit            PduR_Transmit

#endif

