///  pod：motor
name: "motor"

// 由于C语言没有模块化概念，很容易混淆名称，因此需要在名称里加上模块名称来进行区分。
// 下面定义的定时器、状态、事件和函数，都会根据规则添加前缀，例如：
// - 定时器：Tm10ms => MotorTm10ms
// - 状态：Idle => StateMotorIdle
// - 事件：START => EV(TnMotor, START)
// - 广播：G_LOG => EV(Motor, LOG)
// - 函数：on_idle => pod_motor_on_idle

// 定时器的配置。注意，由于变量名不能以数字开头，因此所有的Timer定时器统一用`Tm`开头。
timer Tm10ms {
    period: 10
}

timer OnceStopDebouncer {
    once: true
    period: 100
}

// 状态机配置
tree {
    // 根状态
    state Root {
        entry: on_init
        on {
            // 根据Timer事件转换状态
            Tm10ms: on_tm10ms
            // 根据全局事件转换状态或调用函数
            G_SeatEvents : on_seat_events
        }
    }

    state Idle {
        entry: idle_entry
        on {
            Start -> Start : on_start
        }
    }

    state Start {
        entry: start_entry
        on {
            Moving -> Moving : on_moving
        }
    }

    state Moving {
        entry: moving_entry
        on {
            Stop -> Stopping : on_stopping
        }
    }

    state Stopping {
        entry: stopping_entry
        on {
            OnceStopDebouncer -> Stopped : on_stopped
        }
    }

    state Stopped {
        entry: stopped_entry
        on {
            Idle -> Idle : on_idle
        }
    }
}

