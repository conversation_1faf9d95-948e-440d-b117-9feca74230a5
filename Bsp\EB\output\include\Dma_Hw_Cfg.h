/**
 * file    Dma_Hw_Cfg.h
 * brief   Dma Hw Cfg header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef DMA_HW_CFG_H
#define DMA_HW_CFG_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Hw_Types.h"
#include "Dma_Hw_PBcfg.h"
#include "Dma_Hw_Irq.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_HW_CFG_VENDOR_ID                       (110U)
#define DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_HW_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_HW_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_HW_CFG_SW_MAJOR_VERSION                (1U)
#define DMA_HW_CFG_SW_MINOR_VERSION                (0U)
#define DMA_HW_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Dma_Hw_Types.h file are of the same vendor */
#if (DMA_HW_CFG_VENDOR_ID != DMA_HW_TYPES_VENDOR_ID)
    #error "Dma_Hw_Cfg.h and Dma_Hw_Types.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_Types.h file are of the same Autosar version */
#if ((DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION != DMA_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_MINOR_VERSION != DMA_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_REVISION_VERSION != DMA_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_Types.h are different"
#endif

/* Check if header file and Dma_Hw_Types.h file are of the same Software version */
#if ((DMA_HW_CFG_SW_MAJOR_VERSION != DMA_HW_TYPES_SW_MAJOR_VERSION) || \
     (DMA_HW_CFG_SW_MINOR_VERSION != DMA_HW_TYPES_SW_MINOR_VERSION) || \
     (DMA_HW_CFG_SW_PATCH_VERSION != DMA_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_Types.h are different"
#endif

/* Check if header file and Dma_Hw_PBcfg.h file are of the same vendor */
#if (DMA_HW_CFG_VENDOR_ID != DMA_HW_PBCFG_VENDOR_ID)
    #error "Dma_Hw_Cfg.h and Dma_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_PBcfg.h file are of the same Autosar version */
#if ((DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION != DMA_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_MINOR_VERSION != DMA_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_REVISION_VERSION != DMA_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_PBcfg.h are different"
#endif

/* Check if header file and Dma_Hw_PBcfg.h file are of the same Software version */
#if ((DMA_HW_CFG_SW_MAJOR_VERSION != DMA_HW_PBCFG_SW_MAJOR_VERSION) || \
     (DMA_HW_CFG_SW_MINOR_VERSION != DMA_HW_PBCFG_SW_MINOR_VERSION) || \
     (DMA_HW_CFG_SW_PATCH_VERSION != DMA_HW_PBCFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_PBcfg.h are different"
#endif

/* Check if header file and Dma_Hw_Irq.h file are of the same vendor */
#if (DMA_HW_CFG_VENDOR_ID != DMA_HW_IRQ_VENDOR_ID)
    #error "Dma_Hw_Cfg.h and Dma_Hw_Irq.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_Irq.h file are of the same Autosar version */
#if ((DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION != DMA_HW_IRQ_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_MINOR_VERSION != DMA_HW_IRQ_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_REVISION_VERSION != DMA_HW_IRQ_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_Irq.h are different"
#endif

/* Check if header file and Dma_Hw_Irq.h file are of the same Software version */
#if ((DMA_HW_CFG_SW_MAJOR_VERSION != DMA_HW_IRQ_SW_MAJOR_VERSION) || \
     (DMA_HW_CFG_SW_MINOR_VERSION != DMA_HW_IRQ_SW_MINOR_VERSION) || \
     (DMA_HW_CFG_SW_PATCH_VERSION != DMA_HW_IRQ_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_Cfg.h and Dma_Hw_Irq.h are different"
#endif




/*******************************************************************************
 * Definitions
 ******************************************************************************/

#define DMA_HW_NOF_CFG_LOGIC_INSTANCES   ((uint32)1U)


/* Logic Instance 0 */
#define DMA_LOGIC_INST_0    ((uint32)0U)

/* Number Of Configured Logic Channels */
#define DMA_HW_NOF_CFG_LOGIC_CHANNELS   ((uint32)4U)

/* Logic Channel 0 */
#define DMA_LOGIC_CH_0      ((uint8)0U)


/* Logic Channel 1 */
#define DMA_LOGIC_CH_1      ((uint8)1U)


/* Logic Channel 2 */
#define DMA_LOGIC_CH_2      ((uint8)2U)


/* Logic Channel 3 */
#define DMA_LOGIC_CH_3      ((uint8)3U)



#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

/* DMA Initialization Structure */
extern const DmaHwInit_t stDmaHwxDmaInitPB;

extern const DmaHwLogicChannelConfig_t stDmaHwxLogicChannelResetConfig;

extern const DmaHwLogicInstanceConfig_t stDmaHwxLogicInstanceResetConfig;

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

#define DMA_START_SEC_CODE
#include "Dma_MemMap.h"

/* DMA IRQ Handlers */

ISR(DMA_CH0_IRQHandler);
ISR(DMA_CH1_IRQHandler);
ISR(DMA_CH2_IRQHandler);
ISR(DMA_CH3_IRQHandler);
ISR(DMA_CH4_IRQHandler);
ISR(DMA_CH5_IRQHandler);
ISR(DMA_CH6_IRQHandler);
ISR(DMA_CH7_IRQHandler);
ISR(DMA_CH8_IRQHandler);
ISR(DMA_CH9_IRQHandler);
ISR(DMA_CH10_IRQHandler);
ISR(DMA_CH11_IRQHandler);
ISR(DMA_CH12_IRQHandler);
ISR(DMA_CH13_IRQHandler);
ISR(DMA_CH14_IRQHandler);
ISR(DMA_CH15_IRQHandler);

/* DMA Error IRQ Handlers */
ISR(DMA_ERR_IRQHandler);
ISR(DMA_SM_IRQHandler);
ISR(DMA_ALM_IRQHandler);
#define DMA_STOP_SEC_CODE
#include "Dma_MemMap.h"


#ifdef __cplusplus
}
#endif

#endif /* DMA_HW_CFG_H */

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
