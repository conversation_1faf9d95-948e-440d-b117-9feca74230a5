/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : CanSM_Cfg.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-03-01 14:41:59 
*  @date               : 2025-01-10 18:10:41 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef CANSM_CFG_H
#define CANSM_CFG_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "ComStack_Types.h"
/*generated by configure parameter CanSMGetBusOffDelayHeader*/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
/* development error tracer switch */
#define CANSM_DEV_ERROR_DETECT      STD_ON                /* CanSMDevErrorDetect */

/*<User_GetBusOffDelay> callout function*/
#define CANSM_GET_BUSOFF_DELAY_FUNCTION_USED   STD_OFF

/*Enables or disables support of partial networking*/
#define CANSM_PNC_SUPPORT                    STD_OFF                           /*CanSMPncSupport */

/*Enables or disables support of CanSM_SetBaudrate*/
#define CANSM_SET_BAUDRATE_API               STD_OFF                /*CanSMSetBaudrateApi */

/*Determines whether the ECU passive feature is supported by CanSM*/
#define CANSM_TX_OFFLINE_ACTIVE_SUPPORT      STD_OFF                /*CanSMTxOfflineActiveSupport */

/* version information switch */
#define CANSM_VERSION_INFO_API               STD_OFF                            /*CanSMVersionInfoApi */

/* PN filter functionality on the indicated NM channel*/
#define CANSM_CANNM_CONF_PN_AVA            STD_OFF

#define CANSM_DEM_SUPPORT                    STD_OFF

#define CANSM_MULTIPLE_CONTROLLER_SUPPORT               STD_OFF

#define CANSM_BOR_TX_CONFIRMATION_POLLING_ENABLED   STD_OFF
#define CANSM_BOR_TIME_TX_ENSURED_ENABLED           STD_ON

/* CanSM network handle number */
#define CANSM_NETWORK_NUM                    1u                                    /*CanSMManagerNetwork number */

#define CANSM_TRCV_NUM                       0u

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/
/*******************************************************************************
**                      Global Data Declaration                               **
*******************************************************************************/
/*******************************************************************************
**                      Global Functions                                      **
*******************************************************************************/
#endif /* CANSM_CFG_H */

