#ifndef DEBUG_H
#define DEBUG_H
#include "types.h"

#ifdef USE_DEBUG_FUNCS

typedef struct {
    bool move_to;
    bool stop;
    bool calib_one;
    bool clear;
    bool forward;
    bool backward;
    bool shrink;
    bool expand;
    bool waist_off;
    bool reset;
} tMotorDebugOption;

typedef struct {
    bool calib_all;
    bool dft;
    bool shrink;
    bool force_shrink;
    bool max;
    bool zero;
    bool recover;
} tMotorSeatOption;

typedef struct {
    u8 mid;
    u8 pos;
} tDebugData;

typedef struct {
    bool s1_back;
    bool s2_lift;
    bool s3_front;
    bool s4_legs;
    bool s5_slide;
} tMotorDebugCalibOption;

typedef struct {
    tMotorDebugOption motor;
    tMotorDebugCalibOption calib;
    tMotorSeatOption seat;
    bool massage;
    bool fan;
    bool clean_mem;
} tDebugOption;


void check_debug_funcs(void);
#endif

#endif // DEBUG_H
