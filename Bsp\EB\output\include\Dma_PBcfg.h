/**
 * file    DMA_PBcfg.h
 * brief   dma PBcfg header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
 

#ifndef DMA_PBCFG_H
#define DMA_PBCFG_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_PBCFG_VENDOR_ID                     (110U)
#define DMA_PBCFG_AR_RELEASE_MAJOR_VERSION      (4U)
#define DMA_PBCFG_AR_RELEASE_MINOR_VERSION      (4U)
#define DMA_PBCFG_AR_RELEASE_REVISION_VERSION   (0U)
#define DMA_PBCFG_SW_MAJOR_VERSION              (1U)
#define DMA_PBCFG_SW_MINOR_VERSION              (0U)
#define DMA_PBCFG_SW_PATCH_VERSION              (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"


extern const DMA_ConfigType Dma_Config;


#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif /* #ifndef DMA_PBCFG_H. */

/** @} */

