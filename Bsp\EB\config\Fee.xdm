<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Fee" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Fee" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Fee"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPreCompile">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="FeeSectorGroup" type="MAP">
                <d:ctr name="FeeSectorGroup_0" type="IDENTIFIABLE">
                  <d:lst name="FeeSector" type="MAP">
                    <d:ctr name="FeeSector_0" type="IDENTIFIABLE">
                      <d:lst name="FeeFlsSector" type="MAP">
                        <d:ctr name="FeeFlsSector_0" type="IDENTIFIABLE">
                          <d:var name="FeeFlsSectorIndex" type="INTEGER" 
                                 value="0">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@DEF</a:v>
                              <a:v>@CALC</a:v>
                            </a:a>
                          </d:var>
                          <d:ref name="FeeFlsSectorRef" type="REFERENCE" 
                                 value="ASPath:/Fls/Fls/FlsConfigSet/FlsSectorList/FlsSector_0"/>
                        </d:ctr>
                        <d:ctr name="FeeFlsSector_1" type="IDENTIFIABLE">
                          <d:var name="FeeFlsSectorIndex" type="INTEGER" 
                                 value="1"/>
                          <d:ref name="FeeFlsSectorRef" type="REFERENCE" 
                                 value="ASPath:/Fls/Fls/FlsConfigSet/FlsSectorList/FlsSector_1"/>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                    <d:ctr name="FeeSector_1" type="IDENTIFIABLE">
                      <d:lst name="FeeFlsSector" type="MAP">
                        <d:ctr name="FeeFlsSector_0" type="IDENTIFIABLE">
                          <d:var name="FeeFlsSectorIndex" type="INTEGER" 
                                 value="0"/>
                          <d:ref name="FeeFlsSectorRef" type="REFERENCE" 
                                 value="ASPath:/Fls/Fls/FlsConfigSet/FlsSectorList/FlsSector_2"/>
                        </d:ctr>
                        <d:ctr name="FeeFlsSector_1" type="IDENTIFIABLE">
                          <d:var name="FeeFlsSectorIndex" type="INTEGER" 
                                 value="1">
                            <a:a name="IMPORTER_INFO">
                              <a:v>@CALC</a:v>
                              <a:v>@DEF</a:v>
                            </a:a>
                          </d:var>
                          <d:ref name="FeeFlsSectorRef" type="REFERENCE" 
                                 value="ASPath:/Fls/Fls/FlsConfigSet/FlsSectorList/FlsSector_3"/>
                        </d:ctr>
                      </d:lst>
                    </d:ctr>
                  </d:lst>
                </d:ctr>
              </d:lst>
              <d:lst name="FeeBlockConfiguration" type="MAP">
                <d:ctr name="FeeBlockConfiguration_Motor" type="IDENTIFIABLE">
                  <d:var name="FeeBlockNumber" type="INTEGER" value="1"/>
                  <d:var name="FeeBlockSize" type="INTEGER" value="100"/>
                  <d:var name="FeeImmediateData" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="FeeNumberOfWriteCycles" type="INTEGER" value="0"/>
                  <d:var name="FeeBlockAssignment" type="ENUMERATION" 
                         value="APPLICATION"/>
                  <d:ref name="FeeSectorGroupRef" type="REFERENCE" 
                         value="ASPath:/Fee/Fee/FeeSectorGroup_0"/>
                  <d:ref name="FeeDeviceIndex" type="REFERENCE" 
                         value="ASPath:/Fls/Fls/FlsGeneral"/>
                </d:ctr>
              </d:lst>
              <d:lst name="FeeSectorToRecover" type="MAP">
                <d:ctr name="FeeSectorToRecover_0" type="IDENTIFIABLE">
                  <d:ref name="FeeSectorToRecoverRef" type="REFERENCE" 
                         value="ASPath:/Fee/Fee/FeeSectorGroup_0/FeeSector_0/FeeFlsSector_0"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="FeeGeneral" type="IDENTIFIABLE">
                <d:var name="FeeDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="FeeNvmJobEndNotification" type="FUNCTION-NAME" 
                       value="NvM_JobEndNotification">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="FeeNvmJobErrorNotification" type="FUNCTION-NAME" 
                       value="NvM_JobErrorNotification">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="FeeSectorFormatNotification" type="FUNCTION-NAME" 
                       value="NvM_JobFormatNotification">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="FeePollingMode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeeSetModeSupported" type="BOOLEAN" value="true"/>
                <d:var name="FeeVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="FeeVirtualPageSize" type="INTEGER" value="16"/>
                <d:var name="FeeDataBufferSize" type="INTEGER" value="48">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="FeeBlockAlwaysAvailable" type="BOOLEAN" 
                       value="true"/>
                <d:var name="FeeLegacyEraseMode" type="BOOLEAN" value="false"/>
                <d:var name="FeeSwapForeignBlocksEnabled" type="BOOLEAN" 
                       value="true"/>
                <d:var name="FeeMarkEmptyBlocksInvalid" type="BOOLEAN" 
                       value="true"/>
                <d:var name="FeeConfigAssignment" type="ENUMERATION" 
                       value="APPLICATION"/>
                <d:var name="FeeMaximumNumberBlocks" type="INTEGER" value="10"/>
                <d:var name="FeeSectorRetirement" type="BOOLEAN" value="true"/>
                <d:var name="FeeSectorEraseRetries" type="INTEGER" value="3"/>
                <d:var name="FeeMainFunctionPeriod" type="FLOAT" value="0.01">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="21">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="FeePublishedInformation" type="IDENTIFIABLE">
                <d:var name="FeeBlockOverhead" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="FeePageOverhead" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
