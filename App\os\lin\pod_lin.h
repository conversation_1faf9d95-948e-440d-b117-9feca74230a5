#ifndef POD_LIN_H
#define POD_LIN_H
/// ---------- begin of guard: <includes> -----------------------------------
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
#define LIN_ACTION_NONE 0x00u
#define LIN_ACTION_POS  0x01u
#define LIN_ACTION_NEG  0x02u

#define LIN_MASSAGE_SWITCH 0x01u
#define LIN_MASSAGE_MODE 0x02u
#define LIN_MASSAGE_INTENSITY 0x03u
#define LIN_WAIST 0x04u

#define LIN_MASSAGE_NONE 0x00u
#define LIN_MASSAGE_OFF 0x01u
#define LIN_MASSAGE_ON 0x02u

#define CMD_SIGNALS_NUM 13
/// ---------- end of guard:   <header> -------------------------------------

void pod_lin_on_init(tHsmGenCurr* const ev);
void pod_lin_on_massage_event(tHsmGenCurr* const ev);
void pod_lin_on_tm2ms(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_LIN_H