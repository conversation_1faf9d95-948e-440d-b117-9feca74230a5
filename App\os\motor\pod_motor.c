#include "os_pod.h"
#include "pod_motor.h"
/// ---------- begin of guard: <includes> -------------------------------------
#include "pod_lin.h"
#include "log.h"
#include "types.h"
#include "lin_events.h"
#include "motor.h"
#include "motor_job.h"
#include "mem.h"
#include "motion.h"
/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------
/// ---------- end of guard:   <header> ---------------------------------------

void pod_motor_idle_entry(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_idle_entry> -----------------------------------------
/// ---------- end of guard:   <pod_motor_idle_entry> -----------------------------------------
}

void pod_motor_moving_entry(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_moving_entry> -----------------------------------------
    println(LogInfo, "OS_MOTOR: moving");
/// ---------- end of guard:   <pod_motor_moving_entry> -----------------------------------------
}

void pod_motor_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_init> -----------------------------------------
    println(LogInfo, "OS_MOTOR: init");
    os_timer_start(TmMotor10ms);
    motor_job_init();
/// ---------- end of guard:   <pod_motor_on_init> -----------------------------------------
}

// void pod_motor_on_moving(tHsmGenCurr* const ev)
// {
// /// ---------- begin of guard: <pod_motor_on_moving> -----------------------------------------
// /// ---------- end of guard:   <pod_motor_on_moving> -----------------------------------------
// }

void pod_motor_on_seat_events(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_seat_events> -----------------------------------------
    tMotorEvent* mev = (tMotorEvent*) ev->as.ptr;
    tMotor *motor = motor_find(mev->control);
    if (NULL == motor)
    {
        println(LogError, "OS_MOTOR: motor not found");
        return;
    }
    motor_go(motor, mev->option);

    println(LogInfo, "OS_MOTOR: motor go %d\n", mev->option);
/// ---------- end of guard:   <pod_motor_on_seat_events> -----------------------------------------
}



// void pod_motor_on_stopped(tHsmGenCurr* const ev)
// {
// /// ---------- begin of guard: <pod_motor_on_stopped> -----------------------------------------
// /// ---------- end of guard:   <pod_motor_on_stopped> -----------------------------------------
// }

// void pod_motor_on_stopping(tHsmGenCurr* const ev)
// {
// /// ---------- begin of guard: <pod_motor_on_stopping> -----------------------------------------
// /// ---------- end of guard:   <pod_motor_on_stopping> -----------------------------------------
// }

void pod_motor_on_tm10ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_tm10ms> -----------------------------------------
    static u16 count = 0;

    // motor pid controls
    // motor_control();

    // check motion
    // TODO: consider control inputs in motion check
    // motion_check();

    do_motor_job_queue();
/// ---------- end of guard:   <pod_motor_on_tm10ms> -----------------------------------------
}


/// Event handlers for Motor 1

void pod_motor_on_motor1_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor1_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(TnMotor, Motor1Move), 1, 0);
/// ---------- end of guard:   <pod_motor_on_motor1_start> -----------------------------------------
}

void pod_motor_on_motor1_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor1_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor1OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor1_stopping> -----------------------------------------
}

void pod_motor_on_motor1_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor1_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_1_BACK_REST]);
    os_timer_start(TmMotor1OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor1_stopped> -----------------------------------------
}

void pod_motor_on_motor1_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor1_idled> -----------------------------------------
    // save to nvm
    // motor_stall_clear(&motors[MOTOR_1_BACK_REST]);
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor1_idled> -----------------------------------------
}

#ifdef SEAT_SIDE_RIGHT

/// Event handlers for Motor 2

void pod_motor_on_motor2_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor2_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(TnMotor, Motor2Move), 2, 0);
/// ---------- end of guard:   <pod_motor_on_motor1_start> -----------------------------------------
}


void pod_motor_on_motor2_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor2_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor2OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor2_stopping> -----------------------------------------
}

void pod_motor_on_motor2_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor2_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_2_SEAT_LIFT]);
    os_timer_start(TmMotor2OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor2_stopped> -----------------------------------------
}

void pod_motor_on_motor2_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor2_idled> -----------------------------------------
    // save to nvm
    motor_stall_clear(&motors[MOTOR_2_SEAT_LIFT]);
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor2_idled> -----------------------------------------
}

/// Event handlers for Motor 3

void pod_motor_on_motor3_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor3_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(TnMotor, Motor3Move), 3, 0);
/// ---------- end of guard:   <pod_motor_on_motor1_start> -----------------------------------------
}

void pod_motor_on_motor3_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor3_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor3OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor3_stopping> -----------------------------------------
}

void pod_motor_on_motor3_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor3_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_3_SEAT_SLIDE]);
    os_timer_start(TmMotor3OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor3_stopped> -----------------------------------------
}

void pod_motor_on_motor3_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor3_idled> -----------------------------------------
    // save to nvm
    motor_stall_clear(&motors[MOTOR_3_SEAT_SLIDE]);
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor3_idled> -----------------------------------------
}
#endif


/// Event handlers for Motor 4

void pod_motor_on_motor4_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor4_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(TnMotor, Motor4Move), 4, 0);
/// ---------- end of guard:   <pod_motor_on_motor4_start> -----------------------------------------
}

void pod_motor_on_motor4_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor4_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor4OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor4_stopping> -----------------------------------------
}

void pod_motor_on_motor4_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor4_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_4_FRONT_BACK]);
    os_timer_start(TmMotor4OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor4_stopped> -----------------------------------------
}

void pod_motor_on_motor4_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor4_idled> -----------------------------------------
    // save to nvm
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor4_idled> -----------------------------------------
}

/// Event handlers for Motor 5

void pod_motor_on_motor5_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor5_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(TnMotor, Motor5Move), 5, 0);
/// ---------- end of guard:   <pod_motor_on_motor5_start> -----------------------------------------
}

void pod_motor_on_motor5_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor5_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor5OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor5_stopping> -----------------------------------------
}

void pod_motor_on_motor5_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor5_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_5_LEG_LIFT]);
    os_timer_start(TmMotor5OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor5_stopped> -----------------------------------------
}

void pod_motor_on_motor5_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor5_idled> -----------------------------------------
    // save to nvm
    motor_stall_clear(&motors[MOTOR_5_LEG_LIFT]);
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor5_idled> -----------------------------------------
}

/// Event handlers for Motor 6

void pod_motor_on_motor6_start(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor6_start> -----------------------------------------
    println(LogInfo, "OS_MOTOR: start entry");
    os_submit_event_u16_from_isr(EV(Motor, Motor6Move), 6, 0);
/// ---------- end of guard:   <pod_motor_on_motor6_start> -----------------------------------------
}

void pod_motor_on_motor6_stopping(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor6_stopping> -----------------------------------------
    // start once timer for debounce
    println(LogInfo, "OS_MOTOR: stopping entry");
    os_timer_start(TmMotor6OnceStopDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor6_stopping> -----------------------------------------
}

void pod_motor_on_motor6_stopped(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor6_stopped> -----------------------------------------
    // calculate motor's position
    println(LogInfo, "OS_MOTOR: stopped entry");
    motor_record(&motors[MOTOR_6_LEG_STRETCH]);
    os_timer_start(TmMotor6OnceIdleDebouncer);
/// ---------- end of guard:   <pod_motor_on_motor6_stopped> -----------------------------------------

}

void pod_motor_on_motor6_idled(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_on_motor6_idled> -----------------------------------------
    // save to nvm
    motor_stall_clear(&motors[MOTOR_6_LEG_STRETCH]);
    mem_save();
/// ---------- end of guard:   <pod_motor_on_motor6_idled> -----------------------------------------
}

void pod_motor_stalled_entry(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_motor_stalled_entry> -----------------------------------------
    // stop the motor
//     println(LogInfo, "OS_MOTOR: stalled entry");
//     uint8 idx = 0;
//     tHsmEvent event = GET_SUBM_EV(ev->event);
//     switch (event) {
//     case EV(Motor, Motor1Stall):
//         idx = MOTOR_1_BACK_REST;
//         break;
// #ifdef SEAT_SIDE_RIGHT
//     case EV(Motor, Motor2Stall):
//         idx = MOTOR_2_SEAT_LIFT;
//         break;
//     case EV(Motor, Motor3Stall):
//         idx = MOTOR_3_SEAT_SLIDE;
//         break;
// #endif
//     case EV(Motor, Motor4Stall):
//         idx = MOTOR_4_FRONT_BACK;
//         break;
//     case EV(Motor, Motor5Stall):
//         idx = MOTOR_5_LEG_LIFT;
//         break;
//     case EV(Motor, Motor6Stall):
//         idx = MOTOR_6_LEG_STRETCH;
//         break;
//     }
//     motor_set_stall(&motors[idx]);
/// ---------- end of guard:   <pod_motor_stalled_entry> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------