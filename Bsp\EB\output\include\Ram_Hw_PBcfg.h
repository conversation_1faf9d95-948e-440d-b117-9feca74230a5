/**
 * *************************************************************************
 * @file Ram_Hw_PBcfg.h
 * @brief This file is used for Ram module dynamic code generation
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

#ifndef RAM_HW_PBCFG_H
#define RAM_HW_PBCFG_H

/**
 * *************************************************************************
 * @file   Ram_Hw_PBcfg.h
 * @brief This file is used for Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Ram_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RAM_HW_PBCFG_VENDOR_ID                       (110U)
#define RAM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define RAM_HW_PBCFG_AR_RELEASE_MINOR_VERSION        (4U)
#define RAM_HW_PBCFG_AR_RELEASE_REVISION_VERSION     (0U)
#define RAM_HW_PBCFG_SW_MAJOR_VERSION                (1U)
#define RAM_HW_PBCFG_SW_MINOR_VERSION                (0U)
#define RAM_HW_PBCFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Ram_Hw_Types.h file are of the same vendor */
#if (RAM_HW_PBCFG_VENDOR_ID != RAM_HW_TYPES_VENDOR_ID)
    #error "Ram_Hw_PBcfg.h and Ram_Hw_Types.h have different vendor ids"
#endif

/* Check if header file and Ram_Hw_Types.h file are of the same Autosar version */
#if ((RAM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != RAM_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (RAM_HW_PBCFG_AR_RELEASE_MINOR_VERSION != RAM_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (RAM_HW_PBCFG_AR_RELEASE_REVISION_VERSION != RAM_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Ram_Hw_PBcfg.h and Ram_Hw_Types.h are different"
#endif

/* Check if header file and Ram_Hw_Types.h file are of the same Software version */
#if ((RAM_HW_PBCFG_SW_MAJOR_VERSION != RAM_HW_TYPES_SW_MAJOR_VERSION) || \
     (RAM_HW_PBCFG_SW_MINOR_VERSION != RAM_HW_TYPES_SW_MINOR_VERSION) || \
     (RAM_HW_PBCFG_SW_PATCH_VERSION != RAM_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Ram_Hw_PBcfg.h and Ram_Hw_Types.h are different"
#endif

#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
extern const Ram_Hw_RamModuleConfigType_t Mcu_aRamModuleConfigPB;

extern const Ram_Hw_RamConfigType_t Mcu_aRamConfigPB[];





extern CONST(Ram_Hw_SramEccCallback, AUTOMATIC)  Ram_Hw_BitEccCallback;
extern CONST(Ram_Hw_SramEccCallback, AUTOMATIC)  Ram_Hw_FatalEccCallback;


#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */
#endif /* RAM_HW_PBCFG_H */

