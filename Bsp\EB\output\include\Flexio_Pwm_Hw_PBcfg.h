/**
 * file    Flexio_Pwm_Hw_PBcfg.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_PWM_HW_PBCFG_H
#define FLEXIO_PWM_HW_PBCFG_H

/*==================================================================================================
*                                          INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Flexio_Pwm_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_PWM_HW_PBCFG_VENDOR_ID                    (110U)
#define FLEXIO_PWM_HW_PBCFG_MODULE_ID                    (121U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION     (4U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION     (4U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION  (0U)
#define FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION             (1U)
#define FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION             (0U)
#define FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION             (0U)

/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if header file and Std_Types.h are of the same AUTOSAR version */
    #if ((FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
         (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of FLEXIO_PWM_HW_PBcfg.h and Std_Types.h are different"
    #endif
#endif

/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same vendor */
#if (FLEXIO_PWM_HW_PBCFG_VENDOR_ID != FLEXIO_PWM_HW_TYPES_VENDOR_ID)
    #error "Vendor IDs of FLEXIO_PWM_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same AUTOSAR version */
#if ((FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION    != FLEXIO_PWM_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION    != FLEXIO_PWM_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION != FLEXIO_PWM_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR version numbers of FLEXIO_PWM_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same software version */
#if ((FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION != FLEXIO_PWM_HW_TYPES_SW_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION != FLEXIO_PWM_HW_TYPES_SW_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION != FLEXIO_PWM_HW_TYPES_SW_PATCH_VERSION))
    #error "Software version numbers of FLEXIO_PWM_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/*==================================================================================================
*                                            CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                              ENUMS
==================================================================================================*/

/*==================================================================================================
*                                  STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                  GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"


#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/*==================================================================================================
*                                       FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_PWM_Hw_PBCFG_H */

