/**
 * @file    Stm_Gpt_Hw_PBcfg.c
 * @brief   Stm Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.16
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 20.1, #include directives should only be preceded by preprocessor directives or comments.
 * REASON: Variables and text need to be placed in the specified location
 */

#include "Stm_Gpt_Hw_PBcfg.h"
#include "Gpt_Irq.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define STM_HW_VENDOR_ID_PBCFG_C                    (110U)
#define STM_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C     (4U)
#define STM_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C     (4U)
#define STM_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C  (0U)
#define STM_HW_SW_MAJOR_VERSION_PBCFG_C             (1U)
#define STM_HW_SW_MINOR_VERSION_PBCFG_C             (0U)
#define STM_HW_SW_PATCH_VERSION_PBCFG_C             (0U)
/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#if (STM_HW_VENDOR_ID_PBCFG_C != STM_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Stm_Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((STM_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (STM_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (STM_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Stm_Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((STM_HW_SW_MAJOR_VERSION_PBCFG_C != STM_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (STM_HW_SW_MINOR_VERSION_PBCFG_C != STM_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (STM_HW_SW_PATCH_VERSION_PBCFG_C != STM_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Stm_Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h are different"
#endif

#if (STM_HW_VENDOR_ID_PBCFG_C != GPT_IRQ_VENDOR_ID)
    #error "Stm_Gpt_Hw_PBcfg.c and Gpt_Irq.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((STM_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MAJOR_VERSION) || \
     (STM_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MINOR_VERSION) || \
     (STM_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Stm_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((STM_HW_SW_MAJOR_VERSION_PBCFG_C != GPT_IRQ_SW_MAJOR_VERSION) || \
     (STM_HW_SW_MINOR_VERSION_PBCFG_C != GPT_IRQ_SW_MINOR_VERSION) || \
     (STM_HW_SW_PATCH_VERSION_PBCFG_C != GPT_IRQ_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Stm_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif

/*================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/
#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

#ifdef __cplusplus
}
#endif/* STM_GPT_HW_PBCFG_C*/

/* } */
