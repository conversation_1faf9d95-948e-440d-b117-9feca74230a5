#include "os_pod.h"
#include "pod_power.h"
#include "motor.h"
/// ---------- begin of guard: <includes> -------------------------------------

/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------

/// ---------- end of guard:   <header> ---------------------------------------

void pod_power_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_power_on_init> -----------------------------------------
    os_timer_start(TmPower10ms);
/// ---------- end of guard:   <pod_power_on_init> -----------------------------------------
}

void pod_power_on_tm10ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_power_on_tm10ms> -----------------------------------------
    static uint8_t motor_state[MOTOR_MAX] = {0};
    for (uint8_t i = 0; i < MOTOR_MAX; i++)
    {
        if(motors[i].driver.temp > DRIVER_TEMP_LIMIT)
        {
            if (motor_state[i] == 0)
            {
                motor_state[i] = 1;
                //关闭电机，防止过热
            }
        }
        else if(motors[i].driver.temp < DRIVER_TEMP_LIMIT - 10)
        {
            if (motor_state[i] == 1)
            {
                motor_state[i] = 0;
                //开启电机，恢复工作
            }
        }
    }

    if(accVol10x > ACC_VOL_LIMIT*10)
    {
        //过压处理
    }

    if(seatTemp > SEAT_NTC_LIMIT)
    {
        //坐垫过温处理
    }
/// ---------- end of guard:   <pod_power_on_tm10ms> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------