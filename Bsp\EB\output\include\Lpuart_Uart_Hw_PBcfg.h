/**
 * file    Lpuart_Uart_Hw_PBcfg.h
 * brief   UART driver for LES14XX
 * author  xiali
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef LPUART_UART_HW_PBCFG_H
#define LPUART_UART_HW_PBCFG_H

/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/
#include "Uart_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LPUART_UART_HW_PBCFG_VENDOR_ID                     (110u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION      (4u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION      (4u)
#define LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION   (0u)
#define LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION              (1u)
#define LPUART_UART_HW_PBCFG_SW_MINOR_VERSION              (0u)
#define LPUART_UART_HW_PBCFG_SW_PATCH_VERSION              (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lpuart_Uart_Hw_Types.h */
#if (LPUART_UART_HW_PBCFG_VENDOR_ID != LPUART_UART_HW_TYPES_VENDOR_ID)
    #error "Lpuart_Uart_Hw_PBCfg.h and Lpuart_Uart_Hw_Types.h have different vendor ids"
#endif
#if ((LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION   != LPUART_UART_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION   != LPUART_UART_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION!= LPUART_UART_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lpuart_Uart_Hw_PBCfg.h and Lpuart_Uart_Hw_Types.h are different"
#endif
#if ((LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION!= LPUART_UART_HW_TYPES_SW_MAJOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_MINOR_VERSION!= LPUART_UART_HW_TYPES_SW_MINOR_VERSION) || \
     (LPUART_UART_HW_PBCFG_SW_PATCH_VERSION!= LPUART_UART_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lpuart_Uart_Hw_PBCfg.h and Lpuart_Uart_Hw_Types.h are different"
#endif


/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        GLOBAL MACROS
==================================================================================================*/
/**
* @brief          Link Uart channels symbolic names with Uart hardware channel IDs.
* @details        Link Uart channels symbolic names with Uart hardware channel IDs.
*
* @api
*/


          
            
          
#ifndef LPUART_UART_HW_INSTANCE_USING_0
    #define LPUART_UART_HW_INSTANCE_USING_0    0U
#endif

        

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define LPUART_UART_HW_CONFIG_PB \
extern const Lpuart_Uart_Hw_UserConfig_t Lpuart_Uart_Hw_xHwConfigPB_0;\



/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LPUART_UART_HW_PBCFG_H */
