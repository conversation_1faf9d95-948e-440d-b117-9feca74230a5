/**********************************************************************************************************************
* COPYRIGHT
* -------------------------------------------------------------------------------------------------------------------
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD.
* Without the express written permission of the company, no organization or individual may copy, install, trial,
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User
* License Agreement (EULA) or <NAME_EMAIL> for more assistance.
*
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception,
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's
* LICENSE and EXCEPTION files and the specific exception statement.
* -------------------------------------------------------------------------------------------------------------------
* FILE DESCRIPTION
* -------------------------------------------------------------------------------------------------------------------
*  @MCU                : S32K148
*  @file               : CanIf_Cfg.c
*  @license            : Evaliation
*  @licenseExpiryDate  : 2025-03-01 14:41:59
*  @date               : 2024-12-17 19:05:04
*  @customer           : EasyXMen User
*  @toolVersion        : 2.0.18
*********************************************************************************************************************/
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "CanIf.h"
#include "CanSM_Cbk.h"
#include "PduR_CanIf.h"
#include "CanTp_Cbk.h"
#include "CanTp.h"
#include "Can.h"
#include "CanAdapt.h"

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/
#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
/* PRQA S 1531 ++ */ /* MISRA Rule 8.7 */
CONST(CanIf_DispatchConfigType,CANIF_CONST) CanIf_DispatchConfigData =
/* PRQA S 1531 -- */ /* MISRA Rule 8.7 */
{/* CanIfDispatchUserConfirmPnAvailabilityName */
    &CanSM_ControllerBusOff,/* CanIfDispatchUserCtrlBusOffName */
    &CanSM_ControllerModeIndication, /* CanIfDispatchUserCtrlModeIndicationName */

    NULL_PTR, /* CanIfDispatchUserTrcvModeIndicationName */
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
/* PRQA S 1531 ++ */ /* MISRA Rule 8.7 */
CONST(Can_DriverApiType,CANIF_CONST_PBCFG) Can_DriverApi[CANIF_CANDRIVER_NUMBER] =
/* PRQA S 1531 -- */ /* MISRA Rule 8.7 */
{
    {
        &Can_SetControllerModeAdapt,
        &Can_WriteAdapt,
    },
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
CONST(CanIf_ControllerCfgType,CANIF_CONST) CanIf_CtrlCfgData[CANIF_CANCONTROLLER_NUMBER] =
{
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,
        CANIF_CAN_43_FLEXCAN,
        FALSE,
        0u,0u|0u,
    },
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
static CONST(PduIdType, CANIF_CONST) CanIf_HrhRxPduRef[] =
{
    CANIF_RXPDU_CCU_1, /*CANIF_HOH0_HRH_0 */
    CANIF_RXPDU_CCU_ZCUL_HVSPRL_1, /*CANIF_HOH0_HRH_1 */
    CANIF_RXPDU_CCU_ZCUR_HVSPRR_1, /*CANIF_HOH0_HRH_2 */
    CANIF_RXPDU_IDC_1, /*CANIF_HOH0_HRH_3 */
    CANIF_RXPDU_Fun_Diag_Rx, /*CANIF_HOH0_HRH_4 */
    CANIF_RXPDU_CD_Phys_Diag_Rx, /*CANIF_HOH0_HRH_5 */
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
static CONST(CanIfHrhCfgType,CANIF_CONST) CanIf_HrhCfgData[CANIF_HRH_NUMBER] =
{
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        0u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[0],

    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        1u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[1],

    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        2u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[2],

    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        3u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[3],

    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        4u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[4],

    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,

        5u,
        1u,
        CANIF_FULL_CAN,
        &CanIf_HrhRxPduRef[5],

    },
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
static CONST(CanIfHthCfgType,CANIF_CONST) CanIf_HthCfgData[CANIF_HTH_NUMBER] =
{
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,
        6u,
        CANIF_FULL_CAN,
    },
    {
        CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0,
        7u,
        CANIF_FULL_CAN,
    },
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
CanIfRxPduUserRxIndicationNameApiType CanIf_UpRxIndicationArray[CANIF_RXINDICATION_FUNC_NUMBER] =
{
        &PduR_CanIfRxIndication,
        &CanTp_RxIndication,
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
static CONST(CanIf_RxPduConfigType,CANIF_CONST) CanIf_RxPduConfigData[CANIF_RXPDU_NUMBER] =
{
    {
        /*0  CANIF_RXPDU_CCU_1 */
        FALSE,
        FALSE,

        0,
        CANIF_HOH0_HRH_0,
        PDUR_SRCPDU_CCU_1,

        64u,
        0x268u,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
    {
        /*1  CANIF_RXPDU_CCU_ZCUL_HVSPRL_1 */
        FALSE,
        FALSE,

        0,
        CANIF_HOH0_HRH_1,
        PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1,

        32u,
        0x13bu,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
    {
        /*2  CANIF_RXPDU_CCU_ZCUR_HVSPRR_1 */
        FALSE,
        FALSE,

        0,
        CANIF_HOH0_HRH_2,
        PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1,

        32u,
        0x13cu,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
    {
        /*3  CANIF_RXPDU_IDC_1 */
        FALSE,
        FALSE,

        0,
        CANIF_HOH0_HRH_3,
        PDUR_SRCPDU_IDC_1,

        64u,
        0xa1u,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
    {
        /*4  CANIF_RXPDU_Fun_Diag_Rx */
        FALSE,
        FALSE,

        1,
        CANIF_HOH0_HRH_4,
        CANTP_Fun_Diag_Rx,

        64u,
        0x7dfu,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
    {
        /*5  CANIF_RXPDU_CD_Phys_Diag_Rx */
        FALSE,
        FALSE,

        1,
        CANIF_HOH0_HRH_5,
        CANTP_CD_Phys_Diag_Rx,

        64u,
        0x60du,
        0x7ffu,
        CANIF_RX_STANDARD_FD_CAN,
},
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
CanIfTxPduUserTxConfirmationNameApiType CanIf_UpTxConfirmationArray[CANIF_TXCONFIRMATION_FUNC_NUMBER] =
{
        &CanTp_TxConfirmation,
        &PduR_CanIfTxConfirmation,
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
static CONST(CanIf_TxPduConfigType,CANIF_CONST) CanIf_TxPduConfigData[CANIF_TXPDU_NUMBER] =
{
    {
        /* 0  CANIF_TXPDU_CD_1 */

        FALSE,
        8u,

        1u,
        CANIF_HOH0_HTH_0,
        PDUR_DESTPDU_CD_1,
        0xffffu,

        0xffffu,

        0x3bfu,
        0x7ffu,
        CANIF_TX_STANDARD_FD_CAN,
        CANID_STATIC,
    },
    {
        /* 1  CANIF_TXPDU_CD_Diag_Tx */

        FALSE,
        64u,

        0u,
        CANIF_HOH0_HTH_1,
        CANTP_CD_Diag_Tx,
        0xffffu,

        0xffffu,

        0x68du,
        0x7ffu,
        CANIF_TX_STANDARD_FD_CAN,
        CANID_STATIC,
    },
};
#define CANIF_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
/* PRQA S 1531 ++ */ /* MISRA Rule 8.7 */
CONST(CanIf_ConfigType,CANIF_CONST) CanIf_InitCfgSet =
/* PRQA S 1531 -- */ /* MISRA Rule 8.7 */
{
    CanIf_HrhCfgData,  // CanIfHrhCfgRef;
    CanIf_HthCfgData, // CanIfHthCfgRef
    CanIf_RxPduConfigData, // CanIfRxPduConfigRef
    CanIf_TxPduConfigData, // CanIfTxPduConfigRef
    NULL_PTR,
};
#define CANIF_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"

#if (STD_OFF == CANIF_RXINDICATION_AUTOSAR_PARAMETER)
#define CANIF_START_SEC_PBCONFIG_DATA_16
#include "CanIf_MemMap.h"
static CONST(uint16,CANIF_CONST_PBCFG) CanIf_Can0Hoh2HrhId[6u] =
{
    0x0u,0x1u,0x2u,0x3u,0x4u,0x5u,
};
#define CANIF_STOP_SEC_PBCONFIG_DATA_16
#include "CanIf_MemMap.h"

#define CANIF_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
/* PRQA S 1533 ++ */ /* MISRA Rule 8.9 */
CONST(CanIf_CanHOH2HRHType,CANIF_CONST) CanIf_CanHoh2Hrh[1] =
/* PRQA S 1533 -- */ /* MISRA Rule 8.9 */
{
    {
        CanIf_Can0Hoh2HrhId,
        6u,
    },
};
#define CANIF_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "CanIf_MemMap.h"
#endif/*STD_OFF == CANIF_RXINDICATION_AUTOSAR_PARAMETER*/
/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/
