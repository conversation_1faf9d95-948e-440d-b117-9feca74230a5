<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Mcu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Mcu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Mcu"/>
              <a:a name="IMPORTER_INFO" value="@PRE"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="McuGeneralConfiguration" type="IDENTIFIABLE">
                <d:var name="McuDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="McuVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="McuGetRamStateApi" type="BOOLEAN" value="true"/>
                <d:var name="McuInitClock" type="BOOLEAN" value="true"/>
                <d:var name="McuNoPll" type="BOOLEAN" value="false"/>
                <d:var name="McuEnterLowPowerMode" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCRGNMIENABLE" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuTimeout" type="INTEGER" value="50000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEnableUserModeSupport" type="BOOLEAN" 
                       value="false"/>
                <d:var name="McuPerformResetApi" type="BOOLEAN" value="true"/>
                <d:var name="McuCalloutBeforePerformReset" type="BOOLEAN" 
                       value="false"/>
                <d:var name="McuVeryLowPowerStopAbortNotification" 
                       type="FUNCTION-NAME" value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPerformResetCallout" type="FUNCTION-NAME" 
                       value="McuPerformResetCallBack"/>
                <d:var name="McuSleepWakeUpIsrNotification" 
                       type="FUNCTION-NAME" value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuCmuErrorIsrUsed" type="BOOLEAN" value="true"/>
                <d:var name="McuErrorIsrNotification" type="FUNCTION-NAME" 
                       value="Clock_Hw_ErrorIsrNotification">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="McuDisableRcmInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisablePmcInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuDisableSmcInit" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEnableModeChangeNotification" type="BOOLEAN" 
                       value="false"/>
                <d:var name="McuTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuRegisterValuesOptimization" type="ENUMERATION" 
                       value="DISABLED">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="McuEcucPartitionRef"/>
                <d:var name="McuCmuNotification" type="FUNCTION-NAME" 
                       value="Clock_Hw_CmuNotification">
                  <a:a name="ENABLE" value="false"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuDebugConfiguration" type="IDENTIFIABLE">
                <d:var name="McuDisableDemReportErrorStatus" type="BOOLEAN" 
                       value="true"/>
                <d:var name="McuGetMidrStructureApi" type="BOOLEAN" 
                       value="false"/>
                <d:var name="McuDisableCmuApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuEnablePeripheralCMU" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuSRAMRetentionConfigApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuGetClockFrequencyApi" type="BOOLEAN" 
                       value="true"/>
                <d:var name="McuGetPowerModeStatetApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuPmcAeConfigApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuAecResetConfigApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuPublishedInformation" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="@PRE"/>
                <a:a name="READONLY" value="true"/>
                <d:lst name="McuResetReasonConf" type="MAP">
                  <d:ctr name="MCU_MULTI_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_MDM_DAP_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_CPU_LOCK_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="2">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_HRCER_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="3">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_XTAL_ERR_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="4">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_PLLER_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="5">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_SW_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="6">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_WDG_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="7">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_LVD2_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="8">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_LVD1_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="9">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_BO_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="10">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_PIN_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="11">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_STBY_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="12">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_POR_RST" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="13">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="MCU_RESET_UNDEFINED" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@PRE"/>
                    <a:a name="READONLY" value="true"/>
                    <d:var name="McuResetReason" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@PRE"/>
                      <a:a name="READONLY" value="true"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="100">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="McuModuleConfiguration" type="IDENTIFIABLE">
                <d:var name="McuNumberOfMcuModes" type="INTEGER" value="2">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuRamSectors" type="INTEGER" value="3">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:var name="McuResetSetting" type="INTEGER" value="1">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuRTC_CLKINFrequencyHz" type="FLOAT" 
                       value="32768.0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="McuClockSrcFailureNotification" type="ENUMERATION" 
                       value="DISABLED"/>
                <d:lst name="McuClockSettingConfig" type="MAP">
                  <d:ctr name="McuClockSettingConfig_0" type="IDENTIFIABLE">
                    <d:var name="McuClockSettingId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuSysClockUnderMcuControl" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="McuRunClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSOCCTRLEnable" type="BOOLEAN" value="true"/>
                      <d:var name="McuAdcTrigArbtCk" type="FLOAT" value="1.6E8"/>
                      <d:var name="McuAdc0Ck" type="FLOAT" value="2.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuAdc1Ck" type="FLOAT" value="2.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuCanPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuAfePclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuTopPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuIpbPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuIptPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuGpioPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPmuClkSys" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuClkSys" type="FLOAT" value="1.6E8"/>
                      <d:var name="McuSystemClockSwitch" type="ENUMERATION" 
                             value="SPLL_CLK">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuAdc0Divider" type="INTEGER" value="7"/>
                      <d:var name="McuAdc1Divider" type="INTEGER" value="7"/>
                      <d:var name="McuCanDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuAfeDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuTopDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuIpbDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuIptDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuGpioDivider" type="INTEGER" value="1"/>
                      <d:var name="McuSysDivider" type="INTEGER" value="0"/>
                      <d:var name="McuSysPreClk" type="FLOAT" value="1.6E8"/>
                      <d:var name="GpioDebounceDivider" type="INTEGER" value="1"/>
                    </d:ctr>
                    <d:ctr name="McuHRCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuHRCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHrcDiv1Frequency" type="FLOAT" 
                             value="4.8E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHrcDiv2Frequency" type="FLOAT" 
                             value="2.4E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHRCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCRangeSelect" type="ENUMERATION" 
                             value="TRIMMED_TO_48MHZ">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHRCFrequency" type="FLOAT" value="4.8E7"/>
                      <d:var name="McuHRCRbypEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuMRCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuMRCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMrcDiv1Frequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMrcDiv2Frequency" type="FLOAT" 
                             value="4000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMRCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCRangeSelect" type="ENUMERATION" 
                             value="TRIMMED_TO_8MHZ">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMRCFrequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuSystemPll" type="IDENTIFIABLE">
                      <d:var name="McuSystemPllUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLFrequency" type="FLOAT" value="1.6E8"/>
                      <d:var name="McuSPLLDiv2Frequency" type="FLOAT" 
                             value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLDiv1Frequency" type="FLOAT" 
                             value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLDiv1" type="INTEGER" value="2"/>
                      <d:var name="McuSPLLIClkDividerN" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLReferenceFrequency" type="FLOAT" 
                             value="1.6E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLInputFrequency" type="FLOAT" 
                             value="4.0E7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLInputClkPreMultiplier" type="INTEGER" 
                             value="19"/>
                      <d:var name="McuSPLLSelectSourceClock" type="ENUMERATION" 
                             value="SOSC_CLK">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLIClkDividerP" type="INTEGER" value="1"/>
                    </d:ctr>
                    <d:ctr name="McuSIMClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSIMUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ctr name="McuSimChipConfiguration" type="IDENTIFIABLE">
                        <d:var name="McuChipCLKOUTEnable" type="BOOLEAN" 
                               value="true"/>
                        <d:var name="McuCLKOUTDivider" type="INTEGER" value="4"/>
                        <d:var name="McuCLKOUTSelect" type="ENUMERATION" 
                               value="SPLL_CLK"/>
                        <d:var name="McuClkOutFrequency" type="FLOAT" 
                               value="4.0E7"/>
                      </d:ctr>
                      <d:ctr name="McuLrcClockConfig" type="IDENTIFIABLE">
                        <d:var name="McuLRCClkSelect" type="ENUMERATION" 
                               value="LRC_128K_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRTCClkSelect" type="ENUMERATION" 
                               value="LRC_32K_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuLRCClockEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuLrcFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRtcFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuClkMonitor" type="IDENTIFIABLE">
                      <d:ctr name="McuClkMonitor_0" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false"/>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false"/>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="HRC_CLK"/>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="MRC_CLK"/>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="41">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="32"/>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_1" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="VCO_CLK"/>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="HRC_CLK"/>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="40"/>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="16"/>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_2" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="SOSC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="MRC_CLK"/>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="9"/>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="5">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuFlashSettingConf" type="IDENTIFIABLE">
                      <d:ctr name="McuFlashTiming" type="IDENTIFIABLE">
                        <d:var name="McuFlsTimeTpws" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrh" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrec" type="INTEGER" value="20">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTmpws" type="INTEGER" value="17">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrs" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvs" type="INTEGER" value="1293">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTcyc" type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTacc" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTpgh" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTsmp" type="INTEGER" value="1293">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrcv" type="INTEGER" value="1616">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvh" type="INTEGER" value="808">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvh1" type="INTEGER" 
                               value="16160">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTme" type="INTEGER" 
                               value="3232000">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTsme" type="INTEGER" 
                               value="1616000">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTpgs" type="INTEGER" value="324">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:lst name="McuPeripheralClockConfig" type="MAP">
                      <d:ctr name="McuPeripheralClockConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="HSM">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8"/>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="FLASH">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="GPIO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="GPIO_PCLK_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="IO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_4" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_5" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_6" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_7" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_8" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_9" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_10" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_11" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_12" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_13" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_14" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_15" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART0"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_16" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_17" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_18" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="I2C0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_19" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="I2C1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_20" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="FLEXIO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_21" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PIT"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="TOP_PCLK_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_22" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="TRGMUX">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_23" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="STM"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_24" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="WDT">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_25" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CRC">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_26" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="SENSOR">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_27" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="EWM"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="LRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="3"/>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_28" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="ADC0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="ADC0_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_29" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="ADC1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="ADC1_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_30" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CMP">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="RTC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_31" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PDB0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_32" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PDB1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_33" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SPLL_DIV2_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_34" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SPLL_DIV2_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_35" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SPLL_DIV2_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_36" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SPLL_DIV2_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_37" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="CANSRAM"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SPLL_DIV1_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_38" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SRAM">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_39" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="MPU">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_40" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PMU">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_41" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="WKTMR">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="LRC_128K_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_42" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="RTC">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="RTC32K_CLK"/>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:ctr name="McuSystemOSCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSOSCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuOscDiv1Frequency" type="FLOAT" 
                             value="1.6E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuOscDiv2Frequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSOSCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSOSCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSOSCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuOscType" type="ENUMERATION" 
                             value="CRYSTAL_TYPE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalRfEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalRfSel" type="ENUMERATION" 
                             value="INTER_1MO"/>
                      <d:var name="McuOscWorkCurrentCfg" type="INTEGER" 
                             value="4"/>
                      <d:var name="McuXtalBypassEnable" type="BOOLEAN" 
                             value="true"/>
                      <d:var name="McuXtalAgcGainCfg" type="INTEGER" value="2"/>
                      <d:var name="McuSOSCFrequency" type="FLOAT" value="1.6E7"/>
                    </d:ctr>
                    <d:lst name="McuClockReferencePoint" type="MAP">
                      <d:ctr name="McuClockReferencePoint_Can" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7"/>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="CAN0_CLK"/>
                      </d:ctr>
                      <d:ctr name="McuClockReferencePoint_Pwm" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="PWM0_CLK"/>
                      </d:ctr>
                      <d:ctr name="McuClockReferencePoint_Lin" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="LIN1_CLK"/>
                      </d:ctr>
                      <d:ctr name="McuClockReferencePoint_STM" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="PIT_CLK"/>
                      </d:ctr>
                      <d:ctr name="McuClockReferencePoint_Uart" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7"/>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="UART0_CLK"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="McuClockSettingConfig_1" type="IDENTIFIABLE">
                    <d:var name="McuClockSettingId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuSysClockUnderMcuControl" type="BOOLEAN" 
                           value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="McuRunClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSOCCTRLEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuAdcTrigArbtCk" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuAdc0Ck" type="FLOAT" value="4.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuAdc1Ck" type="FLOAT" value="4.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuCanPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuAfePclk" type="FLOAT" value="8.0E7"/>
                      <d:var name="McuTopPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuIpbPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuIptPclk" type="FLOAT" value="8.0E7"/>
                      <d:var name="McuGpioPclk" type="FLOAT" value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuPmuClkSys" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuClkSys" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSystemClockSwitch" type="ENUMERATION" 
                             value="SPLL_CLK"/>
                      <d:var name="McuAdc0Divider" type="INTEGER" value="3"/>
                      <d:var name="McuAdc1Divider" type="INTEGER" value="3"/>
                      <d:var name="McuCanDivider" type="INTEGER" value="1"/>
                      <d:var name="McuAfeDivider" type="INTEGER" value="1"/>
                      <d:var name="McuTopDivider" type="INTEGER" value="1"/>
                      <d:var name="McuIpbDivider" type="INTEGER" value="1"/>
                      <d:var name="McuIptDivider" type="INTEGER" value="1"/>
                      <d:var name="McuGpioDivider" type="INTEGER" value="1"/>
                      <d:var name="McuSysDivider" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSysPreClk" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="GpioDebounceDivider" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuHRCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuHRCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHrcDiv1Frequency" type="FLOAT" 
                             value="4.8E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHrcDiv2Frequency" type="FLOAT" 
                             value="2.4E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHRCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCRangeSelect" type="ENUMERATION" 
                             value="TRIMMED_TO_48MHZ">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuHRCFrequency" type="FLOAT" value="4.8E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuHRCRbypEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuMRCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuMRCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMrcDiv1Frequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMrcDiv2Frequency" type="FLOAT" 
                             value="4000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMRCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuMRCRangeSelect" type="ENUMERATION" 
                             value="TRIMMED_TO_8MHZ">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuMRCFrequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuSystemPll" type="IDENTIFIABLE">
                      <d:var name="McuSystemPllUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLFrequency" type="FLOAT" value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLDiv2Frequency" type="FLOAT" 
                             value="8.0E7">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLDiv1Frequency" type="FLOAT" 
                             value="1.6E8">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLIClkDividerN" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLReferenceFrequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSPLLInputFrequency" type="FLOAT" 
                             value="4.0E7">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLInputClkPreMultiplier" type="INTEGER" 
                             value="39">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSPLLSelectSourceClock" type="ENUMERATION" 
                             value="MRC_CLK"/>
                      <d:var name="McuSPLLIClkDividerP" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="McuSIMClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSIMUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ctr name="McuSimChipConfiguration" type="IDENTIFIABLE">
                        <d:var name="McuChipCLKOUTEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCLKOUTDivider" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCLKOUTSelect" type="ENUMERATION" 
                               value="MRC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkOutFrequency" type="FLOAT" 
                               value="8000000.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuLrcClockConfig" type="IDENTIFIABLE">
                        <d:var name="McuLRCClkSelect" type="ENUMERATION" 
                               value="LRC_128K_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRTCClkSelect" type="ENUMERATION" 
                               value="LRC_32K_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuLRCClockEnable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuLrcFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuRtcFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuClkMonitor" type="IDENTIFIABLE">
                      <d:ctr name="McuClkMonitor_0" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="HRC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="MRC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="39">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="32">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_1" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="VCO_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="MRC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="124">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="115">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuClkMonitor_2" type="IDENTIFIABLE">
                        <d:var name="McuClockMonitorUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuClkMonitorEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuCmuName" type="ENUMERATION" 
                               value="SOSC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFhhEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventResetEn" 
                               type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorFllEventITEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockSource" 
                               type="ENUMERATION" value="MRC_CLK">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuClkMonitorRefClockPeriodCount" 
                               type="INTEGER" value="9">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountUpper" 
                               type="INTEGER" value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClkMonitorClockCountLower" 
                               type="INTEGER" value="5">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:ctr name="McuFlashSettingConf" type="IDENTIFIABLE">
                      <d:ctr name="McuFlashTiming" type="IDENTIFIABLE">
                        <d:var name="McuFlsTimeTpws" type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrh" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrec" type="INTEGER" value="20">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTmpws" type="INTEGER" value="17">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrs" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvs" type="INTEGER" value="1293">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTcyc" type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTacc" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTpgh" type="INTEGER" value="4">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTsmp" type="INTEGER" value="1293">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTrcv" type="INTEGER" value="1616">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvh" type="INTEGER" value="808">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTnvh1" type="INTEGER" 
                               value="16160">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTme" type="INTEGER" 
                               value="3232000">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTsme" type="INTEGER" 
                               value="1616000">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuFlsTimeTpgs" type="INTEGER" value="324">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:ctr>
                    <d:lst name="McuPeripheralClockConfig" type="MAP">
                      <d:ctr name="McuPeripheralClockConfig_0" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="HSM">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_1" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="FLASH">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_2" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="GPIO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="GPIO_PCLK_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_3" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="IO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_4" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_5" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_6" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_7" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PWM3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_8" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_9" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_10" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_11" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="LIN3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_12" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_13" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_14" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SPI2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV1_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.8E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_15" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_16" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_17" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="UART2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_18" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="I2C0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_19" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="I2C1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_20" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="FLEXIO">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_21" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PIT"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="IPT_PCLK_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_22" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="TRGMUX">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_23" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="STM"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_24" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="WDT">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_25" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CRC">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_26" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="SENSOR">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_27" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="EWM">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="LRC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_28" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="ADC0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="ADC0_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_29" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="ADC1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="ADC1_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="4.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_30" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CMP">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="RTC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_31" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PDB0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_32" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PDB1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="SYS_DIV_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="1.6E8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_33" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV2_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_34" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN1">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV2_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_35" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN2">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV2_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_36" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="CAN3">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="HRC_DIV2_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="2.4E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_37" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true"/>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="CANSRAM"/>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_38" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="SRAM">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_39" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="MPU">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_40" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="PMU">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="CLOCK_IS_OFF">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="0.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_41" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" 
                               value="WKTMR">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="LRC_128K_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="128000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="McuPeripheralClockConfig_42" 
                             type="IDENTIFIABLE">
                        <d:var name="McuPeripheralClockUnderMcuControl" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPerName" type="ENUMERATION" value="RTC">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralFunClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralBusClockEnable" 
                               type="BOOLEAN" value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockSelect" 
                               type="ENUMERATION" value="RTC_CLK">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuPeripheralClockDivider" type="INTEGER" 
                               value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralFractionalDivider" 
                               type="INTEGER" value="1">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="McuPeripheralClockFrequency" type="FLOAT" 
                               value="32000.0">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                    <d:ctr name="McuSystemOSCClockConfig" type="IDENTIFIABLE">
                      <d:var name="McuSOSCUnderMcuControl" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuOscDiv1Frequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuOscDiv2Frequency" type="FLOAT" 
                             value="4000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSOSCEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSOSCDiv2" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuSOSCDiv1" type="INTEGER" value="1">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="McuOscType" type="ENUMERATION" 
                             value="CRYSTAL_TYPE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalRfEnable" type="BOOLEAN" value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalRfSel" type="ENUMERATION" 
                             value="INTER_1MO">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuOscWorkCurrentCfg" type="INTEGER" 
                             value="4">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalBypassEnable" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuXtalAgcGainCfg" type="INTEGER" value="2">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="McuSOSCFrequency" type="FLOAT" 
                             value="8000000.0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                    </d:ctr>
                    <d:lst name="McuClockReferencePoint" type="MAP">
                      <d:ctr name="McuClockReferencePoint_0" 
                             type="IDENTIFIABLE">
                        <d:var name="McuClockReferencePointFrequency" 
                               type="FLOAT" value="8.0E7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@CALC</a:v>
                            <a:v>@DEF</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="McuClockFrequencySelect" 
                               type="ENUMERATION" value="CAN0_CLK"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:ctr name="McuDemEventParameterRefs" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:ref name="MCU_E_TIMEOUT_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="true"/>
                  </d:ref>
                  <d:ref name="MCU_E_CLOCK_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                  <d:ref name="MCU_E_SWITCHMODE_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                </d:ctr>
                <d:lst name="McuModeSettingConf" type="MAP">
                  <d:ctr name="McuModeSettingConf_0" type="IDENTIFIABLE">
                    <d:var name="McuMode" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuPowerMode" type="ENUMERATION" value="RUN">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuEnableSleepOnExit" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRCPowerDisable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuInSleepWdgCountCtrl" type="ENUMERATION" 
                           value="SLEEP_WDG_COUNT_KEEP">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuClkSwitchToMrc" type="ENUMERATION" 
                           value="DSLEEP_WAK_CLK_SW_MRC">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuModeSettingConf_1" type="IDENTIFIABLE">
                    <d:var name="McuMode" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="McuPowerMode" type="ENUMERATION" value="SLEEP"/>
                    <d:var name="McuEnableSleepOnExit" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRCPowerDisable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuInSleepWdgCountCtrl" type="ENUMERATION" 
                           value="SLEEP_WDG_COUNT_KEEP">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuClkSwitchToMrc" type="ENUMERATION" 
                           value="DSLEEP_WAK_CLK_SW_MRC">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:lst name="McuRamSectorSettingConf" type="MAP">
                  <d:ctr name="McuRamSectorSettingConf_0" type="IDENTIFIABLE">
                    <d:var name="McuRamSectorId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamDefaultValue" type="INTEGER" value="10"/>
                    <d:var name="McuRamSectionBaseAddress" type="INTEGER" 
                           value="536780800"/>
                    <d:var name="McuRamSectionSize" type="INTEGER" value="1024"/>
                    <d:var name="McuRamSectionWriteSize" type="INTEGER" 
                           value="4">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamSectionBaseAddrLinkerSym" type="STRING" />
                    <d:var name="McuRamSectionSizeLinkerSym" type="STRING" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuRamSectorSettingConf_1" type="IDENTIFIABLE">
                    <d:var name="McuRamSectorId" type="INTEGER" value="1"/>
                    <d:var name="McuRamDefaultValue" type="INTEGER" value="27"/>
                    <d:var name="McuRamSectionBaseAddress" type="INTEGER" 
                           value="536788992"/>
                    <d:var name="McuRamSectionSize" type="INTEGER" value="256"/>
                    <d:var name="McuRamSectionWriteSize" type="INTEGER" 
                           value="4">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamSectionBaseAddrLinkerSym" type="STRING" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamSectionSizeLinkerSym" type="STRING" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuRamSectorSettingConf_2" type="IDENTIFIABLE">
                    <d:var name="McuRamSectorId" type="INTEGER" value="2"/>
                    <d:var name="McuRamDefaultValue" type="INTEGER" value="44"/>
                    <d:var name="McuRamSectionBaseAddress" type="INTEGER" 
                           value="536977408"/>
                    <d:var name="McuRamSectionSize" type="INTEGER" value="1024"/>
                    <d:var name="McuRamSectionWriteSize" type="INTEGER" 
                           value="4">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamSectionBaseAddrLinkerSym" type="STRING" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRamSectionSizeLinkerSym" type="STRING" >
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:ctr name="McuRamModuleConfig" type="IDENTIFIABLE">
                  <d:var name="McuSramEccITEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuSramEccEimEnable" type="BOOLEAN" value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuRamsBitEccCallback" type="FUNCTION-NAME" 
                         value="NULL_PTR">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuRamsFatalEccCallback" type="FUNCTION-NAME" 
                         value="NULL_PTR">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ctr name="McuSRamLModuleConfig" type="IDENTIFIABLE">
                    <d:var name="McuSramLWriteProtectEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramLEccReadCheckEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramLEccBitEccIrqMskEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramLEccFatalEccIrqMskEnable" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuSRamUModuleConfig" type="IDENTIFIABLE">
                    <d:var name="McuSramUWriteProtectEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramUEccReadCheckEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramUEccBitEccIrqMskEnable" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSramUEccFatalEccIrqMskEnable" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="McuInterruptEvents" type="IDENTIFIABLE">
                  <d:var name="McuVoltageErrorEvent" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuAlternateResetEvent" type="BOOLEAN" 
                         value="false">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="McuResetConfig" type="IDENTIFIABLE">
                  <d:var name="McuResetPinFilterBusClockSelect" type="INTEGER" 
                         value="0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuResetPinFilterInStopMode" type="ENUMERATION" 
                         value="ALL_FILTERING_DISABLE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="McuResetPinFilterInRunAndWait" 
                         type="ENUMERATION" value="ALL_FILTERING_DISABLE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:ctr name="McuSystemInterruptEnable" type="IDENTIFIABLE">
                    <d:var name="McuResetDelayTime" type="ENUMERATION" 
                           value="DELAY_10_LPO_CYCLES">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuStopAcknowledgeErrorInterrupt" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuMDMAPSystemResetInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSoftwareInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuCoreLockupInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuJTAGResetInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuGlobalInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuExternalResetPinInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWatchdogInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuCMULossOfClockResetInterrupt" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLossOfLockInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLossOfClockInterrupt" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuResetGeneratorConfiguration" 
                         type="IDENTIFIABLE">
                    <d:var name="McuPWM01Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuPWM23Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSPI0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSPI1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSPI2Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuUART0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuUART1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuRTCReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLPITReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLPTMRReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuEWMReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuADC0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuADC1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuCMPReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuPDB0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuPDB1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuCANCFGReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuGPIOReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuIOReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuUART2Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuCRCReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuSENSORReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuFLEXIOReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuI2C0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuI2C1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLIN0Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLIN1Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLIN2Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuLIN3Reset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuTRGMUXReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWDTReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWKTMReset" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:ctr>
                <d:ctr name="McuPowerControl" type="IDENTIFIABLE">
                  <d:ctr name="McuDSleepAndStandbyWakeUpEdgeSelectConfig" 
                         type="IDENTIFIABLE">
                    <d:var name="McuWkEdgePortA" type="ENUMERATION" 
                           value="RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkEdgePortB" type="ENUMERATION" 
                           value="RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkEdgePortC" type="ENUMERATION" 
                           value="RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkEdgePortD" type="ENUMERATION" 
                           value="RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkEdgePortE" type="ENUMERATION" 
                           value="RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkEdgeNmi" type="ENUMERATION" 
                           value="FALLING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="McuLvdDetectionConfig" type="IDENTIFIABLE">
                    <d:var name="McuLvd1DetectionEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd2DetectionEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd0OutputResultEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd1OutputResultEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd2OutputResultEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd1DigFilterEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuLvd1DigFilterSamplingPeriod" 
                           type="ENUMERATION" value="SAMPLING_4_0LRC"/>
                    <d:var name="McuLvd2DigFilterEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="McuLvd2DigFilterSamplingPeriod" 
                           type="ENUMERATION" value="SAMPLING_8_0LRC"/>
                  </d:ctr>
                  <d:ctr name="McuDeepSeepWakeConfig" type="IDENTIFIABLE">
                    <d:var name="McuDSeepNmiWakeEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuDSeepRtcPovfWakeEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="McuDSeepRtcPsecWakeEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuDSeepRtcAlrWakeEnable" type="BOOLEAN" 
                           value="true"/>
                    <d:var name="McuDSeepTimerWakeEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="McuPortAWakeEnable" type="BOOLEAN" value="true"/>
                    <d:var name="McuPortBWakeEnable" type="BOOLEAN" value="true"/>
                    <d:var name="McuPortCWakeEnable" type="BOOLEAN" value="true"/>
                    <d:var name="McuPortDWakeEnable" type="BOOLEAN" value="true"/>
                    <d:var name="McuPortEWakeEnable" type="BOOLEAN" value="true"/>
                  </d:ctr>
                  <d:ctr name="McuPmcPdWakeEnConfig" type="IDENTIFIABLE">
                    <d:var name="McuPdWakeNmiEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakeRtcOvfEn" type="BOOLEAN" value="false"/>
                    <d:var name="McuPdWakeRtcAlrEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakeRtcSecEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakeWkTimerEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakePortAEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakePortBEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakePortCEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakePortDEn" type="BOOLEAN" value="true"/>
                    <d:var name="McuPdWakePortEEn" type="BOOLEAN" value="true"/>
                  </d:ctr>
                  <d:ctr name="McuVolDetLevelConfig" type="IDENTIFIABLE">
                    <d:var name="McuPmcLvd1VoltageDetectLevelSelect" 
                           type="ENUMERATION" value="LVD_4V2"/>
                    <d:var name="McuPmcLvd2VoltageDetectLevelSelect" 
                           type="ENUMERATION" value="LVD_4V4"/>
                  </d:ctr>
                  <d:ctr name="McuVolDetITConfig" type="IDENTIFIABLE">
                    <d:var name="McuLvd1ITResetEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="McuLvd1TriggerSet" type="ENUMERATION" 
                           value="GENERATE_RESET"/>
                    <d:var name="McuLvd1ITEdgeSelect" type="ENUMERATION" 
                           value="RISING_EDGE"/>
                    <d:var name="McuLvd1ITType" type="ENUMERATION" 
                           value="INTERRUPT_NOT_MASK"/>
                    <d:var name="McuLvd2ITResetEnable" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="McuLvd2TriggerSet" type="ENUMERATION" 
                           value="GENERATE_RESET"/>
                    <d:var name="McuLvd2ITEdgeSelect" type="ENUMERATION" 
                           value="RISING_EDGE"/>
                    <d:var name="McuLvd2ITType" type="ENUMERATION" 
                           value="INTERRUPT_MASK"/>
                  </d:ctr>
                  <d:ctr name="McuTimerConfigFromStandbyMode" 
                         type="IDENTIFIABLE">
                    <d:var name="McuWkTimerCompValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkTimerClkSelect" type="ENUMERATION" 
                           value="CLK_32K">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="McuWkTimerEnable" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                  <d:var name="McuSleepWakeUpIsrNotificationUse" 
                         type="ENUMERATION" value="DISABLED">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
