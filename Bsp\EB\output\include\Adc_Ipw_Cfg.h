/**
 * file    Adc_Ipw_Cfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_IPW_CFG_H
#define ADC_IPW_CFG_H

/**
*   @file
*
*   @addtogroup adc_ipw_config Adc Ipw Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#include "Adc_HwType.h"
#include "Adc_Ipw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_IPW_VENDOR_ID_CFG                      (110u)
#define ADC_IPW_AR_RELEASE_MAJOR_VERSION_CFG       (4u)
#define ADC_IPW_AR_RELEASE_MINOR_VERSION_CFG       (4u)
#define ADC_IPW_AR_RELEASE_REVISION_VERSION_CFG    (0u)
#define ADC_IPW_SW_MAJOR_VERSION_CFG               (1u)
#define ADC_IPW_SW_MINOR_VERSION_CFG               (0u)
#define ADC_IPW_SW_PATCH_VERSION_CFG               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Adc_Ipw_Cfg.h file and Adc_HwType.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_CFG != ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_Ipw_Cfg.h and Adc_HwType.h have different vendor ids"
#endif

/* Check if Adc_Ipw_Cfg.h file and Adc_HwType.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_CFG != ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_CFG != ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_CFG != ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_Cfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc_Ipw_Cfg.h file and Adc_HwType.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_CFG != ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_IPW_SW_MINOR_VERSION_CFG != ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_IPW_SW_PATCH_VERSION_CFG != ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_Ipw_Cfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc_Ipw_Cfg.h file and Adc_Ipw_PBcfg.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_CFG != ADC_IPW_VENDOR_ID_PBCFG)
    #error "Adc_Ipw_Cfg.h and Adc_Ipw_PBcfg.h have different vendor ids"
#endif

/* Check if Adc_Ipw_Cfg.h file and Adc_Ipw_PBcfg.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_CFG != ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_CFG != ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_CFG != ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_Cfg.h and Adc_Ipw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_Cfg.h file and Adc_Ipw_PBcfg.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_CFG != ADC_IPW_SW_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_MINOR_VERSION_CFG != ADC_IPW_SW_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_PATCH_VERSION_CFG != ADC_IPW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Adc_Ipw_Cfg.h and Adc_Ipw_PBcfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_IPW_CFG_H */

