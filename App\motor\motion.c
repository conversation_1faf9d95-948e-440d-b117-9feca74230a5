#include "types.h"
#include "log.h"
#include "motion.h"
#include "motor.h"

static tMotorMotion motor_motion = MOTION_IDLE;

void motion_start(void)
{
    if (motion_is_free())
    {
        motor_motion = MOTION_ACTIVE;
    }
}

bool motion_is_free(void)
{
    return motor_motion == MOTION_IDLE
       || motor_motion == MOTION_STOP_CONFIRMED;
}

extern tMotor motors[MOTOR_MAX];
static bool is_any_active(void)
{
    bool is_active = FALSE;
    // check speed
    for (usize i = 0; i < MOTOR_MAX; i++)
    {
        if (motor_is_active(&motors[i]))
        {
            is_active = TRUE;
            break;
        }
    }
    return is_active;
}

void motion_check(void)
{
    static u16 stop_count = 0;

    bool motion_detected = is_any_active();

    switch (motor_motion) {
    case MOTION_IDLE:
        if (motion_detected) {
            motor_motion = MOTION_ACTIVE;
            stop_count = 0;
        }
        break;
    case MOTION_ACTIVE:
        if (!motion_detected) {
            motor_motion = MOTION_STOP;
            stop_count = 0;
        }
        break;
    case MOTION_STOP:
        if (motion_detected) { // debounced, back to moving state
            motor_motion = MOTION_ACTIVE;
            stop_count = 0;
        } else {
            stop_count++;
            if (stop_count == 5) { // goto CONFIRMED
                motor_motion = MOTION_STOP_CONFIRMED;
                // trigger stop callback
                motor_record_all();
                // release all motors
                for (usize i = 0; i < MOTOR_MAX; i++) {
                    motor_stall_clear(&motors[i]);
                }
            }
        }
        break;
    case MOTION_STOP_CONFIRMED:
        if (motion_detected) { // debounced, back to moving state
            motor_motion = MOTION_ACTIVE;
            stop_count = 0;
        } else {
            stop_count++;
            if (stop_count >= 10) { // goto IDLE
                motor_motion = MOTION_IDLE;
                // trigger mem_save
                println(LogInfo, "Trigger Mem Save...");
                mem_collect();
                mem_save();
            }
        }
        break;
    }
}
