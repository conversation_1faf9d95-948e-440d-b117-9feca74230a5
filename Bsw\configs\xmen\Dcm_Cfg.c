/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Dcm_Cfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:15 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/******************************* references ************************************/
#include "Dcm_Types.h"
#include "Rte_Dcm.h"
#include "Dcm_Cfg.h"
#if (STD_ON == DCM_UDS_FUNC_ENABLED)
#include "UDS.h"
#endif
#if (STD_ON == DCM_OBD_FUNC_ENABLED)
#include "OBD.h"
#endif

/**********************Clear DTC**************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspClearDTCType, DCM_CONST) Dcm_DspClearDTCCfg =
{
    NULL_PTR, /*DcmDsp_ClearDTCCheckFnc*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*******************Com Control********************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspComControlAllChannelType, DCM_CONST) Dcm_DspComControlAllChannelCfg[1] =
{
    {
        TRUE, /*DcmDspComControlAllChannelUsed*/
        0u, /*DcmDspComMChannelId*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspComControlType, DCM_CONST) Dcm_DspComControlCfg =
{
    1u, /*DcmDspComControlAllChannelNum*/
    &Dcm_DspComControlAllChannelCfg[0], /*DcmDspComControlAllChannel*/
    NULL_PTR, /*DcmDspComControlSetting*/
    0u, /*DcmDspComControlSpecificChannelNum*/
    NULL_PTR, /*DcmDspComControlSpecificChannel*/
    0u, /*DcmDspComControlSubNodeNum*/
    NULL_PTR, /*DcmDspComControlSubNode*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*******************Common Authorization********************/
/* PRQA S 0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)SpecialAuthorization_0x0203_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/* PRQA S 0779-- */ /* MISRA Rule 1.3,Rule 5.2 */
/* PRQA S 0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)CommonAuthorization_Routines_SecRef[1] = {1u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/* PRQA S 0779-- */ /* MISRA Rule 1.3,Rule 5.2 */

/* PRQA S 0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)CommonAuthorization_Routines_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/* PRQA S 0779-- */ /* MISRA Rule 1.3,Rule 5.2 */

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DspCommonAuthorizationType, DCM_CONST) Dcm_DspCommonAuthorizationCfg[3] =
{
    {
        0u,     /*DcmDspCommonAuthorizationSecurityLevelRefNum*/
        NULL_PTR, /*DcmDspCommonAuthorizationSecurityLevelRef*/
        0u, /*DcmDspCommonAuthorizationSessionRefNum*/
        NULL_PTR, /*DcmDspCommonAuthorizationSessionRef*/
    },
    {
        0u,     /*DcmDspCommonAuthorizationSecurityLevelRefNum*/
        NULL_PTR, /*DcmDspCommonAuthorizationSecurityLevelRef*/
        1u, /*DcmDspCommonAuthorizationSessionRefNum*/
        &SpecialAuthorization_0x0203_SesRef[0], /*DcmDspCommonAuthorizationSessionRef*/
    },
    {
        1u,     /*DcmDspCommonAuthorizationSecurityLevelRefNum*/
        &CommonAuthorization_Routines_SecRef[0], /*DcmDspCommonAuthorizationSecurityLevelRef*/
        1u, /*DcmDspCommonAuthorizationSessionRefNum*/
        &CommonAuthorization_Routines_SesRef[0], /*DcmDspCommonAuthorizationSessionRef*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/* PRQA S 0674++ */ /* MISRA Rule 1.1 */
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDataType, DCM_CONST) Dcm_DspDataCfg[25] =
{
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        80u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF199_programmingDateDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF199_programmingDateDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        32u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_WriteData, /*DcmDspDataWriteFnc*/
        168u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_WriteData, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_WriteData, /*DcmDspDataWriteFnc*/
        96u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        32u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        136u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF180_bootSoftwareIdentification_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF180_bootSoftwareIdentification_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        136u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x3400_Usage_mode_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x3400_Usage_mode_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF186_activeDiagnosticSessionDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF186_activeDiagnosticSessionDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF187_GAC_SparePartNumberDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF187_GAC_SparePartNumberDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        112u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        136u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        112u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF193_systemSupplier_ECU_Hardware_Version_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF193_systemSupplier_ECU_Hardware_Version_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        112u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xF195_systemSupplierECUSoftwareVersion_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xF195_systemSupplierECUSoftwareVersion_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        112u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xAE3B_Suction_top_screen_state_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAE3B_Suction_top_screen_state_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        112u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x2110_OTA_Recovery_Status_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x2110_OTA_Recovery_Status_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x2111_OTA_Partition_synchronization_status_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x2111_OTA_Partition_synchronization_status_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x0200_Reprogramming_Counter_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0200_Reprogramming_Counter_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        16u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x0201_Reprogramming_Attempt_Counter_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x0201_Reprogramming_Attempt_Counter_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        16u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x1000_Power_Voltage_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x1000_Power_Voltage_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0x5005_OTA_mode_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0x5005_OTA_mode_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        8u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        NULL_PTR,     /*DcmDspDataConditionCheckReadFnc*/
        FALSE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ReturnControlToECU, /*DcmDspDataReturnControlToECUFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ShortTermAdjustment, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        24u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
    {
        Rte_Call_Dcm_CSDataServices_Data_0xAE45_Running_data_record_ConditionCheckRead,     /*DcmDspDataConditionCheckReadFnc*/
        TRUE, /*DcmConditionCheckReadFncUsed*/
        NULL_PTR,     /*DcmDspDataEcuSignalFnc*/
        NULL_PTR, /*DcmDspDataReadEcuSignalFnc*/
        DCM_OPAQUE, /*DcmDspDataEndianness*/
        NULL_PTR,     /*DcmDspDataFreezeCurrentsStateFnc*/
        NULL_PTR,     /*DcmDspDataGetScalingInfoFnc*/
        NULL_PTR,  /*DcmDspDataReadDataLengthFnc*/
        Rte_Call_Dcm_CSDataServices_Data_0xAE45_Running_data_record_ReadData, /*DcmDspDataReadFnc*/
        NULL_PTR, /*DcmDspDataResetToDefaultFnc*/
        NULL_PTR, /*DcmDspDataReturnControlToECUFnc*/
        NULL_PTR, /*DcmDspDataShortTermAdjustmentFnc*/
        NULL_PTR, /*DcmDspDataWriteFnc*/
        136u, /*DcmDspDataSize*/
        DCM_UINT8_N, /*DcmDspDataType*/
        USE_DATA_ASYNCH_CLIENT_SERVER, /*DcmDspDataUsePort*/
        0u, /*DcmDspDataBlockId*/
        0xffu, /*DcmDspDataInfoIndex*/
        NULL_PTR, /*DcmDspDiagnosisScaling*/
        NULL_PTR, /*DcmDspExternalSRDataElementClass*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/* PRQA S 0674-- */ /* MISRA Rule 1.1 */

/********************Dsp Did******************************/
/******************************************
 *DcmDspDidRead container configration
 *****************************************/                                                                        
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidInfo_0_ReadCfg =
{
    0u, /*DcmDspDidReadSecurityLevelRefNum*/
    NULL_PTR, /*pDcmDspDidReadSecurityLevelRow*/
    0u, /*DcmDspDidReadSessionRefNum*/
    NULL_PTR, /*pDcmDspDidReadSessionRow*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidInfo_1_ReadCfg =
{
    0u, /*DcmDspDidReadSecurityLevelRefNum*/
    NULL_PTR, /*pDcmDspDidReadSecurityLevelRow*/
    0u, /*DcmDspDidReadSessionRefNum*/
    NULL_PTR, /*pDcmDspDidReadSessionRow*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_DidInfo_2_Read_SecRefCfg[1] = {1u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_DidInfo_2_Read_SesRefCfg[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DspDidReadType,DCM_CONST)Dcm_DidInfo_2_ReadCfg =
{
    1u, /*DcmDspDidReadSecurityLevelRefNum*/
    &Dcm_DidInfo_2_Read_SecRefCfg[0], /*pDcmDspDidReadSecurityLevelRow*/
    1u, /*DcmDspDidReadSessionRefNum*/
    &Dcm_DidInfo_2_Read_SesRefCfg[0], /*pDcmDspDidReadSessionRow*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*******************************************
 *DcmDspDidWrite container configuration,
 which is in the DcmDspDidInfo container
 ******************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_DidInfo_0_Write_SecRefCfg[1] = {1u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_DidInfo_0_Write_SesRefCfg[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DspDidWriteType,DCM_CONST)Dcm_DidInfo_0_WriteCfg=
{
    1u,/*DcmDspDidWriteSecurityLevelRefNum*/
    &Dcm_DidInfo_0_Write_SecRefCfg[0],    /*pDcmDspDidWriteSecurityLevelRow*/
    1u,    /*DcmDspDidWriteSessionRefNum*/
    &Dcm_DidInfo_0_Write_SesRefCfg[0], /*pDcmDspDidWriteSessionRow*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*******************************************
 *DcmDspDidControl container configuration,
 which is in the DcmDspDidInfo container
 ******************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(uint8,DCM_CONST)Dcm_DidInfo_2_Control_SecRefCfg[1]= {1u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(uint8,DCM_CONST)Dcm_DidInfo_2_Control_SesRefCfg[1]={3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidControlType,DCM_CONST) Dcm_DidInfo_2_ControlCfg =
{
    DCM_CONTROLMASK_INTERNAL, /*DcmDspDidControlMask*/
    0u, /*DcmDspDidControlMaskSize*/
    1u, /*DcmDspDidControlSecurityLevelRefNum*/
    &Dcm_DidInfo_2_Control_SecRefCfg[0], /*pDcmDspDidControlSecurityLevelRow*/
    1u, /*DcmDspDidControlSessionRefNum*/
    &Dcm_DidInfo_2_Control_SesRefCfg[0], /*pDcmDspDidControlSessionRow*/
    FALSE, /*DcmDspDidFreezeCurrentState*/
    FALSE, /*DcmDspDidResetToDefault*/
    TRUE, /*DcmDspDidShortTermAdjustement*/
    NULL_PTR, /*DcmDspDidControlEnableMask*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/******************************************
 *DcmDspDidInfo container Configuration ***
 ******************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidInfoType,DCM_CONST)Dcm_DspDidInfoCfg[3] =
{
    {
        0u, /*DcmDspDDDIDMaxElements*/
        FALSE, /*DcmDspDidDynamicallyDefined*/
        NULL_PTR, /*pDcmDspDidControl*/
        &Dcm_DidInfo_0_ReadCfg, /*pDcmDspDidRead*/
        &Dcm_DidInfo_0_WriteCfg, /*pDcmDspDidWrite*/
    },
    {
        0u, /*DcmDspDDDIDMaxElements*/
        FALSE, /*DcmDspDidDynamicallyDefined*/
        NULL_PTR, /*pDcmDspDidControl*/
        &Dcm_DidInfo_1_ReadCfg, /*pDcmDspDidRead*/
        NULL_PTR, /*pDcmDspDidWrite*/
    },
    {
        0u, /*DcmDspDDDIDMaxElements*/
        FALSE, /*DcmDspDidDynamicallyDefined*/
        &Dcm_DidInfo_2_ControlCfg, /*pDcmDspDidControl*/
        &Dcm_DidInfo_2_ReadCfg, /*pDcmDspDidRead*/
        NULL_PTR, /*pDcmDspDidWrite*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F184_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[0],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F199_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[1],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F18C_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[2],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_110_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[3],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_120_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[4],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F10B_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[5],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F17F_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[6],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F180_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[7],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_3400_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[8],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F186_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[9],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F187_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[10],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F189_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[11],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F18E_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[12],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F193_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[13],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_F195_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[14],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_AE3B_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[15],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_AE3C_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[16],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_2110_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[17],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_2111_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[18],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_200_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[19],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_201_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[20],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_1000_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[21],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_5005_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[22],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_AEA0_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[23],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidSignalType,DCM_CONST)Dcm_Did_AE45_SignalCfg[1] =
{
    {
        0u,                   /*DcmDspDidDataPos*/
        &Dcm_DspDataCfg[24],     /*pDcmDspDidData*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/**********************************************
 *DcmDspDid container configration*************
 **********************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspDidType,DCM_CONST)Dcm_DspDidCfg[25] =
{
    { /* Did_0xF184 */
        0xF184u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F184_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF199 */
        0xF199u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F199_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF18C */
        0xF18Cu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        0u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F18C_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x110 */
        0x110u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        0u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_110_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x120 */
        0x120u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        0u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_120_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF10B */
        0xF10Bu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F10B_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF17F */
        0xF17Fu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F17F_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF180 */
        0xF180u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F180_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x3400 */
        0x3400u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_3400_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF186 */
        0xF186u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F186_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF187 */
        0xF187u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F187_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF189 */
        0xF189u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F189_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF18E */
        0xF18Eu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F18E_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF193 */
        0xF193u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F193_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xF195 */
        0xF195u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_F195_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xAE3B */
        0xAE3Bu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_AE3B_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xAE3C */
        0xAE3Cu,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_AE3C_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x2110 */
        0x2110u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_2110_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x2111 */
        0x2111u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_2111_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x200 */
        0x200u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_200_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x201 */
        0x201u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_201_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x1000 */
        0x1000u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_1000_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0x5005 */
        0x5005u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_5005_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xAEA0 */
        0xAEA0u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        2u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_AEA0_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
    { /* Did_0xAE45 */
        0xAE45u,     /*DcmDspDidId*/
        TRUE,     /*DcmDspDidUsed*/
        1u,     /*DcmDspDidInfoIndex*/
        0u,     /*DcmDspRefDidNum*/
        NULL_PTR,     /*pDcmDspRefDidIdArray*/
        1u, /*DcmDspDidSignalNum*/
        &Dcm_Did_AE45_SignalCfg[0],     /*pDcmDspDidSignal*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/******************Dsp Memory**************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspAddressAndLengthFormatIdentifierType,DCM_CONST)Dcm_DspAddressAndLengthFormatIdentifierCfg[1] =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    {
        0u,     /*DcmDspSupportedAddressAndLengthFormatIdentifier*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspMemoryIdInfoType,DCM_CONST)Dcm_DspMemoryIdInfoCfg[1] =
{
    {
        0u, /*DcmDspMemoryIdValue*/
        0u,/*DcmDspReadMemoryRangeInfoNum*/
        NULL_PTR, /*DcmDspReadMemoryRangeInfo*/
        0u,/*DcmDspWriteMemoryRangeInfoNum*/
        NULL_PTR, /*DcmDspWriteMemoryRangeInfo*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspMemoryType,DCM_CONST)Dcm_DspMemoryCfg =
{
    1u, /*DcmDspAddressAndLengthFormatIdentifierNum*/
    &Dcm_DspAddressAndLengthFormatIdentifierCfg[0], /*DcmDspAddressAndLengthFormatIdentifier*/
    1u,/*DcmDspMemoryIdInfoNum*/
    &Dcm_DspMemoryIdInfoCfg[0], /*DcmDspMemoryIdInfo*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/******************Dsp Routine**************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0xAE3D_SignalCfg[1] =
{
    {
        DCM_OPAQUE, /*DcmDspRoutineSignalEndianness*/
        16u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0xAE3C_SignalCfg[1] =
{
    {
        DCM_OPAQUE, /*DcmDspRoutineSignalEndianness*/
        16u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0xAEA1_SignalCfg[1] =
{
    {
        DCM_OPAQUE, /*DcmDspRoutineSignalEndianness*/
        16u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0xAEA2_SignalCfg[1] =
{
    {
        DCM_OPAQUE, /*DcmDspRoutineSignalEndianness*/
        16u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0x2110_SignalCfg[1] =
{
    {
        DCM_OPAQUE,     /*DcmDspRoutineSignalEndianness*/
        8u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineInOutSignalType,DCM_CONST)Dcm_Routine_0x2111_SignalCfg[1] =
{
    {
        DCM_OPAQUE,     /*DcmDspRoutineSignalEndianness*/
        8u, /*DcmDspRoutineSignalLength*/
        0u, /*DcmDspRoutineSignalPos*/
        DCM_UINT8_N, /*DcmDspRoutineSignalType*/
        NULL_PTR, /*DcmDspArgumentScaling*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/***********************************
 *DcmDspRequestRoutineResults container
 **********************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0x2110_RequestRoutineResultsOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineOutSignalNum*/
    &Dcm_Routine_0x2110_SignalCfg[0],    /*DcmDspRoutineOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRequestRoutineResultsType,DCM_CONST)Dcm_RoutineInfo_0x2110_ResultsCfg =
{
    Rte_Call_Dcm_RoutineServices_Routine_0x2110_RequestResults, /*DcmDspRequestResultsRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
    NULL_PTR, /*DcmDspRequestRoutineResultsCommonAuthorizationRef*/
    NULL_PTR, /*DcmDspRequestRoutineResultsIn*/
    &Dcm_Routine_0x2110_RequestRoutineResultsOutCfg, /*DcmDspRequestRoutineResultsOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0x2111_RequestRoutineResultsOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineOutSignalNum*/
    &Dcm_Routine_0x2111_SignalCfg[0],    /*DcmDspRoutineOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRequestRoutineResultsType,DCM_CONST)Dcm_RoutineInfo_0x2111_ResultsCfg =
{
    Rte_Call_Dcm_RoutineServices_Routine_0x2111_RequestResults, /*DcmDspRequestResultsRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
    NULL_PTR, /*DcmDspRequestRoutineResultsCommonAuthorizationRef*/
    NULL_PTR, /*DcmDspRequestRoutineResultsIn*/
    &Dcm_Routine_0x2111_RequestRoutineResultsOutCfg, /*DcmDspRequestRoutineResultsOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/***********************************
 *DcmDspRoutineStart container
 **********************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0xAE3D_StartRoutineOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineInOutSignalNum*/
    &Dcm_Routine_0xAE3D_SignalCfg[0],    /*DcmDspRoutineInOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0xAE3D_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0xAE3D_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     &Dcm_Routine_0xAE3D_StartRoutineOutCfg, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0x203_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0x0203_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     NULL_PTR, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0xAE3C_StartRoutineOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineInOutSignalNum*/
    &Dcm_Routine_0xAE3C_SignalCfg[0],    /*DcmDspRoutineInOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0xAE3C_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0xAE3C_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     &Dcm_Routine_0xAE3C_StartRoutineOutCfg, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0xAEA1_StartRoutineOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineInOutSignalNum*/
    &Dcm_Routine_0xAEA1_SignalCfg[0],    /*DcmDspRoutineInOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0xAEA1_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0xAEA1_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     &Dcm_Routine_0xAEA1_StartRoutineOutCfg, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static CONST(Dcm_DspRoutineInOutType,DCM_CONST)Dcm_Routine_0xAEA2_StartRoutineOutCfg =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    1u,                                  /*RoutineInOutSignalNum*/
    &Dcm_Routine_0xAEA2_SignalCfg[0],    /*DcmDspRoutineInOutSignal*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0xAEA2_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0xAEA2_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     &Dcm_Routine_0xAEA2_StartRoutineOutCfg, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0x2110_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0x2110_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     NULL_PTR, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspStartRoutineType,DCM_CONST)Dcm_RoutineInfo_0x2111_StartCfg =
{

     Rte_Call_Dcm_RoutineServices_Routine_0x2111_Start, /*DcmDspStartRoutineFnc*//* PRQA S 0674 */ /* MISRA Rule 1.1 */
     NULL_PTR, /*DcmDspStartRoutineCommonAuthorizationRef*/
     NULL_PTR, /*DcmDspStartRoutineIn*/
     NULL_PTR, /*DcmDspStartRoutineOut*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/***********************************
 *DcmDspRoutineStop container
 **********************************/
/***********************************
 *DcmDspRoutine container configration
 **********************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspRoutineType,DCM_CONST)Dcm_DspRoutineCfg[7] =
{
    { /* Routine_0xAE3D */
        0xAE3Du, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        NULL_PTR, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0xAE3D_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0x0203 */
        0x203u, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[1],     /*DcmDspCommonAuthorizationRef*/
        NULL_PTR, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0x203_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0xAE3C */
        0xAE3Cu, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        NULL_PTR, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0xAE3C_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0xAEA1 */
        0xAEA1u, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        NULL_PTR, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0xAEA1_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0xAEA2 */
        0xAEA2u, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        NULL_PTR, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0xAEA2_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0x2110 */
        0x2110u, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        &Dcm_RoutineInfo_0x2110_ResultsCfg, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0x2110_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
    { /* Routine_0x2111 */
        0x2111u, /*DcmDspRoutineId*/
        0u, /*DcmDspRoutineInfoByte*/
        FALSE, /*DcmDspRoutineUsePort*/
        TRUE,     /*DcmDspRoutineUsed*/
        &Dcm_DspCommonAuthorizationCfg[2],     /*DcmDspCommonAuthorizationRef*/
        &Dcm_RoutineInfo_0x2111_ResultsCfg, /*DcmDspRequestRoutineResults*/
        &Dcm_RoutineInfo_0x2111_StartCfg, /*DcmDspStartRoutine*/
        NULL_PTR, /*DcmDspStopRoutine*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/******************Dsp Security Row**************/
/************************************************
 ****DcmDspSecurityRow container(Multiplicity=0..31)****
 ************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspSecurityRowType,DCM_CONST)Dcm_DspSecurityRow[1] =
{
    { /* Level_1 */
        1u,          /*DcmDspSecurityLevel*/
        4u,          /*DcmDspSecuritySeedSize*/
        4u,          /*DcmDspSecurityKeySize*/
        0u,          /*DcmDspSecurityADRSize*/
        TRUE,        /*DcmDspSecurityAttemptCounterEnabled*/
        3u,    /*DcmDspSecurityNumAttDelay*/
        10000u,  /*DcmDspSecurityDelayTime,10s */
        0u,/*DcmDspSecurityDelayTimeOnBoot*/
        /* PRQA S 0674++ */ /* MISRA Rule 1.1 */                
        Rte_Call_Dcm_SecurityAccess_Level_1_GetSeed,    /*Dcm_GetSeedFnc*/
        Rte_Call_Dcm_SecurityAccess_Level_1_CompareKey,    /*Dcm_CompareKeyFnc*/
        Rte_Call_Dcm_SecurityAccess_Level_1_GetSecurityAttemptCounter,    /*Dcm_GetSecurityAttemptCounterFnc*/
        Rte_Call_Dcm_SecurityAccess_Level_1_SetSecurityAttemptCounter,    /*DcmDspSecurityUsePort*/
        /* PRQA S 0674-- */ /* MISRA Rule 1.1 */
        USE_ASYNCH_CLIENT_SERVER,    /*DcmDspSecurityUsePort*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/************************************************
 ****DcmDspSecurity container(Multiplicity=1)****
 ************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspSecurityType,DCM_CONST)Dcm_DspSecurity =
{
    &Dcm_DspSecurityRow[0],    /*pDcm_DspSecurityRow*/
    1u,    /*DcmDspSecurityRow_Num*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/******************Dsp Session Row**************/
/************************************************
 ****DcmDspSessionRow container(Multiplicity=0..31)
 ************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspSessionRowType,DCM_CONST)Dcm_DspSessionRow[3] =
{
    { /* Default */
        DCM_NO_BOOT,    /*DcmDspSessionForBoot*/
        1u,    /*DcmDspSessionLevel*/
        50u,    /*DcmDspSessionP2ServerMax*/
        2000u,    /*DcmDspSessionP2StarServerMax*/
    },
    { /* Programming */
        DCM_SYS_BOOT,    /*DcmDspSessionForBoot*/
        2u,    /*DcmDspSessionLevel*/
        50u,    /*DcmDspSessionP2ServerMax*/
        2000u,    /*DcmDspSessionP2StarServerMax*/
    },
    { /* Extended */
        DCM_NO_BOOT,    /*DcmDspSessionForBoot*/
        3u,    /*DcmDspSessionLevel*/
        50u,    /*DcmDspSessionP2ServerMax*/
        2000u,    /*DcmDspSessionP2StarServerMax*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/************************************************
 *******Dcm_DspSession container(Multiplicity=1)*
 ************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DspSessionType,DCM_CONST)Dcm_DspSession =
{
    &Dcm_DspSessionRow[0],    /*pDcmDspSessionRow*/
    3u,        /*DcmDspSessionRow_Num*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************
 ****************DcmDsp container configration********
 ****************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DspCfgType,DCM_CONST) Dcm_DspCfg =
{
    NULL_PTR,    /*DcmDspDDDIDcheckPerSourceDID*/
    DCM_BIG_ENDIAN,    /*DcmDspDataDefaultEndianness*/
    FALSE,/*DcmDspEnableObdMirror*/
    5,/*DcmDspMaxDidToRead*/
    DCM_DSP_MAX_PERIODIC_DID_TO_READ,/*DcmDspMaxPeriodicDidToRead*/
    0u,/*DcmDspPowerDownTime*/
    BEFORE_RESET,    /*DcmResponseToEcuReset*/
    &Dcm_DspClearDTCCfg,        /*pDcmDspClearDTC*/
    &Dcm_DspComControlCfg,        /*pDcmDspComControl*/
    &Dcm_DspCommonAuthorizationCfg[0],        /*pDcmDspCommonAuthorization*/
    NULL_PTR,    /*pDcmDspControlDTCSetting*/
    &Dcm_DspDataCfg[0],    /*pDcmDspData*/    NULL_PTR,    /*pDcmDspDataInfo*/
    25u,    /*DcmDspDidNum*/
    &Dcm_DspDidCfg[0],        /*pDcmDspDid*/
    3u,    /*DcmDspDidInfoNum*/
    &Dcm_DspDidInfoCfg[0],        /*pDcmDspDidInfo*/
    0u,    /*DcmDspDidRangeNum*/
    NULL_PTR,        /*pDcmDspDidRange*/
    &Dcm_DspMemoryCfg, /*pDcmDspMemory*/

    NULL_PTR,    /*DcmDspRequestFileTransfer*/

    7u,        /*DcmDspRoutineNum*/
    &Dcm_DspRoutineCfg[0],    /*pDcmDspRoutine*/

    &Dcm_DspSecurity,  /* pDcm_DspSecurity */
    &Dcm_DspSession,  /* pDcm_DspSession */

    DCM_DSP_MAX_PERIODIC_DID_SCHEDULER,
    NULL_PTR,

};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************************************************
 ********************************* DSD container configration*****************************
 *****************************************************************************************/

/*Service_0x10_DiagnosticSessionControl SubService*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x10_1_SesRef[2] = {1u, 3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x10_2_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x10_3_SesRef[2] = {1u, 3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x10[3] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x10_1_SesRef[0],    /*DcmDsdSubServiceSessionLevelRef*/
        2u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x2u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x10_2_SesRef[0],    /*DcmDsdSubServiceSessionLevelRef*/
        1u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x3u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x10_3_SesRef[0],    /*DcmDsdSubServiceSessionLevelRef*/
        2u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x11_ECUReset SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x11[3] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x2u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x3u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x19_ReadDTCInformation SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x19[5] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x2u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x6u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x4u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0xAu,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x27_SecurityAccess SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x27[2] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x2u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x28_ComControl SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x28[2] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x0u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x3u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x31_RoutineControl SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x31[2] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x3u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x3E_TesterPresent SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x3E[1] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x0u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Service_0x85_ControlDTCSetting SubService*/

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdSubServiceCfgType,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x85[2] =
{
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x1u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
    {
        NULL_PTR,    /*DcmDsdSubServiceFnc*/
        0x2u,    /*DcmDsdSubServiceId*/
        TRUE,    /*DcmDsdSubServiceUsed*/
        NULL_PTR,    /*DcmDsdSubServiceModeRuleRef*/
        NULL_PTR,    /*DcmDsdSubServiceSecurityLevelRef*/
        0u,    /*DcmDsdSubServiceSecurityLevel_Num*/
        NULL_PTR,    /*DcmDsdSubServiceSessionLevelRef*/
        0u,    /*DcmDsdSubServiceSessionLevel_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*UDS Service session and security configuration*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x10_SesRef[2] = {1u, 3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x27_SesRef[2] = {3u, 2u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x28_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x2E_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x2F_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x31_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x85_SesRef[1] = {3u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x34_SesRef[1] = {2u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x36_SesRef[1] = {2u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(uint8,DCM_CONST)Dcm_SRVTABLE_UDS_CAN_UDS0x37_SesRef[1] = {2u};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/*DcmDsdService SRVTABLE_UDS_CAN*/    
static  CONST(Dcm_DsdServiceCfgType,DCM_CONST)SRVTABLE_UDS_CAN_Service[15] =
{
    { /*DiagnosticSessionControl*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x10,    /*DcmDsdSidTabFnc*/
        0x10u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        TRUE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        2u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x10_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        3u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x10[0],    /*DcmDsdSubService*/
    },
    { /*ECUReset*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x11,    /*DcmDsdSidTabFnc*/
        0x11u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        TRUE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        0u,    /*DcmDsdSessionLevel_Num*/
        NULL_PTR,    /*pDcmDsdSessionLevelRef*/
        3u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x11[0],    /*DcmDsdSubService*/
    },
    { /*ClearDiagnosticInformation*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x14,    /*DcmDsdSidTabFnc*/
        0x14u,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        0u,    /*DcmDsdSessionLevel_Num*/
        NULL_PTR,    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*ReadDTCInformation*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x19,    /*DcmDsdSidTabFnc*/
        0x19u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        0u,    /*DcmDsdSessionLevel_Num*/
        NULL_PTR,    /*pDcmDsdSessionLevelRef*/
        5u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x19[0],    /*DcmDsdSubService*/
    },
    { /*ReadDataByIdentifier*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x22,    /*DcmDsdSidTabFnc*/
        0x22u,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        0u,    /*DcmDsdSessionLevel_Num*/
        NULL_PTR,    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*SecurityAccess*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x27,    /*DcmDsdSidTabFnc*/
        0x27u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        2u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x27_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        2u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x27[0],    /*DcmDsdSubService*/
    },
    { /*CommunicationControl*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x28,    /*DcmDsdSidTabFnc*/
        0x28u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        TRUE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x28_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        2u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x28[0],    /*DcmDsdSubService*/
    },
    { /*WriteDataByIdentifier*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x2E,    /*DcmDsdSidTabFnc*/
        0x2Eu,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x2E_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*InputOutputControlByIdentifier*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x2F,    /*DcmDsdSidTabFnc*/
        0x2Fu,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x2F_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*RoutineControl*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x31,    /*DcmDsdSidTabFnc*/
        0x31u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x31_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        2u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x31[0],    /*DcmDsdSubService*/
    },
    { /*TesterPresent*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x3E,    /*DcmDsdSidTabFnc*/
        0x3Eu,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        TRUE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        0u,    /*DcmDsdSessionLevel_Num*/
        NULL_PTR,    /*pDcmDsdSessionLevelRef*/
        1u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x3E[0],    /*DcmDsdSubService*/
    },
    { /*ControlDTCSetting*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x85,    /*DcmDsdSidTabFnc*/
        0x85u,    /*DcmDsdServiceId*/
        TRUE,    /*DcmDsdSubfuncAvial*/
        TRUE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYANDFUNC, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x85_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        2u,    /*DcmDsdSubService_Num*/
        &Dcm_SRVTABLE_UDS_CAN_DsdSubService_UDS0x85[0],    /*DcmDsdSubService*/
    },
    { /*RequestDownload*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x34,    /*DcmDsdSidTabFnc*/
        0x34u,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x34_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*TransferData*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x36,    /*DcmDsdSidTabFnc*/
        0x36u,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x36_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
    { /*RequestTransferExit*/
        TRUE,    /*DcmDsdServiceUsed*/
        Dcm_UDS0x37,    /*DcmDsdSidTabFnc*/
        0x37u,    /*DcmDsdServiceId*/
        FALSE,    /*DcmDsdSubfuncAvial*/
        FALSE,  /*DcmDsdSuppressPosRsp*/
        DCM_PHYSICAL, /*DcmDsdSidTabAddressingFormat*/
        NULL_PTR,    /*DcmDsdModeRuleRef*/
        0u, /*DcmDsdSecurityLevel_Num*/
        NULL_PTR,    /*pDcmDsdSecurityLevelRef*/
        1u,    /*DcmDsdSessionLevel_Num*/
        &Dcm_SRVTABLE_UDS_CAN_UDS0x37_SesRef[0],    /*pDcmDsdSessionLevelRef*/
        0u,    /*DcmDsdSubService_Num*/
        NULL_PTR,    /*DcmDsdSubService*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/**********************************************************************/
/*DCM Support Service Table(Multiplicity=1..256)*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DsdServiceTableCfgType,DCM_CONST)Dcm_DsdServiceTable[DCM_SERVICE_TAB_NUM]=
{
    {
        0x0u,    /*DcmDsdSidTabId*/
        &SRVTABLE_UDS_CAN_Service[0],    /*pDcmDsdService*/
        15u,    /*DcmDsdSidTab_ServiceNum*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/**********************************************************************/

/**********************************************************************/
/*Dsd container(Multiplicity=1)*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DsdCfgType,DCM_CONST)Dcm_DsdCfg =
{

    &Dcm_DsdServiceTable[0],    /*pDcmDsdServiceTable*/
    DCM_SERVICE_TAB_NUM,        /*DcmDsdServiceTable_Num*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************************************************
 ********************************* DSL container configration*****************************
 *****************************************************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/*DcmDslBuffer container(Multiplicity=1..256)*/
static  CONST(Dcm_DslBufferType,DCM_CONST)Dcm_DslBufferCfg[DCM_CHANNEL_NUM] =
{
    {/* buffer_can_rx*/
        0x0u,    /*Dcm_DslBufferId*/
        512u,    /*Dcm_DslBufferSize*/
        0u,    /*offset*/
    },
    {/* buffer_can_tx*/
        0x1u,    /*Dcm_DslBufferId*/
        512u,    /*Dcm_DslBufferSize*/
        512u,    /*offset*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/***********************************/
/*DcmDslDiagResp container(Multiplicity=1)*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static  CONST(Dcm_DslDiagRespType,DCM_CONST)Dcm_DslDiagRespCfg =
{
    FALSE,        /*DcmDslDiagRespOnSecondDeclinedRequest*/
    0u,        /*DcmDslDiagRespMaxNumRespPend*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************
 *DcmDslCallbackDCMRequestService port configuration(Multiplicity=1..*)
 *****************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 5.2 */
static  CONST(Dcm_DslCallbackDCMRequestServiceType,DCM_CONST)Dcm_DslCallbackDCMRequestServiceCfg[1] =
/* PRQA S 0779-- */ /* MISRA Rule 5.2 */
{
    {
        NULL_PTR,
        NULL_PTR,
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/********************UDS protocol Connection configuration*******************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslProtocolRxType,DCM_CONST)Dsl_Protocol_Connection_RxCfg[DCM_DSL_RX_ID_NUM]=
{
    {
        0x0u,              /*DcmDslParentMainConnectionCtrlId*/
        DCM_FUNCTIONAL,    /*DcmDslProtocolRxAddrType*/
        DCM_DCM_Func_Diag_Request,       /*DcmDslProtocolRxPduId*/

    },
    {
        0x0u,              /*DcmDslParentMainConnectionCtrlId*/
        DCM_PHYSICAL,    /*DcmDslProtocolRxAddrType*/
        DCM_DCM_Phys_Diag_Request,       /*DcmDslProtocolRxPduId*/

    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslProtocolTxType,DCM_CONST)Dsl_Protocol_Connection_TxCfg[DCM_DSL_TX_ID_NUM]=
    {
        {
        0x0u,            /*DcmDslParentMainConnectionCtrlId*/
        DCM_DCM_Diag_Resp,       /*DcmDslProtocolTxPduId*/
        DCM_PDUR_DCM_Diag_Resp, /*DcmDslProtocolTx Pdu Id of PduR*/
        },
    };
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Connection1,Mainconnection,ProtocolTx configration(Multiplicity=1..*)*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslMainConnectionType,DCM_CONST) Dsl_Protocol_MainConnectionCfg[DCM_MAINCONNECTION_NUM] =
{
    {
        0x00000000u,                            /*DcmDslProtocolRxTesterSourceAddr*/
        NULL_PTR,  /*pDcmDslPeriodicTranmissionConRef*/
        0u,                                     /*DcmDslProtocolComMChannelId*/
        NULL_PTR,  /*pDcmDslROEConnectionRef*/
        &Dsl_Protocol_Connection_RxCfg[0],    /*pDcmDslProtocolRx*/
        2u,                                   /*DcmDslProtocolRx_Num*/
        &Dsl_Protocol_Connection_TxCfg[0],  /*pDcmDslProtocolTx*/
        1u,                                    /*DcmDslProtocolTx_Num*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*Connection1 configration*/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslConnectionType,DCM_CONST)Dsl_Protocol_ConnectionCfg[DCM_CONNECTION_NUM]=
{
    {
        0x0u,                                /*parent protocolRow id*/
        &Dsl_Protocol_MainConnectionCfg[0],    /*pDcmDslMainConnection*/
        NULL_PTR,                             /*pDcmDslPeriodicTransmission*/
        NULL_PTR,                              /*pDcmDslResponseOnEvent*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"
/*****************************************************
 ****Dcm_DslProtocolRow container configration(Multiplicity=1..*)*******
 ****************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslProtocolRowType,DCM_CONST)Dsl_ProtocolRowCfg[DCM_DSLPROTOCOLROW_NUM_MAX] =
{
    {
        DCM_UDS_ON_CAN,        /*DcmDslProtocolID*/
        10u,                    /*DcmDslProtocolPreemptTimes*/
        0u,                    /*DcmDslProtocolPriority*/
        TRUE,                /*true-protocol is available*/
        DCM_PROTOCAL_TRAN_NOT_VALID,
        TRUE,                /*True-send 0x78 before transitioning to the bootloader */
        10u,                     /*DcmTimStrP2ServerAdjust*/
        10u,                /*DcmTimStrP2StarServerAdjust*/
        &Dcm_DslBufferCfg[0],/*DcmDslProtocolRxBuffer*/
        &Dcm_DslBufferCfg[1],/*DcmDslProtocolTxBuffer*/
        0u,                 /*DcmDslServiceTableId*/
        &Dsl_Protocol_ConnectionCfg[0], /*DcmDslConnection*/
        TRUE,              /*DcmDslProtocolRequestQueued*/
    },
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************
 *DcmDslProtocol container configration(Multiplicity=1)
 ****************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
static CONST(Dcm_DslProtocolType,DCM_CONST)Dcm_DslProtocol =
{
    &Dsl_ProtocolRowCfg[0],    /*pDcmDslProtocolRow*/
    DCM_DSLPROTOCOLROW_NUM_MAX,    /*DcmDslProtocolRow_Num*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/*****************************************************
 ****************DcmDsl container configration*****
 ****************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_DslCfgType,DCM_CONST)Dcm_DslCfg =
{
    DCM_CHANNEL_NUM,        /*Number of Channel configration*/
    &Dcm_DslBufferCfg[0],    /*DcmDslBuffer*/
    0u,                        /*Number of DslCallbackDCMRequestService port*/
    &Dcm_DslCallbackDCMRequestServiceCfg[0],    /*pDcmDslCallback_DCMRequestService*/
    &Dcm_DslDiagRespCfg,       /*reference to DcmDslDiagResp configration*/
    &Dcm_DslProtocol,        /*reference to DcmDslProtocol configration*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_CfgType,DCM_CONST)Dcm_Cfg =
{
    &Dcm_DslCfg,    /*pDcmDslCfg*/
    &Dcm_DsdCfg,    /*pDcmDsdCfg*/
    &Dcm_DspCfg,    /*pDcmDspCfg*/
    NULL_PTR,        /*pDcmPageBufferCfg*/
    NULL_PTR,        /*pDcmProcessingConditionsCfg*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

/**********************************************************************
 ***********************DcmGeneral Container***************************
 **********************************************************************/
#define   DCM_START_SEC_CONST_UNSPECIFIED
#include  "Dcm_MemMap.h"
CONST(Dcm_GeneralCfgType,DCM_CONST)Dcm_GeneralCfg =
{
    FALSE, /*DcmDDDIDStorage*/
    DCM_DEV_ERROR_DETECT, /*DcmDevErrorDetect*//* PRQA S 1295 */ /* MISRA Rule 10.3 */
    NULL_PTR, /*DcmHeaderFileInclusion*/
    DCM_RESPOND_ALL_REQUEST, /*DcmRespondAllRequest*//* PRQA S 1295 */ /* MISRA Rule 10.3 */
    DCM_VERSION_INFO_API, /*DcmVersionInfoApi*//* PRQA S 1295 */ /* MISRA Rule 10.3 */
    10, /*DcmTaskTime*/
    NULL_PTR, /*DcmVinRef*/
};
#define  DCM_STOP_SEC_CONST_UNSPECIFIED
#include "Dcm_MemMap.h"

