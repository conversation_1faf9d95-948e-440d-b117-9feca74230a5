#include "os_pod.h"
#include "pod_timer.h"
/// ---------- begin of guard: <includes> -------------------------------------
#include "log.h"
#ifdef WIN32
#else
#include "Can.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Dcm.h"
#include "Dem.h"
#include "Com.h"
#include "CanSM.h"
#include "ComM.h"
#include "Dio.h"
#include "key.h"
#include "iokey.h"
#include "mem.h"
#include "motor.h"
#endif
/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------
#ifdef WIN32
void TaskTimers_handler(void *param)
{
    println(LogInfo, "TaskDemoTimer Handler...\n");
    default_task_handler(param);
}
#endif

static tU32 timer_counter = 0;

#ifdef WIN32
extern void can_task_handler(void);
#else
extern void canif_task_handler(void);
extern void motor_control(void);
#endif

static void bsw_main_functions(void)
{
    ComM_MainFunction_Channel_CanController_0();
    Com_MainFunctionRx();
    Com_MainFunctionTx();
    //BSW TASK
    CanTp_MainFunction();
    Dem_MainFunction(); // Dem MainFunction is placed before Dcm_MainFunction so that task created by Dcm will be handled in the next cycle
    Dcm_MainFunction();
    // CanSM_MainFunction();
}
/// ---------- end of guard:   <header> ---------------------------------------

void pod_timer_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_timer_on_init> -----------------------------------------
    // Note: Need to start timers explicitly?
    os_timer_start(TmTimer10ms);
    os_timer_start(TmTimer100ms);

    mem_init();

/// ---------- end of guard:   <pod_timer_on_init> -----------------------------------------
}

void pod_timer_on_tm100ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_timer_on_tm100ms> -----------------------------------------
    // println(LogInfo, "Demo Timer Process 100ms...");
#ifdef USE_DEBUG_FUNCS
    check_debug_funcs();
#endif
/// ---------- end of guard:   <pod_timer_on_tm100ms> -----------------------------------------
}

void pod_timer_on_tm10ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_timer_on_tm10ms> -----------------------------------------
    timer_counter++;
#ifdef WIN32
#else
    bsw_main_functions();

    // can_task_handler();
    canif_task_handler();
    // println(LogInfo, "Demo Timer Process 10ms...");
    motor_control();
    //testPwmHandle();

    // TODO: move to another timer?
    check_io_keys();
#endif
/// ---------- end of guard:   <pod_timer_on_tm10ms> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------