#include <string.h>
#include "mem.h"
#include "motor.h"
#include "Fee.h"

// FEE Mem starts at 0x0400_0000
// FEE Mem data starts at 0x0400_4000, and grows backup up until 0x0400_0000

// 0x0400_0000  ---- FEE MEM start ---
// 0x0400_0010  .....  index ↓
// 0x0400_0020  .....
// ....         .....  data ↑
// 0x0400_3FF0  .....  data ↑
// 0x0400_4000  ---- FEE MEM end ---


extern tMotor motors[MOTOR_MAX];

// #define START_ADDR                   0x00UL
// #define FLS_BUF_SIZE                 512U

// u8 txbuf[FLS_BUF_SIZE];
// u8 rxbuf[FLS_BUF_SIZE];

static tSeatMem mem = {0};

static void mem_set_motors(void)
{
    for (usize i = 0; i < MOTOR_MAX; i++)
    {
        if (mem.ranges[i] != 0) {
            motors[i].config.range = mem.ranges[i];
        }
        motors[i].encoder.position = mem.current[i];
        motors[i].position_pid.target = mem.current[i];
    }
}

void mem_init(void)
{
    mem_load();
    // write loaded mem to motors
    mem_set_motors();

    // usize size = sizeof(tSeatMem);
    // memcpy(&mem, readbuf, size);
}

void mem_save(void)
{
    MemIf_StatusType status = MEMIF_IDLE;
    /*Write data to block 1*/
    Fee_Write(FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_Motor, (u8*)&mem);
    do
    {
        Fls_MainFunction();
        Fee_MainFunction();
        status = Fee_GetStatus();
    } while (status != MEMIF_IDLE);
}

void mem_load(void)
{
    MemIf_StatusType status = MEMIF_IDLE;
    Fee_Read(FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_Motor, 0U, (u8*)&mem, 100U);
    do
    {
        Fls_MainFunction();
        Fee_MainFunction();
        status = Fee_GetStatus();
    } while (status != MEMIF_IDLE);
}

void mem_collect(void)
{
    for (usize i = 0; i < MOTOR_MAX; i++)
    {
        mem.ranges[i] = motors[i].config.range;
        mem.current[i] = motors[i].encoder.position;
    }
}

void mem_collect_motor(tMotor* motor)
{
    u8 id = motor->type;
    mem.ranges[id] = motor->config.range;
    mem.current[id] = motor->encoder.position;
}


void mem_clean(void)
{
    memset(&mem, 0, sizeof(tSeatMem));
    mem_save();
}
