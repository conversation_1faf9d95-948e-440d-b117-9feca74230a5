/**
 * file    Adc_Cfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef ADC_CFG_H
#define ADC_CFG_H

/**
*   @file
*
*   @addtogroup adc_driver_config Adc Driver Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/
#include "Adc_CfgDefines.h"
#include "Adc_HwType.h"
#include "Adc_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_VENDOR_ID_CFG                       (110u)
#define ADC_AR_RELEASE_MAJOR_VERSION_CFG        (4u)
#define ADC_AR_RELEASE_MINOR_VERSION_CFG        (4u)
#define ADC_AR_RELEASE_REVISION_VERSION_CFG     (0u)
#define ADC_SW_MAJOR_VERSION_CFG                (1u)
#define ADC_SW_MINOR_VERSION_CFG                (0u)
#define ADC_SW_PATCH_VERSION_CFG                (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Check if header file and Adc_CfgDefines header file are of the same vendor */
#if (ADC_VENDOR_ID_CFG != ADC_VENDOR_ID_CFGDEFINES)
    #error "Adc_Cfg.h and Adc_CfgDefines.h have different vendor ids"
#endif

/* Check if header file and Adc_CfgDefines header file are of the same Autosar version */
#if ((ADC_AR_RELEASE_MAJOR_VERSION_CFG != ADC_AR_RELEASE_MAJOR_VERSION_CFGDEFINES) || \
     (ADC_AR_RELEASE_MINOR_VERSION_CFG != ADC_AR_RELEASE_MINOR_VERSION_CFGDEFINES) || \
     (ADC_AR_RELEASE_REVISION_VERSION_CFG != ADC_AR_RELEASE_REVISION_VERSION_CFGDEFINES) \
    )
    #error "AutoSar Version Numbers of Adc_Cfg.h and Adc_CfgDefines.h are different"
#endif

/* Check if header file and Adc_CfgDefines header file are of the same Software version */
#if ((ADC_SW_MAJOR_VERSION_CFG != ADC_SW_MAJOR_VERSION_CFGDEFINES) || \
     (ADC_SW_MINOR_VERSION_CFG != ADC_SW_MINOR_VERSION_CFGDEFINES) || \
     (ADC_SW_PATCH_VERSION_CFG != ADC_SW_PATCH_VERSION_CFGDEFINES) \
    )
    #error "Software Version Numbers of Adc_Cfg.h and Adc_CfgDefines.h are different"
#endif

/* Check if Adc_Cfg.h file and Adc_HwType.h file are of the same vendor */
#if (ADC_VENDOR_ID_CFG != ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_Cfg.h and Adc_HwType.h have different vendor ids"
#endif

/* Check if Adc_Cfg.h file and Adc_HwType.h file are of the same Autosar version */
#if ((ADC_AR_RELEASE_MAJOR_VERSION_CFG != ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_AR_RELEASE_MINOR_VERSION_CFG != ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_AR_RELEASE_REVISION_VERSION_CFG != ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_Cfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc_Cfg.h file and Adc_HwType.h file are of the same Software version */
#if ((ADC_SW_MAJOR_VERSION_CFG != ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_SW_MINOR_VERSION_CFG != ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_SW_PATCH_VERSION_CFG != ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_Cfg.h and Adc_HwType.h are different"
#endif

/* Check if source file and Adc configuration header file are of the same vendor */
#if (ADC_VENDOR_ID_PBCFG != ADC_VENDOR_ID_CFG)
    #error "Adc_PBcfg.h and Adc_Cfg.h have different vendor IDs"
#endif
/* Check if header file and Adc configuration header file are of the same Autosar version */
#if ((ADC_AR_RELEASE_MAJOR_VERSION_PBCFG != ADC_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (ADC_AR_RELEASE_MINOR_VERSION_PBCFG != ADC_AR_RELEASE_MINOR_VERSION_CFG) || \
     (ADC_AR_RELEASE_REVISION_VERSION_PBCFG != ADC_AR_RELEASE_REVISION_VERSION_CFG) \
    )
    #error "AutoSar Version Numbers of Adc_PBcfg.h and Adc_Cfg.h are different"
#endif
/* Check if header file and Adc configuration header file are of the same software version */
#if ((ADC_SW_MAJOR_VERSION_PBCFG != ADC_SW_MAJOR_VERSION_CFG) || \
     (ADC_SW_MINOR_VERSION_PBCFG != ADC_SW_MINOR_VERSION_CFG) || \
     (ADC_SW_PATCH_VERSION_PBCFG != ADC_SW_PATCH_VERSION_CFG) \
    )
    #error "Software Version Numbers of Adc_PBcfg.h and Adc_Cfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define ADC_CONFIG_EXT \
ADC_CONFIG_PB


/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_CFG_H */

