<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Gpt" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Gpt" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Gpt"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild"/>
              <d:ctr name="GptChannelConfigSet" type="IDENTIFIABLE">
                <d:lst name="GptChannelConfiguration" type="MAP">
                  <d:ctr name="GptChannelConfiguration_0" type="IDENTIFIABLE">
                    <d:var name="GptChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="GptHwIp" type="ENUMERATION" value="PIT">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:ref name="GptModuleRef" type="REFERENCE" 
                           value="ASPath:/Gpt/Gpt/GptChannelConfigSet/GptPit_0/GptPitChannels_0"/>
                    <d:var name="GptChannelMode" type="ENUMERATION" 
                           value="GPT_CH_MODE_CONTINUOUS"/>
                    <d:var name="GptChannelTickFrequency" type="INTEGER" 
                           value="80000000">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:ref name="GptChannelClkSrcRef" type="REFERENCE" 
                           value="ASPath:/Gpt/Gpt/GptDriverConfiguration/GptClockReferencePoint_0"/>
                    <d:var name="GptChannelTickValueMax" type="INTEGER" 
                           value="4294967295">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="GptEnableWakeup" type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="GptNotification" type="FUNCTION-NAME" 
                           value="Gpt_5msCycleCallback">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:ctr name="GptWakeupConfiguration" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="GptWakeupSourceRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="GptRtc" type="MAP"/>
                <d:lst name="GptStm" type="MAP"/>
                <d:lst name="GptPit" type="MAP">
                  <d:ctr name="GptPit_0" type="IDENTIFIABLE">
                    <d:var name="GptPitModule" type="ENUMERATION" value="PIT_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:lst name="GptPitChannels" type="MAP">
                      <d:ctr name="GptPitChannels_0" type="IDENTIFIABLE">
                        <d:var name="GptPitChannel" type="ENUMERATION" 
                               value="CH_0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitExternalTrigger" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitReloadOnTrigger" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitStopOnInterrupt" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitStartOnTrigger" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="ChainMode" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitChannelFilterEnable" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitExternalTrigSelect" type="ENUMERATION" 
                               value="PIT_EXTERNALSOURCE_TRGMUX">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitChannelWorkMode" type="ENUMERATION" 
                               value="PIT_MODE_32BIT_COUNT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitChannelFilterWidth" type="ENUMERATION" 
                               value="PIT_EXTPULSEFILWIDTH_1_CYCLE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PitTriggerChannels" type="ENUMERATION" 
                               value="Channel_0_Trigger_Source">
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:lst name="GptHwConfiguration" type="MAP">
                <d:ctr name="GptHwConfiguration_0" type="IDENTIFIABLE">
                  <d:var name="GptIsrHwId" type="ENUMERATION" value="PIT_0_CH_0">
                    <a:a name="IMPORTER_INFO">
                      <a:v>@DEF</a:v>
                      <a:v>@CALC</a:v>
                    </a:a>
                  </d:var>
                  <d:var name="GptIsrEnable" type="BOOLEAN" value="true"/>
                  <d:var name="GptChannelIsUsed" type="BOOLEAN" value="true"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="GptConfigurationOfOptApiServices" 
                     type="IDENTIFIABLE">
                <d:var name="GptDeinitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptEnableDisableNotificationApi" type="BOOLEAN" 
                       value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptTimeElapsedApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptTimeRemainingApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="GptWakeupFunctionalityApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="GptAutosarExt" type="IDENTIFIABLE">
                <d:var name="GptChangeNextTimeoutValueApi" type="BOOLEAN" 
                       value="true"/>
                <d:var name="ChainModeApi" type="BOOLEAN" value="true"/>
                <d:var name="GptStandbyWakeupSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptEnableTriggers" type="BOOLEAN" value="true"/>
              </d:ctr>
              <d:ctr name="GptDriverConfiguration" type="IDENTIFIABLE">
                <d:var name="GptDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GptReportWakeupSource" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="GptKernelEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:lst name="GptClockReferencePoint" type="MAP">
                  <d:ctr name="GptClockReferencePoint_0" type="IDENTIFIABLE">
                    <d:ref name="GptClockReference" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_STM"/>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="100">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
