/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Rte_Dcm.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:15 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#include "Rte_Dcm.h"
#define DCM_UNUSED(a) (void)(a)

/* PRQA S 3673++ */ /* MISRA Rule 8.13 */
/* PRQA S 0791,3408++ */ /* MISRA Rule 5.4,Rule 8.4 */
/* PRQA S 0779,0777++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8  Buffer_FalseAcessCount_Level_1  =  0x0;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
/* PRQA S 0779,0777-- */ /* MISRA Rule 1.3,Rule 5.2 */

/***************************Security Part****************************************/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_SecurityAccess_Level_1_CompareKey( const  uint8*  Key,Dcm_OpStatusType  OpStatus,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(Key);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_SecurityAccess_Level_1_GetSecurityAttemptCounter( Dcm_OpStatusType  OpStatus,uint8*  AttemptCounter )
{
    (*AttemptCounter) = Buffer_FalseAcessCount_Level_1;
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_SecurityAccess_Level_1_GetSeed(
        Dcm_OpStatusType OpStatus,uint8* Seed,Dcm_NegativeResponseCodeType* ErrorCode)
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Seed);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_SecurityAccess_Level_1_SetSecurityAttemptCounter( Dcm_OpStatusType  OpStatus,  uint8  AttemptCounter )
{
    DCM_UNUSED(OpStatus);
    Buffer_FalseAcessCount_Level_1 = AttemptCounter;
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
/***************************Did Part****************************************/

/* PRQA S 0686++ */ /* MISRA Rule 9.3 */
#define  DataLength_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier 10u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier[ DataLength_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier_Default[ DataLength_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF199_programmingDateDataIdentifier 4u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF199_programmingDateDataIdentifier[ DataLength_Data_0xF199_programmingDateDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF199_programmingDateDataIdentifier_Default[ DataLength_Data_0xF199_programmingDateDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier 21u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier[ DataLength_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_Default[ DataLength_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x0110_Manufactory_mode 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x0110_Manufactory_mode[ DataLength_Data_0x0110_Manufactory_mode ] = {0x20} ;
uint8 Buffer_Data_0x0110_Manufactory_mode_Default[ DataLength_Data_0x0110_Manufactory_mode ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x0120_DTC_Setting_control_state 12u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x0120_DTC_Setting_control_state[ DataLength_Data_0x0120_DTC_Setting_control_state ] = {0x20} ;
uint8 Buffer_Data_0x0120_DTC_Setting_control_state_Default[ DataLength_Data_0x0120_DTC_Setting_control_state ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version 4u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version[ DataLength_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version ] = {0x20} ;
uint8 Buffer_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version_Default[ DataLength_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier 17u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier[ DataLength_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier_Default[ DataLength_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF180_bootSoftwareIdentification 17u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF180_bootSoftwareIdentification[ DataLength_Data_0xF180_bootSoftwareIdentification ] = {0x20} ;
uint8 Buffer_Data_0xF180_bootSoftwareIdentification_Default[ DataLength_Data_0xF180_bootSoftwareIdentification ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x3400_Usage_mode 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x3400_Usage_mode[ DataLength_Data_0x3400_Usage_mode ] = {0x20} ;
uint8 Buffer_Data_0x3400_Usage_mode_Default[ DataLength_Data_0x3400_Usage_mode ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF186_activeDiagnosticSessionDataIdentifier 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF186_activeDiagnosticSessionDataIdentifier[ DataLength_Data_0xF186_activeDiagnosticSessionDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF186_activeDiagnosticSessionDataIdentifier_Default[ DataLength_Data_0xF186_activeDiagnosticSessionDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF187_GAC_SparePartNumberDataIdentifier 14u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF187_GAC_SparePartNumberDataIdentifier[ DataLength_Data_0xF187_GAC_SparePartNumberDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF187_GAC_SparePartNumberDataIdentifier_Default[ DataLength_Data_0xF187_GAC_SparePartNumberDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier 17u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier[ DataLength_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier_Default[ DataLength_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier 14u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier[ DataLength_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier ] = {0x20} ;
uint8 Buffer_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier_Default[ DataLength_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF193_systemSupplier_ECU_Hardware_Version 14u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF193_systemSupplier_ECU_Hardware_Version[ DataLength_Data_0xF193_systemSupplier_ECU_Hardware_Version ] = {0x20} ;
uint8 Buffer_Data_0xF193_systemSupplier_ECU_Hardware_Version_Default[ DataLength_Data_0xF193_systemSupplier_ECU_Hardware_Version ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xF195_systemSupplierECUSoftwareVersion 14u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xF195_systemSupplierECUSoftwareVersion[ DataLength_Data_0xF195_systemSupplierECUSoftwareVersion ] = {0x20} ;
uint8 Buffer_Data_0xF195_systemSupplierECUSoftwareVersion_Default[ DataLength_Data_0xF195_systemSupplierECUSoftwareVersion ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xAE3B_Suction_top_screen_state 14u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xAE3B_Suction_top_screen_state[ DataLength_Data_0xAE3B_Suction_top_screen_state ] = {0x20} ;
uint8 Buffer_Data_0xAE3B_Suction_top_screen_state_Default[ DataLength_Data_0xAE3B_Suction_top_screen_state ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query[ DataLength_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query ] = {0x20} ;
uint8 Buffer_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query_Default[ DataLength_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x2110_OTA_Recovery_Status 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x2110_OTA_Recovery_Status[ DataLength_Data_0x2110_OTA_Recovery_Status ] = {0x20} ;
uint8 Buffer_Data_0x2110_OTA_Recovery_Status_Default[ DataLength_Data_0x2110_OTA_Recovery_Status ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x2111_OTA_Partition_synchronization_status 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x2111_OTA_Partition_synchronization_status[ DataLength_Data_0x2111_OTA_Partition_synchronization_status ] = {0x20} ;
uint8 Buffer_Data_0x2111_OTA_Partition_synchronization_status_Default[ DataLength_Data_0x2111_OTA_Partition_synchronization_status ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x0200_Reprogramming_Counter 2u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x0200_Reprogramming_Counter[ DataLength_Data_0x0200_Reprogramming_Counter ] = {0x20} ;
uint8 Buffer_Data_0x0200_Reprogramming_Counter_Default[ DataLength_Data_0x0200_Reprogramming_Counter ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x0201_Reprogramming_Attempt_Counter 2u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x0201_Reprogramming_Attempt_Counter[ DataLength_Data_0x0201_Reprogramming_Attempt_Counter ] = {0x20} ;
uint8 Buffer_Data_0x0201_Reprogramming_Attempt_Counter_Default[ DataLength_Data_0x0201_Reprogramming_Attempt_Counter ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x1000_Power_Voltage 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x1000_Power_Voltage[ DataLength_Data_0x1000_Power_Voltage ] = {0x20} ;
uint8 Buffer_Data_0x1000_Power_Voltage_Default[ DataLength_Data_0x1000_Power_Voltage ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0x5005_OTA_mode 1u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0x5005_OTA_mode[ DataLength_Data_0x5005_OTA_mode ] = {0x20} ;
uint8 Buffer_Data_0x5005_OTA_mode_Default[ DataLength_Data_0x5005_OTA_mode ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xAEA0_Motor_control 3u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xAEA0_Motor_control[ DataLength_Data_0xAEA0_Motor_control ] = {0x20} ;
uint8 Buffer_Data_0xAEA0_Motor_control_Default[ DataLength_Data_0xAEA0_Motor_control ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
#define  DataLength_Data_0xAE45_Running_data_record 17u
#define  DCM_START_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
uint8 Buffer_Data_0xAE45_Running_data_record[ DataLength_Data_0xAE45_Running_data_record ] = {0x20} ;
uint8 Buffer_Data_0xAE45_Running_data_record_Default[ DataLength_Data_0xAE45_Running_data_record ] = {0x0} ;
#define  DCM_STOP_SEC_VAR_POWER_ON_INIT_8
#include "Dcm_MemMap.h"
/* PRQA S 0791,3408,0686-- */ /* MISRA Rule 5.4,Rule 8.4,Rule 9.3 */
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF199_programmingDateDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF180_bootSoftwareIdentification_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x3400_Usage_mode_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF186_activeDiagnosticSessionDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF187_GAC_SparePartNumberDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF193_systemSupplier_ECU_Hardware_Version_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF195_systemSupplierECUSoftwareVersion_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE3B_Suction_top_screen_state_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x2110_OTA_Recovery_Status_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x2111_OTA_Partition_synchronization_status_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0200_Reprogramming_Counter_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0201_Reprogramming_Attempt_Counter_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x1000_Power_Voltage_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x5005_OTA_mode_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE45_Running_data_record_ConditionCheckRead( Dcm_OpStatusType  OpStatus,  Dcm_NegativeResponseCodeType*  ErrorCode )
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF184_ApplicationSoftwareFingerprintDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF199_programmingDateDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF199_programmingDateDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF199_programmingDateDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x0110_Manufactory_mode;index++)
    {
        Data[index] = Buffer_Data_0x0110_Manufactory_mode[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x0120_DTC_Setting_control_state;index++)
    {
        Data[index] = Buffer_Data_0x0120_DTC_Setting_control_state[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version;index++)
    {
        Data[index] = Buffer_Data_0xF10B_GAC_Diagnostic_Parameter_Table_Version[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF17F_GACECUHardwareVersionNumberDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF180_bootSoftwareIdentification_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF180_bootSoftwareIdentification;index++)
    {
        Data[index] = Buffer_Data_0xF180_bootSoftwareIdentification[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x3400_Usage_mode_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x3400_Usage_mode;index++)
    {
        Data[index] = Buffer_Data_0x3400_Usage_mode[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF186_activeDiagnosticSessionDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF186_activeDiagnosticSessionDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF186_activeDiagnosticSessionDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF187_GAC_SparePartNumberDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF187_GAC_SparePartNumberDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF187_GAC_SparePartNumberDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF189_GACECUSoftwareVersionNumberDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier;index++)
    {
        Data[index] = Buffer_Data_0xF18E_GAC_KitAssemblyPartNumberDataIdentifier[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF193_systemSupplier_ECU_Hardware_Version_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF193_systemSupplier_ECU_Hardware_Version;index++)
    {
        Data[index] = Buffer_Data_0xF193_systemSupplier_ECU_Hardware_Version[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF195_systemSupplierECUSoftwareVersion_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xF195_systemSupplierECUSoftwareVersion;index++)
    {
        Data[index] = Buffer_Data_0xF195_systemSupplierECUSoftwareVersion[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE3B_Suction_top_screen_state_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xAE3B_Suction_top_screen_state;index++)
    {
        Data[index] = Buffer_Data_0xAE3B_Suction_top_screen_state[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query;index++)
    {
        Data[index] = Buffer_Data_0xAE3C_Suction_Top_Screen_Calibration_Status_Query[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x2110_OTA_Recovery_Status_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x2110_OTA_Recovery_Status;index++)
    {
        Data[index] = Buffer_Data_0x2110_OTA_Recovery_Status[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x2111_OTA_Partition_synchronization_status_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x2111_OTA_Partition_synchronization_status;index++)
    {
        Data[index] = Buffer_Data_0x2111_OTA_Partition_synchronization_status[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0200_Reprogramming_Counter_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x0200_Reprogramming_Counter;index++)
    {
        Data[index] = Buffer_Data_0x0200_Reprogramming_Counter[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0201_Reprogramming_Attempt_Counter_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x0201_Reprogramming_Attempt_Counter;index++)
    {
        Data[index] = Buffer_Data_0x0201_Reprogramming_Attempt_Counter[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x1000_Power_Voltage_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x1000_Power_Voltage;index++)
    {
        Data[index] = Buffer_Data_0x1000_Power_Voltage[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x5005_OTA_mode_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0x5005_OTA_mode;index++)
    {
        Data[index] = Buffer_Data_0x5005_OTA_mode[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xAEA0_Motor_control;index++)
    {
        Data[index] = Buffer_Data_0xAEA0_Motor_control[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAE45_Running_data_record_ReadData( Dcm_OpStatusType  OpStatus,uint8*  Data,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xAE45_Running_data_record;index++)
    {
        Data[index] = Buffer_Data_0xAE45_Running_data_record[index];
    }
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(Data);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ReturnControlToECU(
    P2CONST(uint8,AUTOMATIC,DCM_VAR)ControlEnableMaskRecord,
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(ControlEnableMaskRecord);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xAEA0_Motor_control_ShortTermAdjustment(
    P2CONST(uint8,AUTOMATIC,DCM_VAR) ControlStateInfo,
    uint16 DataLength,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR) ControlMask,
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    uint8  index;
    for(index = 0;index < DataLength_Data_0xAEA0_Motor_control;index++)
    {
        Buffer_Data_0xAEA0_Motor_control[index] = ControlStateInfo[index];
    }
    DCM_UNUSED(DataLength);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ControlMask);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier_WriteData( const  uint8*  Data,uint16  DataLength,Dcm_OpStatusType  OpStatus,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index = 0;
    for(index = 0; index < DataLength_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier; index++)
    {
        Buffer_Data_0xF18C_GAC_ECUSerialNumberDataIdentifier[index] = Data[index];
    }
    DCM_UNUSED(Data);
    DCM_UNUSED(DataLength);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0110_Manufactory_mode_WriteData( const  uint8*  Data,uint16  DataLength,Dcm_OpStatusType  OpStatus,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index = 0;
    for(index = 0; index < DataLength_Data_0x0110_Manufactory_mode; index++)
    {
        Buffer_Data_0x0110_Manufactory_mode[index] = Data[index];
    }
    DCM_UNUSED(Data);
    DCM_UNUSED(DataLength);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_CSDataServices_Data_0x0120_DTC_Setting_control_state_WriteData( const  uint8*  Data,uint16  DataLength,Dcm_OpStatusType  OpStatus,Dcm_NegativeResponseCodeType*  ErrorCode )
{
    uint8  index = 0;
    for(index = 0; index < DataLength_Data_0x0120_DTC_Setting_control_state; index++)
    {
        Buffer_Data_0x0120_DTC_Setting_control_state[index] = Data[index];
    }
    DCM_UNUSED(Data);
    DCM_UNUSED(DataLength);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
/***************************Routine Part****************************************/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0x2110_RequestResults(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0x2111_RequestResults(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0xAE3D_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0x0203_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0xAE3C_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0xAEA1_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0xAEA2_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0x2110_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType  Rte_Call_Dcm_RoutineServices_Routine_0x2111_Start(
    /* PRQA S 3432++ */ /* MISRA Rule 20.7 */
    P2CONST(uint8,AUTOMATIC,DCM_VAR)InBuffer,
    Dcm_OpStatusType OpStatus,
    P2VAR(uint8,AUTOMATIC,DCM_VAR)OutBuffer,
    P2VAR(uint16,AUTOMATIC,DCM_VAR)currentDataLength,
    P2VAR(Dcm_NegativeResponseCodeType,AUTOMATIC,DCM_VAR)ErrorCode)
/* PRQA S 3432-- */     /* MISRA Rule 20.7 */
{
    DCM_UNUSED(InBuffer);
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(OutBuffer);
    DCM_UNUSED(currentDataLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
/*PreConditon Check*/
Std_ReturnType  Dcm_Rte_PreConditonCheck = E_OK;
Std_ReturnType RTE_PreConditonCheck(void)
{
    return Dcm_Rte_PreConditonCheck;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType SchM_PerformReset(Rte_ModeType_DcmEcuReset Reset)
{
    if (Reset == RTE_MODE_DcmEcuReset_EXECUTE)
    {
//        Mcu_PerformReset();
    }
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

/* PRQA S 3673-- */ /* MISRA Rule 8.13 */
