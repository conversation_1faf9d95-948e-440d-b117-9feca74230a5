/**
 * file    Dma_Cfg_Defines.h
 * brief   dma cfg defines header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
 
#ifndef DMA_CFG_DEFINES_H
#define DMA_CFG_DEFINES_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Hw_Cfg_Defines.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_CFG_DEFINES_VENDOR_ID                       (110U)
#define DMA_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_CFG_DEFINES_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_CFG_DEFINES_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_CFG_DEFINES_SW_MAJOR_VERSION                (1U)
#define DMA_CFG_DEFINES_SW_MINOR_VERSION                (0U)
#define DMA_CFG_DEFINES_SW_PATCH_VERSION                (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Dma_Hw_Cfg_Defines.h file are of the same vendor */
#if (DMA_CFG_DEFINES_VENDOR_ID != DMA_HW_CFG_DEFINES_VENDOR_ID)
    #error "Dma_Cfg_Defines.h and Dma_Hw_Cfg_Defines.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_Cfg_Defines.h file are of the same Autosar version */
#if ((DMA_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION != DMA_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_CFG_DEFINES_AR_RELEASE_MINOR_VERSION != DMA_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION) || \
     (DMA_CFG_DEFINES_AR_RELEASE_REVISION_VERSION != DMA_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Cfg_Defines.h and Dma_Hw_Cfg_Defines.h are different"
#endif

/* Check if header file and Dma_Hw_Cfg_Defines.h file are of the same Software version */
#if ((DMA_CFG_DEFINES_SW_MAJOR_VERSION != DMA_HW_CFG_DEFINES_SW_MAJOR_VERSION) || \
     (DMA_CFG_DEFINES_SW_MINOR_VERSION != DMA_HW_CFG_DEFINES_SW_MINOR_VERSION) || \
     (DMA_CFG_DEFINES_SW_PATCH_VERSION != DMA_HW_CFG_DEFINES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Cfg_Defines.h and Dma_Hw_Cfg_Defines.h are different"
#endif


/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
/*-----------------------------------------------/
/  DMA                                           /
/-----------------------------------------------*/
#define DMA_DMA_IS_AVAILABLE                       DMA_HW_IS_AVAILABLE

/*-----------------------------------------------/
/  VERSION INFO API                              /
/-----------------------------------------------*/
#define DMA_VERSION_INFO_API_IS_AVAILABLE          STD_OFF

#ifdef __cplusplus
}
#endif

#endif /* DMA_CFG_DEFINES_H */
