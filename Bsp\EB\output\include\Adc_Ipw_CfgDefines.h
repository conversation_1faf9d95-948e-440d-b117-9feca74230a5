/**
 * file    Adc_Ipw_CfgDefines.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_IPW_CFGDEFINES_H
#define ADC_IPW_CFGDEFINES_H

/**
*   @file
*
*   @addtogroup adc_ipw_config Adc Ipw Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_IPW_VENDOR_ID_CFGDEFINES                     (110u)
#define ADC_IPW_AR_RELEASE_MAJOR_VERSION_CFGDEFINES      (4u)
#define ADC_IPW_AR_RELEASE_MINOR_VERSION_CFGDEFINES      (4u)
#define ADC_IPW_AR_RELEASE_REVISION_VERSION_CFGDEFINES   (0u)
#define ADC_IPW_SW_MAJOR_VERSION_CFGDEFINES              (1u)
#define ADC_IPW_SW_MINOR_VERSION_CFGDEFINES              (0u)
#define ADC_IPW_SW_PATCH_VERSION_CFGDEFINES              (0u)

/*==================================================================================================
*                              DEFINES AND MACROS
==================================================================================================*/
#define ADC_IPW_INVALID_LOGICAL_UNIT_ID     (0xFFU)

#define ADC_IPW_INVALID_DMA_CHANNEL_ID      (0xFFU)

#define ADC_IPW_INVALID_TRANSFER_TYPE       (0xFFU)

/**
 * @brief          Max number of ADC Hw units.
 */
#define ADC_IPW_MAX_HW_UNITS                    (2U)

/**
 * @brief   Define the number of maximum hardware unit configured
 */
#define ADC_IPW_MAX_HW_UNITS_CFG                (2U)

/**
* @brief           PDB available/unavailable (STD_ON/STD_OFF).
*/
#define ADC_IPW_PDB_AVAILABLE                   (STD_ON)

/**
* @brief           BCTU available/unavailable (STD_ON/STD_OFF).
*/
#define ADC_IPW_BCTU_AVAILABLE                  (STD_OFF)

/**
* @brief           CTU available/unavailable (STD_ON/STD_OFF).
*/
#define ADC_IPW_CTU_AVAILABLE                   (STD_OFF)

/**
* @brief           ADC hardware is supported CALSTAT register
*/
#define ADC_IPW_CALSTAT_AVAILABLE               (STD_OFF)

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_IPW_CFGDEFINES_H */
