<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Dio" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Dio" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Dio"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantLinkTime">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="DioConfig" type="IDENTIFIABLE">
                <d:lst name="DioPort" type="MAP">
                  <d:ctr name="DioPortA" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="0"/>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PTA0_KEY3" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0"/>
                      </d:ctr>
                      <d:ctr name="PTA8_VB_12V_EN" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                      </d:ctr>
                      <d:ctr name="PTA9_AIR_12V_EN" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                      </d:ctr>
                      <d:ctr name="PTA12_MCU_HALL_4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="12"/>
                      </d:ctr>
                      <d:ctr name="PTA13_MCU_HALL_3" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                      </d:ctr>
                      <d:ctr name="PTA17_DRV8145_DRVOFF_3" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="17"/>
                      </d:ctr>
                      <d:ctr name="PTA1_DRV8145_DRVOFF_5" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                      </d:ctr>
                      <d:ctr name="PTA14_DRV8145_NFAULT_6" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="14"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="DioPortB" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="1"/>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PTB11_KEY1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                      </d:ctr>
                      <d:ctr name="PTB10_KEY2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="10"/>
                      </d:ctr>
                      <d:ctr name="PB12_DRV8145_NSLEEP_4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="12"/>
                      </d:ctr>
                      <d:ctr name="PB8_DRV8145_NSLEEP_5" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                      </d:ctr>
                      <d:ctr name="PTB17_DRV8145_NFAULT_3" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="17"/>
                      </d:ctr>
                      <d:ctr name="PTB9_DRV8145_NFAULT_4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="DioPortC" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="2"/>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PTC6_KEY4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="6"/>
                      </d:ctr>
                      <d:ctr name="PTC13_MCU_HALL_1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                      </d:ctr>
                      <d:ctr name="PTC12_MCU_HALL_2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="12"/>
                      </d:ctr>
                      <d:ctr name="PTC9_MCU_HALL_5" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                      </d:ctr>
                      <d:ctr name="PTC8_MCU_HALL_6" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                      </d:ctr>
                      <d:ctr name="PTC3_DRV8145_NFAULT_1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                      </d:ctr>
                      <d:ctr name="PTC7_DRV8145_NFAULT_5" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                      </d:ctr>
                      <d:ctr name="PTC2_DRV8145_NSLEEP_2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="2"/>
                      </d:ctr>
                      <d:ctr name="PTC10_DRV8145_NSLEEP_6" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="10"/>
                      </d:ctr>
                      <d:ctr name="PTC11_DRV8145_DRVOFF_6" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="11"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="DioPortD" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="3"/>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PTD9_LIN_SLP" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="9"/>
                      </d:ctr>
                      <d:ctr name="PTD0_CAN_STB" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="0"/>
                      </d:ctr>
                      <d:ctr name="PTD1_CAN_ERR" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                      </d:ctr>
                      <d:ctr name="PTD8_DRV8145_NSLEEP_3" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="8"/>
                      </d:ctr>
                      <d:ctr name="PTD4_DRV8145_DRVOFF_4" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="4"/>
                      </d:ctr>
                      <d:ctr name="PTD7_DRV8145_DRVOFF_2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="7"/>
                      </d:ctr>
                      <d:ctr name="PTD6_DRV8145_NFAULT_2" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="6"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                  </d:ctr>
                  <d:ctr name="DioPortE" type="IDENTIFIABLE">
                    <d:var name="DioPortId" type="INTEGER" value="4"/>
                    <d:lst name="DioChannel" type="MAP">
                      <d:ctr name="PTE13_CAN_EN" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="13"/>
                      </d:ctr>
                      <d:ctr name="PTE16_MCU_HOLD" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="16"/>
                      </d:ctr>
                      <d:ctr name="PTE15_Motor_PWR_EN" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="15"/>
                      </d:ctr>
                      <d:ctr name="PTE3_DRV8145_DRVOFF_1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="3"/>
                      </d:ctr>
                      <d:ctr name="PTE14_DRV8145_NSLEEP_1" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="14"/>
                      </d:ctr>
                      <d:ctr name="PTE1_UART0_EN" type="IDENTIFIABLE">
                        <d:var name="DioChannelId" type="INTEGER" value="1"/>
                      </d:ctr>
                    </d:lst>
                    <d:lst name="DioChannelGroup" type="MAP"/>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="DioGeneral" type="IDENTIFIABLE">
                <d:var name="DioDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="GpioHwDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioReversePortBits" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioFlipChannelApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioReadZeroForUndefinedPortPins" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DioMaskedWritePortApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="120">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
