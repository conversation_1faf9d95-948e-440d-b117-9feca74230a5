/**
 * file     Fee_Features.h
 * brief    Specification of Flash EEPROM Emulation
 * author   Huhl <<EMAIL>>
 * date     2024.5.25
 * version  1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved
 */

#ifndef FEE_FEATURES_H
#define FEE_FEATURES_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define FEE_FEATURES_VENDOR_ID                    (110U)
#define FEE_FEATURES_AR_RELEASE_MAJOR_VERSION     (4U)
#define FEE_FEATURES_AR_RELEASE_MINOR_VERSION     (4U)
#define FEE_FEATURES_AR_RELEASE_REVISION_VERSION  (0U)
#define FEE_FEATURES_SW_MAJOR_VERSION             (1U)
#define FEE_FEATURES_SW_MINOR_VERSION             (0U)
#define FEE_FEATURES_SW_PATCH_VERSION             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if Fee_Feature.h header file and Std_Types.h header file are of the same Autosar version */
    #if ((FEE_FEATURES_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
         (FEE_FEATURES_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION) \
        )
        #error "Autosar Version Numbers of Fee_Feature.h and Std_Types.h are different"
    #endif
#endif
/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/


/* This configuration enables the sector retirement feature */
#define FEE_SECTOR_RETIREMENT                     (STD_ON)

#if (FEE_SECTOR_RETIREMENT == STD_ON)
    /* The number of bytes in a Fee Sector header, used to store the sectors information */
    #define FEE_SECTOR_HEADER_INFO_SIZE    (16U)

    /* The number of attempts when erasing each sector in the cluster in swap phase */
    #define FEE_SECTOR_ERASE_RETRIES              (3U)

    /* The number of attempts when erasing each sector in the cluster in swap phase */
    #define FEE_NUMBER_OF_SECTORS_TO_RECOVER      (1U)
#endif

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FEE_FEATURES_H */
