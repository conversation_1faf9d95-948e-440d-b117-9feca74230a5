/**
 * file    Dma_Hw_Cfg_Devices.h
 * brief   Dma Hw Cfg Devices header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/* Prevention from multiple including the same header */
#ifndef DMA_HW_CFG_DEVICES_H
#define DMA_HW_CFG_DEVICES_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Hw_Cfg_DeviceRegistersV2.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define DMA_HW_CFG_DEVICES_VENDOR_ID                       (110U)
#define DMA_HW_CFG_DEVICES_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_HW_CFG_DEVICES_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_HW_CFG_DEVICES_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_HW_CFG_DEVICES_SW_MAJOR_VERSION                (1U)
#define DMA_HW_CFG_DEVICES_SW_MINOR_VERSION                (0U)
#define DMA_HW_CFG_DEVICES_SW_PATCH_VERSION                (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Dma_Hw_Cfg_DeviceRegistersV2.h file are of the same vendor */
#if (DMA_HW_CFG_DEVICES_VENDOR_ID != DMA_HW_CFG_DEVICEREGISTERSV2_VENDOR_ID)
    #error "Dma_Hw_Cfg_Devices.h and Dma_Hw_Cfg_DeviceRegistersV2.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_Cfg_DeviceRegistersV2.h file are of the same Autosar version */
#if ((DMA_HW_CFG_DEVICES_AR_RELEASE_MAJOR_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_DEVICES_AR_RELEASE_MINOR_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_CFG_DEVICES_AR_RELEASE_REVISION_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_Cfg_Devices.h and Dma_Hw_Cfg_DeviceRegistersV2.h are different"
#endif

/* Check if header file and Dma_Hw_Cfg_DeviceRegistersV2.h file are of the same Software version */
#if ((DMA_HW_CFG_DEVICES_SW_MAJOR_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_SW_MAJOR_VERSION) || \
     (DMA_HW_CFG_DEVICES_SW_MINOR_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_SW_MINOR_VERSION) || \
     (DMA_HW_CFG_DEVICES_SW_PATCH_VERSION != DMA_HW_CFG_DEVICEREGISTERSV2_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_Cfg_Devices.h and Dma_Hw_Cfg_DeviceRegistersV2.h are different"
#endif

#ifdef __cplusplus
}
#endif

/** @} */

#endif  /* #ifndef DMA_HW_CFG_DEVICES_H. */

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
