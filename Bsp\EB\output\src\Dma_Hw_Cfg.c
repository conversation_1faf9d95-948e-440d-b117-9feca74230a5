/**
 * file    Dma_Hw_cfg.c
 * brief   dma hw cfg file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
 
/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Hw_Cfg.h"

#ifdef __cplusplus
extern "C"
{
#endif

/* @section [global]
 * 5087 EOF
 * This attribute syntax is a language extension.
 * REASON: Variables and text need to be placed in the specified location
*/
/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define DMA_HW_CFG_VENDOR_ID_C                       (110U)
#define DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION_C        (4U)
#define DMA_HW_CFG_AR_RELEASE_MINOR_VERSION_C        (4U)
#define DMA_HW_CFG_AR_RELEASE_REVISION_VERSION_C     (0U)
#define DMA_HW_CFG_SW_MAJOR_VERSION_C                (1U)
#define DMA_HW_CFG_SW_MINOR_VERSION_C                (0U)
#define DMA_HW_CFG_SW_PATCH_VERSION_C                (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if Dma_Hw_Cfg.c file and Dma_Hw_Cfg.h file are of the same vendor */
#if (DMA_HW_CFG_VENDOR_ID_C != DMA_HW_CFG_VENDOR_ID)
    #error "Dma_Hw_Cfg.c and Dma_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Dma_Hw_Cfg.c file and Dma_Hw_Cfg.h file are of the same Autosar version */
#if ((DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_MINOR_VERSION_C != DMA_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_CFG_AR_RELEASE_REVISION_VERSION_C != DMA_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_Cfg.c and Dma_Hw_Cfg.h are different"
#endif

/* Check if Dma_Hw_Cfg.c file and Dma_Hw_Cfg.h file are of the same Software version */
#if ((DMA_HW_CFG_SW_MAJOR_VERSION_C != DMA_HW_CFG_SW_MAJOR_VERSION) || \
     (DMA_HW_CFG_SW_MINOR_VERSION_C != DMA_HW_CFG_SW_MINOR_VERSION) || \
     (DMA_HW_CFG_SW_PATCH_VERSION_C != DMA_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_Cfg.c and Dma_Hw_Cfg.h are different"
#endif

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

/**
* @page misra_violations MISRA-C:2012 violations
**/






#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

/* DMA Logic Instance Reset Configuration */
const DmaHwLogicInstanceConfig_t stDmaHwxLogicInstanceResetConfig =
{
    {
        /* uint32 logicInstId */ 0U,
        /* uint8 hwInst       */ 0U,
    },
    /*timeout unit*/ 1023,
    /* pDmaCallback AlmCallback */ NULL_PTR,
    /* pDmaCallback SMCallback */ NULL_PTR,
};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h" 



#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

/* DMA Logic Channel Reset Configuration */
static const DmaHwGlobalConfig_t GlobalResetConfig =
{
    /* channel timeout */            0x3ff,
    /* peripheral request source */  DMA_ALWAYS_ENABLED_0,
    {
        /* boolean EnRequest */      (boolean)FALSE,
        /* boolean EnChTrigRequest*/ (boolean)FALSE,
    }, /* Request */
    {
        /* boolean EnCompleteInt */  (boolean)FALSE,
		/* uint8 EnErrorInt */       (uint8)0U,
    }, /* Interrupt */
    {
        /* uint8 Level  */           DMA_HW_LEVEL_PRIO3,

    }, /* Priority */
    /* complete irq callback */      NULL_PTR,
    /* link list irq callback */     NULL_PTR,
    /* cfg err irq callback */       NULL_PTR,
    /* trans err irq callback */     NULL_PTR,
    /* list read err irq callback */ NULL_PTR,
};

static const DmaHwTransferConfig_t TransferResetConfig =
{
	/*uint32 next link list address */  EDMAC_CHANNEL0_LLIADDR,
	/* uint32 source addr */            0,
	/* uint32 Destination addr; */      0,
	/* sint16 2D source addr */         0,
	/* sint16 2D Destination addr */    0,
	/* sint16 3D source addr */         0,
	/* sint16 3D Destination addr */    0,
	/* uint16 A Count */                0,
	/* uint16 B Count */                0,
	/* uint16 C Count */                0,
	/* uint8 ChannelID  */              DMA_HW_CH_0,
	/* DmaChLinkMode_t LinkMode */      DMA_LINK_INVALID,           
	/* boolean SrcInc */                (boolean)FALSE,
	/* boolean DesIncMode */            (boolean)FALSE,
	/* DmaIncMode_t SrcIncMode */       A_SYNC,
	/* DmaIncMode_t DesIncMode */       A_SYNC,
	/* DmaBurstSize_t BurstLength */    DMA_BURST_SIZE0,
	/* DmaChTransDataW_t DataWid */     DMA_TRANS_32BIT,
	/* DmaChannelFlowCtl_t FlowCtrl */  DMA_FC_M2M_D,
	/* DmaTransDim_t       Dim */       ONE_DIM,
	/* boolean LrIntEnable */           (boolean)FALSE,
};

const DmaHwLogicChannelConfig_t stDmaHwxLogicChannelResetConfig =
{
    {
        /* uint32 logicChId  */               0U,
        /* uint8 hwInst  */                   0U,
        /* uint8 hwChId  */                   0U,
        /* pfnDmaHwCallback IntCallback  */    NULL_PTR,
        /* pfnDmaHwCallback ErrIntCallback  */ NULL_PTR,
    }, /* DmaHwLogicChannelId_t */
    &GlobalResetConfig,
    &TransferResetConfig,
    NULL_PTR,
};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"


#ifdef __cplusplus
}
#endif

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
