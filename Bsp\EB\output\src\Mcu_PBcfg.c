/**
 * *************************************************************************
 * @file   Mcu_PBcfg.c
 * @brief Data structures for the Mcu driver.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
*                                          INCLUDE FILES

==================================================================================================*/
#include "Mcu.h"
#include "Mcu_Module_Config.h"

#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
#if (!defined(MCU_MODULE_DEM_NOUSE) || (MCU_MODULE_DEM_NOUSE == STD_OFF))
#include "Dem.h"
#endif
#endif /* Note-(MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF) */

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define MCU_PBCFG_VENDOR_ID_C                      (110U)
#define MCU_PBCFG_AR_RELEASE_MAJOR_VERSION_C       (4U)
#define MCU_PBCFG_AR_RELEASE_MINOR_VERSION_C       (4U)
#define MCU_PBCFG_AR_RELEASE_REVISION_VERSION_C    (0U)
#define MCU_PBCFG_SW_MAJOR_VERSION_C               (1U)
#define MCU_PBCFG_SW_MINOR_VERSION_C               (0U)
#define MCU_PBCFG_SW_PATCH_VERSION_C               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Mcu_PBcfg.c file and Dem.h file are of the same Autosar version */
#if ((MCU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DEM_AR_RELEASE_MAJOR_VERSION) || \
     (MCU_PBCFG_AR_RELEASE_MINOR_VERSION_C != DEM_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Mcu_PBcfg.c and Dem.h are different"
#endif
#endif
#endif /* Note-(MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF) */

/* Check if Mcu_PBcfg.c file and Mcu.h file are of the same vendor */
#if (MCU_PBCFG_VENDOR_ID_C != MCU_VENDOR_ID)
    #error "Mcu_PBcfg.c and Mcu.h have different vendor ids"
#endif

/* Check if Mcu_PBcfg.c file and Mcu.h file are of the same Autosar version */
#if ((MCU_PBCFG_AR_RELEASE_MAJOR_VERSION_C != MCU_AR_RELEASE_MAJOR_VERSION) || \
     (MCU_PBCFG_AR_RELEASE_MINOR_VERSION_C != MCU_AR_RELEASE_MINOR_VERSION) || \
     (MCU_PBCFG_AR_RELEASE_REVISION_VERSION_C != MCU_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Mcu_PBcfg.c and Mcu.h are different"
#endif

/* Check if Mcu_PBcfg.c file and Mcu.h file are of the same Software version */
#if ((MCU_PBCFG_SW_MAJOR_VERSION_C != MCU_SW_MAJOR_VERSION) || \
     (MCU_PBCFG_SW_MINOR_VERSION_C != MCU_SW_MINOR_VERSION) || \
     (MCU_PBCFG_SW_PATCH_VERSION_C != MCU_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Mcu_PBcfg.c and Mcu.h are different"
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL VARIABLES
==================================================================================================*/
#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"











/**
* @brief          Initialization data for the MCU driver.
* @details        A pointer to such a structure is provided to the MCU initialization routines for configuration.
* SWS_Mcu_00165
*/

const Mcu_ConfigType Mcu_Config =
{
#if (MCU_INIT_CLOCK == STD_ON)
    /* Globally enable/disable SCM, loss of clock and loss of lock notification. */
    MCU_CLK_NOTIF_DIS,
#endif

#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
    /* Pointer to DEM error reporting configurations. */
    &Mcu_DemConfig,
#endif
    /* Number of RAM Sections configurations. */
    (Mcu_RamSectionType)3U,

    /* Number of Power Modes configurations. */
    (Mcu_ModeType)2U,

#if (MCU_INIT_CLOCK == STD_ON)
    /* Number of Clock Setting configurations. */
    (Mcu_ClockType)2U,
#endif /* Note- (MCU_INIT_CLOCK == STD_ON) */

#ifndef MCU_MAX_NORAMCONFIGS
    /* Pointer to RAM Section configurations. */
    &Mcu_aRamConfigPB,
#endif

    /* Pointer to Power Mode configurations. */
    &Power_Hw_aModeConfigPB,

#if (MCU_INIT_CLOCK == STD_ON)
    /* Pointer to Clock configurations. */
    &Mcu_aClockConfigPB,
#endif /* Note-(MCU_INIT_CLOCK == STD_ON) */

    /* Pointer to Low Level Mcu driver configuration. */
    &Power_Hw_HwIPsConfigPB,
	
	&Mcu_aRamModuleConfigPB
};






#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"


/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

