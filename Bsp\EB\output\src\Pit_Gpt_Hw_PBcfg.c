/**
 * @file    Pit_Gpt_Hw_PBcfg.c
 * @brief   Pit Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.16
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 20.1, #include directives should only be preceded by preprocessor directives or comments.
 * REASON: Variables and text need to be placed in the specified location
 */



#include "Pit_Gpt_Hw_PBcfg.h"
#include "Gpt_Irq.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define PIT_HW_VENDOR_ID_PBCFG_C                    (110U)
#define PIT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C     (4U)
#define PIT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C     (4U)
#define PIT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C  (0U)
#define PIT_HW_SW_MAJOR_VERSION_PBCFG_C             (1U)
#define PIT_HW_SW_MINOR_VERSION_PBCFG_C             (0U)
#define PIT_HW_SW_PATCH_VERSION_PBCFG_C             (0U)
/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#if (PIT_HW_VENDOR_ID_PBCFG_C != PIT_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Pit_Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_Types.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((PIT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (PIT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (PIT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Pit_Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_Types.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((PIT_HW_SW_MAJOR_VERSION_PBCFG_C != PIT_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (PIT_HW_SW_MINOR_VERSION_PBCFG_C != PIT_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (PIT_HW_SW_PATCH_VERSION_PBCFG_C != PIT_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Pit_Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_Types.h are different"
#endif

#if (PIT_HW_VENDOR_ID_PBCFG_C != GPT_IRQ_VENDOR_ID)
    #error "Pit_Gpt_Hw_PBcfg.c and Gpt_Irq.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((PIT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MAJOR_VERSION) || \
     (PIT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MINOR_VERSION) || \
     (PIT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Pit_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((PIT_HW_SW_MAJOR_VERSION_PBCFG_C != GPT_IRQ_SW_MAJOR_VERSION) || \
     (PIT_HW_SW_MINOR_VERSION_PBCFG_C != GPT_IRQ_SW_MINOR_VERSION) || \
     (PIT_HW_SW_PATCH_VERSION_PBCFG_C != GPT_IRQ_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Pit_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif
/*================================================================================================*/


/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/
#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
const Pit_ChannelConfig_t PIT_0_ChannelConfig_PB[1U] = 
{
    /* @brief GptPitChannels_0 */
    {
        /* @brief Pit Channel Id */
        0U,
        /* @brief Pit callback name */
        &Gpt_ProcessCommonInterrupt,
        /* @brief Pit callback param */
        (uint8)0U,
#if (PIT_GPT_HW_ENABLE_EXT_TRIGGERS == STD_ON)
        /* PIT External/Internal Trigger Configuration */
        (uint32)((uint32)0U<< PIT_C0C_TRGSEL_Pos) | \
        (uint32)((uint32)1U << PIT_C0C_TRGSRC_Pos) | \
        (uint32)((uint32)0U << PIT_C0C_TROT_Pos) | \
        (uint32)((uint32)0U << PIT_C0C_TSOI_Pos) | \
        (uint32)((uint32)0U << PIT_C0C_TSOT_Pos),
#endif
        /* @brief Pit channel work mode */
        PIT_MODE_32BIT_COUNT,
        /* @brief Pit channel filter width */
        PIT_EXTPULSEFILWIDTH_1_CYCLE,
        /* @brief Pit channel mode */
        PIT_GPT_HW_CH_MODE_CONTINUOUS,
        /* @brief Pit External Trigger Select, trgmux or gpio */
        PIT_EXTERNALSOURCE_TRGMUX,
        /* @brief Pit Chain mode */
        (boolean)FALSE 
    }
};

#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

#ifdef __cplusplus
}
#endif /* PIT_GPT_HW_PBCFG_C*/

/* } */
