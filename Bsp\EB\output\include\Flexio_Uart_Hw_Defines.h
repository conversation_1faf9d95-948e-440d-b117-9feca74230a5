/**
 * file    Flexio_Uart_Hw_Defines.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
#ifndef FLEXIO_UART_HW_DEFINES_H
#define FLEXIO_UART_HW_DEFINES_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_UART_HW_DEFINES_VENDOR_ID                     (110U)
#define FLEXIO_UART_HW_DEFINES_AR_RELEASE_MAJOR_VERSION      (4U)
#define FLEXIO_UART_HW_DEFINES_AR_RELEASE_MINOR_VERSION      (4U) 
#define FLEXIO_UART_HW_DEFINES_AR_RELEASE_REVISION_VERSION   (0U) 
#define FLEXIO_UART_HW_DEFINES_SW_MAJOR_VERSION              (1U)
#define FLEXIO_UART_HW_DEFINES_SW_MINOR_VERSION              (0U)
#define FLEXIO_UART_HW_DEFINES_SW_PATCH_VERSION              (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((FLEXIO_UART_HW_DEFINES_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (FLEXIO_UART_HW_DEFINES_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of Flexio_Uart_Hw_Defines.h and Mcal.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/

/* @brief Using FLEXIO */
#define FLEXIO_UART_HW_IS_USING                         (STD_OFF)
/* @brief Development error detection */
#define FLEXIO_UART_HW_DEV_ERROR_DETECT                 (STD_OFF)

/* @brief FLEXIO DMA support */
#define FLEXIO_UART_HW_HAS_DMA_ENABLED                  (STD_ON)

/* @brief Number of available hardware shifter and timer */
#define FLEXIO_UART_HW_NUMBER_OF_SHIFTER_AND_TIMER      (4U)

/* @brief Total number of channels configured for FLEXIO*/
#define FLEXIO_UART_HW_NUMBER_OF_INSTANCES              (1U)

/* @brief Uart Osif source counter. This parameter is used to select between different OsIf counter implementation */
#define FLEXIO_UART_HW_TIMEOUT_TYPE                     (OSIF_COUNTER_DUMMY)

/* @brief Number of loops before returning FLEXIO_STATUS_TIMEOUT.*/
#define FLEXIO_UART_HW_TIMEOUT_VALUE_US                 (1000U)

/* @brief Support for User mode. If this parameter has been configured to TRUE, the Uart driver can be executed from both supervisor and user mode. */
#define FLEXIO_UART_HW_ENABLE_USER_MODE_SUPPORT         (STD_OFF)

/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_UART_HW_DEFINES_H */
