/**
* 
* @file    Port_Cfg.h
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOW<PERSON>VE<PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>H<PERSON> IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 


#ifndef PORT_CFG_H
#define PORT_CFG_H


#include "Port_PBcfg.h"

#include "Port_Hw_Types.h"

#ifdef __cplusplus
extern "C" {
#endif
/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_CFG_VENDOR_ID_H                       (110U)
#define PORT_CFG_AR_RELEASE_MAJOR_VERSION_H        (4U)
#define PORT_CFG_AR_RELEASE_MINOR_VERSION_H        (4U)
#define PORT_CFG_AR_RELEASE_REVISION_VERSION_H     (0U)
#define PORT_CFG_SW_MAJOR_VERSION_H                (1U)
#define PORT_CFG_SW_MINOR_VERSION_H                (0U)
#define PORT_CFG_SW_PATCH_VERSION_H                (0U)


/*=================================================================================================
*                                      FILE VERSION CHECKS
=================================================================================================*/

/* Check if the files Port_Cfg.h and Port_PBcfg.h are of the same version */
#if (PORT_CFG_VENDOR_ID_H != PORT_VENDOR_ID_PBCFG_H)
    #error "Port_Cfg.h and Port_PBcfg.h have different vendor IDs"
#endif
/* Check if the files Port_Cfg.h and Port_PBcfg.h are of the same Autosar version */
#if ((PORT_CFG_AR_RELEASE_MAJOR_VERSION_H != PORT_AR_RELEASE_MAJOR_VERSION_PBCFG_H) ||    \
     (PORT_CFG_AR_RELEASE_MINOR_VERSION_H != PORT_AR_RELEASE_MINOR_VERSION_PBCFG_H) ||    \
     (PORT_CFG_AR_RELEASE_REVISION_VERSION_H != PORT_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Port_Cfg.h and Port_PBcfg.h are different"
#endif
/* Check if the files Port_Cfg.h and Port_PBcfg.h are of the same software version */
#if ((PORT_CFG_SW_MAJOR_VERSION_H != PORT_SW_MAJOR_VERSION_PBCFG_H) ||  \
     (PORT_CFG_SW_MINOR_VERSION_H != PORT_SW_MINOR_VERSION_PBCFG_H) ||  \
     (PORT_CFG_SW_PATCH_VERSION_H != PORT_SW_PATCH_VERSION_PBCFG_H)     \
    )
    #error "Software Version Numbers of Port_Cfg.h and Port_PBcfg.h are different"
#endif

/* Check if the files Port_Cfg.h and Port_Hw_Types.h are of the same version */
#if (PORT_CFG_VENDOR_ID_H != PORT_HW_VENDOR_ID_TYPES_H)
    #error "Port_Cfg.h and Port_Hw_Types.h have different vendor IDs"
#endif
/* Check if the files Port_Cfg.h and Port_Hw_Types.h are of the same Autosar version */
#if ((PORT_CFG_AR_RELEASE_MAJOR_VERSION_H != PORT_HW_AR_RELEASE_MAJOR_VERSION_TYPES_H) ||    \
     (PORT_CFG_AR_RELEASE_MINOR_VERSION_H != PORT_HW_AR_RELEASE_MINOR_VERSION_TYPES_H) ||    \
     (PORT_CFG_AR_RELEASE_REVISION_VERSION_H != PORT_HW_AR_RELEASE_REVISION_VERSION_TYPES_H) \
    )
    #error "AutoSar Version Numbers of Port_Cfg.h and Port_Hw_Types.h are different"
#endif
/* Check if the files Port_Cfg.h and Port_Hw_Types.h are of the same software version */
#if ((PORT_CFG_SW_MAJOR_VERSION_H != PORT_HW_SW_MAJOR_VERSION_TYPES_H) ||  \
     (PORT_CFG_SW_MINOR_VERSION_H != PORT_HW_SW_MINOR_VERSION_TYPES_H) ||  \
     (PORT_CFG_SW_PATCH_VERSION_H != PORT_HW_SW_PATCH_VERSION_TYPES_H)     \
    )
    #error "Software Version Numbers of Port_Cfg.h and Port_Hw_Types.h are different"
#endif
/*=================================================================================================
*                                          CONSTANTS
=================================================================================================*/
/* @implements Port_PinType_typedef */

/* [cover SWSID = SWS_Port_00229]
Name: Port_PinType Type: uint  Range:  0 - <number of -- Shall cover all available port pins. 
The type should be port pins:>  chosen for the specific MCU platform (best performance). 
Description:  Data type for the symbolic name of a port pin. Available via:  Port.h*/
/* [cover SWSID = SWS_Port_00013]
The type Port_PinType shall be used for the symbolic name of a Port Pin.*/
/* [cover SWSID = SWS_Port_00219]
The type Port_PinType shall be uint8, uint16 or uint32 based on the specific MCU platform.*/
typedef uint32 Port_PinType;

/**
* @brief Different port pin modes.
* @details A port pin shall be configurable with a number of port pin modes (type Port_PinModeType).
*        The type Port_PinModeType shall be used with the function call Port_SetPinMode
* @implements Port_PinModeType_typedef
*/
/* [cover SWSID = SWS_Port_00231]
Name: Port_PinModeType Type:  uint  Range:  Implementation -- As several port pin modes shall be configurable 
on one specific  pin, the range shall be determined by the implementation. Description: Different port pin 
modes. Available via:  Port.h*/
/* [cover SWSID = SWS_Port_00124]
A port pin shall be configurable with a number of port pin modes (type Port_PinModeType).*/
/* [cover SWSID = SWS_Port_00221]
The type Port_PinModeType shall be uint8, uint16 or uint32.*/
typedef uint8 Port_PinModeType;

/**
* @brief Possible directions of a port pin.
* @implements Port_PinDirectionType_enumeration
*/
/* [cover SWSID = SWS_Port_00230]
Name: Port_PinDirectionType Type: Enumeration Range: PORT_PIN_IN  --  Sets port pin as input. 
PORT_PIN_OUT -- Sets port pin as output. Description: Possible directions of a port pin. Available via:  Port.h*/
/* [cover SWSID = SWS_Port_00046]
The type Port_PinDirectionType is a type for defining the direction of a Port Pin.*/
typedef enum
{
    PORT_PIN_OUT = 0,                  /**< @brief Sets port pin as output. */
    PORT_PIN_IN = 1,                   /**< @brief Sets port pin as input. */
} Port_PinDirectionType;

/*=================================================================================================
*                                      DEFINES AND MACROS
=================================================================================================*/
#define PORT_CONFIG_EXT \
PORT_CONFIG_PB


/**
* @brief       Ensure better readability of the configuration
* @note
*/
#define SHL_PAD_U16(x)                      ((uint16)(((uint16)1) << (x)))

/** @brief Port Alternate 0 Mode */
#define PORT_ALT0_FUNC_MODE             ((Port_PinModeType)0)
/** @brief Port GPIO Mode */
#define PORT_GPIO_MODE                  ((Port_PinModeType)1)
/** @brief Port Alternate 2 Mode */
#define PORT_ALT2_FUNC_MODE             ((Port_PinModeType)2)
/** @brief Port Alternate 3 Mode */
#define PORT_ALT3_FUNC_MODE             ((Port_PinModeType)3)
/** @brief Port Alternate 4 Mode */
#define PORT_ALT4_FUNC_MODE             ((Port_PinModeType)4)
/** @brief Port Alternate 5 Mode */
#define PORT_ALT5_FUNC_MODE             ((Port_PinModeType)5)
/** @brief Port Alternate 6 Mode */
#define PORT_ALT6_FUNC_MODE             ((Port_PinModeType)6)
/** @brief Port Alternate 7 Mode */
#define PORT_ALT7_FUNC_MODE             ((Port_PinModeType)7)

/* [cover SWSID = SWS_Port_00207]
These symbolic names for the individual port pins (e.g. PORT_A_PIN_0) shall be defined in the
configuration tool.*/

    

#define PORT0_ADC0_SE0_CMP0_IN0            (PORT_ALT0_FUNC_MODE)
#define PORT0_GPIO                         (PORT_GPIO_MODE)
#define PORT0_PWM2_CH1                     (PORT_ALT2_FUNC_MODE)
#define PORT0_I2C0_SCL                     (PORT_ALT3_FUNC_MODE)
#define PORT0_FXIO_D2                      (PORT_ALT4_FUNC_MODE)
#define PORT0_UART0_CTS                    (PORT_ALT6_FUNC_MODE)
#define PORT0_TRGMUX_OUT3                  (PORT_ALT7_FUNC_MODE)

#define PORT1_ADC0_SE1_CMP0_IN1            (PORT_ALT0_FUNC_MODE)
#define PORT1_GPIO                         (PORT_GPIO_MODE)
#define PORT1_PWM1_CH1                     (PORT_ALT2_FUNC_MODE)
#define PORT1_I2C0_SDA                     (PORT_ALT3_FUNC_MODE)
#define PORT1_FXIO_D3                      (PORT_ALT4_FUNC_MODE)
#define PORT1_UART0_RTS                    (PORT_ALT6_FUNC_MODE)
#define PORT1_TRGMUX_OUT0                  (PORT_ALT7_FUNC_MODE)


#define PORT2_ADC1_SE0                     (PORT_ALT0_FUNC_MODE)
#define PORT2_GPIO                         (PORT_GPIO_MODE)
#define PORT2_PWM3_CH0                     (PORT_ALT2_FUNC_MODE)
#define PORT2_I2C0_SDA                     (PORT_ALT3_FUNC_MODE)
#define PORT2_EWM_OUT_N                    (PORT_ALT4_FUNC_MODE)
#define PORT2_FXIO_D4                      (PORT_ALT5_FUNC_MODE)
#define PORT2_UART0_RX                     (PORT_ALT6_FUNC_MODE)
#define PORT2_LIN0_RX                      (PORT_ALT7_FUNC_MODE)


#define PORT3_ADC1_SE1                     (PORT_ALT0_FUNC_MODE)
#define PORT3_GPIO                         (PORT_GPIO_MODE)
#define PORT3_PWM3_CH1                     (PORT_ALT2_FUNC_MODE)
#define PORT3_I2C0_SCL                     (PORT_ALT3_FUNC_MODE)
#define PORT3_EWM_IN                       (PORT_ALT4_FUNC_MODE)
#define PORT3_FXIO_D5                      (PORT_ALT5_FUNC_MODE)
#define PORT3_UART0_TX                     (PORT_ALT6_FUNC_MODE)
#define PORT3_LIN0_TX                      (PORT_ALT7_FUNC_MODE)


#define PORT4_DISABLED                     (PORT_ALT0_FUNC_MODE)
#define PORT4_GPIO                         (PORT_GPIO_MODE)
#define PORT4_UART2_TX                     (PORT_ALT2_FUNC_MODE)
#define PORT4_CMP0_OUT                     (PORT_ALT4_FUNC_MODE)
#define PORT4_EWM_OUT_N                    (PORT_ALT5_FUNC_MODE)
#define PORT4_JTAG_TMS                     (PORT_ALT7_FUNC_MODE)


#define PORT5_DISABLED                     (PORT_ALT0_FUNC_MODE)
#define PORT5_GPIO                         (PORT_GPIO_MODE)
#define PORT5_PWM_EXTCLK1                  (PORT_ALT3_FUNC_MODE)
#define PORT5_RESET_b                      (PORT_ALT7_FUNC_MODE)


#define PORT6_ADC0_SE2                     (PORT_ALT0_FUNC_MODE)
#define PORT6_GPIO                         (PORT_GPIO_MODE)
#define PORT6_PWM0_FLT1                    (PORT_ALT2_FUNC_MODE)
#define PORT6_SPI1_SS_N1                   (PORT_ALT3_FUNC_MODE)
#define PORT6_PWM5_CH5                     (PORT_ALT4_FUNC_MODE)
#define PORT6_LIN3_RX                      (PORT_ALT5_FUNC_MODE)
#define PORT6_UART1_CTS                    (PORT_ALT6_FUNC_MODE)


#define PORT7_ADC0_SE3                     (PORT_ALT0_FUNC_MODE)
#define PORT7_GPIO                         (PORT_GPIO_MODE)
#define PORT7_PWM0_FLT2                    (PORT_ALT2_FUNC_MODE)
#define PORT7_PWM5_CH3                     (PORT_ALT3_FUNC_MODE)
#define PORT7_CLK_RTC_32K                  (PORT_ALT4_FUNC_MODE)
#define PORT7_LIN3_TX                      (PORT_ALT5_FUNC_MODE)
#define PORT7_UART1_RTS                    (PORT_ALT6_FUNC_MODE)


#define PORT8_DISABLED                     (PORT_ALT0_FUNC_MODE)
#define PORT8_GPIO                         (PORT_GPIO_MODE)
#define PORT8_UART2_RX                     (PORT_ALT2_FUNC_MODE)
#define PORT8_SPI2_MOSI                    (PORT_ALT3_FUNC_MODE)
#define PORT8_FXIO_D6                      (PORT_ALT4_FUNC_MODE)
#define PORT8_PWM3_FLT3                    (PORT_ALT5_FUNC_MODE)
#define PORT8_PWM4_FLT1                    (PORT_ALT6_FUNC_MODE)
#define PORT8_LIN2_RX                      (PORT_ALT7_FUNC_MODE)

#define PORT9_DISABLED                     (PORT_ALT0_FUNC_MODE)
#define PORT9_GPIO                         (PORT_GPIO_MODE)
#define PORT9_LIN2_TX                      (PORT_ALT2_FUNC_MODE)
#define PORT9_SPI2_SS_N0                   (PORT_ALT3_FUNC_MODE)
#define PORT9_FXIO_D7                      (PORT_ALT4_FUNC_MODE)
#define PORT9_PWM3_FLT2                    (PORT_ALT5_FUNC_MODE)
#define PORT9_PWM1_FLT3                    (PORT_ALT6_FUNC_MODE)
#define PORT9_PWM4_FLT0                    (PORT_ALT7_FUNC_MODE)


#define PORT10_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT10_GPIO                        (PORT_GPIO_MODE)
#define PORT10_PWM1_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT10_FXIO_D0                     (PORT_ALT4_FUNC_MODE)
#define PORT10_PWM4_FLT3                   (PORT_ALT6_FUNC_MODE)
#define PORT10_JTAG_TDO                    (PORT_ALT7_FUNC_MODE)


#define PORT11_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT11_GPIO                        (PORT_GPIO_MODE)
#define PORT11_PWM1_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT11_FXIO_D1                     (PORT_ALT4_FUNC_MODE)
#define PORT11_CMP0_RRT                    (PORT_ALT5_FUNC_MODE)
#define PORT11_PWM4_FLT2                   (PORT_ALT6_FUNC_MODE)


#define PORT12_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT12_GPIO                        (PORT_GPIO_MODE)
#define PORT12_PWM1_CH6                    (PORT_ALT2_FUNC_MODE)
#define PORT12_CAN1_RX                     (PORT_ALT3_FUNC_MODE)
#define PORT12_I2C1_SDA                    (PORT_ALT4_FUNC_MODE)


#define PORT13_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT13_GPIO                        (PORT_GPIO_MODE)
#define PORT13_PWM1_CH7                    (PORT_ALT2_FUNC_MODE)
#define PORT13_CAN1_TX                     (PORT_ALT3_FUNC_MODE)
#define PORT13_I2C1_SCL                    (PORT_ALT4_FUNC_MODE)
#define PORT13_I2C1_SCL                    (PORT_ALT4_FUNC_MODE)


#define PORT14_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT14_GPIO                        (PORT_GPIO_MODE)
#define PORT14_PWM0_FLT0                   (PORT_ALT2_FUNC_MODE)
#define PORT14_PWM3_FLT1                   (PORT_ALT3_FUNC_MODE)
#define PORT14_EWM_IN                      (PORT_ALT4_FUNC_MODE)
#define PORT14_PWM1_FLT0                   (PORT_ALT6_FUNC_MODE)


#define PORT15_ADC1_SE12                   (PORT_ALT0_FUNC_MODE)
#define PORT15_GPIO                        (PORT_GPIO_MODE)
#define PORT15_PWM1_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT15_SPI0_SS_N3                  (PORT_ALT3_FUNC_MODE)
#define PORT15_SPI2_SS_N3                  (PORT_ALT4_FUNC_MODE)


#define PORT16_ADC1_SE13                   (PORT_ALT0_FUNC_MODE)
#define PORT16_GPIO                        (PORT_GPIO_MODE)
#define PORT16_PWM1_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT16_SPI1_SS_N2                  (PORT_ALT3_FUNC_MODE)


#define PORT17_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT17_GPIO                        (PORT_GPIO_MODE)
#define PORT17_PWM0_CH6                    (PORT_ALT2_FUNC_MODE)
#define PORT17_PWM3_FLT0                   (PORT_ALT3_FUNC_MODE)
#define PORT17_EWM_OUT_N                   (PORT_ALT4_FUNC_MODE)
#define PORT17_PWM5_FLT0                   (PORT_ALT5_FUNC_MODE)


#define PORT25_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT25_GPIO                        (PORT_GPIO_MODE)
#define PORT25_PWM5_CH0                    (PORT_ALT2_FUNC_MODE)

#define PORT26_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT26_GPIO                        (PORT_GPIO_MODE)
#define PORT26_PWM5_CH1                    (PORT_ALT2_FUNC_MODE)
#define PORT26_SPI1_SS_N0                  (PORT_ALT3_FUNC_MODE)
#define PORT26_SPI0_SS_N0                  (PORT_ALT4_FUNC_MODE)

#define PORT27_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT27_GPIO                        (PORT_GPIO_MODE)
#define PORT27_PWM5_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT27_SPI1_MOSI                   (PORT_ALT3_FUNC_MODE)
#define PORT27_UART0_TX                    (PORT_ALT4_FUNC_MODE)
#define PORT27_CAN0_TX                     (PORT_ALT5_FUNC_MODE)
#define PORT27_LIN0_TX                     (PORT_ALT6_FUNC_MODE)
#define PORT28_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT28_GPIO                        (PORT_GPIO_MODE)
#define PORT28_PWM5_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT28_SPI1_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT28_UART0_RX                    (PORT_ALT4_FUNC_MODE)
#define PORT28_CAN0_RX                     (PORT_ALT5_FUNC_MODE)
#define PORT28_LIN0_RX                     (PORT_ALT6_FUNC_MODE)

#define PORT29_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT29_GPIO                        (PORT_GPIO_MODE)
#define PORT29_PWM5_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT29_UART2_TX                    (PORT_ALT4_FUNC_MODE)
#define PORT29_SPI1_MISO                   (PORT_ALT5_FUNC_MODE)
#define PORT29_LIN2_TX                     (PORT_ALT6_FUNC_MODE)


#define PORT30_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT30_GPIO                        (PORT_GPIO_MODE)
#define PORT30_PWM5_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT30_UART2_RX                    (PORT_ALT3_FUNC_MODE)
#define PORT30_SPI0_MOSI                   (PORT_ALT4_FUNC_MODE)
#define PORT30_LIN2_RX                     (PORT_ALT6_FUNC_MODE)

#define PORT31_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT31_GPIO                        (PORT_GPIO_MODE)
#define PORT31_PWM5_CH6                    (PORT_ALT2_FUNC_MODE)
#define PORT31_SPI0_SS_N1                  (PORT_ALT4_FUNC_MODE)

#define PORT32_ADC0_SE4_ADC1_SE14          (PORT_ALT0_FUNC_MODE)
#define PORT32_GPIO                        (PORT_GPIO_MODE)
#define PORT32_UART0_RX                    (PORT_ALT2_FUNC_MODE)
#define PORT32_SPI0_SS_N0                  (PORT_ALT3_FUNC_MODE)
#define PORT32_LPIT_ALT3                   (PORT_ALT4_FUNC_MODE)
#define PORT32_CAN0_RX                     (PORT_ALT5_FUNC_MODE)
#define PORT32_PWM4_CH6                    (PORT_ALT6_FUNC_MODE)
#define PORT32_LIN0_RX                     (PORT_ALT7_FUNC_MODE)

#define PORT33_ADC0_SE5_ADC1_SE15          (PORT_ALT0_FUNC_MODE)
#define PORT33_GPIO                        (PORT_GPIO_MODE)
#define PORT33_UART0_TX                    (PORT_ALT2_FUNC_MODE)
#define PORT33_SPI0_MOSI                   (PORT_ALT3_FUNC_MODE)
#define PORT33_PWM_EXTCLK0                 (PORT_ALT4_FUNC_MODE)
#define PORT33_CAN0_TX                     (PORT_ALT5_FUNC_MODE)
#define PORT33_PWM4_CH5                    (PORT_ALT6_FUNC_MODE)
#define PORT33_LIN0_TX                     (PORT_ALT7_FUNC_MODE)

#define PORT34_ADC0_SE6                    (PORT_ALT0_FUNC_MODE)
#define PORT34_GPIO                        (PORT_GPIO_MODE)
#define PORT34_PWM1_CH0                    (PORT_ALT2_FUNC_MODE)
#define PORT34_SPI0_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT34_SPI0_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT34_TRGMUX_IN3                  (PORT_ALT6_FUNC_MODE)

#define PORT35_ADC0_SE7                    (PORT_ALT0_FUNC_MODE)
#define PORT35_GPIO                        (PORT_GPIO_MODE)
#define PORT35_PWM1_CH1                    (PORT_ALT2_FUNC_MODE)
#define PORT35_SPI0_MISO                   (PORT_ALT3_FUNC_MODE)
#define PORT35_TRGMUX_IN2                  (PORT_ALT6_FUNC_MODE)

#define PORT36_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT36_GPIO                        (PORT_GPIO_MODE)
#define PORT36_PWM0_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT36_SPI0_MOSI                   (PORT_ALT3_FUNC_MODE)
#define PORT36_TRGMUX_IN1                  (PORT_ALT6_FUNC_MODE)

#define PORT37_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT37_GPIO                        (PORT_GPIO_MODE)
#define PORT37_PWM0_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT37_SPI0_SS_N1                  (PORT_ALT3_FUNC_MODE)
#define PORT37_SPI0_SS_N0                  (PORT_ALT4_FUNC_MODE)
#define PORT37_CHIP_CLKOUT                 (PORT_ALT5_FUNC_MODE)
#define PORT37_TRGMUX_IN0                  (PORT_ALT6_FUNC_MODE)

#define PORT38_XTAL                        (PORT_ALT0_FUNC_MODE)
#define PORT38_GPIO                        (PORT_GPIO_MODE)
#define PORT38_I2C0_SDA                    (PORT_ALT2_FUNC_MODE)
#define PORT38_LIN3_RX                     (PORT_ALT3_FUNC_MODE)
#define PORT38_CAN3_RX                     (PORT_ALT5_FUNC_MODE)

#define PORT39_EXTAL                       (PORT_ALT0_FUNC_MODE)
#define PORT39_GPIO                        (PORT_GPIO_MODE)
#define PORT39_I2C0_SCL                    (PORT_ALT2_FUNC_MODE)
#define PORT39_LIN3_TX                     (PORT_ALT3_FUNC_MODE)
#define PORT39_CAN3_TX                     (PORT_ALT5_FUNC_MODE)

#define PORT40_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT40_GPIO                        (PORT_GPIO_MODE)
#define PORT40_PWM3_CH0                    (PORT_ALT2_FUNC_MODE)

#define PORT41_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT41_GPIO                        (PORT_GPIO_MODE)
#define PORT41_PWM3_CH1                    (PORT_ALT2_FUNC_MODE)
#define PORT41_I2C0_SCL                    (PORT_ALT3_FUNC_MODE)

#define PORT42_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT42_GPIO                        (PORT_GPIO_MODE)
#define PORT42_PWM3_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT42_I2C0_SDA                    (PORT_ALT3_FUNC_MODE)

#define PORT43_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT43_GPIO                        (PORT_GPIO_MODE)
#define PORT43_PWM3_CH3                    (PORT_ALT2_FUNC_MODE)

#define PORT44_ADC1_SE7                    (PORT_ALT0_FUNC_MODE)
#define PORT44_GPIO                        (PORT_GPIO_MODE)
#define PORT44_PWM0_CH0                    (PORT_ALT2_FUNC_MODE)
#define PORT44_PWM3_FLT2                   (PORT_ALT3_FUNC_MODE)
#define PORT44_CAN2_RX                     (PORT_ALT4_FUNC_MODE)

#define PORT45_ADC1_SE8_ADC0_SE8           (PORT_ALT0_FUNC_MODE)
#define PORT45_GPIO                        (PORT_GPIO_MODE)
#define PORT45_PWM0_CH1                    (PORT_ALT2_FUNC_MODE)
#define PORT45_PWM3_FLT1                   (PORT_ALT3_FUNC_MODE)
#define PORT45_CAN2_TX                     (PORT_ALT4_FUNC_MODE)

#define PORT46_ADC1_SE9_ADC0_SE9           (PORT_ALT0_FUNC_MODE)
#define PORT46_GPIO                        (PORT_GPIO_MODE)
#define PORT46_PWM0_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT46_SPI1_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT46_CAN3_RX                     (PORT_ALT4_FUNC_MODE)

#define PORT47_ADC1_SE14                   (PORT_ALT0_FUNC_MODE)
#define PORT47_GPIO                        (PORT_GPIO_MODE)
#define PORT47_PWM0_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT47_SPI1_MISO                   (PORT_ALT3_FUNC_MODE)
#define PORT47_CAN3_TX                     (PORT_ALT4_FUNC_MODE)

#define PORT48_ADC1_SE15                   (PORT_ALT0_FUNC_MODE)
#define PORT48_GPIO                        (PORT_GPIO_MODE)
#define PORT48_PWM0_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT48_SPI1_MOSI                   (PORT_ALT3_FUNC_MODE)

#define PORT49_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT49_GPIO                        (PORT_GPIO_MODE)
#define PORT49_PWM0_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT49_SPI1_SS_N3                  (PORT_ALT3_FUNC_MODE)
#define PORT49_PWM5_FLT1                   (PORT_ALT4_FUNC_MODE)

#define PORT50_ADC0_SE16                   (PORT_ALT0_FUNC_MODE)
#define PORT50_GPIO                        (PORT_GPIO_MODE)
#define PORT50_PWM5_CH7                    (PORT_ALT2_FUNC_MODE)
#define PORT50_SPI1_SS_N1                  (PORT_ALT4_FUNC_MODE)

#define PORT52_ADC0_SE17                   (PORT_ALT0_FUNC_MODE)
#define PORT52_GPIO                        (PORT_GPIO_MODE)

#define PORT53_ADC0_SE18                   (PORT_ALT0_FUNC_MODE)
#define PORT53_GPIO                        (PORT_GPIO_MODE)

#define PORT54_ADC0_SE19                   (PORT_ALT0_FUNC_MODE)
#define PORT54_GPIO                        (PORT_GPIO_MODE)
#define PORT54_UART1_TX                    (PORT_ALT5_FUNC_MODE)
#define PORT54_LIN1_TX                     (PORT_ALT6_FUNC_MODE)

#define PORT55_ADC0_SE20                   (PORT_ALT0_FUNC_MODE)
#define PORT55_GPIO                        (PORT_GPIO_MODE)
#define PORT55_UART1_RX                    (PORT_ALT3_FUNC_MODE)
#define PORT55_LIN1_RX                     (PORT_ALT6_FUNC_MODE)

#define PORT57_ADC0_SE21                   (PORT_ALT0_FUNC_MODE)
#define PORT57_GPIO                        (PORT_GPIO_MODE)
#define PORT57_SPI2_SS_N0                  (PORT_ALT5_FUNC_MODE)

#define PORT59_ADC0_SE22                   (PORT_ALT0_FUNC_MODE)
#define PORT59_GPIO                        (PORT_GPIO_MODE)
#define PORT59_SPI2_MOSI                   (PORT_ALT5_FUNC_MODE)

#define PORT60_ADC0_SE23                   (PORT_ALT0_FUNC_MODE)
#define PORT60_GPIO                        (PORT_GPIO_MODE)
#define PORT60_SPI2_MISO                   (PORT_ALT5_FUNC_MODE)

#define PORT61_ADC0_SE24                   (PORT_ALT0_FUNC_MODE)
#define PORT61_GPIO                        (PORT_GPIO_MODE)
#define PORT61_SPI2_SCLK                   (PORT_ALT5_FUNC_MODE)

#define PORT64_ADC0_SE8                    (PORT_ALT0_FUNC_MODE)
#define PORT64_GPIO                        (PORT_GPIO_MODE)
#define PORT64_PWM0_CH0                    (PORT_ALT2_FUNC_MODE)
#define PORT64_SPI2_MISO                   (PORT_ALT3_FUNC_MODE)
#define PORT64_PWM5_FLT3                   (PORT_ALT4_FUNC_MODE)
#define PORT64_PWM1_CH6                    (PORT_ALT6_FUNC_MODE)

#define PORT65_ADC0_SE9                    (PORT_ALT0_FUNC_MODE)
#define PORT65_GPIO                        (PORT_GPIO_MODE)
#define PORT65_PWM0_CH1                    (PORT_ALT2_FUNC_MODE)
#define PORT65_SPI2_MOSI                   (PORT_ALT3_FUNC_MODE)
#define PORT65_PWM5_FLT2                   (PORT_ALT4_FUNC_MODE)
#define PORT65_PWM1_CH7                    (PORT_ALT6_FUNC_MODE)

#define PORT66_ADC0_SE10_CMP0_IN5          (PORT_ALT0_FUNC_MODE)
#define PORT66_GPIO                        (PORT_GPIO_MODE)
#define PORT66_PWM0_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT66_CAN0_RX                     (PORT_ALT3_FUNC_MODE)
#define PORT66_UART0_RX                    (PORT_ALT4_FUNC_MODE)
#define PORT66_LIN0_RX                     (PORT_ALT5_FUNC_MODE)
#define PORT66_ETM_TRACE_CHIP_CLKOUT       (PORT_ALT6_FUNC_MODE)

#define PORT67_ADC0_SE11_CMP0_IN4          (PORT_ALT0_FUNC_MODE)
#define PORT67_GPIO                        (PORT_GPIO_MODE)
#define PORT67_PWM0_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT67_CAN0_TX                     (PORT_ALT3_FUNC_MODE)
#define PORT67_UART0_TX                    (PORT_ALT4_FUNC_MODE)
#define PORT67_LIN0_TX                     (PORT_ALT5_FUNC_MODE)

#define PORT68_CMP0_IN2                    (PORT_ALT0_FUNC_MODE)
#define PORT68_GPIO                        (PORT_GPIO_MODE)
#define PORT68_PWM1_CH0                    (PORT_ALT2_FUNC_MODE)
#define PORT68_RTC_CLK                     (PORT_ALT3_FUNC_MODE)
#define PORT68_EWM_IN                      (PORT_ALT5_FUNC_MODE)
#define PORT68_JTAG_TCLK                   (PORT_ALT7_FUNC_MODE)

#define PORT69_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT69_GPIO                        (PORT_GPIO_MODE)
#define PORT69_PWM2_CH0                    (PORT_ALT2_FUNC_MODE)
#define PORT69_RTC_CLK                     (PORT_ALT3_FUNC_MODE)
#define PORT69_JTAG_TDI                    (PORT_ALT7_FUNC_MODE)

#define PORT70_ADC1_SE4                    (PORT_ALT0_FUNC_MODE)
#define PORT70_GPIO                        (PORT_GPIO_MODE)
#define PORT70_UART1_RX                    (PORT_ALT2_FUNC_MODE)
#define PORT70_CAN1_RX                     (PORT_ALT3_FUNC_MODE)
#define PORT70_PWM3_CH2                    (PORT_ALT4_FUNC_MODE)
#define PORT70_LIN1_RX                     (PORT_ALT5_FUNC_MODE)

#define PORT71_ADC1_SE5                    (PORT_ALT0_FUNC_MODE)
#define PORT71_GPIO                        (PORT_GPIO_MODE)
#define PORT71_UART1_TX                    (PORT_ALT2_FUNC_MODE)
#define PORT71_CAN1_TX                     (PORT_ALT3_FUNC_MODE)
#define PORT71_PWM3_CH3                    (PORT_ALT4_FUNC_MODE)
#define PORT71_LIN1_TX                     (PORT_ALT5_FUNC_MODE)

#define PORT72_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT72_GPIO                        (PORT_GPIO_MODE)
#define PORT72_UART1_RX                    (PORT_ALT2_FUNC_MODE)
#define PORT72_PWM1_FLT0                   (PORT_ALT3_FUNC_MODE)
#define PORT72_PWM5_CH1                    (PORT_ALT4_FUNC_MODE)
#define PORT72_LIN1_RX                     (PORT_ALT5_FUNC_MODE)
#define PORT72_UART0_CTS                   (PORT_ALT6_FUNC_MODE)

#define PORT73_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT73_GPIO                        (PORT_GPIO_MODE)
#define PORT73_UART1_TX                    (PORT_ALT2_FUNC_MODE)
#define PORT73_PWM1_FLT1                   (PORT_ALT3_FUNC_MODE)
#define PORT73_PWM5_CH0                    (PORT_ALT4_FUNC_MODE)
#define PORT73_LIN1_TX                     (PORT_ALT5_FUNC_MODE)
#define PORT73_UART0_RTS                   (PORT_ALT6_FUNC_MODE)

#define PORT74_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT74_GPIO                        (PORT_GPIO_MODE)
#define PORT74_PWM3_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT74_PWM5_FLT3                   (PORT_ALT5_FUNC_MODE)
#define PORT74_TRGMUX_IN11                 (PORT_ALT6_FUNC_MODE)

#define PORT75_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT75_GPIO                        (PORT_GPIO_MODE)
#define PORT75_PWM3_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT75_PWM4_CH2                    (PORT_ALT3_FUNC_MODE)
#define PORT75_PWM5_FLT2                   (PORT_ALT5_FUNC_MODE)
#define PORT75_TRGMUX_IN10                 (PORT_ALT6_FUNC_MODE)

#define PORT76_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT76_GPIO                        (PORT_GPIO_MODE)
#define PORT76_PWM3_CH6                    (PORT_ALT2_FUNC_MODE)
#define PORT76_PWM2_CH6                    (PORT_ALT3_FUNC_MODE)
#define PORT76_UART2_CTS                   (PORT_ALT4_FUNC_MODE)

#define PORT77_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT77_GPIO                        (PORT_GPIO_MODE)
#define PORT77_PWM3_CH7                    (PORT_ALT2_FUNC_MODE)
#define PORT77_PWM2_CH7                    (PORT_ALT3_FUNC_MODE)
#define PORT77_UART2_RTS                   (PORT_ALT4_FUNC_MODE)

#define PORT78_ADC0_SE12                   (PORT_ALT0_FUNC_MODE)
#define PORT78_GPIO                        (PORT_GPIO_MODE)
#define PORT78_PWM1_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT78_SPI2_SS_N0                  (PORT_ALT3_FUNC_MODE)
#define PORT78_TRGMUX_IN9                  (PORT_ALT6_FUNC_MODE)

#define PORT79_ADC0_SE13                   (PORT_ALT0_FUNC_MODE)
#define PORT79_GPIO                        (PORT_GPIO_MODE)
#define PORT79_PWM1_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT79_SPI2_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT79_TRGMUX_IN8                  (PORT_ALT6_FUNC_MODE)

#define PORT80_ADC0_SE14                   (PORT_ALT0_FUNC_MODE)
#define PORT80_GPIO                        (PORT_GPIO_MODE)
#define PORT80_PWM1_FLT2                   (PORT_ALT2_FUNC_MODE)
#define PORT80_CAN2_RX                     (PORT_ALT3_FUNC_MODE)

#define PORT81_ADC0_SE15                   (PORT_ALT0_FUNC_MODE)
#define PORT81_GPIO                        (PORT_GPIO_MODE)
#define PORT81_PWM1_FLT3                   (PORT_ALT2_FUNC_MODE)
#define PORT81_CAN2_TX                     (PORT_ALT3_FUNC_MODE)

#define PORT83_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT83_GPIO                        (PORT_GPIO_MODE)
#define PORT83_SPI2_SS_N1                  (PORT_ALT5_FUNC_MODE)

#define PORT87_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT87_GPIO                        (PORT_GPIO_MODE)
#define PORT87_SPI0_SCLK                   (PORT_ALT2_FUNC_MODE)

#define PORT91_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT91_GPIO                        (PORT_GPIO_MODE)
#define PORT91_PWM4_CH4                    (PORT_ALT2_FUNC_MODE)

#define PORT92_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT92_GPIO                        (PORT_GPIO_MODE)
#define PORT92_PWM4_CH7                    (PORT_ALT2_FUNC_MODE)

#define PORT93_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT93_GPIO                        (PORT_GPIO_MODE)
#define PORT93_PWM5_CH2                    (PORT_ALT2_FUNC_MODE)

#define PORT94_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT94_GPIO                        (PORT_GPIO_MODE)
#define PORT94_PWM5_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT94_FXIO_D0                     (PORT_ALT3_FUNC_MODE)

#define PORT95_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT95_GPIO                        (PORT_GPIO_MODE)
#define PORT95_PWM5_CH6                    (PORT_ALT2_FUNC_MODE)
#define PORT95_FXIO_D1                     (PORT_ALT3_FUNC_MODE)
#define PORT95_I2C1_SDA                    (PORT_ALT4_FUNC_MODE)

#define PORT96_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT96_GPIO                        (PORT_GPIO_MODE)
#define PORT96_PWM0_CH2                    (PORT_ALT2_FUNC_MODE)
#define PORT96_SPI1_SCLK                   (PORT_ALT3_FUNC_MODE)
#define PORT96_PWM2_CH0                    (PORT_ALT4_FUNC_MODE)
#define PORT96_ETM_TRACE_D0                (PORT_ALT5_FUNC_MODE)
#define PORT96_FXIO_D0                     (PORT_ALT6_FUNC_MODE)
#define PORT96_TRGMUX_OUT1                 (PORT_ALT7_FUNC_MODE)

#define PORT97_DISABLED                    (PORT_ALT0_FUNC_MODE)
#define PORT97_GPIO                        (PORT_GPIO_MODE)
#define PORT97_PWM0_CH3                    (PORT_ALT2_FUNC_MODE)
#define PORT97_SPI1_MISO                   (PORT_ALT3_FUNC_MODE)
#define PORT97_PWM2_CH1                    (PORT_ALT4_FUNC_MODE)
#define PORT97_FXIO_D1                     (PORT_ALT6_FUNC_MODE)
#define PORT97_TRGMUX_OUT2                 (PORT_ALT7_FUNC_MODE)

#define PORT98_ADC1_SE2                    (PORT_ALT0_FUNC_MODE)
#define PORT98_GPIO                        (PORT_GPIO_MODE)
#define PORT98_PWM3_CH4                    (PORT_ALT2_FUNC_MODE)
#define PORT98_SPI1_MOSI                   (PORT_ALT3_FUNC_MODE)
#define PORT98_FXIO_D4                     (PORT_ALT4_FUNC_MODE)
#define PORT98_FXIO_D6                     (PORT_ALT5_FUNC_MODE)
#define PORT98_TRGMUX_IN5                  (PORT_ALT6_FUNC_MODE)

#define PORT99_ADC1_SE3                    (PORT_ALT0_FUNC_MODE)
#define PORT99_GPIO                        (PORT_GPIO_MODE)
#define PORT99_PWM3_CH5                    (PORT_ALT2_FUNC_MODE)
#define PORT99_SPI1_SS_N0                  (PORT_ALT3_FUNC_MODE)
#define PORT99_FXIO_D5                     (PORT_ALT4_FUNC_MODE)
#define PORT99_FXIO_D7                     (PORT_ALT5_FUNC_MODE)
#define PORT99_TRGMUX_IN4                  (PORT_ALT6_FUNC_MODE)
#define PORT99_NMI_b                       (PORT_ALT7_FUNC_MODE)

#define PORT100_ADC1_SE6                   (PORT_ALT0_FUNC_MODE)
#define PORT100_GPIO                       (PORT_GPIO_MODE)
#define PORT100_PWM0_FLT3                  (PORT_ALT2_FUNC_MODE)
#define PORT100_PWM3_FLT3                  (PORT_ALT3_FUNC_MODE)

#define PORT101_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT101_GPIO                       (PORT_GPIO_MODE)
#define PORT101_PWM2_CH3                   (PORT_ALT2_FUNC_MODE)
#define PORT101_LPIT_ALT2                  (PORT_ALT3_FUNC_MODE)
#define PORT101_PWM2_FLT1                  (PORT_ALT4_FUNC_MODE)
#define PORT101_TRGMUX_IN7                 (PORT_ALT6_FUNC_MODE)

#define PORT102_CMP0_IN7                   (PORT_ALT0_FUNC_MODE)
#define PORT102_GPIO                       (PORT_GPIO_MODE)
#define PORT102_UART2_RX                   (PORT_ALT2_FUNC_MODE)
#define PORT102_CAN3_RX                    (PORT_ALT3_FUNC_MODE)
#define PORT102_PWM2_FLT2                  (PORT_ALT4_FUNC_MODE)
#define PORT102_LIN2_RX                    (PORT_ALT5_FUNC_MODE)

#define PORT103_CMP0_IN6                   (PORT_ALT0_FUNC_MODE)
#define PORT103_GPIO                       (PORT_GPIO_MODE)
#define PORT103_UART2_TX                   (PORT_ALT2_FUNC_MODE)
#define PORT103_CAN3_TX                    (PORT_ALT3_FUNC_MODE)
#define PORT103_PWM2_FLT3                  (PORT_ALT4_FUNC_MODE)
#define PORT103_LIN2_TX                    (PORT_ALT5_FUNC_MODE)
#define PORT103_ETM_TRACE_D0               (PORT_ALT6_FUNC_MODE)

#define PORT104_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT104_GPIO                       (PORT_GPIO_MODE)
#define PORT104_I2C1_SDA                   (PORT_ALT2_FUNC_MODE)
#define PORT104_PWM2_FLT2                  (PORT_ALT4_FUNC_MODE)
#define PORT104_FXIO_D1                    (PORT_ALT5_FUNC_MODE)
#define PORT104_PWM1_CH4                   (PORT_ALT6_FUNC_MODE)

#define PORT105_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT105_GPIO                       (PORT_GPIO_MODE)
#define PORT105_I2C1_SCL                 (PORT_ALT2_FUNC_MODE)
#define PORT105_FXIO_D0                    (PORT_ALT3_FUNC_MODE)
#define PORT105_PWM2_FLT3                  (PORT_ALT4_FUNC_MODE)
#define PORT105_PWM1_CH5                   (PORT_ALT6_FUNC_MODE)

#define PORT106_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT106_GPIO                       (PORT_GPIO_MODE)
#define PORT106_PWM2_CH0                   (PORT_ALT2_FUNC_MODE)
#define PORT106_LIN3_RX                    (PORT_ALT3_FUNC_MODE)
#define PORT106_ETM_TRACE_D3               (PORT_ALT4_FUNC_MODE)
#define PORT106_CHIP_CLKOUT                (PORT_ALT6_FUNC_MODE)

#define PORT107_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT107_GPIO                       (PORT_GPIO_MODE)
#define PORT107_PWM2_CH1                   (PORT_ALT2_FUNC_MODE)
#define PORT107_LIN3_TX                    (PORT_ALT3_FUNC_MODE)
#define PORT107_ETM_TRACE_D2               (PORT_ALT4_FUNC_MODE)
#define PORT107_UART2_CTS                  (PORT_ALT6_FUNC_MODE)

#define PORT108_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT108_GPIO                       (PORT_GPIO_MODE)
#define PORT108_PWM2_CH2                   (PORT_ALT2_FUNC_MODE)
#define PORT108_ETM_TRACE_D1               (PORT_ALT4_FUNC_MODE)
#define PORT108_UART2_RTS                  (PORT_ALT6_FUNC_MODE)

#define PORT109_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT109_GPIO                       (PORT_GPIO_MODE)
#define PORT109_PWM2_CH4                   (PORT_ALT2_FUNC_MODE)
#define PORT109_UART1_RX                   (PORT_ALT3_FUNC_MODE)
#define PORT109_LIN1_RX                    (PORT_ALT5_FUNC_MODE)
#define PORT109_RTC_CLK                    (PORT_ALT7_FUNC_MODE)

#define PORT110_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT110_GPIO                       (PORT_GPIO_MODE)
#define PORT110_PWM2_CH5                   (PORT_ALT2_FUNC_MODE)
#define PORT110_UART1_TX                   (PORT_ALT3_FUNC_MODE)
#define PORT110_LIN1_TX                    (PORT_ALT5_FUNC_MODE)
#define PORT110_CHIP_CLKOUT                (PORT_ALT7_FUNC_MODE)

#define PORT111_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT111_GPIO                       (PORT_GPIO_MODE)
#define PORT111_PWM0_CH0                   (PORT_ALT2_FUNC_MODE)
#define PORT111_ETM_TRACE_D3               (PORT_ALT3_FUNC_MODE)
#define PORT111_SPI0_SCLK                  (PORT_ALT4_FUNC_MODE)

#define PORT112_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT112_GPIO                       (PORT_GPIO_MODE)
#define PORT112_PWM0_CH1                   (PORT_ALT2_FUNC_MODE)
#define PORT112_ETM_TRACE_D2               (PORT_ALT3_FUNC_MODE)
#define PORT112_SPI0_MISO                  (PORT_ALT4_FUNC_MODE)
#define PORT112_CMP0_RRT                   (PORT_ALT5_FUNC_MODE)
#define PORT112_ETM_TRACE_CHIP_CLKOUT      (PORT_ALT6_FUNC_MODE)

#define PORT113_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT113_GPIO                       (PORT_GPIO_MODE)
#define PORT113_PWM0_FLT2                  (PORT_ALT2_FUNC_MODE)
#define PORT113_UART2_RX                   (PORT_ALT3_FUNC_MODE)
#define PORT113_PWM5_FLT1                  (PORT_ALT4_FUNC_MODE)
#define PORT113_LIN2_RX                    (PORT_ALT5_FUNC_MODE)

#define PORT114_ADC1_SE16                  (PORT_ALT0_FUNC_MODE)
#define PORT114_GPIO                       (PORT_GPIO_MODE)
#define PORT114_PWM5_CH7                   (PORT_ALT2_FUNC_MODE)
#define PORT114_FXIO_D2                    (PORT_ALT3_FUNC_MODE)

#define PORT115_ADC1_SE17                  (PORT_ALT0_FUNC_MODE)
#define PORT115_GPIO                       (PORT_GPIO_MODE)
#define PORT115_FXIO_D3                    (PORT_ALT3_FUNC_MODE)
#define PORT115_I2C1_SCL                   (PORT_ALT4_FUNC_MODE)

#define PORT118_ADC1_SE18                  (PORT_ALT0_FUNC_MODE)
#define PORT118_GPIO                       (PORT_GPIO_MODE)

#define PORT119_ADC1_SE19                  (PORT_ALT0_FUNC_MODE)
#define PORT119_GPIO                       (PORT_GPIO_MODE)

#define PORT120_ADC1_SE20                  (PORT_ALT0_FUNC_MODE)
#define PORT120_GPIO                       (PORT_GPIO_MODE)

#define PORT123_ADC1_SE21                  (PORT_ALT0_FUNC_MODE)
#define PORT123_GPIO                       (PORT_GPIO_MODE)

#define PORT124_ADC1_SE22                  (PORT_ALT0_FUNC_MODE)
#define PORT124_GPIO                       (PORT_GPIO_MODE)

#define PORT125_ADC1_SE23                  (PORT_ALT0_FUNC_MODE)
#define PORT125_GPIO                       (PORT_GPIO_MODE)
#define PORT125_LIN3_TX                    (PORT_ALT2_FUNC_MODE)

#define PORT126_ADC1_SE24                  (PORT_ALT0_FUNC_MODE)
#define PORT126_GPIO                       (PORT_GPIO_MODE)
#define PORT126_LIN3_RX                    (PORT_ALT2_FUNC_MODE)

#define PORT128_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT128_GPIO                       (PORT_GPIO_MODE)
#define PORT128_SPI0_SCLK                  (PORT_ALT2_FUNC_MODE)
#define PORT128_PWM_EXTCLK2                (PORT_ALT3_FUNC_MODE)
#define PORT128_I2C1_SDA                   (PORT_ALT4_FUNC_MODE)
#define PORT128_SPI1_MOSI                  (PORT_ALT5_FUNC_MODE)
#define PORT128_PWM1_FLT2                  (PORT_ALT6_FUNC_MODE)

#define PORT129_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT129_GPIO                       (PORT_GPIO_MODE)
#define PORT129_SPI0_MISO                  (PORT_ALT2_FUNC_MODE)
#define PORT129_I2C1_SCL                   (PORT_ALT4_FUNC_MODE)
#define PORT129_SPI1_SS_N0                 (PORT_ALT5_FUNC_MODE)
#define PORT129_PWM1_FLT1                  (PORT_ALT6_FUNC_MODE)

#define PORT130_ADC1_SE10                  (PORT_ALT0_FUNC_MODE)
#define PORT130_GPIO                       (PORT_GPIO_MODE)
#define PORT130_SPI0_MOSI                  (PORT_ALT2_FUNC_MODE)
#define PORT130_LPIT_ALT0                  (PORT_ALT3_FUNC_MODE)
#define PORT130_PWM3_CH6                   (PORT_ALT4_FUNC_MODE)
#define PORT130_UART1_CTS                  (PORT_ALT6_FUNC_MODE)

#define PORT131_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT131_GPIO                       (PORT_GPIO_MODE)
#define PORT131_PWM0_FLT0                  (PORT_ALT2_FUNC_MODE)
#define PORT131_UART2_RTS                  (PORT_ALT3_FUNC_MODE)
#define PORT131_PWM2_FLT0                  (PORT_ALT4_FUNC_MODE)
#define PORT131_TRGMUX_IN6                 (PORT_ALT6_FUNC_MODE)
#define PORT131_CMP0_OUT                   (PORT_ALT7_FUNC_MODE)

#define PORT132_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT132_GPIO                       (PORT_GPIO_MODE)
#define PORT132_ETM_TRACE_D1               (PORT_ALT2_FUNC_MODE)
#define PORT132_PWM2_CH2                   (PORT_ALT4_FUNC_MODE)
#define PORT132_CAN0_RX                    (PORT_ALT5_FUNC_MODE)
#define PORT132_FXIO_D6                    (PORT_ALT6_FUNC_MODE)
#define PORT132_EWM_OUT_N                  (PORT_ALT7_FUNC_MODE)

#define PORT133_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT133_GPIO                       (PORT_GPIO_MODE)
#define PORT133_PWM_EXTCLK3                (PORT_ALT2_FUNC_MODE)
#define PORT133_PWM2_CH3                   (PORT_ALT4_FUNC_MODE)
#define PORT133_CAN0_TX                    (PORT_ALT5_FUNC_MODE)
#define PORT133_FXIO_D7                    (PORT_ALT6_FUNC_MODE)
#define PORT133_EWM_IN                     (PORT_ALT7_FUNC_MODE)

#define PORT134_ADC1_SE11                  (PORT_ALT0_FUNC_MODE)
#define PORT134_GPIO                       (PORT_GPIO_MODE)
#define PORT134_SPI0_SS_N2                 (PORT_ALT2_FUNC_MODE)
#define PORT134_PWM3_CH7                   (PORT_ALT4_FUNC_MODE)
#define PORT134_UART1_RTS                  (PORT_ALT6_FUNC_MODE)

#define PORT136_CMP0_IN3                   (PORT_ALT0_FUNC_MODE)
#define PORT136_GPIO                       (PORT_GPIO_MODE)
#define PORT136_PWM0_CH6                   (PORT_ALT2_FUNC_MODE)

#define PORT137_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT137_GPIO                       (PORT_GPIO_MODE)
#define PORT137_PWM0_CH7                   (PORT_ALT2_FUNC_MODE)
#define PORT137_UART2_CTS                  (PORT_ALT3_FUNC_MODE)

#define PORT138_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT138_GPIO                       (PORT_GPIO_MODE)
#define PORT138_CHIP_CLKOUT                (PORT_ALT2_FUNC_MODE)
#define PORT138_SPI2_SS_N1                 (PORT_ALT3_FUNC_MODE)
#define PORT138_PWM2_CH4                   (PORT_ALT4_FUNC_MODE)
#define PORT138_PWM4_FLT3                  (PORT_ALT5_FUNC_MODE)
#define PORT138_FXIO_D4                    (PORT_ALT6_FUNC_MODE)
#define PORT138_TRGMUX_OUT4                (PORT_ALT7_FUNC_MODE)

#define PORT139_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT139_GPIO                       (PORT_GPIO_MODE)
#define PORT139_SPI2_SS_N0                 (PORT_ALT2_FUNC_MODE)
#define PORT139_LPIT_ALT1                  (PORT_ALT3_FUNC_MODE)
#define PORT139_PWM2_CH5                   (PORT_ALT4_FUNC_MODE)
#define PORT139_PWM4_FLT2                  (PORT_ALT5_FUNC_MODE)
#define PORT139_FXIO_D5                    (PORT_ALT6_FUNC_MODE)
#define PORT139_TRGMUX_OUT5                (PORT_ALT7_FUNC_MODE)

#define PORT140_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT140_GPIO                       (PORT_GPIO_MODE)
#define PORT140_PWM0_FLT3                  (PORT_ALT2_FUNC_MODE)
#define PORT140_UART2_TX                   (PORT_ALT3_FUNC_MODE)
#define PORT140_PWM5_FLT0                  (PORT_ALT4_FUNC_MODE)
#define PORT140_LIN2_TX                    (PORT_ALT5_FUNC_MODE)

#define PORT141_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT141_GPIO                       (PORT_GPIO_MODE)
#define PORT141_PWM4_CH5                   (PORT_ALT2_FUNC_MODE)
#define PORT141_SPI2_SS_N2                 (PORT_ALT3_FUNC_MODE)
#define PORT141_PWM2_FLT0                  (PORT_ALT4_FUNC_MODE)

#define PORT142_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT142_GPIO                       (PORT_GPIO_MODE)
#define PORT142_PWM0_FLT1                  (PORT_ALT2_FUNC_MODE)
#define PORT142_PWM2_FLT1                  (PORT_ALT4_FUNC_MODE)

#define PORT143_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT143_GPIO                       (PORT_GPIO_MODE)
#define PORT143_UART1_CTS                  (PORT_ALT2_FUNC_MODE)
#define PORT143_SPI2_SCLK                  (PORT_ALT3_FUNC_MODE)
#define PORT143_PWM2_CH6                   (PORT_ALT4_FUNC_MODE)
#define PORT143_PWM4_FLT1                  (PORT_ALT5_FUNC_MODE)
#define PORT143_FXIO_D2                    (PORT_ALT6_FUNC_MODE)
#define PORT143_TRGMUX_OUT6                (PORT_ALT7_FUNC_MODE)

#define PORT144_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT144_GPIO                       (PORT_GPIO_MODE)
#define PORT144_UART1_RTS                  (PORT_ALT2_FUNC_MODE)
#define PORT144_SPI2_MISO                  (PORT_ALT3_FUNC_MODE)
#define PORT144_PWM2_CH7                   (PORT_ALT4_FUNC_MODE)
#define PORT144_PWM4_FLT0                  (PORT_ALT5_FUNC_MODE)
#define PORT144_FXIO_D3                    (PORT_ALT6_FUNC_MODE)
#define PORT144_TRGMUX_OUT7                (PORT_ALT7_FUNC_MODE)

#define PORT147_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT147_GPIO                       (PORT_GPIO_MODE)

#define PORT148_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT148_GPIO                       (PORT_GPIO_MODE)
#define PORT148_PWM4_CH0                   (PORT_ALT2_FUNC_MODE)

#define PORT149_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT149_GPIO                       (PORT_GPIO_MODE)
#define PORT149_PWM4_CH1                   (PORT_ALT2_FUNC_MODE)
#define PORT149_CAN3_TX                    (PORT_ALT3_FUNC_MODE)

#define PORT150_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT150_GPIO                       (PORT_GPIO_MODE)
#define PORT150_PWM4_CH2                   (PORT_ALT2_FUNC_MODE)
#define PORT150_CAN3_RX                    (PORT_ALT3_FUNC_MODE)

#define PORT151_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT151_GPIO                       (PORT_GPIO_MODE)
#define PORT151_PWM4_CH3                   (PORT_ALT2_FUNC_MODE)

#define PORT152_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT152_GPIO                       (PORT_GPIO_MODE)
#define PORT152_PWM4_CH4                   (PORT_ALT2_FUNC_MODE)
#define PORT152_CAN2_TX                    (PORT_ALT3_FUNC_MODE)

#define PORT153_DISABLED                   (PORT_ALT0_FUNC_MODE)
#define PORT153_GPIO                       (PORT_GPIO_MODE)
#define PORT153_PWM4_CH5                   (PORT_ALT2_FUNC_MODE)
#define PORT153_CAN2_RX                    (PORT_ALT3_FUNC_MODE)

/**
* @brief Enable/Disable Development Error Detection
*
* @implements   PORT_DEV_ERROR_DETECT_define
*/
#define PORT_DEV_ERROR_DETECT           (STD_ON)

/**
* @brief Use/remove Port_SetPinDirection function from the compiled driver
*
* @implements   PORT_SET_PIN_DIRECTION_API_define
*/
#define PORT_SET_PIN_DIRECTION_API      (STD_ON)


/**
* @brief Enable/Disable multicore function from the driver
*/

/**
* @brief Use/remove Port_SetPinMode function from the compiled driver
*
* @implements   PORT_SET_PIN_MODE_API_define
*/
#define PORT_SET_PIN_MODE_API           (STD_ON)

/**
* @brief Use/remove Port_SetAsUnusedPin/Port_SetAsUsedPin function from the compiled driver.
*
*/
#define PORT_SET_AS_UNUSED_PIN_API           (STD_OFF)

/**
* @brief Use/remove Port_ResetPinMode function from the compiled driver.
*
*/
#define PORT_RESET_PIN_MODE_API           (STD_OFF)

/**
* @brief Enable/Disable Port_SetPinMode function updating the output level of the pins configured at runtime as GPIO
*/
#define PORT_SETPINMODE_DOES_NOT_TOUCH_GPIO_LEVEL           (STD_OFF)

/**
* @brief Use/remove Port_GetVersionInfo function from the compiled driver
*
* @implements   PORT_VERSION_INFO_API_define
*/
#define PORT_VERSION_INFO_API           (STD_ON)

/**
* @brief The number of configured partition on the platform
*/

/**
* @brief Port code size optimization macro define.
*
*/
#define PORT_CODE_SIZE_OPTIMIZATION           (STD_OFF)

/**
* @brief Port low power macro switch.
*
*/
#define PORT_LOW_POWER_PIN_MODE_API                  (STD_OFF)



/**
* @brief Port Pin symbolic names
* @details Get All Symbolic Names from configuration tool
*
*/
/* [cover SWSID = SWS_Port_00006]
The user of the PORT Driver module shall configure the symbolic names of the port pins of the MCU.*/
/* [cover SWSID = SWS_Port_00208]
The  PORT  Driver  module’s  implementer  shall  publish  the symbolic names through the file Port.h*/

#define PortConf_PortPin_PTE13_CAN_EN  0
#define PortConf_PortPin_PTD0_CAN_STB  1
#define PortConf_PortPin_PTE5_CAN_TX  2
#define PortConf_PortPin_PTE4_CAN_RX  3
#define PortConf_PortPin_PTD1_CAN_ERR  4
#define PortConf_PortPin_PTD9_LIN_SLP  5
#define PortConf_PortPin_PTD14_LIN1_TX  6
#define PortConf_PortPin_PTD13_LIN1_RX  7
#define PortConf_PortPin_PTE12_LIN2_TX  8
#define PortConf_PortPin_PTD17_LIN2_RX  9
#define PortConf_PortPin_PTB0_USART0_RX  10
#define PortConf_PortPin_PTB1_USART0_TX  11
#define PortConf_PortPin_PTE1_USART0_EN  77
#define PortConf_PortPin_PTE11_HEAT_PWM  12
#define PortConf_PortPin_PTE10_FAN_PWM  13
#define PortConf_PortPin_PTA3_WAIST_PWM1  14
#define PortConf_PortPin_PTA2_WAIST_PWM2  15
#define PortConf_PortPin_PTD16_IN1_1  16
#define PortConf_PortPin_PTD15_IN1_2  17
#define PortConf_PortPin_PTE9_IN2_1  18
#define PortConf_PortPin_PTE8_IN2_2  19
#define PortConf_PortPin_PTB5_IN3_1  20
#define PortConf_PortPin_PTB4_IN3_2  21
#define PortConf_PortPin_PTD5_IN4_1  22
#define PortConf_PortPin_PTD12_IN4_2  23
#define PortConf_PortPin_PTD11_IN5_1  24
#define PortConf_PortPin_PTD10_IN5_2  25
#define PortConf_PortPin_PTD3_IN6_1  26
#define PortConf_PortPin_PTD2_IN6_2  27
#define PortConf_PortPin_PTC1_ADC_KL30_DET  28
#define PortConf_PortPin_PTE2_KEY4_ADC  29
#define PortConf_PortPin_PTE6_KEY3_ADC  30
#define PortConf_PortPin_PTA15_KEY2_ADC  31
#define PortConf_PortPin_PTA16_KEY1_ADC  32
#define PortConf_PortPin_PTC0_NTC_AD1  33
#define PortConf_PortPin_PTC17_NTC_AD2  34
#define PortConf_PortPin_PTC16_NTC_AD3  35
#define PortConf_PortPin_PTC15_NTC_AD4  36
#define PortConf_PortPin_PTC14_NTC_AD5  37
#define PortConf_PortPin_PTB3_NTC_AD6  38
#define PortConf_PortPin_PTA7_IPROPI_1  39
#define PortConf_PortPin_PTA6_IPROPI_2  40
#define PortConf_PortPin_PTB16_IPROPI_3  41
#define PortConf_PortPin_PTB15_IPROPI_4  42
#define PortConf_PortPin_PTB14_IPROPI_5  43
#define PortConf_PortPin_PTB13_IPROPI_6  44
#define PortConf_PortPin_PTB2_NTC_DET  79
#define PortConf_PortPin_PTC3_NFAULT_1  45
#define PortConf_PortPin_PTD6_NFAULT_2  46
#define PortConf_PortPin_PTB17_NFAULT_3  47
#define PortConf_PortPin_PTB9_NFAULT_4  48
#define PortConf_PortPin_PTC7_NFAULT_5  49
#define PortConf_PortPin_PTA14_NFAULT_6  50
#define PortConf_PortPin_PTE14_NSLEEP_1  51
#define PortConf_PortPin_PTC2_NSLEEP_2  52
#define PortConf_PortPin_PTD8_NSLEEP_3  53
#define PortConf_PortPin_PTB12_NSLEEP_4  54
#define PortConf_PortPin_PTB8_NSLEEP_5  55
#define PortConf_PortPin_PTC10_NSLEEP_6  56
#define PortConf_PortPin_PTE3_DRVOFF_1  57
#define PortConf_PortPin_PTD7_DRVOFF_2  58
#define PortConf_PortPin_PTA17_DRVOFF_3  59
#define PortConf_PortPin_PTD4_DRVOFF_4  60
#define PortConf_PortPin_PTA1_DRVOFF_5  61
#define PortConf_PortPin_PTC11_DRVOFF_6  62
#define PortConf_PortPin_PTC13_MCU_HALL_1  63
#define PortConf_PortPin_PTC12_MCU_HALL_2  64
#define PortConf_PortPin_PTA13_MCU_HALL_3  65
#define PortConf_PortPin_PTA12_MCU_HALL_4  66
#define PortConf_PortPin_PTC9_MCU_HALL_5  67
#define PortConf_PortPin_PTC8_MCU_HALL_6  68
#define PortConf_PortPin_PTB11_KEY1  69
#define PortConf_PortPin_PTB10_KEY2  70
#define PortConf_PortPin_PTA0_KEY3  71
#define PortConf_PortPin_PTC6_KEY4  72
#define PortConf_PortPin_PTA8_VB_12V_EN  73
#define PortConf_PortPin_PTA9_AIR_12V_EN  74
#define PortConf_PortPin_PTE16_MCU_HOLD  75
#define PortConf_PortPin_PTE15_Motor_PWR_EN  80
#define PortConf_PortPin_PTE0  76
#define PortConf_PortPin_PTA11  78


/**
* @brief Number of available pad modes options
* @details Platform constant
*/
#define PAD_MODE_OPTIONS_U8         ((uint8)8)
/**
* @brief Number of pad 16 blocks
* @details Platform constant
*/
#define PAD_16BLOCK_NO_U8           ((uint8)9)
/**
 * @brief The last supported pin number
 */
#define PORT_MAX_PIN_PACKAGE_U16    ((uint16)153)

/**
* @brief The maximum number of configured pins
*/
#define PORT_MAX_CONFIGURED_PADS_U16                        ((uint16)81)

/**
 * @brief Number of UnUsed pin array
*/
#define PORT_MAX_UNUSED_PADS_U16   (41U)

/**
* @brief Port driver Pre-Compile configuration switch
*/
#define PORT_PRECOMPILE_SUPPORT     (STD_OFF)

/*=================================================================================================
*                                             ENUMS
=================================================================================================*/


/*=================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
=================================================================================================*/
typedef struct
{
    uint32   u32IOSEL;                          /**< Content of IOSEL Register */
    uint32   u32IOCFG;                        /**< Content of IOCFG Register */
    Port_PinDirectionType ePadDir;            /**< @brief Pad Data Direction */
    uint8    u8PDO;                           /**< Pad Data Output */
} PorUnUsedPinConfig_t;

/**
* @brief   Single pin configuration.
* @details This structure contains all configuration parameters of a single pin
*          identified by @p SIUPin.
*/
typedef struct
{
    uint16 Pin;                               /**< @brief Index of the pin's Mscr */
    uint32 u32IOSEL;                            /**< @brief Pad Control Register */
	uint32 u32IOCFG;                          /**< @brief GPIO Config Register */
	uint32 u32DLP;                            /**< @brief Low Power Register */
    uint8  u8PDO;                              /**< @brief Pad Data Output */
    Port_PinDirectionType ePadDir;            /**< @brief Pad Data Direction */
    uint8  bGPIO;                            /**< @brief GPIO initial mode*/
    uint8  bDC;                              /**< @brief Direction changebility*/
    uint8  bMC;                              /**< @brief Mode changebility*/
} PortPinConfig_t;

/**
* @brief   Structure needed by @p Port_Init().
* @details The structure @p Port_ConfigType is a type for the external data
*          structure containing the initialization data for the PORT Driver.
* @note    The user must use the symbolic names defined in the configuration
*          tool.
*
* @implements Port_ConfigType_struct
*/
/* [cover SWSID = SWS_Port_00228]
Name: Port_ConfigType Type: Structure Range: Hardware 
The contents of the initialization data structure are specific to Dependent the microcontroller. 
Structure Description: Type of the external data structure containing the initialization data for this module. 
Available via:  Port.h*/

/* [cover SWSID = SWS_Port_00073]
The type Port_ConfigType is a type for the external data structure containing the initialization data for the 
PORT Driver.*/

/* [cover SWSID = SWS_Port_00072]

A list of possible port configurations for the structure Port_ConfigType is given below: Pin mode 
(e.g. DIO, ADC, SPI …) – this port pin configuration is mandatory unless the port pin is configured for DIO. 
·Pin direction (input, output) – this port pin configuration is mandatory when the port pin is to be used for DIO. 
·Pin level init value (see SWS_Port_00055) – this port pin configuration is mandatory when the port pin is used for DIO. 
·Pin direction changeable during runtime (STD_ON/STD_OFF) – this port pin configuration is MCU dependent.
·Pin mode changeable during runtime (STD_ON/STD_OFF) – configuration is MCU dependent.        
Optional parameters (if supported by hardware) 
·Slew rate control. 
·Activation of internal pull-ups. 
·Microcontroller specific port pin properties.*/

typedef struct
{
    uint16 u16NumPins;                                                       /**< @brief Number of used pads (to be configured) */
    uint16 u16NumUnusedPins;                                                 /**< @brief Number of unused pads */
    const uint16 * pUnusedPads;                                              /**< @brief Unused pad id's array */
    const PorUnUsedPinConfig_t * pUnusedPadConfig;                       /**< @brief Unused pad configuration */
    const PortPinConfig_t * pUsedPadConfig;                               /**< @brief Used pads data configuration */
    uint8  u8NumDigitalFilterPorts;                                          /**< @brief Number of configured digital filter ports */
    const PortDigitalFilterConfig_t * pDigitalFilterConfig;         /**< @brief Digital filter ports configuration */
    const uint32 *pau32Port_PinToPartitionMap;                               /**< @brief Pointer to pin partition mapping */
    const uint8 *pau8Port_PartitionList;                                     /**< @brief Pointer to used partitions */
    const PortPinSettingsConfig_t *IpConfigPtr;                            /**< @brief Hw configuration */
} Port_ConfigType;

/*=================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
=================================================================================================*/

#define PORT_START_SEC_CONST_16
#include "Port_MemMap.h"

#if (STD_ON == PORT_SET_PIN_MODE_API)
/**
* @brief External declaration of the Port pin description array
*/
extern const uint16 Port_au16PinDescription[8][9];

#endif

#define PORT_STOP_SEC_CONST_16
#include "Port_MemMap.h"

/*=================================================================================================
*                                    FUNCTION PROTOTYPES
=================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PORT_CFG_H */

/* End of File */

