#ifndef MOTION_H
#define MOTION_H

typedef enum {
    MOTION_IDLE, // no motion for a long time
    MOTION_ACTIVE, // the motor is active
    MOTION_STOP, // the motor is stopped, but needs to be debounced for 100ms.
    MOTION_STOP_CONFIRMED, // the motor is stopped, but needs to be debounced for 100ms.
} tMotorMotion;

void motion_check(void);
void motion_start(void);
bool motion_is_free(void);

#endif // MOTION_H