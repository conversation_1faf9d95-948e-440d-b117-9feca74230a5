/**
 * @file     Lin_Cfg.c
 * @brief    the configure files for <PERSON> .
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-04
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/4    1.0.0     zhangb    Initilization
 * 
 * section [global]
 * Violates MISRA C 2012 Advisory Rule 5.4, The macro definition name cannot be changed
 * reason: Macro definitions have similar names, version number definitions have the same prefix, but different keywords.
 * 
 * section [global]
 * Violates MISRA C 2012 Dir-1.1-C99 [L] Number of macro definitions exceeds 4095 - program does not conform strictly to ISO C99.
 * Reason: include this file is needed.
 **/


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/

#include "Std_Types.h"
#include "Lin_Cfg.h"

#if (LIN_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
#include "Dem.h"
#endif

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LIN_CFG_VENDOR_ID_C                    (110u)
#define LIN_CFG_AR_RELEASE_MAJOR_VERSION_C     (4u)
#define LIN_CFG_AR_RELEASE_MINOR_VERSION_C     (4u)
#define LIN_CFG_AR_RELEASE_REVISION_VERSION_C  (0u)
#define LIN_CFG_SW_MAJOR_VERSION_C             (1u)
#define LIN_CFG_SW_MINOR_VERSION_C             (0u)
#define LIN_CFG_SW_PATCH_VERSION_C             (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#if (LIN_CFG_VENDOR_ID_C != LIN_CFG_VENDOR_ID)
    #error "Lin_Cfg.c and Lin_Cfg.h have different vendor ids"
#endif
/* Check if current file and Lin_Cfg header file are of the same Autosar version */
#if ((LIN_CFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_CFG_AR_RELEASE_MINOR_VERSION_C    != LIN_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_CFG_AR_RELEASE_REVISION_VERSION_C != LIN_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Lin_Cfg.c and Lin_Cfg.h are different"
#endif
/* Check if current file and Lin_Cfg header file are of the same Software version */
#if ((LIN_CFG_SW_MAJOR_VERSION_C != LIN_CFG_SW_MAJOR_VERSION) || \
     (LIN_CFG_SW_MINOR_VERSION_C != LIN_CFG_SW_MINOR_VERSION) || \
     (LIN_CFG_SW_PATCH_VERSION_C != LIN_CFG_SW_PATCH_VERSION) )
    #error "Software Version Numbers of Lin_Cfg.c and Lin_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((LIN_CFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (LIN_CFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Lin_Cfg.c and Std_Types.h are different"
    #endif

    #if (STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS)
        /* Check if current file and Dem.h header file are of the same Autosar version */
        #if ((LIN_CFG_AR_RELEASE_MAJOR_VERSION_C != DEM_AR_RELEASE_MAJOR_VERSION) || \
             (LIN_CFG_AR_RELEASE_MINOR_VERSION_C != DEM_AR_RELEASE_MINOR_VERSION))
            #error "AutoSar Version Numbers of Lin_Cfg.c and Dem.h are different"
        #endif
    #endif /* STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS */
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/
#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"


#if (STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS)
/**
 * @brief   DEM error parameters
 */
const Mcal_DemErrorType  Lin_E_TimeoutCfg =
{

    (uint32)STD_OFF,
    0U

};
#endif /* STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS */

#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */
