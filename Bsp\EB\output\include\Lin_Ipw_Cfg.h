/**
 * @file     Lin_Ipw_Cfg.h
 * @brief    Lin_Ipw_Cfg.h Software h files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 **/

#ifndef LIN_IPW_CFG_H
#define LIN_IPW_CFG_H


/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/

#include "Lin_Ipw_PBcfg.h"
#include "Lin_Ipw_Types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LIN_IPW_CFG_VENDOR_ID                    (110u)
#define LIN_IPW_CFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define LIN_IPW_CFG_AR_RELEASE_MINOR_VERSION     (4u)
#define LIN_IPW_CFG_AR_RELEASE_REVISION_VERSION  (0u)
#define LIN_IPW_CFG_SW_MAJOR_VERSION             (1u)
#define LIN_IPW_CFG_SW_MINOR_VERSION             (0u)
#define LIN_IPW_CFG_SW_PATCH_VERSION             (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lin_Ipw_PBcfg.h */
#if (LIN_IPW_CFG_VENDOR_ID != LIN_IPW_PBCFG_VENDOR_ID)
    #error "Lin_Ipw_Cfg.h and Lin_Ipw_PBcfg.h have different vendor ids"
#endif
#if ((LIN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_IPW_CFG_AR_RELEASE_REVISION_VERSION != LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lin_Ipw_Cfg.h and Lin_Ipw_PBcfg.h are different"
#endif
#if ((LIN_IPW_CFG_SW_MAJOR_VERSION != LIN_IPW_PBCFG_SW_MAJOR_VERSION) || \
     (LIN_IPW_CFG_SW_MINOR_VERSION != LIN_IPW_PBCFG_SW_MINOR_VERSION) || \
     (LIN_IPW_CFG_SW_PATCH_VERSION != LIN_IPW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Ipw_Cfg.h and Lin_Ipw_PBcfg.h are different"
#endif


/* Checks against Lin_Ipw_Types.h */
#if (LIN_IPW_CFG_VENDOR_ID != LIN_IPW_TYPES_VENDOR_ID)
    #error "Lin_Ipw_Cfg.h and Lin_Ipw_Types.h have different vendor ids"
#endif
#if ((LIN_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != LIN_IPW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_IPW_CFG_AR_RELEASE_MINOR_VERSION    != LIN_IPW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (LIN_IPW_CFG_AR_RELEASE_REVISION_VERSION != LIN_IPW_TYPES_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lin_Ipw_Cfg.h and Lin_Ipw_Types.h are different"
#endif
#if ((LIN_IPW_CFG_SW_MAJOR_VERSION != LIN_IPW_TYPES_SW_MAJOR_VERSION) || \
     (LIN_IPW_CFG_SW_MINOR_VERSION != LIN_IPW_TYPES_SW_MINOR_VERSION) || \
     (LIN_IPW_CFG_SW_PATCH_VERSION != LIN_IPW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Ipw_Cfg.h and Lin_Ipw_Types.h are different"
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

#define LIN_IPW_CONFIG_EXT \
    LIN_IPW_CONFIG_PB
/**
* @brief          None EcuMWakeUpSource was referrd when LinChannelWakeupSupport is disable
*/
#define LIN_NONE_ECUM_WAKEUP_SOURCE_REF     (0UL)




/**
* @brief   No of Channels configured for Lin
*
* 
*/
#define LIN_HW_NUMBER_OF_INSTANCES            (2U)
/**
* @brief   Switches the Development Error Detection and Notification ON or OFF.
*
* 
*/
#define  LIN_HW_DEV_ERROR_DETECT               (STD_ON) /*!< Development error detection */

/**
* 
* @internal 
* @brief          Link Lin channels symbolic names with Lin hardware channel IDs.
* @details        Link Lin channels symbolic names with Lin hardware channel IDs.
*
* 
*/
                                                                                            
#define LIN_HW_1    ((uint8)1U)
                                                                                                                        
#define LIN_HW_2    ((uint8)2U)
                                                                                            
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_IPW_CFG_H */
