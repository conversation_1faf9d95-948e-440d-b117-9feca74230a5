#ifndef UART_CFG_H
#define UART_CFG_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"
#include "CDD_Uart_PBcfg.h"
#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define UART_CFG_VENDOR_ID                    (110u)
#define UART_CFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define UART_CFG_AR_RELEASE_MINOR_VERSION     (4u)
#define UART_CFG_AR_RELEASE_REVISION_VERSION  (0u)
#define UART_CFG_SW_MAJOR_VERSION             (1u)
#define UART_CFG_SW_MINOR_VERSION             (0u)
#define UART_CFG_SW_PATCH_VERSION             (0u)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Checks against CDD_Uart_PBcfg.h */
#if (UART_CFG_VENDOR_ID != UART_PBCFG_VENDOR_ID)
    #error "CDD_Uart_Cfg.h and CDD_Uart_PBcfg.h have different vendor ids"
#endif
#if ((UART_CFG_AR_RELEASE_MAJOR_VERSION    != UART_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_CFG_AR_RELEASE_MINOR_VERSION    != UART_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_CFG_AR_RELEASE_REVISION_VERSION != UART_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of CDD_Uart_Cfg.h and CDD_Uart_PBcfg.h are different"
#endif
#if ((UART_CFG_SW_MAJOR_VERSION != UART_PBCFG_SW_MAJOR_VERSION) || \
     (UART_CFG_SW_MINOR_VERSION != UART_PBCFG_SW_MINOR_VERSION) || \
     (UART_CFG_SW_PATCH_VERSION != UART_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of CDD_Uart_Cfg.h and CDD_Uart_PBcfg.h are different"
#endif


#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((UART_CFG_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (UART_CFG_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of CDD_Uart_Cfg.h and Mcal.h are different"
    #endif
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define UART_CONFIG_EXT \
    UART_CONFIG_PB


/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif
/** @} */
#endif /* UART_CFG_H */
