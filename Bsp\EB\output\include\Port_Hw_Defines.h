/**
* 
* @file    Port_Hw_Defines.h
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 


#ifndef PORT_HW_DEFINES_H
#define PORT_HW_DEFINES_H


#include "gpio_reg.h"
#include "port_reg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_HW_VENDOR_ID_DEFINES_H                       (110U)
#define PORT_HW_AR_RELEASE_MAJOR_VERSION_DEFINES_H        (4U)
#define PORT_HW_AR_RELEASE_MINOR_VERSION_DEFINES_H        (4U)
#define PORT_HW_AR_RELEASE_REVISION_VERSION_DEFINES_H     (0U)
#define PORT_HW_SW_MAJOR_VERSION_DEFINES_H                (1U)
#define PORT_HW_SW_MINOR_VERSION_DEFINES_H                (0U)
#define PORT_HW_SW_PATCH_VERSION_DEFINES_H                (0U)


/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*! @brief PORT_CI module has DSE bit */
#define FEATURE_PORT_HW_HAS_DRIVE_STRENGTH

/*! @brief PORT_CI module has LK bit */
#define FEATURE_PORT_HW_HAS_LOCK_REGISTER

/*! @brief PORT_CI module has PIDR register */

/*! @brief PORT_CI module has interleave */
#define FEATURE_PORT_HW_HAS_ADC_INTERLEAVE

/*! @brief PORT_CI module has ODE bit */
#define FEATURE_PORT_HW_HAS_OPEN_DRAIN   (STD_ON)

#define PORT_ENABLE_USER_MODE_SUPPORT   (STD_OFF)

#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
   #ifdef PORT_ENABLE_USER_MODE_SUPPORT
     #if (STD_ON == PORT_ENABLE_USER_MODE_SUPPORT)
        #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Port in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined
     #endif /* (STD_ON == PORT_ENABLE_USER_MODE_SUPPORT) */
   #endif /* ifdef PORT_ENABLE_USER_MODE_SUPPORT*/
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */

/* Pre-processor switch to enable/disable development error detection for Port_Ci Hw API */
#define PORT_HW_DEV_ERROR_DETECT         (STD_OFF)


/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PORT_HW_DEFINES_H */
