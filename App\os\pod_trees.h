// ----------------------------------  TREES ----------------------------------------
// HSM Tree for Module pod.name
TREE_BEGIN(TREE_POWER)

STATE (StatePowerRoot,                            TREE_ROOT,                     pod_power_on_init)
	TRANS     (EV(TnPower, Tm10ms),				  INTERNAL,				    	 pod_power_on_tm10ms)
STATE_END (NULL_ACTION)

TREE_END(TREE_POWER)

// HSM Tree for Module pod.name
TREE_BEGIN(TREE_BLUETOOTH)

STATE (StateBluetoothRoot,                            TREE_ROOT,                     pod_bluetooth_on_init)
	TRANS     (EV(TnBluetooth, Tm10ms),				 INTERNAL,				    	pod_bluetooth_on_tm10ms)
STATE_END (NULL_ACTION)

TREE_END(TREE_BLUETOOTH)

// HSM Tree for Module pod.name
TREE_BEGIN(TREE_CMD)

STATE (StateCmdRoot,                            TREE_ROOT,                     pod_cmd_on_init)
	TRANS     (EV(Cmd, KeyEV),				 INTERNAL,				    	pod_cmd_on_key)
	TRANS     (EV(Cmd, IOKeyEV),				 INTERNAL,				    	pod_cmd_on_io_key)
	TRANS     (EV(Cmd, LinEV),				 INTERNAL,				    	pod_cmd_on_lin)
	TRANS     (EV(Cmd, BluetoothEV),				 INTERNAL,				    	pod_cmd_on_bluetooth)
STATE_END (NULL_ACTION)

TREE_END(TREE_CMD)

// HSM Tree for Module pod.name
TREE_BEGIN(TREE_LIN)

STATE (StateLinRoot,                            TREE_ROOT,                     pod_lin_on_init)
	TRANS     (EV(TnLin, Tm2ms),				 INTERNAL,				    	pod_lin_on_tm2ms)
	TRANS     (EV(Lin, MassageEV),				 INTERNAL,				    	pod_lin_on_massage_event)
STATE_END (NULL_ACTION)

TREE_END(TREE_LIN)

// HSM Tree for Module pod.name
TREE_BEGIN(TREE_MOTOR)

STATE (StateMotorRoot,                          TREE_ROOT,                     pod_motor_on_init)
	// TRANS     (DEFAULT,				 			StateMotorIdle,				NULL_ACTION)
	TRANS     (EV(TnMotor, Tm10ms),				INTERNAL,				    	pod_motor_on_tm10ms)
	TRANS     (EV(Motor, SeatEvents),			INTERNAL,				    	pod_motor_on_seat_events)
	// TRANS	 (EV(Motor, Stall),				StateMotorStalled,				    	NULL_ACTION)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR)

TREE_BEGIN(TREE_MOTOR1)

STATE (StateMotor1Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor1Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor1Stall),			StateMotor1Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor1Idle,                         StateMotor1Root,            pod_motor_idle_entry)
	TRANS    (EV(TnMotor, Motor1Start),			StateMotor1Start,			pod_motor_on_motor1_start)
STATE_END (NULL_ACTION)

STATE (StateMotor1Start,                        StateMotor1Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Motor1Move),			StateMotor1Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor1Moving,                       StateMotor1Root,            pod_motor_moving_entry)
	TRANS     (EV(TnMotor, Motor1Stop),			StateMotor1Stopping,		pod_motor_on_motor1_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor1Stopping,                     StateMotor1Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once1StopDebouncer),	StateMotor1Stopped,			pod_motor_on_motor1_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor1Stopped,                      StateMotor1Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once1IdleDebouncer),	StateMotor1Idle,			pod_motor_on_motor1_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor1Stalled,                      StateMotor1Root,            NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor1Idle,			pod_motor_on_motor1_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR1)

#ifdef SEAT_SIDE_RIGHT

TREE_BEGIN(TREE_MOTOR2)

STATE (StateMotor2Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor2Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor2Stall),			StateMotor2Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor2Idle,                         StateMotor2Root,            pod_motor_idle_entry)
	TRANS    (EV(TnMotor, Motor2Start),			StateMotor2Start,			pod_motor_on_motor2_start)
STATE_END (NULL_ACTION)

STATE (StateMotor2Start,                        StateMotor2Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Motor2Move),			StateMotor2Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor2Moving,                       StateMotor2Root,            pod_motor_moving_entry)
	TRANS     (EV(TnMotor, Motor2Stop),			StateMotor2Stopping,		pod_motor_on_motor2_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor2Stopping,                     StateMotor2Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once2StopDebouncer),	StateMotor2Stopped,			pod_motor_on_motor2_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor2Stopped,                      StateMotor2Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once2IdleDebouncer),	StateMotor2Idle,			pod_motor_on_motor2_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor2Stalled,                      StateMotor2Root,            pod_motor_stalled_entry)
	TRANS     (DEFAULT,				 			StateMotor2Idle,			pod_motor_on_motor2_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR2)

TREE_BEGIN(TREE_MOTOR3)

STATE (StateMotor3Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor3Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor3Stall),			StateMotor3Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor3Idle,                         StateMotor3Root,            pod_motor_idle_entry)
	TRANS    (EV(TnMotor, Motor3Start),			StateMotor3Start,			pod_motor_on_motor3_start)
STATE_END (NULL_ACTION)

STATE (StateMotor3Start,                        StateMotor3Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Motor3Move),			StateMotor3Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor3Moving,                       StateMotor3Root,            pod_motor_moving_entry)
	TRANS     (EV(TnMotor, Motor3Stop),			StateMotor3Stopping,		pod_motor_on_motor3_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor3Stopping,                     StateMotor3Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once3StopDebouncer),	StateMotor3Stopped,			pod_motor_on_motor3_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor3Stopped,                      StateMotor3Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once3IdleDebouncer),	StateMotor3Idle,			pod_motor_on_motor3_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor3Stalled,                      StateMotor3Root,            pod_motor_stalled_entry)
	TRANS     (DEFAULT,				 			StateMotor3Idle,			pod_motor_on_motor3_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR3)

#endif

TREE_BEGIN(TREE_MOTOR4)

STATE (StateMotor4Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor4Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor4Stall),			StateMotor4Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor4Idle,                         StateMotor4Root,            pod_motor_idle_entry)
	TRANS    (EV(TnMotor, Motor4Start),			StateMotor4Start,			pod_motor_on_motor4_start)
STATE_END (NULL_ACTION)

STATE (StateMotor4Start,                        StateMotor4Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Motor4Move),			StateMotor4Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor4Moving,                       StateMotor4Root,            pod_motor_moving_entry)
	TRANS     (EV(TnMotor, Motor4Stop),			StateMotor4Stopping,		pod_motor_on_motor4_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor4Stopping,                     StateMotor4Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once4StopDebouncer),	StateMotor4Stopped,			pod_motor_on_motor4_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor4Stopped,                      StateMotor4Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once4IdleDebouncer),	StateMotor4Idle,			pod_motor_on_motor4_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor4Stalled,                      StateMotor4Root,            pod_motor_stalled_entry)
	TRANS     (DEFAULT,				 			StateMotor4Idle,			pod_motor_on_motor4_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR4)

TREE_BEGIN(TREE_MOTOR5)

STATE (StateMotor5Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor5Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor5Stall),			StateMotor5Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor5Idle,                         StateMotor5Root,            pod_motor_idle_entry)
	TRANS    (EV(TnMotor, Motor5Start),			StateMotor5Start,			pod_motor_on_motor5_start)
STATE_END (NULL_ACTION)

STATE (StateMotor5Start,                        StateMotor5Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Motor5Move),			StateMotor5Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor5Moving,                       StateMotor5Root,            pod_motor_moving_entry)
	TRANS     (EV(TnMotor, Motor5Stop),			StateMotor5Stopping,		pod_motor_on_motor5_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor5Stopping,                     StateMotor5Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once5StopDebouncer),	StateMotor5Stopped,			pod_motor_on_motor5_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor5Stopped,                      StateMotor5Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once5IdleDebouncer),	StateMotor5Idle,			pod_motor_on_motor5_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor5Stalled,                      StateMotor5Root,            pod_motor_stalled_entry)
	TRANS     (DEFAULT,				 			StateMotor5Idle,			pod_motor_on_motor5_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR5)

TREE_BEGIN(TREE_MOTOR6)

STATE (StateMotor6Root,                         TREE_ROOT,                  NULL_ACTION)
	TRANS     (DEFAULT,				 			StateMotor6Idle,			NULL_ACTION)
	TRANS	 (EV(Motor, Motor6Stall),			StateMotor6Stalled,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor6Idle,                         StateMotor6Root,            pod_motor_idle_entry)
	TRANS    (EV(Motor, Motor6Start),			StateMotor6Start,			pod_motor_on_motor6_start)
STATE_END (NULL_ACTION)

STATE (StateMotor6Start,                        StateMotor6Root,            NULL_ACTION)
	TRANS     (EV(Motor, Motor6Move),			StateMotor6Moving,			NULL_ACTION)
STATE_END (NULL_ACTION)

STATE (StateMotor6Moving,                       StateMotor6Root,            pod_motor_moving_entry)
	TRANS     (EV(Motor, Motor6Stop),			StateMotor6Stopping,		pod_motor_on_motor6_stopping)
STATE_END (NULL_ACTION)

STATE (StateMotor6Stopping,                     StateMotor6Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once6StopDebouncer),	StateMotor6Stopped,			pod_motor_on_motor6_stopped)
STATE_END (NULL_ACTION)

STATE (StateMotor6Stopped,                      StateMotor6Root,            NULL_ACTION)
	TRANS     (EV(TnMotor, Once6IdleDebouncer),	StateMotor6Idle,			pod_motor_on_motor6_idled)
STATE_END (NULL_ACTION)

STATE (StateMotor6Stalled,                      StateMotor6Root,            pod_motor_stalled_entry)
	TRANS     (DEFAULT,				 			StateMotor6Idle,			pod_motor_on_motor6_idled)
STATE_END (NULL_ACTION)

TREE_END(TREE_MOTOR6)

// HSM Tree for Module pod.name
TREE_BEGIN(TREE_TIMER)

STATE (StateTimerRoot,                            TREE_ROOT,                     pod_timer_on_init)
	TRANS     (EV(TnTimer, Tm10ms),				 INTERNAL,				    	pod_timer_on_tm10ms)
	TRANS     (EV(TnTimer, Tm100ms),				 INTERNAL,				    	pod_timer_on_tm100ms)
STATE_END (NULL_ACTION)

TREE_END(TREE_TIMER)
