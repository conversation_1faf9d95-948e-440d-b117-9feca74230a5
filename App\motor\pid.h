#ifndef __PID_H__
#define __PID_H__

typedef struct pid{
    float integral;
    float target;
    float real;
    float error, error_last;
    float kp, ki, kd;
    float deadband;
    float output;
}tPID;


#define LIMIT(val, min, max)  val = ((val) < (min) ? (min) : ((val) > (max) ? (max) : (val)))
#define IS_IN_RANGE(val, min, max)  ((val) >= (min) && (val) <= (max))


void PID_init(tPID * pid);
void pid_reset(tPID * pid);
void pid_set_target(tPID * pid, float temp_val);
float pid_get_target(tPID * pid);
float pid_loop(tPID * pid, float error);


#endif
