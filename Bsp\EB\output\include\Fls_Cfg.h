/**
 * file    Fls_Cfg.h
 * brief   Flash driver for LES14XX
 * author  huhl
 * date    2025.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
/*
* @section [global]
* Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
* Reason: The used compilers use more than 31 chars for identifiers.
*/


#ifndef FLS_CFG_H
#define FLS_CFG_H



/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fls_Types.h"
#include "MemIf_Types.h"
#include "Fls_Hw_Cfg.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_VENDOR_ID_CFG                    (110u)
#define FLS_AR_RELEASE_MAJOR_VERSION_CFG     (4u)
#define FLS_AR_RELEASE_MINOR_VERSION_CFG     (4u)
#define FLS_AR_RELEASE_REVISION_VERSION_CFG  (0u)
#define FLS_SW_MAJOR_VERSION_CFG             (1u)
#define FLS_SW_MINOR_VERSION_CFG             (0u)
#define FLS_SW_PATCH_VERSION_CFG             (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Fls configuration header file are of the same vendor */
#if (FLS_TYPES_VENDOR_ID != FLS_VENDOR_ID_CFG)
    #error "Fls_Types.h and Fls_Cfg.h have different vendor ids"
#endif
/* Check if current file and Fls configuration header file are of the same Autosar version */
#if ((FLS_TYPES_AR_RELEASE_MAJOR_VERSION    != FLS_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (FLS_TYPES_AR_RELEASE_MINOR_VERSION    != FLS_AR_RELEASE_MINOR_VERSION_CFG) || \
     (FLS_TYPES_AR_RELEASE_REVISION_VERSION != FLS_AR_RELEASE_REVISION_VERSION_CFG) \
    )
    #error "AutoSar Version Numbers of Fls_Types.h and Fls_Cfg.h are different"
#endif
/* Check if current file and Fls configuration header file are of the same software version */
#if ((FLS_TYPES_SW_MAJOR_VERSION != FLS_SW_MAJOR_VERSION_CFG) || \
     (FLS_TYPES_SW_MINOR_VERSION != FLS_SW_MINOR_VERSION_CFG) || \
     (FLS_TYPES_SW_PATCH_VERSION != FLS_SW_PATCH_VERSION_CFG) \
    )
    #error "Software Version Numbers of Fls_Types.h and Fls_Cfg.h are different"
#endif

/* Check if current file and MemIf_Types.h file are of the same Autosar version */
#if ((FLS_AR_RELEASE_MAJOR_VERSION_CFG != MEMIF_AR_RELEASE_MAJOR_VERSION) || \
      (FLS_AR_RELEASE_MINOR_VERSION_CFG != MEMIF_AR_RELEASE_MINOR_VERSION) \
    )
    #error "AutoSar Version Numbers of Fls_Cfg.h and MemIf_Types.h are different"
#endif

/* Check if current file and Fls_Hw_Cfg header file are of the same vendor */
#if (FLS_VENDOR_ID_CFG != FLS_HW_VENDOR_ID_CFG)
  #error "Fls_Cfg.h and Fls_Hw_Cfg.h have different vendor ids"
#endif

/* Check if current file and Fls_Hw_Cfg header file are of the same Autosar version */
#if ((FLS_AR_RELEASE_MAJOR_VERSION_CFG    != FLS_HW_AR_RELEASE_MAJOR_VERSION_CFG) || \
    (FLS_AR_RELEASE_MINOR_VERSION_CFG    != FLS_HW_AR_RELEASE_MINOR_VERSION_CFG) || \
    (FLS_AR_RELEASE_REVISION_VERSION_CFG != FLS_HW_AR_RELEASE_REVISION_VERSION_CFG) \
  )
  #error "AutoSar Version Numbers of Fls_Cfg.h and Fls_Hw_Cfg.h are different"
#endif

/* Check if current file and Fls_Hw_Cfg header file are of the same Software version */
#if ((FLS_SW_MAJOR_VERSION_CFG != FLS_HW_SW_MAJOR_VERSION_CFG) || \
    (FLS_SW_MINOR_VERSION_CFG != FLS_HW_SW_MINOR_VERSION_CFG) || \
    (FLS_SW_PATCH_VERSION_CFG != FLS_HW_SW_PATCH_VERSION_CFG) \
  )
  #error "Software Version Numbers of Fls_Cfg.h and Fls_Hw_Cfg.h are different"
#endif


/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/



/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
                                 GLOBAL CONSTANT DECLARATIONS
==================================================================================================*/
#define FLS_PRECOMPILE_SUPPORT

#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"



extern const Fls_ConfigType Fls_Config;


#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"



/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/***/

#endif /* FLS_CFG_H */
