
/**
 * file    Can_Cfg.h
 * brief   Can Configurations header file.
 * author  <PERSON><PERSON>
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef CAN_CFG_H
#define CAN_CFG_H

#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================[macros]================================================*/
/*Tx FIFO shall be recognized */
#define CAN_CONTROLLER_TX_FIFO 32u


/* Can moudle version infomation */
#define CAN_SW_MAJOR_VERSION 1u
#define CAN_SW_MINOR_VERSION 0u
#define CAN_SW_PATCH_VERSION 0u

/** Switches the Wakeup Support ON or OFF */
#define CAN_WAKEUP_SUPPORT_ENABLE       STD_OFF

/** Switches the Os Counter Support ON or OFF */
#define CAN_OS_COUNTER_SUPPORT_ENABLE   STD_OFF

#define CAN_TIMEOUT_COUNT               (100000u)

/** Switches pretended networking mode shall be supported. ON or OFF  */
#define CAN_PUBLIC_ICOM_SUPPORT         STD_ON

/** Switches the Development Error Notification ON or OFF. */
#define CAN_DEV_ERROR_DETECT            STD_ON

#define CAN_HW_TRANSMIT_CANCELLATION    STD_OFF
#define CAN_TRIGGER_TRANSMIT_SUPPORT    STD_OFF

/** Switches the diagnostic event manager Notification ON or OFF. */
#define CAN_DEM_E_HW_ERROR STD_OFF


#define CAN_RX_INDICATION_COMPATIBILITY CAN_ASR_422_COMPATIBILITY

/* Initialization structure according to ECUM182_Conf (not a symbolic name). */
#define CanConfigSet_0 (Can_Config[0u])

/* CanController Invalid DMA channels */
#define DMA_CHANNEL_INVALID 0xFFFFFFFu

/* CanController symbolic names */
#define CanConf_CanController_0_CanController_0                     0u

/* CanHardwareObject symbolic names */
#define CanConf_CanHardwareObject_0_CanHardwareObject_Receive          0u
#define CanConf_CanHardwareObject_0_CanHardwareObject_Transmit         1u

/* Interrupt channel symbolic names */
#define CAN_INTERRUPT_39   0u
#define CAN_INTERRUPT_40   0u


/* DMA Channel Enable status. */



#ifdef __cplusplus
}
#endif
#endif

