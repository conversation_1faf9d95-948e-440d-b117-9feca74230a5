/**
 * @file     Lin_Hw_Cfg.h
 * @brief    Lin_Hw_Cfg Software h files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 **/
 
#ifndef LIN_HW_CFG_H
#define LIN_HW_CFG_H


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Lin_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LIN_HW_CFG_VENDOR_ID                    (110u)
#define LIN_HW_CFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define LIN_HW_CFG_AR_RELEASE_MINOR_VERSION     (4u)
#define LIN_HW_CFG_AR_RELEASE_REVISION_VERSION  (0u)
#define LIN_HW_CFG_SW_MAJOR_VERSION             (1u)
#define LIN_HW_CFG_SW_MINOR_VERSION             (0u)
#define LIN_HW_CFG_SW_PATCH_VERSION             (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Lin_Hw_PBcfg.h */
#if (LIN_HW_CFG_VENDOR_ID != LIN_HW_PBCFG_VENDOR_ID)
    #error "Lin_Hw_Cfg.h and Lin_Hw_PBcfg.h have different vendor ids"
#endif
#if ((LIN_HW_CFG_AR_RELEASE_MAJOR_VERSION      != LIN_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_HW_CFG_AR_RELEASE_MINOR_VERSION      != LIN_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_HW_CFG_AR_RELEASE_REVISION_VERSION   != LIN_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_Hw_Cfg.h and Lin_Hw_PBcfg.h are different"
#endif
#if ((LIN_HW_CFG_SW_MAJOR_VERSION != LIN_HW_PBCFG_SW_MAJOR_VERSION) || \
     (LIN_HW_CFG_SW_MINOR_VERSION != LIN_HW_PBCFG_SW_MINOR_VERSION) || \
     (LIN_HW_CFG_SW_PATCH_VERSION != LIN_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Hw_Cfg.h and Lin_Hw_PBcfg.h are different"
#endif


/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/
#define LIN_HW_CONFIG_EXT \
    LIN_HW_CONFIG_PB

/**
* @brief   Switches the Development Error Detection and Notification ON or OFF.
*
* 
*/
#define LIN_HW_DEV_ERROR_DETECT               (STD_ON) /*!< Development error detection */

/**
* @brief   No of Channels configured for Lin
*
* 
*/
#define LIN_HW_NUMBER_OF_INSTANCES         (2U)


/**
* @brief          Lin Osif source counter
* @details        This parameter is used to select between different OsIf counter implementation
*
* 
*/
#define  LIN_HW_TIMEOUT_TYPE       (OSIF_COUNTER_DUMMY)

/**
* @brief   Number of loops before returning LIN_STATUS_TIMEOUT.
*
* 
*/
#define LIN_HW_TIMEOUT_VALUE_US    (1000U)

/**
* @brief   Enable/Disable Autobaud feature. This feature only support for IPV layer
* So HLD layer always disable for this feature.
*
*
*/
#define LIN_HW_AUTO_BAUD           (STD_OFF)

/**
* @brief   Enable/Disable timeout feature.
*
*
*/
#define LIN_HW_FRAME_TIMEOUT_DISABLE        (STD_ON)


/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/
#define LIN_START_SEC_CODE
#include "Lin_MemMap.h"



extern void Lin_Ipw_Callback(const uint8 u8HwInstance, const LinHwStateStruct_t *patLinHwStateStruce);

#define LIN_STOP_SEC_CODE
#include "Lin_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif 
