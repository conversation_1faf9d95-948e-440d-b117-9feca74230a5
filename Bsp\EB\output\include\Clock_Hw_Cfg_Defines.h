/**
 * *************************************************************************
 * @file   Clock_Hw_Cfg_Defines.h
 * @brief  Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
#ifndef CLOCK_HW_CFG_DEFINES_H
#define CLOCK_HW_CFG_DEFINES_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/

#include "crg_reg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CLOCK_HW_CFG_DEFINES_VENDOR_ID                       (110U)
#define CLOCK_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION        (4U)
#define CLOCK_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION        (4U)
#define CLOCK_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION     (0U)
#define CLOCK_HW_CFG_DEFINES_SW_MAJOR_VERSION                (1U)
#define CLOCK_HW_CFG_DEFINES_SW_MINOR_VERSION                (0U)
#define CLOCK_HW_CFG_DEFINES_SW_PATCH_VERSION                (0U)

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
/**
* @brief            Derivative used.
*/
#define CLOCK_HW_LSE14XX

/**
* @brief            HW sseries used.
*/
#define CLOCK_HW_LS14XX

/**
* @brief            Max number of internal oscillators
*/
#define CLOCK_HW_IRCOSCS_COUNT       (2U)

/**
* @brief            Max number of external oscillators
*/
#define CLOCK_HW_XOSCS_COUNT       (1U)

/**
* @brief            Max number of pll devices
*/
#define CLOCK_HW_PLLS_COUNT       (1U)

/**
* @brief            Max number of selectors
*/
#define CLOCK_HW_SELECTORS_COUNT       (13U)

/**
* @brief            Max number of dividers
*/
#define CLOCK_HW_DIVIDERS_COUNT       (20U)

/**
* @brief            Max number of divider triggers
*/
#define CLOCK_HW_DIVIDER_TRIGGERS_COUNT       (0U)

/**
* @brief            Max number of fractional dividers
*/
#define CLOCK_HW_FRACTIONAL_DIVIDERS_COUNT       (0U)

/**
* @brief            Max number of external clocks
*/
#define CLOCK_HW_EXT_CLKS_COUNT       (0U)

/**
* @brief            Max number of pcfs
*/
#define CLOCK_HW_PCFS_COUNT       (0U)

/**
* @brief            Max number of clock gates
*/
#define CLOCK_HW_GATES_COUNT       (64U)

/**
* @brief            Max number of clock monitoring units
*/
#define CLOCK_HW_CMUS_COUNT       (3U)

/**
* @brief            Max number of configured frequencies values
*/
#define CLOCK_HW_CONFIGURED_FREQUENCIES_COUNT       (28U)

/**
* @brief            Max number of specific peripheral (eMIOS) units
*/
#define CLOCK_HW_SPECIFIC_PERIPH_COUNT       (0U)

/**
* @brief            Supported power mode.
*/

#define CLOCK_HW_HAS_RUN_MODE                0U

#define CLOCK_HW_LPO_128K_FREQUENCY         128000
#define CLOCK_HW_LRC_32K_FREQUENCY          32000
#define CLOCK_HW_LRC_1K_FREQUENCY           1000



#define CLOCK_HW_HRC_FREQUENCY              48000000

#define CLOCK_HW_MRC_FREQUENCY              8000000

#define CLOCK_HW_DEFAULT_SOSC_FREQUENCY      40000000

#define CLOCK_HW_HAS_LOW_GAIN                         0U

#define CLOCK_HW_HAS_HIGH_GAIN                        1U

#define CLOCK_HW_HAS_MONITOR_DISABLE                  0U

#define CLOCK_HW_HAS_MONITOR_INT                      1U

#define CLOCK_HW_HAS_MONITOR_RESET                    2U

#define CLOCK_HW_HAS_SAFE_CLOCK_DISABLEMENT           1U

#if CLOCK_HW_CMUS_COUNT > 0U
/**
* @brief            Cmu formula constant values.
*/
#define CLOCK_HW_FEATURE_OFFSET_REFERENCE_COUNT_FORMULA1               1U
#define CLOCK_HW_FEATURE_MULTIPLIER_REFERENCE_COUNT_FORMULA1           3U
#define CLOCK_HW_FEATURE_OFFSET_REFERENCE_COUNT_FORMULA2               7U
#define CLOCK_HW_FEATURE_MULTIPLIER_REFERENCE_COUNT_FORMULA2           3U
#endif

/**
* @brief            Clock hw supports clock frequency
*/
#define CLOCK_HW_GET_FREQUENCY_API              (STD_ON)


/**
* @brief            Clock hw supports ram wait states
*/


/**
* @brief            Clock hw supports flash wait states
*/


/**
* @brief            Supports wait states configuration
*/
#define CLOCK_HW_SUPPORTS_WAIT_STATES       STD_OFF

/**
* @brief            Clock hw supports power mode change notification
*/
#define CLOCK_HW_POWER_MODE_CHANGE_NOTIFICATION         STD_ON

/**
* @brief            Support for CRG interrupt type.
*/
#define MCU_CRG_INTERRUPT_NMI           (STD_ON)


/**
* @brief            Supported clocks.
*/
#define CLOCK_HW_HAS_LRC128K_CLK         1U
#define CLOCK_HW_HAS_LRC32K_CLK         2U
#define CLOCK_HW_HAS_LRC1K_CLK         3U
#define CLOCK_HW_HAS_HRC_CLK         4U
#define CLOCK_HW_HAS_SOSC_CLK         5U
#define CLOCK_HW_HAS_MRC_CLK         6U
#define CLOCK_HW_HAS_SPLL_CLK         7U
#define CLOCK_HW_HAS_HRC_DIV1_CLK         8U
#define CLOCK_HW_HAS_HRC_DIV2_CLK         9U
#define CLOCK_HW_HAS_OSC_DIV1_CLK         10U
#define CLOCK_HW_HAS_OSC_DIV2_CLK         11U
#define CLOCK_HW_HAS_MRC_DIV1_CLK         12U
#define CLOCK_HW_HAS_MRC_DIV2_CLK         13U
#define CLOCK_HW_HAS_SPLL_DIV1_CLK         14U
#define CLOCK_HW_HAS_SPLL_DIV2_CLK         15U
#define CLOCK_HW_HAS_RTC32K_CLK         16U
#define CLOCK_HW_HAS_LRC_CLK         17U
#define CLOCK_HW_HAS_RTC_CLK         18U
#define CLOCK_HW_HAS_SYS_PRE_CLK         19U
#define CLOCK_HW_HAS_SYS_DIV_CLK         20U
#define CLOCK_HW_HAS_ADC0_DIV_CLK         21U
#define CLOCK_HW_HAS_ADC1_DIV_CLK         22U
#define CLOCK_HW_HAS_CAN_PCLK_CLK         23U
#define CLOCK_HW_HAS_AFE_PCLK_CLK         24U
#define CLOCK_HW_HAS_TOP_PCLK_CLK         25U
#define CLOCK_HW_HAS_IPB_PCLK_CLK         26U
#define CLOCK_HW_HAS_IPT_PCLK_CLK         27U
#define CLOCK_HW_HAS_GPIO_PCLK_CLK         28U
#define CLOCK_HW_HAS_PMU_SYS_CLK         29U
#define CLOCK_HW_HAS_PLLDESTO_CLK         30U
#define CLOCK_HW_HAS_TRACE_CLK         31U
#define CLOCK_HW_HAS_CLOKOUT_CLK         32U
#define CLOCK_HW_HAS_RTC_FUN_CLK         33U
#define CLOCK_HW_HAS_HSM_SLV_CLK         34U
#define CLOCK_HW_HAS_HSM_CLK         35U
#define CLOCK_HW_HAS_FLASH_CLK         36U
#define CLOCK_HW_HAS_GPIO_CLK         37U
#define CLOCK_HW_HAS_IO_CLK         38U
#define CLOCK_HW_HAS_PWM0_CLK         39U
#define CLOCK_HW_HAS_PWM1_CLK         40U
#define CLOCK_HW_HAS_PWM2_CLK         41U
#define CLOCK_HW_HAS_PWM3_CLK         42U
#define CLOCK_HW_HAS_LIN0_CLK         43U
#define CLOCK_HW_HAS_LIN1_CLK         44U
#define CLOCK_HW_HAS_LIN2_CLK         45U
#define CLOCK_HW_HAS_LIN3_CLK         46U
#define CLOCK_HW_HAS_SPI0_FUN_CLK         47U
#define CLOCK_HW_HAS_SPI0_CLK         48U
#define CLOCK_HW_HAS_SPI1_FUN_CLK         49U
#define CLOCK_HW_HAS_SPI1_CLK         50U
#define CLOCK_HW_HAS_SPI2_FUN_CLK         51U
#define CLOCK_HW_HAS_SPI2_CLK         52U
#define CLOCK_HW_HAS_UART0_CLK         53U
#define CLOCK_HW_HAS_UART1_CLK         54U
#define CLOCK_HW_HAS_UART2_CLK         55U
#define CLOCK_HW_HAS_I2C0_CLK         56U
#define CLOCK_HW_HAS_I2C1_CLK         57U
#define CLOCK_HW_HAS_FLEXIO_CLK         58U
#define CLOCK_HW_HAS_PIT_FUN_CLK         59U
#define CLOCK_HW_HAS_PIT_CLK         60U
#define CLOCK_HW_HAS_TRGMUX_CLK         61U
#define CLOCK_HW_HAS_STM_FUN_CLK         62U
#define CLOCK_HW_HAS_STM_CLK         63U
#define CLOCK_HW_HAS_WDT_CLK         64U
#define CLOCK_HW_HAS_CRC_CLK         65U
#define CLOCK_HW_HAS_SENSOR_CLK         66U
#define CLOCK_HW_HAS_EWM_FUN_CLK         67U
#define CLOCK_HW_HAS_EWM_CLK         68U
#define CLOCK_HW_HAS_ADC0_FUN_CLK         69U
#define CLOCK_HW_HAS_ADC0_CLK         70U
#define CLOCK_HW_HAS_ADC1_FUN_CLK         71U
#define CLOCK_HW_HAS_ADC1_CLK         72U
#define CLOCK_HW_HAS_CMP_FUN_CLK         73U
#define CLOCK_HW_HAS_CMP_CLK         74U
#define CLOCK_HW_HAS_PDB0_FUN_CLK         75U
#define CLOCK_HW_HAS_PDB0_CLK         76U
#define CLOCK_HW_HAS_PDB1_FUN_CLK         77U
#define CLOCK_HW_HAS_PDB1_CLK         78U
#define CLOCK_HW_HAS_CAN0_FUN_CLK         79U
#define CLOCK_HW_HAS_CAN0_CLK         80U
#define CLOCK_HW_HAS_CAN1_FUN_CLK         81U
#define CLOCK_HW_HAS_CAN1_CLK         82U
#define CLOCK_HW_HAS_CAN2_FUN_CLK         83U
#define CLOCK_HW_HAS_CAN2_CLK         84U
#define CLOCK_HW_HAS_CAN3_FUN_CLK         85U
#define CLOCK_HW_HAS_CAN3_CLK         86U
#define CLOCK_HW_HAS_CANSRAM_CLK         87U
#define CLOCK_HW_HAS_SRAM_CLK         88U
#define CLOCK_HW_HAS_MPU_CLK         89U
#define CLOCK_HW_HAS_PMU_CLK         90U
#define CLOCK_HW_HAS_WKTMR_CLK         91U
#define CLOCK_HW_HAS_ADC_TRIG_CLK         92U
#define CLOCK_HW_HAS_SPLL_VCO_CLK         93U
#define CLOCK_HW_HAS_SOCCTRL_CLK         94U
#define CLOCK_HW_HAS_GPIO_DEBOUNCE_CLK         95U
#define CLOCK_HW_FEATURE_PRODUCERS_NO         96U
#define CLOCK_HW_FEATURE_NAMES_NO         97U

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */
#endif /* #ifndef CLOCK_HW_CFG_DEFINES_H */

