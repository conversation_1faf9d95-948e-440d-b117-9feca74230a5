/**
 * @file    Stm_Gpt_Hw_PBcfg.h
 * @brief   Stm Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 */

#ifndef STM_GPT_HW_PBCFG_H
#define STM_GPT_HW_PBCFG_H

#include "Stm_Gpt_Hw_Types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
#define STM_GPT_HW_VENDOR_ID_PBCFG_H                    (110U)
#define STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H     (4U)
#define STM_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H     (4U)
#define STM_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H  (0U)
#define STM_GPT_HW_SW_MAJOR_VERSION_PBCFG_H             (1U)
#define STM_GPT_HW_SW_MINOR_VERSION_PBCFG_H             (0U)
#define STM_GPT_HW_SW_PATCH_VERSION_PBCFG_H             (0U)
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/
#if (STM_GPT_HW_VENDOR_ID_PBCFG_H != STM_GPT_HW_TYPES_VENDOR_ID)
    #error "Stm_Gpt_Hw_PBcfg.h and Stm_Gpt_Hw_Types.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H != STM_GPT_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (STM_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H != STM_GPT_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (STM_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H != STM_GPT_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Stm_Gpt_Hw_PBcfg.h and Stm_Gpt_Hw_Types.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((STM_GPT_HW_SW_MAJOR_VERSION_PBCFG_H != STM_GPT_HW_TYPES_SW_MAJOR_VERSION) || \
     (STM_GPT_HW_SW_MINOR_VERSION_PBCFG_H != STM_GPT_HW_TYPES_SW_MINOR_VERSION) || \
     (STM_GPT_HW_SW_PATCH_VERSION_PBCFG_H != STM_GPT_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Stm_Gpt_Hw_PBcfg.h and Stm_Gpt_Hw_Types.h are different"
#endif
/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"



#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif/*Stm_Gpt_Hw_PBCFG_H*/
/** } */

#endif  /* Stm_Gpt_Hw_PBCFG_H */
