/**
 * 
 * @file Pwm_Cfg.c
 * @brief AUTOSAR Pwm - contains the data exported by the Pwm module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * @section[blobal]
 * Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
 * Reason: The macro definition names are similar, the version number definition prefixes are the same, 
 * and the keywords are different.
 *
 */ 
 
#include "Pwm.h"
#include "Pwm_Cfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define PWM_VENDOR_ID_PCCFG_C                       (110U)
#define PWM_AR_RELEASE_MAJOR_VERSION_PCCFG_C        (4U)
#define PWM_AR_RELEASE_MINOR_VERSION_PCCFG_C        (4U)
#define PWM_AR_RELEASE_REVISION_VERSION_PCCFG_C     (0U)  
#define PWM_SW_MAJOR_VERSION_PCCFG_C                (1U)
#define PWM_SW_MINOR_VERSION_PCCFG_C                (0U)
#define PWM_SW_PATCH_VERSION_PCCFG_C                (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
/* Check Pwm_Cfg.c against Pwm.h file versions */
#if (PWM_VENDOR_ID_PCCFG_C != PWM_VENDOR_ID)
    #error "Pwm_Cfg.c and Pwm.h have different vendor IDs"
#endif

#if ((PWM_AR_RELEASE_MAJOR_VERSION_PCCFG_C != PWM_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_AR_RELEASE_MINOR_VERSION_PCCFG_C != PWM_AR_RELEASE_MINOR_VERSION) || \
     (PWM_AR_RELEASE_REVISION_VERSION_PCCFG_C != PWM_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_Cfg.c and Pwm.h are different"
#endif

#if ((PWM_SW_MAJOR_VERSION_PCCFG_C != PWM_SW_MAJOR_VERSION) || \
     (PWM_SW_MINOR_VERSION_PCCFG_C != PWM_SW_MINOR_VERSION) || \
     (PWM_SW_PATCH_VERSION_PCCFG_C != PWM_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_Cfg.c and Pwm.h are different"
#endif

#if (PWM_VENDOR_ID_PCCFG_C != PWM_CFG_VENDOR_ID)
    #error "Pwm_Cfg.c and Pwm_Cfg.h have different vendor IDs"
#endif
	
#if ((PWM_AR_RELEASE_MAJOR_VERSION_PCCFG_C != PWM_CFG_AR_RELEASE_MAJOR_VERSION) || \
		 (PWM_AR_RELEASE_MINOR_VERSION_PCCFG_C != PWM_CFG_AR_RELEASE_MINOR_VERSION) || \
		 (PWM_AR_RELEASE_REVISION_VERSION_PCCFG_C != PWM_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_Cfg.c and Pwm_Cfg.h are different"
#endif
	
#if ((PWM_SW_MAJOR_VERSION_PCCFG_C != PWM_CFG_SW_MAJOR_VERSION) || \
		 (PWM_SW_MINOR_VERSION_PCCFG_C != PWM_CFG_SW_MINOR_VERSION) || \
		 (PWM_SW_PATCH_VERSION_PCCFG_C != PWM_CFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_Cfg.c and Pwm_Cfg.h are different"
#endif

#ifdef __cplusplus
}
#endif


