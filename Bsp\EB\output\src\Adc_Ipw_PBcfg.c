/**
 * file    Adc_Ipw_PBcfg.c
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


/**
*   @file
*
*   @addtogroup adc_ipw_config Adc Ipw Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#include "Adc_Ipw_CfgDefines.h"
#include "Adc_Ipw_PBcfg.h"
#include "Adc_Hw_PBcfg.h"
#include "Pdb_Adc_Hw_PBcfg.h"

#ifdef ADC_DMA_SUPPORTED
#include "Dma_Hw_Cfg.h"
#endif /* ADC_DMA_SUPPORTED */

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_IPW_VENDOR_ID_PBCFG_C                      (110u)
#define ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C       (4u)
#define ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C       (4u)
#define ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG_C    (0u)
#define ADC_IPW_SW_MAJOR_VERSION_PBCFG_C               (1u)
#define ADC_IPW_SW_MINOR_VERSION_PBCFG_C               (0u)
#define ADC_IPW_SW_PATCH_VERSION_PBCFG_C               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_CfgDefines.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_PBCFG_C != ADC_IPW_VENDOR_ID_CFGDEFINES)
    #error "Adc_Ipw_PBcfg.c and Adc_Ipw_CfgDefines.h have different vendor ids"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_CfgDefines.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_MAJOR_VERSION_CFGDEFINES) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_MINOR_VERSION_CFGDEFINES) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_REVISION_VERSION_CFGDEFINES) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.c and Adc_Ipw_CfgDefines.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_CfgDefines.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_PBCFG_C != ADC_IPW_SW_MAJOR_VERSION_CFGDEFINES) || \
     (ADC_IPW_SW_MINOR_VERSION_PBCFG_C != ADC_IPW_SW_MINOR_VERSION_CFGDEFINES) || \
     (ADC_IPW_SW_PATCH_VERSION_PBCFG_C != ADC_IPW_SW_PATCH_VERSION_CFGDEFINES) \
    )
  #error "Software Version Numbers of Adc_Ipw_PBcfg.c and Adc_Ipw_CfgDefines.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_PBcfg.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_PBCFG_C != ADC_IPW_VENDOR_ID_PBCFG)
    #error "Adc_Ipw_PBcfg.c and Adc_Ipw_PBcfg.h have different vendor ids"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_PBcfg.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG_C != ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.c and Adc_Ipw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Ipw_PBcfg.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_PBCFG_C != ADC_IPW_SW_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_MINOR_VERSION_PBCFG_C != ADC_IPW_SW_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_PATCH_VERSION_PBCFG_C != ADC_IPW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Adc_Ipw_PBcfg.c and Adc_Ipw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_PBCFG_C != ADC_HW_VENDOR_ID_PBCFG)
    #error "Adc_Ipw_PBcfg.c and Adc_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.c and Adc_Hw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_PBCFG_C != ADC_HW_SW_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_MINOR_VERSION_PBCFG_C != ADC_HW_SW_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_PATCH_VERSION_PBCFG_C != ADC_HW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Adc_Ipw_PBcfg.c and Adc_Hw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same vendor */
#if (ADC_IPW_VENDOR_ID_PBCFG_C != PDB_ADC_HW_VENDOR_ID_PBCFG)
    #error "Adc_Ipw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_AR_RELEASE_REVISION_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h are different"
#endif

/* Check if Adc_Ipw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same Software version */
#if ((ADC_IPW_SW_MAJOR_VERSION_PBCFG_C != PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_MINOR_VERSION_PBCFG_C != PDB_ADC_HW_SW_MINOR_VERSION_PBCFG) || \
     (ADC_IPW_SW_PATCH_VERSION_PBCFG_C != PDB_ADC_HW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Adc_Ipw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
#ifdef ADC_DMA_SUPPORTED
/* Check if Adc_Ipw_PBcfg.c file and Dma_Hw_Cfg.h file are of the same Autosar version */
#if ((ADC_IPW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (ADC_IPW_AR_RELEASE_MINOR_VERSION_PBCFG_C != DMA_HW_CFG_AR_RELEASE_MINOR_VERSION) \
    )
    #error "AutoSar Version Numbers of Adc_Ipw_PBcfg.c and Dma_Hw_Cfg.h are different"
#endif
#endif /* ADC_DMA_SUPPORTED */
#endif /* DISABLE_MCAL_INTERMODULE_ASR_CHECK */

/*==================================================================================================
*                                   GLOBAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/


/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"


#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

#define ADC_START_SEC_CONFIG_DATA_16
#include "Adc_MemMap.h"
/**
* @brief          Group Channel Delays for configuration variant.
*
*/
static const uint16 Adc_GroupChannelDelays0[] =
{
    33U,
    64U,
    95U,
    126U,
    157U,
    188U,
    219U,
    250U,
    281U,
    312U,
};
static const uint16 Adc_GroupChannelDelays1[] =
{
    40U,
    74U,
    108U,
    142U,
    176U,
    210U,
    244U,
    278U,
    312U,
};

#define ADC_STOP_SEC_CONFIG_DATA_16
#include "Adc_MemMap.h"

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/**
* @brief          Adc Ipw Config configuration variant .
*/
const Adc_Ipw_Config_t AdcIpwCfg =
{
    { &AdcHwConfig_0, &AdcHwConfig_1 }, /* AdcConfigPtrArr */
    { &PdbAdcHwConfig_0, &PdbAdcHwConfig_1 }, /* PdbConfig */
    { 0U, 1U }, /* AdcPhysicalIdArr */
#if (ADC_ENABLE_LIMIT_CHECK == STD_ON)
    { NULL_PTR, NULL_PTR }, /* ChannelLimitCheckingConfigs */
#endif /* (ADC_ENABLE_LIMIT_CHECK == STD_ON) .*/
    /* Mapping */
    {
        { ADC_DMA, ADC_DMA }, /* AdcDmaInterruptSoftware */
        /**< @brief number of groups per hw unit > */
        { 1U, 1U }, /* AdcGroups */
        /**< @brief number of channels per hw unit > */
        { 10U, 9U }, /* AdcChannels */
        { ADC_IPW_INVALID_DMA_CHANNEL_ID, ADC_IPW_INVALID_DMA_CHANNEL_ID }, /* AdcDmaChannelLogicId */
        { STD_ON, STD_ON }, /* AdcHwUnitArr */
        { 0U, 1U } /* HwLogicalId */
    }
};

/**
* @brief          ADC Ipw Group 0 Config .
*/
const Adc_Ipw_GroupConfig_t AdcIpwGroupConfig_0 =
{
#if (ADC_SET_ADC_CONV_TIME_ONCE == STD_OFF)
    /**< @brief Main Moving-Average enable status of group */
    STD_OFF,/* GroupMovAvgEn */
    /**< @brief Main Moving-Average selection of group */
    ADC_HW_MovAVG_4_CONV,/* GroupMovAvgSel */
    /**< @brief Main Average enable status of group */
    STD_OFF, /* GroupAvgEnable */
    /**< @brief Main Average selection of group */
    ADC_HW_AVG_4_CONV, /* GroupAvgSelect */
    ADC_HW_DEFAULT_SAMPLE_TIME, /* ConvTime */
#endif /* (ADC_SET_ADC_CONV_TIME_ONCE == STD_OFF) .*/
    0U, /* PdbPeriod */
    (boolean)STD_OFF,
    0U, /* PdbDelay */
    (boolean)FALSE, /* AdcGroupEnableBackToBack */
    (boolean)TRUE, /* AdcGroupEnableChannelDelays */
    Adc_GroupChannelDelays0 /* DelayPtr */
};

/**
* @brief          ADC Ipw Group 1 Config .
*/
const Adc_Ipw_GroupConfig_t AdcIpwGroupConfig_1 =
{
#if (ADC_SET_ADC_CONV_TIME_ONCE == STD_OFF)
    /**< @brief Main Moving-Average enable status of group */
    STD_OFF,/* GroupMovAvgEn */
    /**< @brief Main Moving-Average selection of group */
    ADC_HW_MovAVG_4_CONV,/* GroupMovAvgSel */
    /**< @brief Main Average enable status of group */
    STD_OFF, /* GroupAvgEnable */
    /**< @brief Main Average selection of group */
    ADC_HW_AVG_4_CONV, /* GroupAvgSelect */
    ADC_HW_DEFAULT_SAMPLE_TIME, /* ConvTime */
#endif /* (ADC_SET_ADC_CONV_TIME_ONCE == STD_OFF) .*/
    0U, /* PdbPeriod */
    (boolean)STD_OFF,
    0U, /* PdbDelay */
    (boolean)FALSE, /* AdcGroupEnableBackToBack */
    (boolean)TRUE, /* AdcGroupEnableChannelDelays */
    Adc_GroupChannelDelays1 /* DelayPtr */
};


#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

