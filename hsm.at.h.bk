#include "types.h"

typedef u32 tHsmTreeInst;
typedef u32 tHsmStateIndex;
typedef u8 tHsmSubm;

typedef struct {
    tHsmStateIndex data[40];
    tU32     size;
} tHsmStack;

typedef struct {
    tHsmTreeInst nr_inst;            
    tHsmSubm* inst_subm;             
    tHsmStateIndex current_state; 	 
    tHsmStateIndex target_state; 	 
    tHsmStack* const stack; 		 
} tHsmTreeState;

typedef u32 tHsmEvent;
typedef u32 tHsmTreeIndex;
typedef u32 tHsmTransIndex;
typedef u16 tHsmParamLen;

typedef void* const tHsmParam;
typedef enum {
    TYPE_U32,
    TYPE_PTR,
    TYPE_U16,
    TYPE_U8,
} tHsmDataType;

typedef union {
    u32 u32;
    void *ptr;
    u16 u16[2];
    u8 u8[4];
} tHsmDataUnion;

typedef struct {
    tHsmEvent event;                             
    tHsmParamLen param_len;                      
    u16 type;									 
    tHsmDataUnion as;							 
} tHsmGenCurr;

typedef u32(*tHsmAction)(tHsmGenCurr* const event);
typedef u32 tHsmSubmPermTab;

typedef struct {
    const tHsmEvent event;               
    const tHsmStateIndex next_state;     
    const tHsmAction action;             
} tHsmTrans;

typedef struct {
    const tChar* const name;             
    const tHsmStateIndex parent;         
    const tHsmAction entry;              
    const tHsmTrans* const trans_tab;    
    const tHsmTransIndex trans_len;      
    const tHsmAction exit;               
} tHsmState;

typedef struct {
    const tChar* const name;             
    const tHsmState* const states;       
    const tHsmStateIndex states_len;     
    tHsmSubmPermTab* const perm_subm;    
} tHsmTree;

typedef struct {
    const tHsmSubm submitter;                    
    const tChar* const subm_name;                
    const tChar* const* event_names_table;       
    const u32 event_nr;  						 
} tHsmSubmEvNames;

typedef struct {
    tHsmTreeState* const* tree_states;           
    const tHsmTreeIndex tree_number;             
    const tHsmTree* const* tree_ptr;             
    const tHsmSubmEvNames* subm_event_names;     
    const u32 subm_nr;                          
    tHsmGenCurr* const curr;                     
} tHsmGen;

typedef struct {
    const tChar* const name;     
    const tHsmGen* hsm_gen;      
    const void* self;            
} tHsmInst;

inline tHsmEvent hsm_get_curr_event(tHsmGenCurr* const ev)
{
    return ((tHsmEvent)((ev->event) & (u32)0xFF00FFFFU));
}

inline tHsmSubm hsm_get_curr_event_inst(tHsmGenCurr* const ev)
{
    return ((tHsmSubm)((((u32)(ev->event)) >> ((u32)(16U))) & (u32)0xFFU));
}

void hsm_create(const tHsmInst *hsm);
void hsm_start(const tHsmInst *hsm);
void hsm_stop(void);
void hsm_on_event(const tHsmInst *hsm);
inline tHsmStateIndex hsm_get_current_state(const tHsmInst *hsm, tHsmTreeIndex index);
const tChar* const hsm_get_current_state_name(const tHsmInst *hsm, tHsmTreeIndex index);
const tChar *hsm_get_event_name(const tHsmInst *hsm, tHsmEvent event);
void hsm_null_action(void);
void hsm_init_inst(const tHsmInst* hsm);
void hsm_down_inst(const tHsmInst* hsm);
void hsm_create_tree(const tHsmInst* hsm, tHsmTreeIndex tree_index, tHsmSubm subm);
void hsm_remove_tree(const tHsmInst* hsm, tHsmTreeIndex tree_index, tHsmSubm subm);
tHsmEvent hsm_get_event(const tHsmInst* hsm);
tHsmSubm hsm_get_inst_sumbmitter(const tHsmInst* hsm);
tHsmStateIndex hsm_get_state(const tHsmInst* hsm, tHsmTreeIndex tree_index, tHsmTreeInst tree_inst);
const tChar* hsm_get_state_name(const tHsmInst* hsm, tHsmTreeIndex tree_index, tHsmStateIndex state_index);

/// HSM Instances

extern const tHsmInst hsm_bluetooth;

enum hsm_bluetooth_TREES_INDICES {
    TREE_BLUETOOTH,
    hsm_bluetooth_TREES_END
};

// TODO: more hsm instances

/// Submitters

enum hsm_SUBMITTERS {
    HSM_FIRST_SUBMITTER_hsm = (0U),
// ----- Task Notify Submitters ----
    SUBMITTER_TnBluetooth,
    SUBMITTER_TnCmd,
    SUBMITTER_TnLin,
    SUBMITTER_TnMotor,
    SUBMITTER_TnTimer,
// ---------------------------------
    SUBMITTER_TnIdle,
// ------ Global Submitters --------
    SUBMITTER_Bluetooth,
    SUBMITTER_Cmd,
    SUBMITTER_Lin,
    SUBMITTER_Motor,
    SUBMITTER_Timer,
// ---------------------------------
    HSM_NR_SUBMITTERS_hsm
};


/// Event Names Tables

static const tChar* const TnBluetooth_event_name_table[] = {
    "Tm10ms",
    ""
};

static const tChar* const Bluetooth_event_name_table[] = {
    ""
};


/// HSM Trees

extern const tHsmTree TREE_BLUETOOTH_struct;
static tHsmStack TREE_BLUETOOTH_stack = {
    {0U}, 0U
};

static tHsmTreeState TREE_BLUETOOTH_state = {
    (tHsmTreeInst)0U,
    ((void *)0),
    (tHsmStateIndex)0U,
    (tHsmStateIndex)0U,
    &TREE_BLUETOOTH_stack
};

const tChar hsm_bluetooth_name[] = {
    "hsm_bluetooth"
};

tHsmTreeState* const hsm_bluetooth_tree_state[hsm_bluetooth_TREES_END] = {
    &TREE_BLUETOOTH_state,
};

tHsmGenCurr hsm_bluetooth_curr = {
    (tHsmEvent)0U,
    (tHsmParamLen)0U,
    (u16)0U,
    (u32)0U,
};

/// Submitter Event Names
const tHsmSubmEvNames hsm_subm_event_names[] = {
    {(tHsmSubm)0U, "", ((void *)0), 0U},
    {(tHsmSubm)SUBMITTER_TnBluetooth, "TnBluetooth", TnBluetooth_event_name_table, (sizeof(TnBluetooth_event_name_table) / sizeof(TnBluetooth_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_TnCmd, "TnCmd", TnCmd_event_name_table, (sizeof(TnCmd_event_name_table) / sizeof(TnCmd_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_TnLin, "TnLin", TnLin_event_name_table, (sizeof(TnLin_event_name_table) / sizeof(TnLin_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_TnMotor, "TnMotor", TnMotor_event_name_table, (sizeof(TnMotor_event_name_table) / sizeof(TnMotor_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_TnTimer, "TnTimer", TnTimer_event_name_table, (sizeof(TnTimer_event_name_table) / sizeof(TnTimer_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_TnIdle, "TnIdle", TnIdle_event_name_table, (sizeof(TnIdle_event_name_table) / sizeof(TnIdle_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_Bluetooth, "Bluetooth", Bluetooth_event_name_table, (sizeof(Bluetooth_event_name_table) / sizeof(Bluetooth_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_Cmd, "Cmd", Cmd_event_name_table, (sizeof(Cmd_event_name_table) / sizeof(Cmd_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_Lin, "Lin", Lin_event_name_table, (sizeof(Lin_event_name_table) / sizeof(Lin_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_Motor, "Motor", Motor_event_name_table, (sizeof(Motor_event_name_table) / sizeof(Motor_event_name_table[0]))},
    {(tHsmSubm)SUBMITTER_Timer, "Timer", Timer_event_name_table, (sizeof(Timer_event_name_table) / sizeof(Timer_event_name_table[0]))},
};

const tHsmTree* const hsm_bluetooth_tree_ptr[(u32)hsm_bluetooth_TREES_END + (u32)1U] = {
    &TREE_BLUETOOTH_struct,
    ((void *)0)
};

const tHsmGen hsm_bluetooth_gen = {
    hsm_bluetooth_tree_state,
    (tHsmTreeIndex)hsm_bluetooth_TREES_END,
    hsm_bluetooth_tree_ptr,
    hsm_subm_event_names,
    (sizeof(hsm_subm_event_names) / sizeof(tHsmSubmEvNames)),
    &hsm_bluetooth_curr
};

const tHsmInst hsm_bluetooth = {
    hsm_bluetooth_name,
    &hsm_bluetooth_gen,
    &hsm_bluetooth
};

enum {
    TnBluetoothDirect = 0,
    TnBluetoothTm10ms,
    TnBluetoothWdgevent=30,
    TnBluetoothLastEvent
};

enum {
    BluetoothDirect = 0,
    BluetoothWdgevent=30,
    BluetoothLastEvent
};

enum {
    StateBluetoothRoot,
    TREE_BLUETOOTH_LAST_STATE
};

static const tChar TREE_BLUETOOTH_name[]= {
    "TREE_BLUETOOTH"
};

static const tChar StateBluetoothRoot_name[]= {
    "StateBluetoothRoot"
};

static const tHsmTrans StateBluetoothRoot_state[] = {
    {(tHsmEvent)((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnBluetooth))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnBluetoothTm10ms) & (u32)0xFFFFU) ) ), (tHsmStateIndex)(tHsmStateIndex)0xFFFFU, (tHsmAction)pod_bluetooth_on_tm10ms},
    {(tHsmEvent)0U, (tHsmStateIndex)0xFFFFU, (tHsmAction)hsm_null_action}
};

static tHsmSubmPermTab TREE_BLUETOOTH_perm_subm[8] = {
    0
    |(u32)(1U<<((tHsmSubm)((((u32)(((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnBluetooth))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnBluetoothTm10ms) & (u32)0xFFFFU) ) ))) >> ((u32)(24U))) & (u32)0xFFU)))
};

static const tHsmState TREE_BLUETOOTH_states[] = {
    {
        StateBluetoothRoot_name,
        (tHsmStateIndex)(tHsmStateIndex)0xFFFFU,
        (tHsmAction)pod_bluetooth_on_init,
        StateBluetoothRoot_state,
        sizeof(StateBluetoothRoot_state)/sizeof(tHsmTrans),
        (tHsmAction)hsm_null_action
    },
};

const tHsmTree TREE_BLUETOOTH_struct = {
    TREE_BLUETOOTH_name,
    TREE_BLUETOOTH_states,
    sizeof(TREE_BLUETOOTH_states)/sizeof(tHsmState),
    TREE_BLUETOOTH_perm_subm
};

/// OSAL

typedef enum {
    TaskTmrSvc,
    TaskBluetooth,
    TaskCmd,
    TaskLin,
    TaskMotor,
    TaskTimer,
    IdleTask,
    TASK_NR
} tTaskId;

typedef enum {
    TmBluetooth10ms,
    TmLin2ms,
    TmMotor10ms,
    TmTimer10ms,
    TmTimer100ms,
    TIMER_NR
} tTimerId;

typedef enum {
    POOL_S,
    POOL_M,
    POOL_L,
    POOL_NR
} tPoolId;

typedef enum {
    QUEUE_PS,
    QUEUE_PM,
    QUEUE_PL,
    QUEUE_BSI2C,
    QueueTaskBluetooth,
    QueueTaskCmd,
    QueueTaskLin,
    QueueTaskMotor,
    QueueTaskTimer,
    QUEUE_NR
} tQueueId;

typedef enum {
    BUFFER_SI2C,
    BUFFER_NR
} tBufferId;

static StackType_t stack_buffer_TaskTmrSvc[( ( ( unsigned short ) 512 ) * 2 )] = { 0U };
static StaticTask_t task_buffer_TaskTmrSvc = {
    0U
};

static void TaskTmrSvc_handler(void *param) __attribute__((alias("default_task_handler")));

static StackType_t stack_buffer_TaskBluetooth[500] = {
    0U
};
static StaticTask_t task_buffer_TaskBluetooth = {
    0U
};
static void TaskBluetooth_handler(void *param) __attribute__((alias("default_task_handler")));

static StackType_t stack_buffer_IdleTask[200] = {
    0U
};
static StaticTask_t task_buffer_IdleTask = {
    0U
};
static void IdleTask_handler(void *param) __attribute__((alias("default_task_handler")));

/// Timer buffer
static StaticTimer_t timer_buffer_TmBluetooth10ms = { 0U };

/// Buffer
static u8 memory_pool_POOL_S[(64)*(32)] = {0U};
static u8 memory_pool_POOL_M[(32)*(64)] = {0U};
static u8 memory_pool_POOL_L[(4)*(256)] = {0U};

/// Queue
static StaticQueue_t queue_buffer_QUEUE_PS = { 0U };
static u8 storage_bufferQUEUE_PS[(64)*(sizeof(void*))] = {0U};

static StaticQueue_t queue_buffer_QUEUE_PM = { 0U };
static u8 storage_bufferQUEUE_PM[(32)*(sizeof(void*))] = {0U};

static StaticQueue_t queue_buffer_QUEUE_PL = { 0U };
static u8 storage_bufferQUEUE_PL[(4)*(sizeof(void*))] = {0U};

static StaticQueue_t queue_buffer_QUEUE_BSI2C = { 0U };
static u8 storage_bufferQUEUE_BSI2C[(32)*(sizeof(tDataPacket))] = {0U};

static u8 buffer_BUFFER_SI2C[1024] = {0U};
static tBufferIndex buffer_index_BUFFER_SI2C = {0U, 0U};

static StaticQueue_t queue_buffer_QueueTaskBluetooth = { 0U };
static u8 storage_bufferQueueTaskBluetooth[(10)*(sizeof(tHsmGenCurr))] = {0U};

/// Tasks

tTaskContext os_task_context[TASK_NR] = {
    {TaskTmrSvc_handler, "TaskTmrSvc", ( ( ( unsigned short ) 512 ) * 2 ), ( ( 7 ) - 1 ), (StackType_t *)(stack_buffer_TaskTmrSvc), &(task_buffer_TaskTmrSvc), 0, 0, ((u32)-1), 0},
    {TaskBluetooth_handler, "TaskBluetooth", 500, 3, (StackType_t *)(stack_buffer_TaskBluetooth), &(task_buffer_TaskBluetooth), 0, &hsm_bluetooth, QueueTaskBluetooth, 6},
    {TaskCmd_handler, "TaskCmd", 500, 3, (StackType_t *)(stack_buffer_TaskCmd), &(task_buffer_TaskCmd), 0, &hsm_cmd, QueueTaskCmd, 6},
    {TaskLin_handler, "TaskLin", 500, 3, (StackType_t *)(stack_buffer_TaskLin), &(task_buffer_TaskLin), 0, &hsm_lin, QueueTaskLin, 6},
    {TaskMotor_handler, "TaskMotor", 500, 3, (StackType_t *)(stack_buffer_TaskMotor), &(task_buffer_TaskMotor), 0, &hsm_motor, QueueTaskMotor, 6},
    {TaskTimer_handler, "TaskTimer", 500, 3, (StackType_t *)(stack_buffer_TaskTimer), &(task_buffer_TaskTimer), 0, &hsm_timer, QueueTaskTimer, 6},
    {IdleTask_handler, "IdleTask", 200, 0, (StackType_t *)(stack_buffer_IdleTask), &(task_buffer_IdleTask), 0, 0, ((u32)-1), 3},
};

tTimerContext os_timer_context[TIMER_NR] = {
    {"TmBluetooth10ms", ( ( TickType_t ) ( ( ( TickType_t ) ( (10) ) * ( TickType_t ) ( 1000 ) ) / ( TickType_t ) 1000U ) ), 1U, (void *)TmBluetooth10ms, os_timer_callback, &(timer_buffer_TmBluetooth10ms), 0, ((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnBluetooth))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnBluetoothTm10ms) & (u32)0xFFFFU) ) )},
    {"TmLin2ms", ( ( TickType_t ) ( ( ( TickType_t ) ( (2) ) * ( TickType_t ) ( 1000 ) ) / ( TickType_t ) 1000U ) ), 1U, (void *)TmLin2ms, os_timer_callback, &(timer_buffer_TmLin2ms), 0, ((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnLin))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnLinTm2ms) & (u32)0xFFFFU) ) )},
    {"TmMotor10ms", ( ( TickType_t ) ( ( ( TickType_t ) ( (10) ) * ( TickType_t ) ( 1000 ) ) / ( TickType_t ) 1000U ) ), 1U, (void *)TmMotor10ms, os_timer_callback, &(timer_buffer_TmMotor10ms), 0, ((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnMotor))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnMotorTm10ms) & (u32)0xFFFFU) ) )},
    {"TmTimer10ms", ( ( TickType_t ) ( ( ( TickType_t ) ( (10) ) * ( TickType_t ) ( 1000 ) ) / ( TickType_t ) 1000U ) ), 1U, (void *)TmTimer10ms, os_timer_callback, &(timer_buffer_TmTimer10ms), 0, ((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnTimer))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnTimerTm10ms) & (u32)0xFFFFU) ) )},
    {"TmTimer100ms", ( ( TickType_t ) ( ( ( TickType_t ) ( (100) ) * ( TickType_t ) ( 1000 ) ) / ( TickType_t ) 1000U ) ), 1U, (void *)TmTimer100ms, os_timer_callback, &(timer_buffer_TmTimer100ms), 0, ((tHsmEvent)( ((((u32)(((tHsmSubm)(SUBMITTER_TnTimer))) & (u32)0xFFU)) << ((u32)24U)) | ((((u32)(0) & (u32)0xFFU)) << ((u32)16U)) | ((u32)(TnTimerTm100ms) & (u32)0xFFFFU) ) )},
};

const tPoolContext os_pool_context[POOL_NR] = {
    {64, 32, memory_pool_POOL_S, memory_pool_POOL_S+(64)*(32), QUEUE_PS},
    {32, 64, memory_pool_POOL_M, memory_pool_POOL_M+(32)*(64), QUEUE_PM},
    {4, 256, memory_pool_POOL_L, memory_pool_POOL_L+(4)*(256), QUEUE_PL},
};

tQueueContext os_queue_context[QUEUE_NR] = {
    {64, sizeof(void*), storage_bufferQUEUE_PS, &(queue_buffer_QUEUE_PS), 0},
    {32, sizeof(void*), storage_bufferQUEUE_PM, &(queue_buffer_QUEUE_PM), 0},
    {4, sizeof(void*), storage_bufferQUEUE_PL, &(queue_buffer_QUEUE_PL), 0},
    {32, sizeof(tDataPacket), storage_bufferQUEUE_BSI2C, &(queue_buffer_QUEUE_BSI2C), 0},
    {10, sizeof(tHsmGenCurr), storage_bufferQueueTaskBluetooth, &(queue_buffer_QueueTaskBluetooth), 0},
    {10, sizeof(tHsmGenCurr), storage_bufferQueueTaskCmd, &(queue_buffer_QueueTaskCmd), 0},
    {10, sizeof(tHsmGenCurr), storage_bufferQueueTaskLin, &(queue_buffer_QueueTaskLin), 0},
    {10, sizeof(tHsmGenCurr), storage_bufferQueueTaskMotor, &(queue_buffer_QueueTaskMotor), 0},
    {10, sizeof(tHsmGenCurr), storage_bufferQueueTaskTimer, &(queue_buffer_QueueTaskTimer), 0},
};

const tBufferContext os_buffer_context[BUFFER_NR] = {
    {80, 1024, buffer_BUFFER_SI2C, &buffer_index_BUFFER_SI2C, QUEUE_BSI2C},
};

static const u8 os_task_tn_list[HSM_NR_SUBMITTERS_hsm] = {
    [((0U))]=TaskTmrSvc,
    [SUBMITTER_TnBluetooth]=TaskBluetooth,
    [SUBMITTER_TnCmd]=TaskCmd,
    [SUBMITTER_TnLin]=TaskLin,
    [SUBMITTER_TnMotor]=TaskMotor,
    [SUBMITTER_TnTimer]=TaskTimer,
    [SUBMITTER_TnIdle]=IdleTask,
};

