/**
 * @file    Stm_Gpt_Hw_Cfg.h
 * @brief   Stm Gpt Cfg Head file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 */

#ifndef STM_GPT_HW_CFG_H
#define STM_GPT_HW_CFG_H


#include "Stm_Gpt_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define STM_GPT_HW_VENDOR_ID_CFG                    (110U)
#define STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_CFG     (4U)
#define STM_GPT_HW_AR_RELEASE_MINOR_VERSION_CFG     (4U)
#define STM_GPT_HW_AR_RELEASE_REVISION_VERSION_CFG  (0U)
#define STM_GPT_HW_SW_MAJOR_VERSION_CFG             (1U)
#define STM_GPT_HW_SW_MINOR_VERSION_CFG             (0U)
#define STM_GPT_HW_SW_PATCH_VERSION_CFG             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

#if (STM_GPT_HW_VENDOR_ID_CFG != STM_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Stm_Gpt_Hw_Cfg.h and Stm_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_CFG != STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (STM_GPT_HW_AR_RELEASE_MINOR_VERSION_CFG != STM_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (STM_GPT_HW_AR_RELEASE_REVISION_VERSION_CFG != STM_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Stm_Gpt_Hw_Cfg.h and Stm_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((STM_GPT_HW_SW_MAJOR_VERSION_CFG != STM_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (STM_GPT_HW_SW_MINOR_VERSION_CFG != STM_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (STM_GPT_HW_SW_PATCH_VERSION_CFG != STM_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Stm_Gpt_Hw_Cfg.h and Stm_Gpt_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
 * @brief Dev error detect switch
 *
 */
#define STM_GPT_HW_DEV_ERROR_DETECT (STD_ON)

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif
/** } */
#endif  /* STM_GPT_HW_CFG_H */
