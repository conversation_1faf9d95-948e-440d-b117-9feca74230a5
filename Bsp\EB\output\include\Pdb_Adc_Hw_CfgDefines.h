/**
 * file    Pdb_Adc_Hw_CfgDefines.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef PDB_ADC_HW_CFGDEFINES_H
#define PDB_ADC_HW_CFGDEFINES_H

/**
*   @file
*
*   @addtogroup pdb_adc_hw_config Pdb Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                         INCLUDE FILES

==================================================================================================*/

#include "pdb_reg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PDB_ADC_HW_VENDOR_ID_CFGDEFINES                      (110u)
#define PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_CFGDEFINES       (4u)
#define PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_CFGDEFINES       (4u)
#define PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_CFGDEFINES    (0u)
#define PDB_ADC_HW_SW_MAJOR_VERSION_CFGDEFINES               (1u)
#define PDB_ADC_HW_SW_MINOR_VERSION_CFGDEFINES               (0u)
#define PDB_ADC_HW_SW_PATCH_VERSION_CFGDEFINES               (0u)
/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/* @brief PDB has  back to back mode ,between insrances or inner instances */
#define FEATURE_PDB_ENABLE_BACKTOBACK     		(STD_OFF)


#define PDB_ADC_HW_DEV_ERROR_DETECT             (STD_OFF)


/*==================================================================================================
*                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PDB_ADC_HW_CFGDEFINES_H */

