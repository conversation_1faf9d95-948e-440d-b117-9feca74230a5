/**
 * @file     LinIf.h
 * @brief    LinIf Software f stubs file 
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb    Initilization
 **/

#ifndef LIN_IF_H
#define LIN_IF_H

/**
*   @file   LinIf.h
*
*   @addtogroup  LINIF
*   @{
*/

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "ComStack_Types.h"
#include "Lin_GeneralTypes.h"
#include "EcuM.h"

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LINIF_VENDOR_ID                      255
#define LINIF_MODULE_ID                      62
#define LINIF_AR_RELEASE_MAJOR_VERSION       4
#define LINIF_AR_RELEASE_MINOR_VERSION       4
#define LINIF_AR_RELEASE_REVISION_VERSION    0
#define LINIF_SW_MAJOR_VERSION               1
#define LINIF_SW_MINOR_VERSION               0
#define LINIF_SW_PATCH_VERSION               0

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/           
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if current file and Lin_GeneralTypes header file are of the same Autosar version */
    #if ((LINIF_AR_RELEASE_MAJOR_VERSION != LIN_GENERALTYPES_AR_RELEASE_MAJOR_VERSION) || \
         (LINIF_AR_RELEASE_MINOR_VERSION != LIN_GENERALTYPES_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of LinIf.h and Lin_GeneralTypes.h are different"
    #endif
    /* Check if current file and EcuM header file are of the same version */
    #if ((LINIF_AR_RELEASE_MAJOR_VERSION != ECUM_AR_RELEASE_MAJOR_VERSION) || \
         (LINIF_AR_RELEASE_MINOR_VERSION != ECUM_AR_RELEASE_MINOR_VERSION) \
        )
        #error "AutoSar Version Numbers of LinIf.h and EcuM.h are different"
    #endif
#endif
/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
#define LINIF_WAKEUP_SUPPORT (STD_ON)

/*==================================================================================================
                                             ENUMS
==================================================================================================*/

/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/
Std_ReturnType LinIf_CheckWakeup(EcuM_WakeupSourceType WakeupSource);

void LinIf_WakeupConfirmation(EcuM_WakeupSourceType WakeupSource);
Std_ReturnType LinIf_HeaderIndication(NetworkHandleType Channel, Lin_PduType* PduPtr);
void LinIf_RxIndication(NetworkHandleType Channel, uint8* Lin_SduPtr);
void LinIf_TxConfirmation(NetworkHandleType Channel);
void LinIf_LinErrorIndication(NetworkHandleType Channel, Lin_SlaveErrorType ErrorStatus);

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_IF_H */
