/**
 * *************************************************************************
 * @file   Ram_Hw_Cfg.h
 * @brief  Code template for Post-Build(PB) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
 
#ifndef RAM_HW_CFG_H
#define RAM_HW_CFG_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Ram_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RAM_HW_CFG_VENDOR_ID                       (110U)
#define RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define RAM_HW_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define RAM_HW_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define RAM_HW_CFG_SW_MAJOR_VERSION                (1U)
#define RAM_HW_CFG_SW_MINOR_VERSION                (0U)
#define RAM_HW_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Ram_Hw_Cfg.h file and Ram_Hw_PBcfg.h file are of the same vendor */
#if (RAM_HW_CFG_VENDOR_ID != RAM_HW_PBCFG_VENDOR_ID)
    #error "Ram_Hw_Cfg.h and Ram_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Ram_Hw_Cfg.h file and Ram_Hw_PBcfg.h file are of the same Autosar version */
#if ((RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION != RAM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (RAM_HW_CFG_AR_RELEASE_MINOR_VERSION != RAM_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (RAM_HW_CFG_AR_RELEASE_REVISION_VERSION != RAM_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Ram_Hw_Cfg.h and Ram_Hw_PBcfg.h are different"
#endif

/* Check if Ram_Hw_Cfg.h file and Ram_Hw_PBcfg.h file are of the same Software version */
#if ((RAM_HW_CFG_SW_MAJOR_VERSION != RAM_HW_PBCFG_SW_MAJOR_VERSION) || \
     (RAM_HW_CFG_SW_MINOR_VERSION != RAM_HW_PBCFG_SW_MINOR_VERSION) || \
     (RAM_HW_CFG_SW_PATCH_VERSION != RAM_HW_PBCFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Ram_Hw_Cfg.h and Ram_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
#define RAM_HW_DEV_ERROR_DETECT         (STD_OFF)

#if (RAM_HW_GET_RAM_STATE_API == STD_ON)
#define RAM_HW_TIMEOUT_TYPE                (OSIF_COUNTER_DUMMY)

#define RAM_HW_TIMEOUT_VALUE_US            (50000U)
#endif

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
                                             ENUMS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */
#endif /* RAM_HW_CFG_H */

