///  pod：timer
name: "timer"

/// prefix: 用来区分模块的前缀，如果不指定，默认用`name`值作为前缀
// 由于C语言没有模块化概念，很容易混淆名称，因此需要在名称前面加前缀区分模块。
// 下面定义的定时器、状态、事件和函数，都会根据规则添加前缀，例如：
// - 定时器：Tm10ms => Timer_Motor_Tm10ms
// - 状态：Idle => State_Motor_Idle
// - 事件：START => EV(TnMotor, START)
// - 广播：G_LOG => EV(Motor, LOG)
// - 函数：on_idle => pod_timer_on_idle
prefix: "timer"

// 定时器的配置。注意，由于变量名不能以数字开头，因此所有的Timer定时器统一用`Tm`开头。
timer Tm10ms {
    period: 10
}

timer Tm100ms {
    period: 100
}

// 状态机配置
tree {
    // 根状态
    state Root {
        entry: on_init
        on {
            // 根据Timer事件转换状态
            Tm10ms: on_tm10ms
            Tm100ms: on_tm100ms
        }
    }
}

