/**
 * file    Adc_Hw_PBcfg.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef ADC_HW_PBCFG_H
#define ADC_HW_PBCFG_H

/**
*   @file
*
*   @addtogroup adc_hw_config Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/
#include "Adc_HwType.h"
#include "Pdb_Adc_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ADC_HW_VENDOR_ID_PBCFG                     (110u)
#define ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG      (4u)
#define ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG      (4u)
#define ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG   (0u)
#define ADC_HW_SW_MAJOR_VERSION_PBCFG              (1u)
#define ADC_HW_SW_MINOR_VERSION_PBCFG              (0u)
#define ADC_HW_SW_PATCH_VERSION_PBCFG              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Adc_Hw_PBcfg.h file and Adc_HwType.h file are of the same vendor */
#if (ADC_HW_VENDOR_ID_PBCFG != ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_Hw_PBcfg.h and Adc_HwType.h have different vendor ids"
#endif

/* Check if Adc_Hw_PBcfg.h file and Adc_HwType.h file are of the same Autosar version */
#if ((ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG != ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG != ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_Hw_PBcfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc_Hw_PBcfg.h file and Adc_HwType.h file are of the same Software version */
#if ((ADC_HW_SW_MAJOR_VERSION_PBCFG != ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_HW_SW_MINOR_VERSION_PBCFG != ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_HW_SW_PATCH_VERSION_PBCFG != ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_Hw_PBcfg.h and Adc_HwType.h are different"
#endif

/* Check if Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same vendor */
#if (ADC_HW_VENDOR_ID_PBCFG != PDB_ADC_HW_VENDOR_ID_TYPES)
    #error "Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h have different vendor ids"
#endif

/* Check if Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same Autosar version */
#if ((ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/* Check if Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same Software version */
#if ((ADC_HW_SW_MAJOR_VERSION_PBCFG != PDB_ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (ADC_HW_SW_MINOR_VERSION_PBCFG != PDB_ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (ADC_HW_SW_PATCH_VERSION_PBCFG != PDB_ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

#define ADC_START_SEC_CODE
#include "Adc_MemMap.h"


#define ADC_STOP_SEC_CODE
#include "Adc_MemMap.h"

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/**
* @brief          ADC Hw Config for Logical ID 0 corresponding to the ADC0 configuration variant .
*/
extern const Adc_Hw_Config_t AdcHwConfig_0;

/**
* @brief          ADC Hw Config for Logical ID 1 corresponding to the ADC1 configuration variant .
*/
extern const Adc_Hw_Config_t AdcHwConfig_1;



#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_HW_PBCFG_H */

