#ifndef UART_HW_CFG_H
#define UART_HW_CFG_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Lpuart_Uart_Hw_PBcfg.h"
#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LPUART_UART_HW_CFG_VENDOR_ID                     (110u)
#define LPUART_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION      (4u)
#define LPUART_UART_HW_CFG_AR_RELEASE_MINOR_VERSION      (4u)
#define LPUART_UART_HW_CFG_AR_RELEASE_REVISION_VERSION   (0u)
#define LPUART_UART_HW_CFG_SW_MAJOR_VERSION              (1u)
#define LPUART_UART_HW_CFG_SW_MINOR_VERSION              (0u)
#define LPUART_UART_HW_CFG_SW_PATCH_VERSION              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Lpuart_Uart_Hw_PBcfg.h */
#if (LPUART_UART_HW_CFG_VENDOR_ID != LPUART_UART_HW_PBCFG_VENDOR_ID)
    #error "Uart_Hw_Cfg.h and Lpuart_Uart_Hw_PBcfg.h have different vendor ids"
#endif
#if ((LPUART_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION    != LPUART_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LPUART_UART_HW_CFG_AR_RELEASE_MINOR_VERSION    != LPUART_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LPUART_UART_HW_CFG_AR_RELEASE_REVISION_VERSION != LPUART_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Uart_Hw_Cfg.h and Lpuart_Uart_Hw_PBcfg.h are different"
#endif
#if ((LPUART_UART_HW_CFG_SW_MAJOR_VERSION != LPUART_UART_HW_PBCFG_SW_MAJOR_VERSION) || \
     (LPUART_UART_HW_CFG_SW_MINOR_VERSION != LPUART_UART_HW_PBCFG_SW_MINOR_VERSION) || \
     (LPUART_UART_HW_CFG_SW_PATCH_VERSION != LPUART_UART_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Uart_Hw_Cfg.h and Lpuart_Uart_Hw_PBcfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((LPUART_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
            (LPUART_UART_HW_CFG_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Uart_Hw_Cfg.h and Std_Types.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/
#define LPUART_UART_HW_CONFIG_EXT \
    LPUART_UART_HW_CONFIG_PB


/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/**
* @brief    Declare callback function if it is used by user
*/

#define UART_START_SEC_CODE
#include "Uart_MemMap.h"

/* Define User callback function */
extern FUNC(void, UART_IPW_CODE) Uart_Ipw_LpuartCallback(CONST(uint8, UART_IPW_CODE) u8HwInstance, CONST(Lpuart_Uart_Hw_Event_t, UART_IPW_CODE) Event, P2VAR(void, UART_IPW_CODE, UART_IPW_CODE) UserData);

#define UART_STOP_SEC_CODE
#include "Uart_MemMap.h"


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LPUART_UART_HW_CFG_H */
