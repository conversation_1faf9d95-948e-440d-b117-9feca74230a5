/// @brief 模块名称
// 由于C语言没有模块化概念，很容易混淆名称，因此需要在名称里加上模块名称来进行区分。
// 下面定义的定时器、状态、事件和函数，都会根据规则添加前缀。
// 例如，假设模块名称为`motor`，则：
// - 定时器：Tm10ms => TmMotor10ms 
// - 状态：Idle => StateMotorIdle
// - 事件：START => EV(TnMotor, START)
// - 广播：G_LOG => EV(Motor, LOG)
// - 函数：on_idle => pod_motor_on_idle
name: "bluetooth"

/// @brief 定时器配置
//  注意，由于变量名不能以数字开头，因此所有的Timer定时器统一用`Tm`开头。
timer Tm10ms {
    period: 10
}

/// @brief 状态机配置
tree {
    // 根状态默认取名为Root，生成的头文件中会显示为State{PodName}Root
    state Root {
        entry: on_init
        on {
            Tm10ms: on_tm10ms
        }
    }
}

