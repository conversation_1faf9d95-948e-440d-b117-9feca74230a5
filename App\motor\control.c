/*
 * @Author: <PERSON><PERSON><PERSON>.Song <EMAIL>
 * @Date: 2025-04-20 13:56:27
 * @LastEditors: FuWei.Song <EMAIL>
 * @LastEditTime: 2025-04-26 11:45:37
 * @FilePath: \SCU001\App\motor\control.c
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
#include "log.h"
#include "pid.h"
#include "encoder.h"
#include "Pwm.h"
#include "Pwm_Cfg.h"
#include "Pwm_Types.h"
#include "motor.h"

#define PWM_DUTY_CYCLE_MIN 0x0000
#define PWM_DUTY_CYCLE_MAX 0x8000

extern tMotor motors[MOTOR_MAX];

void pwm_out_control(tMotor* motor, uint32_t pwm)
{
    uint8 gpmc_id = motor->config.gpmc_id;

    //限制PWM输出范围
    pwm = (pwm > PWM_DUTY_CYCLE_MAX)?PWM_DUTY_CYCLE_MAX:pwm;
    pwm = (pwm < PWM_DUTY_CYCLE_MIN)?PWM_DUTY_CYCLE_MIN:pwm;
    
    if (pwm > 0) {
        motor->driver.pwm = pwm;
    }

    if (motor->driver.dir == DIR_FORWARD)
    {
        Pwm_SetDutyCycle(motor->config.channel_backward, 0);    //设置B通道PWM占空比输出为0
        // z_PWM_SetGpcmCtrl(gpmc_id, DIR_BACKWARD, 4);            //关闭B通道输出
        Pwm_SetDutyCycle(motor->config.channel_forward, pwm);   //设置A通道PWM占空比
        // z_PWM_SetGpcmCtrl(gpmc_id, DIR_FORWARD, (pwm>0)?0:4);   //pwm不为0则使能A通道输出
    }
    else
    {
        Pwm_SetDutyCycle(motor->config.channel_forward, 0);     //设置A通道PWM占空比输出为0
        // z_PWM_SetGpcmCtrl(gpmc_id, DIR_FORWARD, 4);             //关闭A通道输出
        Pwm_SetDutyCycle(motor->config.channel_backward, pwm);  //设置B通道PWM占空比
        // z_PWM_SetGpcmCtrl(gpmc_id, DIR_BACKWARD, (pwm>0)?0:4);  //pwm不为0则使能B通道输出
    }
}

static bool is_over_target(tMotor* motor, float current)
{
    if (current > 0) {
        return motor->encoder.position >= motor->position_pid.target;
    } else {
        return motor->encoder.position <= motor->position_pid.target;
    }
}

static bool is_near_target(tMotor* motor, bool is_forward)
{
    if (is_forward) {
        return motor->encoder.position >= motor->position_pid.target;
    } else {
        return motor->encoder.position <= motor->position_pid.target;
    }
}

static bool is_near_zero(float v)
{
    return fabs(v) < 0.01f;
}

static u32 stopped_count = 0;

void motor_set_current(tMotor* motor, float current)
{
    //通过设置电机PWM占空比来改变电流，达到调速目的

    float abs_current = fabs(current);

    bool is_last_active = motor->driver.pwm > 0.01f;

    if((motor->driver.stall_flag != NO_STALL)
      || ((motor->driver.target_dir == TARGET_DIR_FORWARD) && (motor->encoder.position >= motor->position_pid.target))
      || ((motor->driver.target_dir == TARGET_DIR_BACKWARD) && (motor->encoder.position <= motor->position_pid.target))
    )
    //   || ((motor->encoder.position >= motor->config.range)))
    {
        stopped_count++;
        motor->driver.pwm = 0;
        pid_reset(&motor->speed_pid);
        pid_reset(&motor->position_pid);
        motor_stop(motor);
        // motor_stall_clear(motor);
        pwm_out_control(motor, motor->driver.pwm);
    }
    else
    {
        if((fabs(current) < 1) && (motor->speed_pid.target != 0))
        {
            motor->driver.pwm = motor->driver.pwm;
        }
        else
        {
            motor->driver.pwm = abs_current;
        }

        if (motor->config.openloop) {
            motor->driver.pwm = PWM_DUTY_CYCLE_MAX;
        }

        // motor->driver.dir = current > 0 ? DIR_FORWARD : DIR_BACKWARD;
        pwm_out_control(motor, motor->driver.pwm);
    }

}

void motor_set_speed(tMotor* motor, float speed)
{
    //设置电机目标速度
    float cur_speed = get_speed(motor);
    float cur_position = get_position(motor);
    float tar_position = motor->position_pid.target;

    if (fabs(speed) > motor->config.rated_speed)
    {
        //达到额定速度后额定运行
        speed = speed > 0 ? motor->config.rated_speed: -motor->config.rated_speed;
    }
    motor->speed_pid.target = speed;
}

/* 速度环控制器 */
float speed_controller(tMotor* motor)
{
    pid_loop(&motor->speed_pid, motor->speed_pid.target - get_speed(motor));

    // if (motor->position_pid.target == motor->encoder.position)
    // {
    //     motor->speed_pid.output = 0;
    // } 

    return motor->speed_pid.output;
}

/* 位置环控制器 */
float position_controller(tMotor* motor)
{
    pid_loop(&motor->position_pid, motor->position_pid.target - get_position(motor));

    motor->driver.dir = motor->position_pid.output > 0 ? DIR_FORWARD : DIR_BACKWARD;

    return motor->position_pid.output;
}

void motor_control(void)
{
    static uint32_t cnt[MOTOR_MAX] = 0;
    for (int i = 0; i < MOTOR_MAX; i++)
    {
        // if (motor_is_idle(&motors[i])) {
        //     // stop the motor completely
        //     motors[i].driver.pwm = 0;
        //     pwm_out_control(&motors[i], 0);
        //     pid_reset(&motors[i].speed_pid);
        //     pid_reset(&motors[i].position_pid);
        //     continue;
        // }
        if (motors[i].config.openloop) {
            if (abs(motors[i].position_pid.target - motors[i].encoder.position) <= 1) {
                motors[i].driver.dir = DIR_FORWARD;
                pwm_out_control(&motors[i], 0);
                motor_stop(&motors[i]);
            }
            else if (motors[i].position_pid.target > motors[i].encoder.position)
            {
                motors[i].driver.dir = DIR_FORWARD;
                pwm_out_control(&motors[i], PWM_DUTY_CYCLE_MAX);
            } else if (motors[i].position_pid.target < motors[i].encoder.position)
            {
                motors[i].driver.dir = DIR_BACKWARD;
                pwm_out_control(&motors[i], PWM_DUTY_CYCLE_MAX);
            }
        }
        else
        {
            if((cnt[i]++)%5 == 0)
            {
                cnt[i] = 0;
                //位置环设定目标速度
                motor_set_speed(&motors[i], position_controller(&motors[i]));
            }
            //速度环设定目标电流
            motor_set_current(&motors[i], speed_controller(&motors[i]));
        }
    }
}
