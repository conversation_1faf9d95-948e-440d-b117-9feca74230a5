/**
 * @file     LinIf_Stubs.c
 * @brief    Lin Software C stubs file 
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb    Initilization
 **/

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Mcal.h"
/* #include "check_example.h" */
#include "LinIf.h"


#if (LINIF_WAKEUP_SUPPORT == STD_ON)
/**
* @brief   The LIN Driver or LIN Transceiver Driver will call this function to report the wake up 
*          source after the successful wakeup detection during CheckWakeup or after power on by bus. 
* @details This is a function stub only. 
*     
* @param[in]  WakeupSource - Source device which initiated the wakeup event: LIN controller
*                            or LIN transceiver.
*
* @Requirements
*/
void LinIf_WakeupConfirmation(EcuM_WakeupSourceType WakeupSource)
{
    /* Cast to avoid CW */
    (void)WakeupSource;
    return;
}

/** 
* @brief   Will be called when the EcuM has been notified about a wakeup on a specific LIN channel. 
*
* @details This is a function stub only. 
*     
* @param[in]  WakeupSource - Source device which initiated the wakeup event: LIN controller
*                            or LIN transceiver.
*
* @Requirements
*/
Std_ReturnType LinIf_CheckWakeup(EcuM_WakeupSourceType WakeupSource)
{
    /* Cast to avoid CW */
    (void)WakeupSource;

    return E_OK;
}

/**
* @brief   The LIN Driver will call this function to report a received LIN header. This function is 
*          only applicable for LIN slave nodes (available only if the ECU has any LIN slave channel).
*
* @details This is a function stub only.
*
* @param[in]    Channel  Identification of the LIN channel.
*
* @param[inout] PduPtr   Pointer to PDU providing the received PID and pointer to the SDU data buffer
*                        as in parameter. Upon return, the length, checksum type and frame response 
*                        type are received as out parameter. If the frame response type is 
*                        LIN_FRAMERESPONSE_TX, then the SDU data buffer contains the transmission data.
*
* @return                Std_ReturnType.
* @retval E_OK           Request has been accepted.
* @retval E_NOT_OK       Request has not been accepted, development or production error occurred.
*
* @Requirements
*/
extern void SlaveCallBack(Lin_PduType * PduPtr);
Std_ReturnType LinIf_HeaderIndication(NetworkHandleType Channel, Lin_PduType * PduPtr)
{
    /* Only Used for test */
    SlaveCallBack(PduPtr);

    (void)Channel;
    (void)PduPtr;
    return E_OK;
}

/**
* @brief   The LIN Driver will call this function to report a successfully received response and
*          provides the reception data to the LIN Interface. This function is only applicable for
*          LIN slave nodes (available only if the ECU has any LIN slave channel).
*
* @details This is a function stub only.
*
* @param[in]  Channel    Identification of the LIN channel.
* @param[in]  Lin_SduPtr Pointer to pointer to a shadow buffer or memory mapped LIN Hardware receive
*                        buffer where the current SDU is stored. This pointer is only valid if the 
*                        response is received.
*
* @Requirements
*/
void LinIf_RxIndication(NetworkHandleType Channel, uint8* Lin_SduPtr)
{
    /* Cast to avoid CW */
    (void)Channel;
    (void)Lin_SduPtr;
}

/** 
* @brief   The LIN Driver will call this function to report a successfully transmitted response.
*          This function is only applicable for LIN slave nodes (available only if the ECU has
*          any LIN slave channel).
*
* @details This is a function stub only.
*
* @param[in]  Channel    Identification of the LIN channel.
*
* @Requirements
*/
void LinIf_TxConfirmation(NetworkHandleType Channel)
{
    /* Cast to avoid CW */
    (void)Channel;
}

/**
* @brief   The LIN Driver will call this function to report a detected error event during header
*          or response processing. This function is only applicable for LIN slave nodes (available 
*          only if the ECU has any LIN slave channel).
*
* @details This is a function stub only.
*
* @param[in]  Channel    Identification of the LIN channel.
* @param[in]  ErrorStatus Type of detected error
*
* @Requirements
*/
void LinIf_LinErrorIndication(NetworkHandleType Channel, Lin_SlaveErrorType ErrorStatus)
{
    /* Cast to avoid CW */
    (void)Channel;
    (void)ErrorStatus;
}
#endif

#ifdef __cplusplus
}
#endif

/* End of File */

/** @}*/
