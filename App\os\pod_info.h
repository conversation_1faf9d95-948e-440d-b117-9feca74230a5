/***********************************************************************************************
 * AutoRTOS Configuration Headers. DO NOT EDIT! This file is auto-generated by auto-rtos command
 ***********************************************************************************************/




// ----------------------------------  TASKS ----------------------------------------

TASK_BEGIN
/*the first task is TmrSvc task, please don't move the first task!*/
	TASK(TaskTmrSvc, configTIMER_TASK_STACK_DEPTH, configTIMER_TASK_PRIORITY, 	NULL, 	FIRST, ((tU32)-1), 0)

    TASK(TaskPower, 	500, 	3, 	&hsm_power,		    TnPower,		    QueueTaskPower, 	6)
    TASK(TaskBluetooth, 500, 	3, 	&hsm_bluetooth,		TnBluetooth,		QueueTaskBluetooth,	6)
    TASK(TaskCmd, 	    500, 	3, 	&hsm_cmd,		    TnCmd,		        QueueTaskCmd, 	    6)
    TASK(TaskLin, 	    500, 	3, 	&hsm_lin,		    TnLin,		        QueueTaskLin, 	    6)
    TASK(TaskMotor, 	500, 	3, 	&hsm_motor,		    TnMotor,	    	QueueTaskMotor, 	6)
    TASK(TaskTimer, 	500, 	3, 	&hsm_timer,		    TnTimer,		    QueueTaskTimer, 	6)


	TASK(IdleTask,  	200, 	0,	NULL, 		        TnIdle,	        	((tU32)-1),         3)
/*the last task is IDLE task, please don't move the last task!*/
TASK_END


// ----------------------------------  TIMERS ----------------------------------------

/// Timers definition table
// Duration: msec(10) / sec(1)
// Mode: TM_REPEAT / TM_ONCE
TIMER_BEGIN
// TIMER(TIMER_NAME, 		            Duration,		Mode,    	 	Event                       	)
    TIMER(TmPower10ms, 	                msec(10), 	TM_REPEAT, 	EV(TnPower, Tm10ms))
    TIMER(TmBluetooth10ms, 	            msec(10), 	TM_REPEAT, 	EV(TnBluetooth, Tm10ms))
    TIMER(TmLin2ms, 	                msec(2), 	TM_REPEAT, 	EV(TnLin, Tm2ms))
    TIMER(TmMotor10ms, 	                msec(10), 	TM_REPEAT, 	EV(TnMotor, Tm10ms))

    TIMER(TmMotor1OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once1StopDebouncer))
    TIMER(TmMotor1OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once1IdleDebouncer))

#ifdef SEAT_SIDE_RIGHT
    TIMER(TmMotor2OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once2StopDebouncer))
    TIMER(TmMotor2OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once2IdleDebouncer))

    TIMER(TmMotor3OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once3StopDebouncer))
    TIMER(TmMotor3OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once3IdleDebouncer))
#endif

    TIMER(TmMotor4OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once4StopDebouncer))
    TIMER(TmMotor4OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once4IdleDebouncer))

    TIMER(TmMotor5OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once5StopDebouncer))
    TIMER(TmMotor5OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once5IdleDebouncer))

    TIMER(TmMotor6OnceStopDebouncer, 	msec(20), 	TM_ONCE, 	EV(TnMotor, Once6StopDebouncer))
    TIMER(TmMotor6OnceIdleDebouncer, 	msec(100), 	TM_ONCE, 	EV(TnMotor, Once6IdleDebouncer))
    
    TIMER(TmTimer10ms,              	msec(10), 	TM_REPEAT, 	EV(TnTimer, Tm10ms))
    TIMER(TmTimer100ms, 	            msec(100), 	TM_REPEAT, 	EV(TnTimer, Tm100ms))

TIMER_END

// ----------------------------------  POOLS ----------------------------------------

POOL_BEGIN
POOL(POOL_S, 		64, 		32,     	QUEUE_PS)
POOL(POOL_M, 		32, 		64,			QUEUE_PM)
POOL(POOL_L, 		4, 			256,		QUEUE_PL)
POOL_END

// ----------------------------------  QUEUES ----------------------------------------

QUEUE_BEGIN
QUEUE(QUEUE_PS, 			    64,		sizeof(void*))
QUEUE(QUEUE_PM, 			    32, 	sizeof(void*))
QUEUE(QUEUE_PL, 			    4, 		sizeof(void*))
QUEUE(QUEUE_BSI2C, 			    32, 	sizeof(tDataPacket))
QUEUE(QueueTaskPower, 		        10, 	sizeof(tHsmGenCurr))
QUEUE(QueueTaskBluetooth,           10, 	sizeof(tHsmGenCurr))
QUEUE(QueueTaskCmd, 			    10, 	sizeof(tHsmGenCurr))
QUEUE(QueueTaskLin, 			    10, 	sizeof(tHsmGenCurr))
QUEUE(QueueTaskMotor, 			    10, 	sizeof(tHsmGenCurr))
QUEUE(QueueTaskTimer, 			    10, 	sizeof(tHsmGenCurr))
/*motor_module*/
QUEUE_END

// ----------------------------------  BUFFERS -------------------------------------------

// OS config for buffers

BUFFER_BEGIN
/* buffer name 		max_len  max_bytes_of_item   queue_id_used*/
// Buffer for I2C ?
BUFFER(BUFFER_SI2C, 	80, 		1024,     	QUEUE_BSI2C)

BUFFER_END