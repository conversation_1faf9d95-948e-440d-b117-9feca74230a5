/**
* 
* @file    Port_Hw_PBcfg.c
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>HER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
*
* @section[blobal]
* Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
* Reason: The macro definition names are similar, the version number definition prefixes are the same, 
* and the keywords are different.
*
* @section [global]
* Violates MISRA C 2012 Advisory Rule 11.4, Disables conversion between pointer to object and integer types.
* Reason: Register operations need to force the register base address unsigned integer into a struct pointer 
* to achieve register reading and writing.
*
*/ 

#include "Port_Hw.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PORT_HW_VENDOR_ID_PBCFG_C                          (110U)
#define PORT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C           (4U)
#define PORT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C           (4U)
#define PORT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C        (0U)
#define PORT_HW_SW_MAJOR_VERSION_PBCFG_C                   (1U)
#define PORT_HW_SW_MINOR_VERSION_PBCFG_C                   (0U)
#define PORT_HW_SW_PATCH_VERSION_PBCFG_C                   (0U)


/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if the files Port_Hw_PBcfg.c and Port_Hw.h are of the same vendor */
#if (PORT_HW_VENDOR_ID_PBCFG_C != PORT_HW_VENDOR_ID_H)
    #error "Port_Hw_PBcfg.c and Port_Hw.h have different vendor ids"
#endif
/* Check if the files Port_Hw_PBcfg.c and Port_Hw.h are of the same Autosar version */
#if ((PORT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C    != PORT_HW_AR_RELEASE_MAJOR_VERSION_H)  || \
     (PORT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C    != PORT_HW_AR_RELEASE_MINOR_VERSION_H)  || \
     (PORT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != PORT_HW_AR_RELEASE_REVISION_VERSION_H)  \
    )
    #error "AutoSar Version Numbers of Port_Hw_PBcfg.c and Port_Hw.h are different"
#endif
/* Check if the files Port_Hw_PBcfg.c and Port_Hw.h are of the same software version */
#if ((PORT_HW_SW_MAJOR_VERSION_PBCFG_C != PORT_HW_SW_MAJOR_VERSION_H) || \
     (PORT_HW_SW_MINOR_VERSION_PBCFG_C != PORT_HW_SW_MINOR_VERSION_H) || \
     (PORT_HW_SW_PATCH_VERSION_PBCFG_C != PORT_HW_SW_PATCH_VERSION_H)    \
    )
    #error "Software Version Numbers of Port_Hw_PBcfg.c and Port_Hw.h are different"
#endif


/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/
#define PORT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"

const PortPinSettingsConfig_t g_pin_mux_InitConfigArr[NUM_OF_CONFIGURED_PINS] =
{
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 13,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 0,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 5,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 4,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 1,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 9,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 14,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 13,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 12,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 17,
        .mux                         = PORT_MUX_ALT5,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 0,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 1,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 1,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 11,
        .mux                         = PORT_MUX_ALT4,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 10,
        .mux                         = PORT_MUX_ALT4,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 3,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 2,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 16,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 15,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 9,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 8,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 5,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 4,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 5,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 12,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 11,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 10,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 3,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 2,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 1,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 2,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 6,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 15,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 16,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 0,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 17,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 16,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 15,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 14,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 3,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 7,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 6,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 16,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 15,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 14,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 13,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 2,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)GPIOC_BASE,
        .pinPortIdx                  = 3,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 6,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 17,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 9,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)GPIOC_BASE,
        .pinPortIdx                  = 7,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 14,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 14,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)GPIOC_BASE,
        .pinPortIdx                  = 2,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 8,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 12,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 8,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)GPIOC_BASE,
        .pinPortIdx                  = 10,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 3,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 7,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 17,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTD_BASE,
        .gpioBase                    = (GpioType_t *)GPIOD_BASE,
        .pinPortIdx                  = 4,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 1,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)GPIOC_BASE,
        .pinPortIdx                  = 11,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_HIGH
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 13,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 12,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 13,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 12,
        .mux                         = PORT_MUX_ALT2,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 9,
        .mux                         = PORT_MUX_ALT4,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 8,
        .mux                         = PORT_MUX_ALT4,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 11,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTB_BASE,
        .gpioBase                    = (GpioType_t *)GPIOB_BASE,
        .pinPortIdx                  = 10,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 0,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_UP_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTC_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 6,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = TRUE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 8,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *)GPIOA_BASE,
        .pinPortIdx                  = 9,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 16,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)GPIOE_BASE,
        .pinPortIdx                  = 15,
        .mux                         = PORT_MUX_AS_GPIO,
        .direction                   = PORT_HW_PORT_PIN_OUT,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE,
        .initValue                   = GPIO_OUTPUT_LOW
    },
    {
        .portBase                    = (PortType_t *)PORTE_BASE,
        .gpioBase                    = (GpioType_t *)NULL_PTR,
        .pinPortIdx                  = 0,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    },
    {
        .portBase                    = (PortType_t *)PORTA_BASE,
        .gpioBase                    = (GpioType_t *) NULL_PTR,
        .pinPortIdx                  = 11,
        .mux                         = PORT_PIN_DISABLED,
        .direction                   = PORT_HW_PORT_PIN_IN,
        .pullConfig                  = PORT_INTERNAL_PULL_NOT_ENABLED,
        .PowerLevel                  = PORT_DS0,
        .passiveFilter               = FALSE,
        .LowPowerState               = PORT_KEEP_CONFIG,
        .PortRetm                    = PORT_EXT_RTE,
        .enOutputMode                = PORT_PIN_OUT_PUSHUPULL,
        .IntputControl               = SCHMITT,
        .InputEn                     = FALSE,
        .digitalFilter               = FALSE
    }
};

#define PORT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"

/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */
