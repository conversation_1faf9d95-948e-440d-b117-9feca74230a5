/**
 * *************************************************************************
 * @file   Mcu_Cfg.c
 * @brief  Code template for Post-Compile(PC) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * SWS_Mcu_00233,SWS_Mcu_00236,SWS_Mcu_00238,SWS_Mcu_00240
 * SWS_Mcu_00152
 * *************************************************************************/
 
/*==================================================================================================
*                                          INCLUDE FILES

==================================================================================================*/
#include "Mcu.h"
#include "Mcu_Module_Config.h"
#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
#if (!defined(MCU_MODULE_DEM_NOUSE) || (MCU_MODULE_DEM_NOUSE == STD_OFF))
#include "Dem.h"
#endif
#endif /* Note-(MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF) */

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define MCU_CFG_VENDOR_ID_C                       (110U)
#define MCU_CFG_AR_RELEASE_MAJOR_VERSION_C        (4U)
#define MCU_CFG_AR_RELEASE_MINOR_VERSION_C        (4U)
#define MCU_CFG_AR_RELEASE_REVISION_VERSION_C     (0U)
#define MCU_CFG_SW_MAJOR_VERSION_C                (1U)
#define MCU_CFG_SW_MINOR_VERSION_C                (0U)
#define MCU_CFG_SW_PATCH_VERSION_C                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Mcu_Cfg.c file and Dem.h file are of the same Autosar version */
#if ((MCU_CFG_AR_RELEASE_MAJOR_VERSION_C != DEM_AR_RELEASE_MAJOR_VERSION) || \
     (MCU_CFG_AR_RELEASE_MINOR_VERSION_C != DEM_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Mcu_Cfg.c and Dem.h are different"
#endif
#endif
#endif /* Note-(MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF) */

/* Check if Mcu_Cfg.c file and Mcu.h file are of the same vendor */
#if (MCU_CFG_VENDOR_ID_C != MCU_VENDOR_ID)
    #error "Mcu_Cfg.c and Mcu.h have different vendor ids"
#endif

/* Check if Mcu_Cfg.c file and Mcu.h file are of the same Autosar version */
#if ((MCU_CFG_AR_RELEASE_MAJOR_VERSION_C != MCU_AR_RELEASE_MAJOR_VERSION) || \
     (MCU_CFG_AR_RELEASE_MINOR_VERSION_C != MCU_AR_RELEASE_MINOR_VERSION) || \
     (MCU_CFG_AR_RELEASE_REVISION_VERSION_C != MCU_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Mcu_Cfg.c and Mcu.h are different"
#endif

/* Check if Mcu_Cfg.c file and Mcu.h file are of the same Software version */
#if ((MCU_CFG_SW_MAJOR_VERSION_C != MCU_SW_MAJOR_VERSION) || \
     (MCU_CFG_SW_MINOR_VERSION_C != MCU_SW_MINOR_VERSION) || \
     (MCU_CFG_SW_PATCH_VERSION_C != MCU_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Mcu_Cfg.c and Mcu.h are different"
#endif
/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL VARIABLES
==================================================================================================*/
#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"







#if (MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
/**
* @brief        DEM error reporting configuration
* @details      Set the state and id for Mcal_DemErrorType.
*/
const Mcu_DemConfigType Mcu_DemConfig =
{

    /* DEM error reporting MCU_E_TIMEOUT_FAILURE */
    {(uint32)STD_OFF, 0U},

    /* DEM error reporting MCU_E_INVALIDFXOSC_CONFIG */
    {(uint32)STD_OFF, 0U},

    /* DEM error reporting MCU_E_CLOCKMUXSWITCH_FAILURE */
    {(uint32)STD_OFF, 0U},

    /* DEM error reporting MCU_E_CLOCK_FAILURE */
    {(uint32)STD_OFF, 0U},

    /* DEM error reporting MCU_E_SWITCHMODE_FAILURE */
    {(uint32)STD_OFF, 0U}

};
#endif /* Note-(MCU_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF) */



#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"


/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/
/**
* @brief            The callout configured by the user for CMU notifications.
*/



/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

