/*
 * @Author: <PERSON><PERSON><PERSON>.Song <EMAIL>
 * @Date: 2025-04-20 13:56:27
 * @LastEditors: <PERSON><PERSON><PERSON>.Song <EMAIL>
 * @LastEditTime: 2025-04-22 19:57:12
 * @FilePath: \SCU001\App\key.c
 * @Description: 
 * 
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved. 
 */
#include <stdint.h>
#include <stdbool.h>
#include "key.h"
#include "Pwm.h"
#include "log.h"
#include "os_module.h"
#include "pod_motor.h"

Key_Status_t key_status[KEY_SUM_NUMBLE], key_status_old[KEY_SUM_NUMBLE];

uint16_t keyAdcValueRange[KEY_ADC_PORT_NUMBLE][2] = {
    {KEY_ADC_VALUE_A_MIN, KEY_ADC_VALUE_A_MAX},
    {KEY_ADC_VALUE_B_MIN, K<PERSON><PERSON>_ADC_VALUE_B_MAX},
    {KEY_ADC_VALUE_C_MIN, KEY_ADC_VALUE_C_MAX},
    {KEY_ADC_VALUE_D_MIN, KEY_ADC_VALUE_D_MAX}
};

uint16_t key5AdcValueRange[2][2] = {
    {KEY_ADC_VALUE_51_MIN, KEY_ADC_VALUE_51_MAX},
    {KEY_ADC_VALUE_52_MIN, KEY_ADC_VALUE_52_MAX}
};

static void Key_Pressed_Callback(uint8_t keyId)
{
    // TODO: should use TnCmd instead of Cmd Event
    os_submit_event_u16_from_isr(EV(Cmd, KeyEV), keyId, KEY_OPTION_PRESSED);
}

static void Key_Released_Callback(uint8_t keyId) {
    // TODO: should use TnCmd instead of Cmd Event
    os_submit_event_u16_from_isr(EV(Cmd, KeyEV), keyId, KEY_OPTION_RELEASED);
}

void Key_ProcessHandle(uint32_t *keyAdcVal)
{
  uint8_t keyId = 0;
#ifdef USE_DEBUG_KEYS
  for(int i=0; i<KEY_ADC_PORT_NUMBLE; i++)
  {
    for(int j=0; j<KEY_ONE_ADC_KEY_NUMBLE; j++)
    {
      keyId = i * KEY_ONE_ADC_KEY_NUMBLE + j;
      if ((keyAdcVal[i] > keyAdcValueRange[j][0]) && (keyAdcVal[i] < keyAdcValueRange[j][1]))
      {
        key_status[keyId].key_press_cnt++;
        if (key_status[keyId].key_press_cnt >= 254)
        {
            key_status[keyId].key_press_cnt = 254;
        }
        
        if (key_status[keyId].key_press_cnt == KEY_SHORT_PRESS_TIME)
        {
            key_status[keyId].key_event = KEY_EVENT_PRESSED;
            key_status[keyId].key_state = KEY_STATE_PRESSED;

            Key_Pressed_Callback(keyId);
        }
        else if (key_status[keyId].key_press_cnt == KEY_LONG_PRESS_TIME)
        {
            key_status[keyId].key_event = KEY_EVENT_LONG_PRESSED;
            key_status[keyId].key_state = KEY_STATE_LONG_PRESSED;
        }
        else
        {
           key_status[keyId].key_event = KEY_EVENT_NONE;
        }
      }
      else
      {
        if(key_status_old[keyId].key_state == KEY_STATE_PRESSED)
        {
            key_status[keyId].key_event = KEY_EVENT_RELEASED;
            Key_Released_Callback(keyId);
        }           
        else if(key_status_old[keyId].key_state == KEY_STATE_LONG_PRESSED)
        {
            key_status[keyId].key_event = KEY_EVENT_LONG_RELEASED;
            Key_Released_Callback(keyId);
        }
        else
        {
            key_status[keyId].key_event = KEY_EVENT_NONE;
        }
        key_status[keyId].key_state = KEY_STATE_RELEASED;
        key_status[keyId].key_press_cnt = 0;
      }
      key_status_old[keyId].key_state = key_status[keyId].key_state;
    }
  }
#else
  keyId = KEY_ID_ZERO_GRAVITY_NEG - 1;
#endif

  for(int i = 0; i < 2; i++)
  {
    keyId += 1;
    if ((keyAdcVal[4] > key5AdcValueRange[i][0]) && (keyAdcVal[4] < key5AdcValueRange[i][1]))
    {
      key_status[keyId].key_press_cnt++;
      if (key_status[keyId].key_press_cnt >= 254)
      {
          key_status[keyId].key_press_cnt = 254;
      }
      
      if (key_status[keyId].key_press_cnt == KEY_SHORT_PRESS_TIME)
      {
          key_status[keyId].key_event = KEY_EVENT_PRESSED;
          key_status[keyId].key_state = KEY_STATE_PRESSED;

          Key_Pressed_Callback(keyId);
      }
      else if (key_status[keyId].key_press_cnt == KEY_LONG_PRESS_TIME)
      {
          key_status[keyId].key_event = KEY_EVENT_LONG_PRESSED;
          key_status[keyId].key_state = KEY_STATE_LONG_PRESSED;
      }
      else
      {
          key_status[keyId].key_event = KEY_EVENT_NONE;
      }
    }
    else
    {
      if(key_status_old[keyId].key_state == KEY_STATE_PRESSED)
      {
          key_status[keyId].key_event = KEY_EVENT_RELEASED;
      }           
      else if(key_status_old[keyId].key_state == KEY_STATE_LONG_PRESSED)
      {
          key_status[keyId].key_event = KEY_EVENT_LONG_RELEASED;
      }
      else
      {
          key_status[keyId].key_event = KEY_EVENT_NONE;
      }
      key_status[keyId].key_state = KEY_STATE_RELEASED;
      key_status[keyId].key_press_cnt = 0;
    }
  }
}

void testPwmHandle()
{
  static bool enterFlag = FALSE;
  static long int pwmVal = 0;
  if((key_status[0].key_state == KEY_STATE_PRESSED) || (key_status[1].key_state == KEY_STATE_PRESSED))
  {
    if(enterFlag == FALSE)
    {
      enterFlag = TRUE;
      if(key_status[0].key_state == KEY_STATE_PRESSED)
      {
        pwmVal += 0x400;
        if (pwmVal >= 0x8000)
        {
          pwmVal = 0x8000;
        }        
      }
      else if(key_status[1].key_state == KEY_STATE_PRESSED)
      {
        pwmVal -= 0x400;
        if (pwmVal <= -0x8000)
        {
          pwmVal = 0x8000;
        }
      }
      
      if(pwmVal>0)
      {
        Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A,pwmVal);
        if(pwmVal != 0)
        {
          // z_PWM_SetGpcmCtrl(0, 0, 0);
        }
        Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B,0);
        // z_PWM_SetGpcmCtrl(0, 1, 4);
      }
      else
      {
        Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A,0);
        // z_PWM_SetGpcmCtrl(0, 0, 4);
        Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B,-pwmVal);
        if(pwmVal != 0)
        {
          // z_PWM_SetGpcmCtrl(0, 1, 0);
        }
      }
    }
  }
  else
  {
    enterFlag = FALSE;
  }
}

