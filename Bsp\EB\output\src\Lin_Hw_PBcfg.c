/**
 * @file     Lin_Hw_PBcfg.c
 * @brief    Lin_Hw_PBcfg Software c files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 * 
 * section [global]
 * Violates MISRA C 2012 Advisory Rule 5.4, The macro definition name cannot be changed
 * reason: Macro definitions have similar names, version number definitions have the same prefix, but different keywords.
 * 
 * section [global]
 * Violates MISRA C 2012 Dir-1.1-C99 [L] Number of macro definitions exceeds 4095 - program does not conform strictly to ISO C99.
 * Reason: include this file is needed.
 **/


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/

#include "Lin_Hw_Cfg.h"
#include "Lin_Defines.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LIN_HW_PBCFG_VENDOR_ID_C                     (110u)
#define LIN_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define LIN_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define LIN_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define LIN_HW_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define LIN_HW_PBCFG_SW_MINOR_VERSION_C              (0u)
#define LIN_HW_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Lin_Hw_Cfg.h */
#if (LIN_HW_PBCFG_VENDOR_ID_C != LIN_HW_CFG_VENDOR_ID)
    #error "Lin_Hw_PBcfg.c and Lin_Hw_Cfg.h have different vendor ids"
#endif
/* Check if current file and Lin_Hw_Cfg header file are of the same Autosar version */
#if ((LIN_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_HW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Lin_Hw_PBcfg.c and Lin_Hw_Cfg.h are different"
#endif
/* Check if current file and Lin_Hw_Cfg header file are of the same Software version */
#if ((LIN_HW_PBCFG_SW_MAJOR_VERSION_C != LIN_HW_CFG_SW_MAJOR_VERSION) || \
     (LIN_HW_PBCFG_SW_MINOR_VERSION_C != LIN_HW_CFG_SW_MINOR_VERSION) || \
     (LIN_HW_PBCFG_SW_PATCH_VERSION_C != LIN_HW_CFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Hw_PBcfg.c and Lin_Hw_Cfg.h are different"
#endif

/* Checks against Lin_Defines.h */
#if (LIN_HW_PBCFG_VENDOR_ID_C != LIN_DEFINES_VENDOR_ID)
    #error "Lin_Hw_PBcfg.c and Lin_Defines.h have different vendor ids"
#endif
/* Check if current file and Lin_Defines header file are of the same Autosar version */
#if ((LIN_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_DEFINES_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_DEFINES_AR_RELEASE_MINOR_VERSION) || \
     (LIN_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_DEFINES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Lin_Hw_PBcfg.c and Lin_Defines.h are different"
#endif
/* Check if current file and Lin_Defines header file are of the same Software version */
#if ((LIN_HW_PBCFG_SW_MAJOR_VERSION_C != LIN_DEFINES_SW_MAJOR_VERSION) || \
     (LIN_HW_PBCFG_SW_MINOR_VERSION_C != LIN_DEFINES_SW_MINOR_VERSION) || \
     (LIN_HW_PBCFG_SW_PATCH_VERSION_C != LIN_DEFINES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Hw_PBcfg.c and Lin_Defines.h are different"
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      BUFFER DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                   STATE STRUCTURE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/



#define LIN_START_SEC_VAR_CLEARED_UNSPECIFIED
#include "Lin_MemMap.h"

extern LinHwStateStruct_t g_astLinHwStateStructure[LIN_HW_MAX_AVAILABLE_MODULES];

#define LIN_STOP_SEC_VAR_CLEARED_UNSPECIFIED
#include "Lin_MemMap.h"



#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/**
* @brief          Hardware configuration for Lin Hardware Instance LIN_HW_1 - Configuration:
*
*
*/
const LinHwUserConfig_t Lin_Hw_pHwConfigPB_0 =
{

    1U, /*!< Hardware instance */

    0U, /* lin channel id */

    0x104U, /*!< Baudrate divisor */

    LIN_HW_MASTER, /*!< Master node */

    (boolean)FALSE, /*!< Disable slave mode autobaud feature */

    LIN_HW_CLASSIC_CS, /*!< Checksum mode */

    LIN_INTERRUPT_MODE, /*!< Transfer mode */

    (LinHwCallback_t)Lin_Ipw_Callback, /*!< Callback function to invoke after receiving a byte or transmitting a byte. */

    &g_astLinHwStateStructure[0U], /*!< Runtime state structure refference/ */

    2292U, /*!< HeaderTimeoutValue in microseconds */

    73U, /*!< ResponseTimeoutValue in microseconds for 1 byte */

    80000000U /*!< Channel clock */
};

/**
* @brief          Hardware configuration for Lin Hardware Instance LIN_HW_2 - Configuration:
*
*
*/
const LinHwUserConfig_t Lin_Hw_pHwConfigPB_1 =
{

    2U, /*!< Hardware instance */

    1U, /* lin channel id */

    0x104U, /*!< Baudrate divisor */

    LIN_HW_MASTER, /*!< Master node */

    (boolean)FALSE, /*!< Disable slave mode autobaud feature */

    LIN_HW_CLASSIC_CS, /*!< Checksum mode */

    LIN_INTERRUPT_MODE, /*!< Transfer mode */

    (LinHwCallback_t)Lin_Ipw_Callback, /*!< Callback function to invoke after receiving a byte or transmitting a byte. */

    &g_astLinHwStateStructure[1U], /*!< Runtime state structure refference/ */

    2292U, /*!< HeaderTimeoutValue in microseconds */

    73U, /*!< ResponseTimeoutValue in microseconds for 1 byte */

    80000000U /*!< Channel clock */
};

#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */
