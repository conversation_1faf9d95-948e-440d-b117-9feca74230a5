/**
 * file    DMA_PBCfg.c
 * brief   dma PBcfg file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Cfg.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
* @page misra_violations MISRA-C:2012 violations
**/

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define DMA_PBCFG_VENDOR_ID_C                     (110U)
#define DMA_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4U)
#define DMA_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4U)
#define DMA_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0U)
#define DMA_PBCFG_SW_MAJOR_VERSION_C              (1U)
#define DMA_PBCFG_SW_MINOR_VERSION_C              (0U)
#define DMA_PBCFG_SW_PATCH_VERSION_C              (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if Dma_PBcfg.c file and DMA_Cfg.h file are of the same vendor */
#if (DMA_PBCFG_VENDOR_ID_C != DMA_CFG_VENDOR_ID)
    #error "Dma_PBcfg.c and DMA_Cfg.h have different vendor ids"
#endif

/* Check if Dma_PBcfg.c file and DMA_Cfg.h file are of the same Autosar version */
#if ((DMA_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DMA_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_PBCFG_AR_RELEASE_MINOR_VERSION_C != DMA_CFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_PBCFG_AR_RELEASE_REVISION_VERSION_C != DMA_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_PBcfg.c and DMA_Cfg.h are different"
#endif

/* Check if Dma_PBcfg.c file and DMA_Cfg.h file are of the same Software version */
#if ((DMA_PBCFG_SW_MAJOR_VERSION_C != DMA_CFG_SW_MAJOR_VERSION) || \
     (DMA_PBCFG_SW_MINOR_VERSION_C != DMA_CFG_SW_MINOR_VERSION) || \
     (DMA_PBCFG_SW_PATCH_VERSION_C != DMA_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_PBcfg.c and DMA_Cfg.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                      LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"

const DMA_ConfigType Dma_Config =
{
    &stDmaHwxDmaInitPB,

};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
 /* @section [global]
 * 5087 ++
 * This attribute syntax is a language extension.
 * REASON: Variables and text need to be placed in the specified location
 */
#include "Dma_MemMap.h"
/* 5087 -- */

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
