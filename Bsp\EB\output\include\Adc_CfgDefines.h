/**
 * file    Adc_CfgDefines.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_CFGDEFINES_H
#define ADC_CFGDEFINES_H
/**
*   @file
*
*   @addtogroup adc_driver_config Adc Driver Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define ADC_VENDOR_ID_CFGDEFINES                      (110u)
#define ADC_AR_RELEASE_MAJOR_VERSION_CFGDEFINES       (4u)
#define ADC_AR_RELEASE_MINOR_VERSION_CFGDEFINES       (4u)
#define ADC_AR_RELEASE_REVISION_VERSION_CFGDEFINES    (0u)
#define ADC_SW_MAJOR_VERSION_CFGDEFINES               (1u)
#define ADC_SW_MINOR_VERSION_CFGDEFINES               (0u)
#define ADC_SW_PATCH_VERSION_CFGDEFINES               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/**
* @brief          Specifies if Current channel is used
*/
#define ADC_CURRENT_CHANNEL_USED

/**
* @brief          Multicore feature is disabled on this platform.
*/
#define ADC_MULTICORE_SUPPORT               (STD_OFF)

/**
* @brief          Number of configured partitions.
*/
#define ADC_MAX_PARTITIONS                  (1U)

/**
* @brief           Adds/removes ((STD_ON)/(STD_OFF)) the services Adc_EnableHardwareTrigger() and Adc_DisableHardwareTrigger() from the code.
*/
#define ADC_HW_TRIGGER_API                  (STD_ON)

/**
* @brief           AdcSetHwUnitPowerMode api enabled/disabled ((STD_ON)/(STD_OFF)).
*/
/* #define ADC_SET_HW_UNIT_POWER_MODE_API                    (STD_OFF). */

/**
* @brief           Enables or disables limit checking feature in the ADC driver.
*/
#define ADC_ENABLE_LIMIT_CHECK              (STD_OFF)

/**
* @brief           Group notification mechanism is/is not ((STD_ON)/(STD_OFF)) available at runtime.
*/
#define ADC_GRP_NOTIF_CAPABILITY            (STD_ON)

/**
* @brief           Type of prioritization mechanism ADC_PRIORITY_HW, ADC_PRIORITY_HW_SW and ADC_PRIORITY_NONE.
*/
/* SWS_Adc_00522 */
#define ADC_PRIORITY_HW                     (0U)
#define ADC_PRIORITY_HW_SW                  (1U)
#define ADC_PRIORITY_NONE                   (2U)

/**
* @brief           Priority implementation.
*/
#define ADC_PRIORITY_IMPLEMENTATION         (ADC_PRIORITY_HW_SW)

/**
* @brief           Type of alignment mechanism ADC_ALIGN_RIGHT, ADC_ALIGN_LEFT.
*/
/* SWS_Adc_00525 */
#define ADC_ALIGN_RIGHT                     (0U)
#define ADC_ALIGN_LEFT                      (1U)



/**
* @brief         This is a timeout value which is used to wait until
*                  - the conversion is not aborted.
*                  - ADC hardware is not entered in power down state.
*                  - ADC hardware is not entered in idle state.
*/
#define ADC_TIMEOUT_COUNTER                 (100000UL)



/**
* @brief           Specifies to configure BCTU/CTU list at initialization for ctu hardware trigger mode groups.
*/
#define ADC_CTU_HW_TRIGGER_OPTIMIZATION     (STD_OFF)

/**
* @brief           Active DMA.
*/
#define ADC_DMA_SUPPORTED

/**
* @brief           Active DMA transfer notification.
*/

/**
* @brief           Add/Remove Autosar Extension Adc_EnableChannel() and Adc_DisableChannel() api.
*/
/* #define ADC_ENABLE_CH_DISABLE_CH_NONAUTO_API      (STD_OFF). */

/**
* @brief           Get injected conversion status api enabled/disabled (STD_ON/STD_OFF).
*/
/* #define ADC_GET_INJECTED_CONVERSION_STATUS_API    (STD_OFF). */

/**
 * @brief   Define the number of maximum hardware unit configured
 */
#define ADC_MAX_HW_UNITS_CFG                ADC_IPW_MAX_HW_UNITS_CFG

/**
* @brief           Adc transfer type.
*/
#ifdef ADC_DMA_SUPPORTED
#define ADC_DMA                             (0U)
#endif /* ADC_DMA_SUPPORTED */
#define ADC_INTERRUPT                       (1U)


/**
* @brief          Add/remove ((STD_ON)/(STD_OFF)) the service Adc_Calibration() from the code.
*/
#define ADC_CALIBRATION                           (STD_ON)


/**
* @brief IRQ definition
*/

#define ADC_UNIT_0_ISR_USED

#define ADC_UNIT_1_ISR_USED

/**
* @brief End Of Chain Notification
*/

/**
* @brief           max queue depth configured across all configset.
*/
#define ADC_QUEUE_MAX_DEPTH_MAX             (1U)

/**
* @brief           max number of groups configured across all configset.
*/
#define ADC_MAX_GROUPS                      (2U)

/**
* @brief           Invalid Hardware group ID to determine there is no ongoing hardware group
*/
#define ADC_INVALID_HW_GROUP_ID             (0xFFFFU)

#ifdef ADC_DMA_SUPPORTED
/**
* @brief           Maximum number of channels across all hardware units.
*/
#define ADC_MAX_CHANNEL_PER_HW_UNIT         (10U)
#endif /* ADC_DMA_SUPPORTED */


/**
* @brief          Configuration Precompile variant.
* @details        Configuration Precompile variant.
*/
#define ADC_PRECOMPILE_SUPPORT                    (STD_OFF)

/**
* @brief          Development error detection enabled/disabled ((STD_ON)/(STD_OFF)).
*/
#define ADC_DEV_ERROR_DETECT                      (STD_OFF)

/**
* @brief          Add/remove ((STD_ON)/(STD_OFF)) the service Adc_GetVersionInfo() from the code.
*/
#define ADC_VERSION_INFO_API                      (STD_ON)

/**
* @brief          Add/remove ((STD_ON)/(STD_OFF)) the service Adc_DeInit() from the code.
*/
#define ADC_DEINIT_API                            (STD_ON)

/**
* @brief          Add/remove ((STD_ON)/(STD_OFF)) the services Adc_StartGroupConversion()and Adc_StopGroupConversion() from the code.
*/
#define ADC_ENABLE_START_STOP_GROUP_API           (STD_ON)

/**
* @brief          Add/Remove the services Adc_ReadGroup() from the code.
*/
#define ADC_READ_GROUP_API                        (STD_ON)

/**
* @brief          Add/Remove One time setting of Conversion time registers from Init() function.
*/
#define ADC_SET_ADC_CONV_TIME_ONCE                (STD_ON)


/**
* @brief           This switch is used to enable the queue.
*/
#define ADC_ENABLE_QUEUING                        (STD_ON)

/**
* @brief           Symbolic names of ADC Hardware units.
* @details         Values generated are the ADC Logical Unit ID selected from configurator.
*                  These defines are recommended to be used with any ADC driver API that takes as input parameter Adc Unit.
*/
#define AdcHwUnit_0                               (0U)
#define AdcHwUnit_1                               (1U)

/**
* @brief          Adc channel id.
*/
#define ADC_ID_CHANNEL_U8(Id)                     (Id)

/**
* @brief          macros to simplify access to structures.
* @details        Streaming number of samples
*/
#define ADC_STREAMING_NUM_SAMPLES(num)            (num)

/**
* @brief          Adc group priority.
*/
#define ADC_GROUP_PRIORITY(Priority)              (Priority)

/**
* @brief           AUTOSAR Symbolic names of channels on all HW units with encoded value.
* @details         Bit fields [12-15]:  HW unit logical id
*                  Bit fields [0-11]:   Logical id of channel in HW unit (used in Adc_EnableChannel()/Adc_DisableChannel())
*/
#define AdcChannel_KL30                       (0x0U)
#define AdcChannel_NTC1                       (0x1U)
#define AdcChannel_NTC2                       (0x2U)
#define AdcChannel_NTC3                       (0x3U)
#define AdcChannel_NTC4                       (0x4U)
#define AdcChannel_NTC5                       (0x5U)
#define AdcChannel_NTC6                       (0x6U)
#define AdcChannel_IPROPI1                       (0x7U)
#define AdcChannel_IPROPI2                       (0x8U)
#define AdcChannel_NTC_DET                       (0x9U)
#define AdcChannel_KEY1                       (0x1000U)
#define AdcChannel_KEY2                       (0x1001U)
#define AdcChannel_KEY3                       (0x1002U)
#define AdcChannel_KEY4                       (0x1003U)
#define AdcChannel_IMPORTI3                       (0x1004U)
#define AdcChannel_IMPORTI4                       (0x1005U)
#define AdcChannel_IMPORTI5                       (0x1006U)
#define AdcChannel_IMPORTI6                       (0x1007U)
#define AdcChannel_Key5                       (0x1008U)


/**
* @brief           Autosar Extension symbolic names of channels per groups, on all HW units, with value set to channel index in the group.
*/
#define Adc0_HardOneShot_AdcChannel_KL30                   (0U)
#define Adc0_HardOneShot_AdcChannel_NTC1                   (1U)
#define Adc0_HardOneShot_AdcChannel_NTC2                   (2U)
#define Adc0_HardOneShot_AdcChannel_NTC3                   (3U)
#define Adc0_HardOneShot_AdcChannel_NTC4                   (4U)
#define Adc0_HardOneShot_AdcChannel_NTC5                   (5U)
#define Adc0_HardOneShot_AdcChannel_NTC6                   (6U)
#define Adc0_HardOneShot_AdcChannel_IPROPI1                   (7U)
#define Adc0_HardOneShot_AdcChannel_IPROPI2                   (8U)
#define Adc0_HardOneShot_AdcChannel_NTC_DET                   (9U)
#define Adc1_HardOneShot_AdcChannel_KEY1                   (0U)
#define Adc1_HardOneShot_AdcChannel_KEY2                   (1U)
#define Adc1_HardOneShot_AdcChannel_KEY3                   (2U)
#define Adc1_HardOneShot_AdcChannel_KEY4                   (3U)
#define Adc1_HardOneShot_AdcChannel_Key5                   (4U)
#define Adc1_HardOneShot_AdcChannel_IMPORTI3                   (5U)
#define Adc1_HardOneShot_AdcChannel_IMPORTI4                   (6U)
#define Adc1_HardOneShot_AdcChannel_IMPORTI5                   (7U)
#define Adc1_HardOneShot_AdcChannel_IMPORTI6                   (8U)

/**
* @brief           Symbolic names of groups.
*/

#define Adc0_HardOneShot                                (0U)
#define Adc1_HardOneShot                                (1U)

/**
* @brief           Symbolic names of groups - ecuc 2108 compliant.
*/

#define AdcConf_AdcGroup_Adc0_HardOneShot               (0U)
#define AdcConf_AdcGroup_Adc1_HardOneShot               (1U)


/*==================================================================================================
*                                             ENUMS
==================================================================================================*/


/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/


/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_CFGDEFINES_H */
