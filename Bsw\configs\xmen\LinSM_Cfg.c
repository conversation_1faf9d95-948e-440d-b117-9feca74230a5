/*******************************************************************************
**                                 Includes                                   **
*******************************************************************************/
#include "LinSM.h"

/*******************************************************************************
**                               Configuration                                **
*******************************************************************************/
#define LINSM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "LinSM_MemMap.h"
static CONST(LinSM_ScheduleType, LINSM_CONST)  LinSM_ScheduleData[]=
{
    {
        LINSM_LinIfScheduleTable_schedule_1, /* LinSMScheduleIndex */
        LinIfScheduleTable_schedule_1,    /* LinSMScheduleIndexRef */
    },
};
static CONST(LinSM_ChannelType, LINSM_CONST)  LinSM_ChannelCfg[LINSM_CHANNEL_NUM] =
{

    {
        5000u,                     /* LinSMConfirmationTimeout */
        LINSM_MASTER,               /* LinSMNodeType */
        0u,  /* LinSMSilenceAfterWakeupTimeout */
        FALSE,                /* LinSMTransceiverPassiveModeSupport */
        FALSE,                    /* LinSMTransceiverPassiveMode */
        ComMChannel_DSCU,                        /* LinSMComMNetworkHandleRef */
        &LinSM_ScheduleData[0],    /* LinSMSchedule */
        1u,                        /* LinSMScheduleNum */
    },
};

CONST(LinSM_ConfigType, LINSM_CONST)  LinSM_PCConfig =  /* PRQA S 1531 */ /* MISRA Rule 8.7 */
{
    2,                  /* LinSMModeRequestRepetitionMax */
    LinSM_ChannelCfg,    /* LinSMChannel */
};
#define LINSM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "LinSM_MemMap.h"

