/**
 * file    Fls_Cfg_Defines.h
 * brief   Flash driver for LES14XX
 * author  huhl
 * date    2025.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
/*
* @section [global]
* Violates MISRA C 2012 Advisory Directive 4.4, The code seems to be commented.
* Reason: Identify comments as source code

* @section [global]
* Violates MISRA C 2012 Advisory Rule 2.5, A project should not contain unused macro declarations.
* Reason: Declaration is reserved for future feature.
*/
#ifndef FLS_CFG_DEFINES_H
#define FLS_CFG_DEFINES_H



#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/


/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_VENDOR_ID_CFG_DEFINES                    (110u)
#define FLS_AR_RELEASE_MAJOR_VERSION_CFG_DEFINES     (4u)
#define FLS_AR_RELEASE_MINOR_VERSION_CFG_DEFINES     (4u)
#define FLS_AR_RELEASE_REVISION_VERSION_CFG_DEFINES  (0u)
#define FLS_SW_MAJOR_VERSION_CFG_DEFINES             (1u)
#define FLS_SW_MINOR_VERSION_CFG_DEFINES             (0u)
#define FLS_SW_PATCH_VERSION_CFG_DEFINES             (0u)


/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
/* STD_ON: Flash access code loaded on job start / unloaded on job end or error */
#define FLS_AC_LOAD_ON_JOB_START            STD_OFF

/* Compile switch to enable and disable the Fls_Cancel function */
#define FLS_CANCEL_API                       (STD_ON)

/* Compile switch to enable and disable the Fls_Compare function */
#define FLS_COMPARE_API                      (STD_ON)

/* Compile switch to enable and disable the Fls_BlankCheck function */
#define FLS_BLANK_CHECK_API                  (STD_ON)

/* Pre-processor switch to enable and disable development error detection */
#define FLS_DEV_ERROR_DETECT                 (STD_ON)

/* Compile switch to enable and disable the Fls_GetStatus function */
#define FLS_GET_STATUS_API                   (STD_ON)


/* Compile switch to enable and disable the Fls_SetMode function */
#define FLS_SET_MODE_API                     (STD_ON)

/* Pre-processor switch to enable / disable the API to read out the modules version information */
#define FLS_VERSION_INFO_API                 (STD_ON)

/* Compile switch to enable and disable the Fls_GetJobResult function */
#define FLS_GET_JOB_RESULT_API               (STD_ON)

/* only support internal flash */
#define FLS_INTERNAL_SECTORS_CONFIGURED      STD_ON

#define FLS_INTERNAL_WRITE_SIZE              16U

/* Pre-processor switch to enable / disable the API to report data storage (ECC) errors to the flash driver */
#define FLS_ECC_CHECK                         (STD_OFF)

/* Pre-processor switch to enable / disable the FLS ECC error interrupt  */
#define FLS_ECC_IRQ_EN                         (STD_OFF)
  
/* Compile switch to enable and disable the Fls command complete interrupt */
#define FLS_COMPLETE_IRQ_EN                   (STD_OFF)

/* Compile switch to enable and disable the Fls command conflict interrupt */
#define FLS_CONFLICT_IRQ_EN                   (STD_OFF)


/*Return value for Fls_DsiHandler and Fls_MciHandler*/
/**
*   Return value for Fls_DsiHandler and Fls_MciHandler.
*   module does not feel responsible (e.g. address does not belong to its current job,
*   there is no current pending read/compare job, the syndrome is different).
*
*/
#define FLS_UNHANDLED       (0U)
/**
*   Return value for Fls_DsiHandler and Fls_MciHandler.
*   module feels responsible, but wants to repeat the  causing instruction.
*   Maybe: it still uses information in MCM or ECSM module, but they are outdated
*   (e.g. due to an erroneous DMA transfer in the meantime)
*
*/
#define FLS_HANDLED_RETRY   (1U)
/**
*   Return value for Fls_DsiHandler and Fls_MciHandler.
*   module feels responsible, the current job is marked as failed,
*   processing may continue, skipping the causing instruction.
*
*/
#define FLS_HANDLED_SKIP    (2U)
/**
*   Return value for Fls_DsiHandler and Fls_MciHandler.
*   module  feels responsible, but the only reaction is to stop the system
*   (e.g.: try to shut-down in a quite safe way)
*
*/
#define FLS_HANDLED_STOP    (3U)
/*==================================================================================================
                                 GLOBAL CONSTANT DECLARATIONS
==================================================================================================*/
#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


#ifdef __cplusplus
}
#endif

/***/

#endif /* FLS_CFG_DEFINES_H */
