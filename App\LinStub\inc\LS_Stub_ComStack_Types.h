
#ifndef COMSTACK_TYPES_H
#define COMSTACK_TYPES_H

/** \brief AUTOSAR Communication Stack Types header
 **
 ** This file contains the implementation of the AUTOSAR module Base
 ** Communication Stack Types header
 **
 ** Do not edit this file manually.
 ** Any change might compromise the safety integrity level of
 ** the software partition it is contained in.
 **
 ** Product: SW-MCAL42-DRV
 **
 ** (c) 2017-2018, LanShan Semiconductor Corporation. All rights reserved.
 **
 ** Warranty and Disclaimer
 **
 ** This software product is property of LanShan Semiconductor Corporation or
 ** its subsidiaries.
 ** Any use and/or distribution rights for this software product are provided
 ** only under the LanShan Software License Agreement.
 ** Any use and/or distribution of this software product not in accordance with
 ** the terms of the LanShan Software License Agreement are unauthorized and
 ** shall constitute an infringement of LanShan intellectual property rights.
*/



/*==================[inclusions]=============================================*/

#include <Std_Types.h> /* AUTOSAR standard type declarations */
#include <ComStack_Cfg.h> /* AUTOSAR Communication Stack PDU types */

/*==================[macros]=================================================*/

/*------------------[standard version declarations]--------------------------*/

#if (defined COMTYPE_AR_RELEASE_MAJOR_VERSION) /* prevent double definition */
#error COMTYPE_AR_RELEASE_MAJOR_VERSION already defined
#endif /*  if (defined COMTYPE_AR_RELEASE_MAJOR_VERSION) */

/** \brief definition of major version number of the comstack types */
#define COMTYPE_AR_RELEASE_MAJOR_VERSION 0x04U

#if (defined COMTYPE_AR_RELEASE_MINOR_VERSION) /* prevent double definition */
#error COMTYPE_AR_RELEASE_MINOR_VERSION already defined
#endif /* if (defined COMTYPE_AR_RELEASE_MINOR_VERSION) */

/** \brief definition of minor version number of the comstack types */
#define COMTYPE_AR_RELEASE_MINOR_VERSION 0x02U

#if (defined COMTYPE_AR_RELEASE_REVISION_VERSION) /* prevent double definition */
#error COMTYPE_AR_RELEASE_REVISION_VERSION already defined
#endif /* if (defined COMTYPE_AR_RELEASE_REVISION_VERSION) */

/** \brief definition of patch version number of the comstack types */
#define COMTYPE_AR_RELEASE_REVISION_VERSION 0x02U

#if (defined COMTYPE_SW_MAJOR_VERSION) /* prevent double definition */
#error COMTYPE_SW_MAJOR_VERSION already defined
#endif /* if (defined COMTYPE_SW_MAJOR_VERSION) */

/* \brief definition of software major version of the comstack types */
#define COMTYPE_SW_MAJOR_VERSION 1U


#if (defined COMTYPE_SW_MINOR_VERSION) /* prevent double definition */
#error COMTYPE_SW_MINOR_VERSION already defined
#endif /* if (defined COMTYPE_SW_MINOR_VERSION) */

/* \brief definition of sotware minor version of the comstack types */
#define COMTYPE_SW_MINOR_VERSION 6U


#if (defined COMTYPE_SW_PATCH_VERSION) /* prevent double definition */
#error COMTYPE_SW_PATCH_VERSION already defined
#endif /* if (defined COMTYPE_SW_PATCH_VERSION) */

/* \brief definition of software patch version of the comstack types */
#define COMTYPE_SW_PATCH_VERSION 0U


#if (defined COMTYPE_VENDOR_ID) /* prevent double definition */
#error COMTYPE_VENDOR_ID already defined
#endif /* if (defined COMTYPE_VENDOR_ID) */

/* \brief definition of vendor ID of the dedicated implementation of this module. */
#define COMTYPE_VENDOR_ID 66U


#if (defined COMTYPE_MODULE_ID) /* prevent double definition */
#error COMTYPE_MODULE_ID already defined
#endif /* if (defined COMTYPE_MODULE_ID) */

/* \brief definition of the module ID. */
/* Note: This module's ID was not defined by AUTOSAR. */
#define COMTYPE_MODULE_ID 196U

/*------------------[Bus Error: General Codes]-------------------------------*/

#if (defined BUSTRCV_OK) /* prevent double definition */
#error BUSTRCV_OK already defined
#endif /* if (defined BUSTRCV_OK) */

/** \brief Code for: no bus error
 **
 ** There is no bus transceiver error seen by the driver of transceiver
 ** does not support the detection of bus errors. */
#define BUSTRCV_OK 0x00U

#if (defined BUSTRCV_E_ERROR) /* prevent double definition */
#error BUSTRCV_E_ERROR already defined
#endif /* if (defined BUSTRCV_E_ERROR) */

/** \brief Bus transceiver detected an unclassified error. */
#define BUSTRCV_E_ERROR 0x01U

/*==================[type definitions]=======================================*/


/** \brief Type for storage of basic information about a PDU
 *
 * This type shall be used to store the basic information about a PDU of
 * any type, namely a pointer variable pointing to it's SDU (payload) and
 * the corresponding length of the SDU in bytes.
 */
typedef struct
{
   /** \brief Pointer to the SDU of the PDU  */
   P2VAR(uint8, AUTOMATIC, AUTOSAR_COMSTACKDATA) SduDataPtr;
   PduLengthType SduLength; /** \brief Length of SDU in bytes */
} PduInfoType;

/** \brief Specify the parameter to which the value has to be changed (BS, BC or STmin) */
typedef enum
{
   /** \brief Separation Time */
   TP_STMIN,
   /** \brief Block Size */
   TP_BS,
   /** \The Band width control parameter used in FlexRay transport protocol module. */
   TP_BC
} TPParameterType;

/** \brief Type for storage of the result of a buffer request */
typedef enum
{
   /** \brief Buffer request accomplished successul */
   BUFREQ_OK = 0,
   /** \brief Buffer request not successful, buffer cannot be accessed. */
   BUFREQ_E_NOT_OK = 1,
   /** \brief Temporarily no buffer available. */
   BUFREQ_E_BUSY = 2,
   /** \brief No buffer of the required length can be provided */
   BUFREQ_E_OVFL = 3
} BufReq_ReturnType;

/** \brief Variables of this type are used to store the state of TP buffer. */
typedef enum
{
   /** \brief All data are confirmed and can be removed from the TP buffer */
   TP_DATACONF,
   /** \brief API call shall copy already copied data in order to recover from an error. */
   TP_DATARETRY,
   /** \brief The previously copied data must remain in the TP */
   TP_CONFPENDING
} TpDataStateType;

/** \brief Variables of this type are used to store the information about Tp buffer handling */
typedef struct
{
   /** \brief To store the state of Tp buffer  */
   TpDataStateType TpDataState;
   PduLengthType TxTpDataCnt; /** \brief Length of the SDU in bytes */
} RetryInfoType;

/** \brief Bus status type definition
 **
 ** Variables of this type are used to return the bus status
 ** evaluated by a transceiver. */
typedef uint8 BusTrcvErrorType;

/** \brief Network Handle type definition
 **
 ** Variables of this type are used to store the identifier of a communication channel
 */
typedef uint8 NetworkHandleType;

/** \brief Configuration ID definition
 **
 ** ID of 0 is the default configuration. ID greater than 0 shall identify a configuration
 ** for Pretended Networking. There is more than 1 configuration possible
 */
typedef uint8 IcomConfigIdType;

/** \brief Definition for errors which can occur when activating or deactivating
 ** Pretended Networking. */
typedef enum
{
   /** \brief Activation of Pretended Networking was successful. */
   ICOM_SWITCH_E_OK,
   /** \brief Activation of Pretended Networking was not successful. */
   ICOM_SWITCH_E_FAILED
} IcomSwitch_ErrorType;

/** \brief PNC Handle type definition
 **
 ** Variables of this type are used to store the identifier of a partial network cluster
 */
typedef uint8 PNCHandleType;

/*==================[external function declarations]=========================*/

/*==================[internal function declarations]=========================*/

/*==================[external constants]=====================================*/

/*==================[internal constants]=====================================*/

/*==================[external data]==========================================*/

/*==================[internal data]==========================================*/

/*==================[external function definitions]==========================*/

/*==================[internal function definitions]==========================*/

#endif /* ifndef COMSTACK_TYPES_H */
/*==================[end of file]============================================*/
