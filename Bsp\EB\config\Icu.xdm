<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Icu" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Icu" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Icu"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="IcuConfigSet" type="IDENTIFIABLE">
                <d:var name="IcuMaxChannel" type="INTEGER" value="12">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:lst name="IcuChannel" type="MAP">
                  <d:ctr name="IcuChannel_HALL1" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall1/IcuPwmGpcmChannels_Hall1_A_PWM3_CH7"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_1">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER"/>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" 
                             value="icuTimestampMeasurementCallback">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_HALL2" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="1"/>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall2/IcuPwmGpcmChannels_Hall2_A_PWM3_CH6"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_2">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_HALL3" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="2"/>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall3/IcuPwmGpcmChannels_Hall3_A_PWM1_CH7"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_3">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_HALL4" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="3"/>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall4/IcuPwmGpcmChannels_Hall4_A_PWM1_CH6"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_4">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_HALL5" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="4"/>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall5/IcuPwmGpcmChannels_Hall5_A_PWM5_CH0"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_5">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_HALL6" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="5"/>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall6/IcuPwmGpcmChannels_Hall6_A_PWM5_CH1"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="Hall_EdgeDetect_6">
                        <a:a name="ENABLE" value="true"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE"/>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_0" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="6">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall1/IcuPwmGpcmChannels_Hall1_B_PWM4_CH4_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_1" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="7">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall2/IcuPwmGpcmChannels_Hall2_B_PWM4_CH2_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_2" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="8">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall3/IcuPwmGpcmChannels_Hall3_B_PWM1_CH1_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_3" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="9">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall4/IcuPwmGpcmChannels_Hall4_B_PWM1_CH5_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_4" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="10">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE"/>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall5/IcuPwmGpcmChannels_Hall5_B_PWM5_CH3_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="IcuChannel_5" type="IDENTIFIABLE">
                    <d:var name="IcuChannelId" type="INTEGER" value="11">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="IcuDefaultStartEdge" type="ENUMERATION" 
                           value="ICU_RISING_EDGE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuMeasurementMode" type="ENUMERATION" 
                           value="ICU_MODE_SIGNAL_MEASUREMENT"/>
                    <d:var name="IcuWakeupCapability" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuChannelRef" type="REFERENCE" 
                           value="ASPath:/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall6/IcuPwmGpcmChannels_Hall6_B_PWM3_CH2_NoUse"/>
                    <d:ref name="IcuDMAChannelRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:ctr name="IcuSignalEdgeDetection" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuSignalNotification" type="FUNCTION-NAME" 
                             value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuSignalMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="true"/>
                      <d:var name="IcuSignalMeasurementProperty" 
                             type="ENUMERATION" value="ICU_DUTY_CYCLE">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuTimestampMeasurement" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:var name="IcuTimestampMeasurementProperty" 
                             type="ENUMERATION" value="ICU_LINEAR_BUFFER">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="IcuTimestampNotification" 
                             type="FUNCTION-NAME" value="NULL_PTR">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                    </d:ctr>
                    <d:ctr name="IcuWakeup" type="IDENTIFIABLE">
                      <a:a name="ENABLE" value="false"/>
                      <d:ref name="IcuChannelWakeupInfo" type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
                <d:lst name="IcuPwmChannels" type="MAP">
                  <d:ctr name="IcuPwmChannels_Hall1" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="14"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" value="">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall1_A_PWM3_CH7" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_13"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH7"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall1_B_PWM4_CH4_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_27"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM4_CH4"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="IcuPwmChannels_Hall2" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="9"/>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="15"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall2_A_PWM3_CH6" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_12"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH6"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall2_B_PWM4_CH2_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_11"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM4_CH2"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="IcuPwmChannels_Hall3" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="3"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall3_A_PWM1_CH7" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_0_CH_13"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM1_CH7"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall3_B_PWM1_CH1_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_0_CH_1"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM1_CH1"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="IcuPwmChannels_Hall4" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="4"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall4_A_PWM1_CH6" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_0_CH_12"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM1_CH6"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall4_B_PWM1_CH5_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_0_CH_11"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM1_CH5"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="IcuPwmChannels_Hall5" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="22"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall5_A_PWM5_CH0" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_9"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM5_CH0"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall5_B_PWM5_CH3_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_7"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH3"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="IcuPwmChannels_Hall6" type="IDENTIFIABLE">
                    <d:var name="IcuExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPrescaler" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmPeriodCountValue" type="INTEGER" 
                           value="4294967294">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPwmGpcmId" type="INTEGER" value="23"/>
                    <d:var name="IcuCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="IcuPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="IcuPwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="IcuPwmGpcmChannels" type="MAP">
                      <d:ctr name="IcuPwmGpcmChannels_Hall6_A_PWM5_CH1" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_8"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM5_CH1"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" 
                               value="400"/>
                      </d:ctr>
                      <d:ctr name="IcuPwmGpcmChannels_Hall6_B_PWM3_CH2_NoUse" 
                             type="IDENTIFIABLE">
                        <d:var name="IcuPwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="IcuPwmPad" type="ENUMERATION" 
                               value="PORT_2_CH_6"/>
                        <d:var name="IcuPwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH2"/>
                        <d:var name="IcuPwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:lst name="IcuPort" type="MAP"/>
                <d:lst name="IcuHwInterruptConfigList" type="MAP"/>
              </d:ctr>
              <d:ctr name="IcuGeneral" type="IDENTIFIABLE">
                <d:var name="IcuDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuReportWakeupSource" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="IcuKernelEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="IcuOptionalApis" type="IDENTIFIABLE">
                <d:var name="IcuDeInitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuDisableWakeupApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEdgeCountApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEnableWakeupApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetDutyCycleValuesApi" type="BOOLEAN" 
                       value="true"/>
                <d:var name="IcuGetInputStateApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetTimeElapsedApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuGetVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSetModeApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuSignalMeasurementApi" type="BOOLEAN" 
                       value="true"/>
                <d:var name="IcuTimestampApi" type="BOOLEAN" value="true"/>
                <d:var name="IcuWakeupFunctionalityApi" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="IcuEdgeDetectApi" type="BOOLEAN" value="true"/>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="122">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
