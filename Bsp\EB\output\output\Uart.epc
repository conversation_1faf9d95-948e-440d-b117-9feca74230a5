<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Uart</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Uart</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/LS_LSE14M01I0R0/Uart</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-POST-BUILD</IMPLEMENTATION-CONFIG-VARIANT>
          <POST-BUILD-VARIANT-USED>true</POST-BUILD-VARIANT-USED>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>255</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/VendorApiInfix</DEFINITION-REF>
                  <VALUE></VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>110</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>GeneralConfiguration</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/DisableUartRuntimeErrorDetect</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartCallback</DEFINITION-REF>
                  <VALUE>BluetoothUartCallback</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartCallbackCapability</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartDevErrorDetect</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartDmaEnable</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartEnableUserModeSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartMulticoreSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartTimeoutDuration</DEFINITION-REF>
                  <VALUE>1000</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-TEXTUAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartTimeoutMethod</DEFINITION-REF>
                  <VALUE>OSIF_COUNTER_DUMMY</VALUE>
                </ECUC-TEXTUAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/GeneralConfiguration/UartVersionInfoApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>UartGlobalConfig</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>UartChannel_0</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/UartChannelId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/UartHwUsing</DEFINITION-REF>
                      <VALUE>LPUART_HW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/UartClockRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Uart</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>DetailModuleConfiguration</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/CustomBaudrateValue</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/DesireBaudrate</DEFINITION-REF>
                          <VALUE>LPUART_UART_BAUDRATE_115200</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/OverSamplingRatio</DEFINITION-REF>
                          <VALUE>16</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartAutoFlowControlEnable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartFlowControlCTSRTSValidPolarity</DEFINITION-REF>
                          <VALUE>STD_LOW</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartFrameFormat</DEFINITION-REF>
                          <VALUE>UART_FRAME_FORMAT_IDLELINE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartHwChannel</DEFINITION-REF>
                          <VALUE>LPUART_0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartInternalLoopbackEnable</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartInteruptDmaMethod</DEFINITION-REF>
                          <VALUE>LPUART_UART_HW_USING_INTERRUPTS</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartMode</DEFINITION-REF>
                          <VALUE>UART_FULL_DUPLEX_MODE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartParityType</DEFINITION-REF>
                          <VALUE>LPUART_UART_HW_PARITY_DISABLED</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRS485ModeDePol</DEFINITION-REF>
                          <VALUE>UART_RS485_DEPOL_SAME</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRS485ModeDirection</DEFINITION-REF>
                          <VALUE>UART_RS485_DIR_TX</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRS485ModeRxFrameAddr</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRS485ModeRxFrameAddrMatchEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRS485SleepModeEn</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRxEndian</DEFINITION-REF>
                          <VALUE>UART_FRAME_ENDIAN_LSB</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartRxFifoWaterLine</DEFINITION-REF>
                          <VALUE>UART_RXFIFO_WATERLINE_0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartStopBitNumber</DEFINITION-REF>
                          <VALUE>UART_HW_ONE_STOP_BIT</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartTxEndian</DEFINITION-REF>
                          <VALUE>UART_FRAME_ENDIAN_LSB</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartTxFifoWaterLine</DEFINITION-REF>
                          <VALUE>UART_TXFIFO_WATERLINE_3</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartWordLength</DEFINITION-REF>
                          <VALUE>LPUART_UART_HW_8_BITS_PER_CHAR</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                      <REFERENCE-VALUES>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartDmaRxChannelRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Dma/Dma/DmaConfig/dmaLogicChannel_Type_0</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                        <ECUC-REFERENCE-VALUE>
                          <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Uart/UartGlobalConfig/UartChannel/DetailModuleConfiguration/UartDmaTxChannelRef</DEFINITION-REF>
                          <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Dma/Dma/DmaConfig/dmaLogicChannel_uart0_tx</VALUE-REF>
                        </ECUC-REFERENCE-VALUE>
                      </REFERENCE-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
