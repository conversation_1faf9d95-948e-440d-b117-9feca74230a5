/**
 * @file    Gpt_Cfg.c
 * @brief   Gpt Configuration file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 */

#include "Gpt_Cfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_CFG_C                    (110U)
#define GPT_RELEASE_MAJOR_VERSION_CFG_C        (4U)
#define GPT_RELEASE_MINOR_VERSION_CFG_C        (4U)
#define GPT_RELEASE_REVISION_VERSION_CFG_C     (0U)
#define GPT_SW_MAJOR_VERSION_CFG_C             (1U)
#define GPT_SW_MINOR_VERSION_CFG_C             (0U)
#define GPT_SW_PATCH_VERSION_CFG_C             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and GPT header file are of the same vendor */
#if (GPT_VENDOR_ID_CFG_C != GPT_VENDOR_ID_CFG)
    #error "Gpt_Cfg.c and Gpt_Cfg.h have different vendor ids"
#endif
/* Check if source file and GPT header file are of the same Autosar version */
#if ((GPT_RELEASE_MAJOR_VERSION_CFG_C != GPT_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (GPT_RELEASE_MINOR_VERSION_CFG_C != GPT_AR_RELEASE_MINOR_VERSION_CFG) || \
     (GPT_RELEASE_REVISION_VERSION_CFG_C != GPT_AR_RELEASE_REVISION_VERSION_CFG) \
    )
    #error "AutoSar Version Numbers of Gpt_Cfg.c and Gpt_Cfg.h are different"
#endif
/* Check if source file and Gpt_Cfg header file are of the same Software version */
#if ((GPT_SW_MAJOR_VERSION_CFG_C != GPT_SW_MAJOR_VERSION_CFG) || \
     (GPT_SW_MINOR_VERSION_CFG_C != GPT_SW_MINOR_VERSION_CFG) || \
     (GPT_SW_PATCH_VERSION_CFG_C != GPT_SW_PATCH_VERSION_CFG) \
    )
    #error "Software Version Numbers of Gpt_Cfg.c and Gpt_Cfg.h are different"
#endif
/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif /* GPT_CFG_C */
/** } */

