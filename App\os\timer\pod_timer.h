#ifndef POD_TIMER_H
#define POD_TIMER_H
/// ---------- begin of guard: <includes> -----------------------------------
#include "config.h"
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
#ifdef WIN32
void TaskTimer_handler(void *param);
#endif
/// ---------- end of guard:   <header> -------------------------------------

void pod_timer_on_init(tHsmGenCurr* const ev);
void pod_timer_on_tm100ms(tHsmGenCurr* const ev);
void pod_timer_on_tm10ms(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_TIMER_H