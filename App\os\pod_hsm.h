
// ----------------------------------  HSM ----------------------------------------

/** Define here all application instances and trees */
HSM_BEGIN(hsm_power)
    HSM_TREE(TREE_POWER)

HSM_END(hsm_power)

HSM_BEGIN(hsm_bluetooth)
    HSM_TREE(TREE_BLUETOOTH)

HSM_END(hsm_bluetooth)

HSM_BEGIN(hsm_cmd)
    HSM_TREE(TREE_CMD)

HSM_END(hsm_cmd)

HSM_BEGIN(hsm_lin)
    HSM_TREE(TREE_LIN)

HSM_END(hsm_lin)

HSM_BEGIN(hsm_motor)
    HSM_TREE(TREE_MOTOR)
    HSM_TREE(TREE_MOTOR1)
#ifdef SEAT_SIDE_RIGHT
    HSM_TREE(TREE_MOTOR2)
    HSM_TREE(TREE_MOTOR3)
#endif
    HSM_TREE(TREE_MOTOR4)
    HSM_TREE(TREE_MOTOR5)
    HSM_TREE(TREE_MOTOR6)

HSM_END(hsm_motor)

HSM_BEGIN(hsm_timer)
    HSM_TREE(TREE_TIMER)

HSM_END(hsm_timer)


// ----------------------------------  SUBMITTERS -------------------------------------

/**
 * Define here all application submitters
 */
SUBMITTERS_BEGIN(hsm)

// Tn Submitters should be put BEFORE TnIdle
    SUBMITTER(TnPower)
    SUBMITTER(TnBluetooth)
    SUBMITTER(TnCmd)
    SUBMITTER(TnLin)
    SUBMITTER(TnMotor)
    SUBMITTER(TnTimer)

    // Dot not edit this one
    SUBMITTER(TnIdle)


// Normal Submitters should be put AFTER TnIdle
    SUBMITTER(Power)
    SUBMITTER(Bluetooth)
    SUBMITTER(Cmd)
    SUBMITTER(Lin)
    SUBMITTER(Motor)
    SUBMITTER(Timer)

SUBMITTERS_END(hsm)