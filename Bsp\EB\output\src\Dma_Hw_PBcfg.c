/**
 * file    Dma_Hw_PBcfg.c
 * brief   dma hw PBcfg file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
 /* @section [global]
 * 0791 EOF 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
 /* @section [global]
 * 5087 EOF
 * This attribute syntax is a language extension.
 * REASON: Variables and text need to be placed in the specified location
 */
 /* @section [global]
 * 0306 EOF #Misra-C:2012 Rule-11.4 A conversion should not be performed between a pointer to object and an integer type
 * Cast between a pointer to object and an integral type.
 * REASON: Unavoidable when addressing memory mapped registers.
 */
/* @section [global]
 * 1504 EOF 
 * #Misra-C:2012 Rule-8.7 Functions and objects should not be defined with external linkage if they are referenced in only one translation unit
 * Reason: it will be used by user 
 */

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Hw_Cfg.h"
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                                    SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_HW_PBCFG_VENDOR_ID_C                        (110U)
#define DMA_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C         (4U)
#define DMA_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C         (4U)
#define DMA_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C      (0U)
#define DMA_HW_PBCFG_SW_MAJOR_VERSION_C                 (1U)
#define DMA_HW_PBCFG_SW_MINOR_VERSION_C                 (0U)
#define DMA_HW_PBCFG_SW_PATCH_VERSION_C                 (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Dma_Hw_PBcfg.c file and Dma_Hw_Cfg.h file are of the same vendor */
#if (DMA_HW_PBCFG_VENDOR_ID_C != DMA_HW_CFG_VENDOR_ID)
    #error "Dma_Hw_PBcfg.c and Dma_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Dma_Hw_PBcfg.c file and Dma_Hw_Cfg.h file are of the same Autosar version */
#if ((DMA_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != DMA_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != DMA_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_PBcfg.c and Dma_Hw_Cfg.h are different"
#endif

/* Check if Dma_Hw_PBcfg.c file and Dma_Hw_Cfg.h file are of the same Software version */
#if ((DMA_HW_PBCFG_SW_MAJOR_VERSION_C != DMA_HW_CFG_SW_MAJOR_VERSION) || \
     (DMA_HW_PBCFG_SW_MINOR_VERSION_C != DMA_HW_CFG_SW_MINOR_VERSION) || \
     (DMA_HW_PBCFG_SW_PATCH_VERSION_C != DMA_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_PBcfg.c and Dma_Hw_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Dma_Hw_PBcfg.c file and Std_Types.h file are of the same vendor */
#if (DMA_HW_PBCFG_VENDOR_ID_C != STD_AR_VENDOR_ID)
    #error "Dma_Hw_PBcfg.c and Std_Types.h have different vendor ids"
#endif

/* Check if Dma_Hw_PBcfg.c file and Std_Types.h file are of the same Autosar version */
#if ((DMA_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION) || \
     (DMA_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != STD_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Hw_PBcfg.c and Std_Types.h are different"
#endif

/* Check if Dma_Hw_PBcfg.c file and Std_Types.h file are of the same Software version */
#if ((DMA_HW_PBCFG_SW_MAJOR_VERSION_C != STD_AR_SW_MAJOR_VERSION) || \
     (DMA_HW_PBCFG_SW_MINOR_VERSION_C != STD_AR_SW_MINOR_VERSION) || \
     (DMA_HW_PBCFG_SW_PATCH_VERSION_C != STD_AR_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Hw_PBcfg.c and Std_Types.h are different"
#endif
#endif

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/




#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"
/* DMA Logic Instance Configuration */
static const DmaHwLogicInstanceConfig_t LogicInstance0ConfigPB =
{
    {
        /* uint32 logicInstId  */  DMA_LOGIC_INST_0,
        /* uint8 hwInst        */  DMA_HW_INST_0,
    },
	/* uint16_t    TimeUnit  */    1023,
	/* pDmaCallback AlmCallback  */ NULL_PTR,
	/* pDmaCallback SMCallback  */  NULL_PTR,
};

/* DMA Logic Instance Configuration Array */
static const DmaHwLogicInstanceConfig_t * const Dma_Hw_paxLogicInstanceConfigArrayPB[DMA_HW_NOF_CFG_LOGIC_INSTANCES] =
{
    &LogicInstance0ConfigPB,
};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h"



#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h" 
/* DMA Logic Channel Configurations */
/* DMA Logic Channel 0 */
static const DmaHwGlobalConfig_t LogicChannel0GlobalConfigPB =
{
	/* u32Channel timeout */ 		   4095,
	/* peripheral request source */    DMA_ADC0,
	{
		/* boolean EnRequest  */       (boolean)TRUE,
		/* boolean EnChTrigRequest  */ (boolean)FALSE,
		
	}, /* Request */
	{
		/* uint8 EnChCompleteInt  */   (uint8)(0U|DMA_CHANNEL_IRQ),
		/* uint8 EnErrorInt  */        (uint8)(0U),
	}, /* Interrupt */
	{
		/* uint8 Level  */             DMA_HW_LEVEL_PRIO0,
	}, /* Priority */
	/* complete irq callback */            Dma_Adc0_CallBack,
	/* link list irq callback */           NULL_PTR,
	/* config error irq callback */        NULL_PTR,
	/* u32Channel trans error irq callback */ NULL_PTR,
	/* list read error irq callback */     NULL_PTR,
};
/* DMA Logic Channel 1 */
static const DmaHwGlobalConfig_t LogicChannel1GlobalConfigPB =
{
	/* u32Channel timeout */ 		   4095,
	/* peripheral request source */    DMA_ADC1,
	{
		/* boolean EnRequest  */       (boolean)TRUE,
		/* boolean EnChTrigRequest  */ (boolean)FALSE,
		
	}, /* Request */
	{
		/* uint8 EnChCompleteInt  */   (uint8)(0U|DMA_CHANNEL_IRQ),
		/* uint8 EnErrorInt  */        (uint8)(0U),
	}, /* Interrupt */
	{
		/* uint8 Level  */             DMA_HW_LEVEL_PRIO0,
	}, /* Priority */
	/* complete irq callback */            Dma_Adc1_CallBack,
	/* link list irq callback */           NULL_PTR,
	/* config error irq callback */        NULL_PTR,
	/* u32Channel trans error irq callback */ NULL_PTR,
	/* list read error irq callback */     NULL_PTR,
};
/* DMA Logic Channel 2 */
static const DmaHwGlobalConfig_t LogicChannel2GlobalConfigPB =
{
	/* u32Channel timeout */ 		   1,
	/* peripheral request source */    DMA_UART0_TX,
	{
		/* boolean EnRequest  */       (boolean)TRUE,
		/* boolean EnChTrigRequest  */ (boolean)FALSE,
		
	}, /* Request */
	{
		/* uint8 EnChCompleteInt  */   (uint8)(0U|DMA_CHANNEL_IRQ),
		/* uint8 EnErrorInt  */        (uint8)(0U),
	}, /* Interrupt */
	{
		/* uint8 Level  */             DMA_HW_LEVEL_PRIO0,
	}, /* Priority */
	/* complete irq callback */            Lpuart_0_Uart_Hw_DmaTxCompleteCallback,
	/* link list irq callback */           NULL_PTR,
	/* config error irq callback */        NULL_PTR,
	/* u32Channel trans error irq callback */ NULL_PTR,
	/* list read error irq callback */     NULL_PTR,
};
/* DMA Logic Channel 3 */
static const DmaHwGlobalConfig_t LogicChannel3GlobalConfigPB =
{
	/* u32Channel timeout */ 		   1,
	/* peripheral request source */    DMA_UART0_RX,
	{
		/* boolean EnRequest  */       (boolean)TRUE,
		/* boolean EnChTrigRequest  */ (boolean)FALSE,
		
	}, /* Request */
	{
		/* uint8 EnChCompleteInt  */   (uint8)(0U|DMA_CHANNEL_IRQ),
		/* uint8 EnErrorInt  */        (uint8)(0U),
	}, /* Interrupt */
	{
		/* uint8 Level  */             DMA_HW_LEVEL_PRIO0,
	}, /* Priority */
	/* complete irq callback */            Lpuart_0_Uart_Hw_DmaRxCompleteCallback,
	/* link list irq callback */           NULL_PTR,
	/* config error irq callback */        NULL_PTR,
	/* u32Channel trans error irq callback */ NULL_PTR,
	/* list read error irq callback */     NULL_PTR,
};

/* DMA Logic Channel 0 */
static const DmaHwTransferConfig_t LogicChannel0TransferConfigPB =
{
	/*uint32 next link list address */  (uint32)0U,
	/* uint32 source addr  */           (uint32)0x4800012CU, 
	/* uint32 Destination addr  */      (uint32)&Adc0_SoftOneShotResultBuff,
	/* uint16 2D source addr  */        4U,
	/* uint16 2D Destination addr  */   2U,
	/* uint16 3D source addr  */        0U,
	/* uint16 3D Destination addr  */   0U,
	/* uint16 A Count  */               40U,
	/* uint16 B Count  */               0U,
	/* uint16 C Count  */               0U,
	/* uint8 NextChannelID  */          DMA_HW_CH_0,
	/* DmaChLinkMode_t LinkMode  */     DMA_LINK_INVALID,           
	/* boolean SrcInc  */               (boolean)TRUE,
	/* boolean DesInc  */               (boolean)TRUE,
	/* DmaIncMode_t SrcIncMode  */      A_SYNC,
	/* DmaIncMode_t DesIncMode  */      A_SYNC,
	/* DmaBurstSize_t BurstLength  */   DMA_BURST_SIZE0,
	/* DmaChTransDataW_t DataWid  */    DMA_TRANS_32BIT,
	/* DmaChannelFlowCtl_t FlowCtrl */  DMA_FC_M2P_D,
	/* DmaTransDim_t       Dim  */      ONE_DIM,
	/* boolean LrIntEnable  */          (boolean)FALSE,
};
/* DMA Logic Channel 1 */
static const DmaHwTransferConfig_t LogicChannel1TransferConfigPB =
{
	/*uint32 next link list address */  (uint32)0U,
	/* uint32 source addr  */           (uint32)0x4800112CU, 
	/* uint32 Destination addr  */      (uint32)&Adc1_SoftOneShotResultBuff,
	/* uint16 2D source addr  */        4U,
	/* uint16 2D Destination addr  */   2U,
	/* uint16 3D source addr  */        0U,
	/* uint16 3D Destination addr  */   0U,
	/* uint16 A Count  */               36U,
	/* uint16 B Count  */               0U,
	/* uint16 C Count  */               0U,
	/* uint8 NextChannelID  */          DMA_HW_CH_0,
	/* DmaChLinkMode_t LinkMode  */     DMA_LINK_INVALID,           
	/* boolean SrcInc  */               (boolean)TRUE,
	/* boolean DesInc  */               (boolean)TRUE,
	/* DmaIncMode_t SrcIncMode  */      A_SYNC,
	/* DmaIncMode_t DesIncMode  */      A_SYNC,
	/* DmaBurstSize_t BurstLength  */   DMA_BURST_SIZE0,
	/* DmaChTransDataW_t DataWid  */    DMA_TRANS_32BIT,
	/* DmaChannelFlowCtl_t FlowCtrl */  DMA_FC_M2P_D,
	/* DmaTransDim_t       Dim  */      ONE_DIM,
	/* boolean LrIntEnable  */          (boolean)FALSE,
};
/* DMA Logic Channel 2 */
static const DmaHwTransferConfig_t LogicChannel2TransferConfigPB =
{
	/*uint32 next link list address */  (uint32)0U,
	/* uint32 source addr  */           (uint32)0U, 
	/* uint32 Destination addr  */      (uint32)0U,
	/* uint16 2D source addr  */        0U,
	/* uint16 2D Destination addr  */   0U,
	/* uint16 3D source addr  */        0U,
	/* uint16 3D Destination addr  */   0U,
	/* uint16 A Count  */               1U,
	/* uint16 B Count  */               0U,
	/* uint16 C Count  */               0U,
	/* uint8 NextChannelID  */          DMA_HW_CH_0,
	/* DmaChLinkMode_t LinkMode  */     DMA_LINK_INVALID,           
	/* boolean SrcInc  */               (boolean)FALSE,
	/* boolean DesInc  */               (boolean)FALSE,
	/* DmaIncMode_t SrcIncMode  */      A_SYNC,
	/* DmaIncMode_t DesIncMode  */      A_SYNC,
	/* DmaBurstSize_t BurstLength  */   DMA_BURST_SIZE0,
	/* DmaChTransDataW_t DataWid  */    DMA_TRANS_8BIT,
	/* DmaChannelFlowCtl_t FlowCtrl */  DMA_FC_M2P_D,
	/* DmaTransDim_t       Dim  */      ONE_DIM,
	/* boolean LrIntEnable  */          (boolean)FALSE,
};
/* DMA Logic Channel 3 */
static const DmaHwTransferConfig_t LogicChannel3TransferConfigPB =
{
	/*uint32 next link list address */  (uint32)0U,
	/* uint32 source addr  */           (uint32)0U, 
	/* uint32 Destination addr  */      (uint32)0U,
	/* uint16 2D source addr  */        0U,
	/* uint16 2D Destination addr  */   0U,
	/* uint16 3D source addr  */        0U,
	/* uint16 3D Destination addr  */   0U,
	/* uint16 A Count  */               1U,
	/* uint16 B Count  */               0U,
	/* uint16 C Count  */               0U,
	/* uint8 NextChannelID  */          DMA_HW_CH_0,
	/* DmaChLinkMode_t LinkMode  */     DMA_LINK_INVALID,           
	/* boolean SrcInc  */               (boolean)FALSE,
	/* boolean DesInc  */               (boolean)FALSE,
	/* DmaIncMode_t SrcIncMode  */      A_SYNC,
	/* DmaIncMode_t DesIncMode  */      A_SYNC,
	/* DmaBurstSize_t BurstLength  */   DMA_BURST_SIZE0,
	/* DmaChTransDataW_t DataWid  */    DMA_TRANS_8BIT,
	/* DmaChannelFlowCtl_t FlowCtrl */  DMA_FC_M2P_D,
	/* DmaTransDim_t       Dim  */      ONE_DIM,
	/* boolean LrIntEnable  */          (boolean)FALSE,
};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h" 


#define DMA_START_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Dma_MemMap.h" 

#define DMA_STOP_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Dma_MemMap.h" 


#define DMA_START_SEC_VAR_INIT_UNSPECIFIED
#include "Dma_MemMap.h" 


#define DMA_STOP_SEC_VAR_INIT_UNSPECIFIED
#include "Dma_MemMap.h" 


#define DMA_START_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Dma_MemMap.h" 
/* DMA Hardware u32Channel 0 */
static DmaHwChannelState_t HwChannel0StatePB;
/* DMA Hardware u32Channel 1 */
static DmaHwChannelState_t HwChannel1StatePB;
/* DMA Hardware u32Channel 2 */
static DmaHwChannelState_t HwChannel2StatePB;
/* DMA Hardware u32Channel 3 */
static DmaHwChannelState_t HwChannel3StatePB;

#define DMA_STOP_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Dma_MemMap.h" 

#define DMA_START_SEC_VAR_INIT_UNSPECIFIED_NO_CACHEABLE 
#include "Dma_MemMap.h" 
/* DMA Hardware u32Channel State and DMA Logic u32Channel Configuration Array */
static DmaHwChannelState_t * Dma_Hw_paxHwChannelStateArrayPB[DMA_HW_NOF_CFG_LOGIC_CHANNELS] =
{
    &HwChannel0StatePB,
    &HwChannel1StatePB,
    &HwChannel2StatePB,
    &HwChannel3StatePB,
};

#define DMA_STOP_SEC_VAR_INIT_UNSPECIFIED_NO_CACHEABLE 
#include "Dma_MemMap.h" 

#define DMA_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h" 
static const DmaHwLogicChannelConfig_t LogicChannel0ConfigPB =
{
    {
        /* uint32 LogicChId  */               DMA_LOGIC_CH_0,
        /* uint8 HwInst  */                   DMA_HW_INST_0,
        /* uint8 HwChId  */                   DMA_HW_CH_0,
		/* pfnDmaHwCallback IntCallback  */    Dma_Adc0_CallBack,
		/* pfnDmaHwCallback ErrIntCallback  */ NULL_PTR,

    },  /* DmaHwLogicChannelId_t */
    &LogicChannel0GlobalConfigPB,
    &LogicChannel0TransferConfigPB,
    NULL_PTR,
};
static const DmaHwLogicChannelConfig_t LogicChannel1ConfigPB =
{
    {
        /* uint32 LogicChId  */               DMA_LOGIC_CH_1,
        /* uint8 HwInst  */                   DMA_HW_INST_0,
        /* uint8 HwChId  */                   DMA_HW_CH_1,
		/* pfnDmaHwCallback IntCallback  */    Dma_Adc1_CallBack,
		/* pfnDmaHwCallback ErrIntCallback  */ NULL_PTR,

    },  /* DmaHwLogicChannelId_t */
    &LogicChannel1GlobalConfigPB,
    &LogicChannel1TransferConfigPB,
    NULL_PTR,
};
static const DmaHwLogicChannelConfig_t LogicChannel2ConfigPB =
{
    {
        /* uint32 LogicChId  */               DMA_LOGIC_CH_2,
        /* uint8 HwInst  */                   DMA_HW_INST_0,
        /* uint8 HwChId  */                   DMA_HW_CH_2,
		/* pfnDmaHwCallback IntCallback  */    Lpuart_0_Uart_Hw_DmaTxCompleteCallback,
		/* pfnDmaHwCallback ErrIntCallback  */ NULL_PTR,

    },  /* DmaHwLogicChannelId_t */
    &LogicChannel2GlobalConfigPB,
    &LogicChannel2TransferConfigPB,
    NULL_PTR,
};
static const DmaHwLogicChannelConfig_t LogicChannel3ConfigPB =
{
    {
        /* uint32 LogicChId  */               DMA_LOGIC_CH_3,
        /* uint8 HwInst  */                   DMA_HW_INST_0,
        /* uint8 HwChId  */                   DMA_HW_CH_3,
		/* pfnDmaHwCallback IntCallback  */    Lpuart_0_Uart_Hw_DmaRxCompleteCallback,
		/* pfnDmaHwCallback ErrIntCallback  */ NULL_PTR,

    },  /* DmaHwLogicChannelId_t */
    &LogicChannel3GlobalConfigPB,
    &LogicChannel3TransferConfigPB,
    NULL_PTR,
};

static const DmaHwLogicChannelConfig_t * const Dma_Hw_paxLogicChannelConfigArrayPB[DMA_HW_NOF_CFG_LOGIC_CHANNELS] =
{
    &LogicChannel0ConfigPB,
    &LogicChannel1ConfigPB,
    &LogicChannel2ConfigPB,
    &LogicChannel3ConfigPB,
};

/* DMA Initialization Structure */
const DmaHwInit_t stDmaHwxDmaInitPB =
{
    /* DmaHwChannelState_t ** HwChStateArray */                          &Dma_Hw_paxHwChannelStateArrayPB[0U],          /* Static */
    /* const DmaHwLogicChannelConfig_t * const LogicChConfigArray   */     &stDmaHwxLogicChannelResetConfig,                   /* Static */
    /* const DmaHwLogicChannelConfig_t * const * LogicChConfigArray */     &Dma_Hw_paxLogicChannelConfigArrayPB[0U],      /* Static */
    /* const DmaHwLogicInstanceConfig_t * const LogicInstConfigArray   */  &stDmaHwxLogicInstanceResetConfig,                  /* Static */
    /* const DmaHwLogicInstanceConfig_t * const * LogicInstConfigArray */  &Dma_Hw_paxLogicInstanceConfigArrayPB[0U],     /* Static */
};

#define DMA_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dma_MemMap.h" 



#ifdef __cplusplus
}
#endif

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
