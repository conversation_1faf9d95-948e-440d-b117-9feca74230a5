#ifndef KEY_H
#define KEY_<PERSON>

#include <stdint.h>

#define KEY_ID_SEAT_FRONT_BACK_POS      0u
#define KEY_ID_SEAT_FRONT_BACK_NEG      1u
#define KEY_ID_BACK_REST_POS            2u
#define KEY_ID_BACK_REST_NEG            3u
#define KEY_ID_SEAT_UP_DOWN_POS         4u
#define KEY_ID_SEAT_UP_DOWN_NEG         5u
#define KEY_ID_LEG_FRONT_BACK_POS       6u
#define KEY_ID_LEG_FRONT_BACK_NEG       7u
#define KEY_ID_MASSAGE                  8u
#define KEY_ID_HEATER                   9u // NOTE: HEATER and MASSAG<PERSON> keys are reversed on the board
#define KEY_ID_FAN                      10u
#define KEY_ID_ZERO_GRAVITY_TMP         11u
#define KEY_ID_LEFT_WAIST_POS           12u
#define KEY_ID_LEFT_WAIST_NEG           13u
#define KEY_ID_RIGHT_WAIST_POS          14u
#define KEY_ID_RIGHT_WAIST_NEG          15u
// NOTE: key 16 is Zero_Gravity_Recover
#define KEY_ID_ZERO_GRAVITY_NEG         16u
// NOTE: key 17 is Zero_Gravity_Expand
#define KEY_ID_ZERO_GRAVITY_POS         17u

#define KEY_OPTION_NONE 0u
#define KEY_OPTION_PRESSED  1u
#define KEY_OPTION_RELEASED 2u


#define KEY_ADC_PORT_NUMBLE    4u
#define KEY_ONE_ADC_KEY_NUMBLE 4u
#define KEY_SUM_NUMBLE         (KEY_ADC_PORT_NUMBLE*KEY_ONE_ADC_KEY_NUMBLE + 2u) 

#define KEY_ADC_SACN_TIME      10u//ms
#define KEY_SHORT_PRESS_TIME   1u//(1+1)*KEY_ADC_SACN_TIME = 20ms
#define KEY_LONG_PRESS_TIME    99u//(99+1)*KEY_ADC_SACN_TIME = 1s

#define KEY_ADC_VALUE_A_MIN 500u
#define KEY_ADC_VALUE_A_MAX 1000u
#define KEY_ADC_VALUE_B_MIN 1300u
#define KEY_ADC_VALUE_B_MAX 1700u
#define KEY_ADC_VALUE_C_MIN 2300u
#define KEY_ADC_VALUE_C_MAX 2700u
#define KEY_ADC_VALUE_D_MIN 2800u
#define KEY_ADC_VALUE_D_MAX 3200u
#define KEY_ADC_VALUE_51_MIN 400u
#define KEY_ADC_VALUE_51_MAX 600u
#define KEY_ADC_VALUE_52_MIN 700u
#define KEY_ADC_VALUE_52_MAX 900u
#define KEY_ADC_VALUE_MAX   4095u

typedef enum{
    KEY_EVENT_NONE,
    KEY_EVENT_PRESSED,
    KEY_EVENT_LONG_PRESSED,
    KEY_EVENT_RELEASED,
    KEY_EVENT_LONG_RELEASED
}Key_Event_t;

typedef enum{
    KEY_STATE_RELEASED,
    KEY_STATE_PRESSED,
    KEY_STATE_LONG_PRESSED
}Key_State_t;

typedef struct{
    Key_State_t key_state;
    Key_Event_t key_event;
    uint8_t key_press_cnt;
}Key_Status_t;

extern Key_Status_t key_status[], key_status_old[];
extern uint16_t keyAdcValueRange[KEY_ADC_PORT_NUMBLE][2];

extern void Key_ProcessHandle(uint32_t *keyAdcVal);


extern void testPwmHandle();

#endif
