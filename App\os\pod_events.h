// ----------------------------------  EVENTS --------------------------------------------

// Configuration for Events in Auto OS

// Each event has 3 properties:
// 1. Submitter, who is responsible for sending the event to OS. They are defined in os_submitters.h
// 2. Event name used in HSM State transition trees. Refere to os_trees.h
// 3. Data Type of the event. Currently only tChar/tU32 is supported.

// Note: Task Notification Event (TnEvent) vs Normal Event (Event).
// 1. TnEvent is send to related task only
// 2. Normal Event is broadcasted to all queues
// So if you need faster communication, and it's local, use TnEvent

// Side Note: might as well rename TnEvent as LocalEvent, and normal Event as BroadcastEvent

/*do not change the order of below EVENTS_BEGIN()*/

EVENTS_BEGIN(TnPower)
/* EVENT(subm, event, data_type) */
    EVENT(TnPower, Tm10ms, tU32)
EVENTS_END(TnPower)

EVENTS_BEGIN(Power)
/* EVENT(subm, event, data_type) */

EVENTS_END(Power)

EVENTS_BEGIN(TnBluetooth)
/* EVENT(subm, event, data_type) */
    EVENT(TnBluetooth, Tm10ms, tU32)
EVENTS_END(TnBluetooth)

EVENTS_BEGIN(Bluetooth)
/* EVENT(subm, event, data_type) */

EVENTS_END(Bluetooth)

EVENTS_BEGIN(TnCmd)
/* EVENT(subm, event, data_type) */

EVENTS_END(TnCmd)

EVENTS_BEGIN(Cmd)
/* EVENT(subm, event, data_type) */
    EVENT(Cmd, BluetoothEV, tU32)
    EVENT(Cmd, IOKeyEV, tU32)
    EVENT(Cmd, KeyEV, tU32)
    EVENT(Cmd, LinEV, tU32)
EVENTS_END(Cmd)

EVENTS_BEGIN(TnLin)
/* EVENT(subm, event, data_type) */
    EVENT(TnLin, Tm2ms, tU32)
EVENTS_END(TnLin)

EVENTS_BEGIN(Lin)
/* EVENT(subm, event, data_type) */
    EVENT(Lin, MassageEV, tU32)
EVENTS_END(Lin)

EVENTS_BEGIN(TnMotor)
/* EVENT(subm, event, data_type) */
    EVENT(TnMotor, Motor1Start, tU32)
    EVENT(TnMotor, Motor1Move, tU32)
    EVENT(TnMotor, Motor1Stop, tU32)
    EVENT(TnMotor, Once1StopDebouncer, tU32)
    EVENT(TnMotor, Once1IdleDebouncer, tU32)

#ifdef SEAT_SIDE_RIGHT
    EVENT(TnMotor, Motor2Start, tU32)
    EVENT(TnMotor, Motor2Move, tU32)
    EVENT(TnMotor, Motor2Stop, tU32)
    EVENT(TnMotor, Once2StopDebouncer, tU32)
    EVENT(TnMotor, Once2IdleDebouncer, tU32)

    EVENT(TnMotor, Motor3Start, tU32)
    EVENT(TnMotor, Motor3Move, tU32)
    EVENT(TnMotor, Motor3Stop, tU32)
    EVENT(TnMotor, Once3StopDebouncer, tU32)
    EVENT(TnMotor, Once3IdleDebouncer, tU32)

#endif

    EVENT(TnMotor, Motor4Start, tU32)
    EVENT(TnMotor, Motor4Move, tU32)
    EVENT(TnMotor, Motor4Stop, tU32)
    EVENT(TnMotor, Once4StopDebouncer, tU32)
    EVENT(TnMotor, Once4IdleDebouncer, tU32)

    EVENT(TnMotor, Motor5Start, tU32)
    EVENT(TnMotor, Motor5Move, tU32)
    EVENT(TnMotor, Motor5Stop, tU32)
    EVENT(TnMotor, Once5StopDebouncer, tU32)
    EVENT(TnMotor, Once5IdleDebouncer, tU32)

    // NOTE: On TaskNotifier hold 30 TnEvents max, so we have to put Motor6 Events to None TN block below
    EVENT(TnMotor, Once6StopDebouncer, tU32)
    EVENT(TnMotor, Once6IdleDebouncer, tU32)


    EVENT(TnMotor, Tm10ms, tU32)
EVENTS_END(TnMotor)

EVENTS_BEGIN(Motor)
/* EVENT(subm, event, data_type) */
    EVENT(Motor, SeatEvents, tU32)

    EVENT(Motor, Motor1Stall, tU32)
    EVENT(Motor, Motor2Stall, tU32)
    EVENT(Motor, Motor3Stall, tU32)
    EVENT(Motor, Motor4Stall, tU32)
    EVENT(Motor, Motor5Stall, tU32)
    EVENT(Motor, Motor6Stall, tU32)

    EVENT(Motor, Motor6Move, tU32)
    EVENT(Motor, Motor6Start, tU32)
    EVENT(Motor, Motor6Stop, tU32)
EVENTS_END(Motor)

EVENTS_BEGIN(TnTimer)
/* EVENT(subm, event, data_type) */
    EVENT(TnTimer, Tm100ms, tU32)
    EVENT(TnTimer, Tm10ms, tU32)
EVENTS_END(TnTimer)

EVENTS_BEGIN(Timer)
/* EVENT(subm, event, data_type) */

EVENTS_END(Timer)


EVENTS_BEGIN(TnIdle)
/* EVENT(subm, event, data_type) */
EVENTS_END(TnIdle)