/**
* 
* @file    Dio_Hw_Cfg.h
* @brief   Digital Input/Output for LSE14XX
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* <PERSON><PERSON><PERSON><PERSON>R CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON>ER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 

#ifndef DIO_HW_CFG_H
#define DIO_HW_CFG_H

#include "gpio_reg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DIO_HW_VENDOR_ID_CFG_H                       (110U)
#define DIO_HW_AR_RELEASE_MAJOR_VERSION_CFG_H        (4U)
#define DIO_HW_AR_RELEASE_MINOR_VERSION_CFG_H        (4U)
#define DIO_HW_AR_RELEASE_REVISION_VERSION_CFG_H     (0U)
#define DIO_HW_SW_MAJOR_VERSION_CFG_H                (1U)
#define DIO_HW_SW_MINOR_VERSION_CFG_H                (0U)
#define DIO_HW_SW_PATCH_VERSION_CFG_H                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define DIO_VIRTWRAPPER_SUPPORT        (STD_OFF)

/**
* @brief          Pre-processor switch to enable/disable development error detection for Gpio Hw API
*
* @implements     GpioDioErrorDetect_define
*/
#define DIO_HW_DEV_ERROR_DETECT        (STD_OFF)

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

#endif /* DIO_HW_CFG_H */

/** @} */
