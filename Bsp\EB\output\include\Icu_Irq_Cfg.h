/**
 * 
 * @file Icu_Irq_Cfg.h
 * @brief AUTOSAR Icu - contains the data exported by the Icu module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef ICU_IRQ_CFG_H
#define ICU_IRQ_CFG_H


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define ICU_IRQ_CFG_VENDOR_ID                       (110u)
#define ICU_IRQ_CFG_AR_RELEASE_MAJOR_VERSION        (4u)
#define ICU_IRQ_CFG_AR_RELEASE_MINOR_VERSION        (4u)
#define ICU_IRQ_CFG_AR_RELEASE_REVISION_VERSION     (0u)
#define ICU_IRQ_CFG_SW_MAJOR_VERSION                (1u)
#define ICU_IRQ_CFG_SW_MINOR_VERSION                (0u)
#define ICU_IRQ_CFG_SW_PATCH_VERSION                (0u)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/

/**************************************** PORT IRQ DEFINES ****************************************/

/**************************************** PWM IRQ DEFINES ****************************************/

#ifdef __cplusplus
}
#endif


#endif  /* ICU_IRQ_CFG_H */
