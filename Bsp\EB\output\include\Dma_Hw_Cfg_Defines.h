/**
 * file    Dma_Hw_Cfg_Defines.h
 * brief   Dma Hw Cfg Device defines header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
/* Prevention from multiple including the same header */
#ifndef DMA_HW_CFG_DEFINES_H
#define DMA_HW_CFG_DEFINES_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
 #define DMA_HW_CFG_DEFINES_VENDOR_ID                      (110U)
#define DMA_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_HW_CFG_DEFINES_SW_MAJOR_VERSION                (1U)
#define DMA_HW_CFG_DEFINES_SW_MINOR_VERSION                (0U)
#define DMA_HW_CFG_DEFINES_SW_PATCH_VERSION                (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types header file are of the same Autosar version */
#if ((DMA_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Dma_Hw_Cfg_Defines.h and Std_Types.h are different"
#endif
#endif

/*===============================================================================================
                                       DEFINES AND MACROS
===============================================================================================*/

/*-----------------------------------------------/
/  DMA IP SUPPORT                                /
/-----------------------------------------------*/
#define DMA_HW_IS_AVAILABLE                              STD_ON

/*-------------------------------------------------------/
/  DMA PARAM LIST DIMENASION RELATED TO HARDWARE VERSION /
/-------------------------------------------------------*/
#define DMA_HW_LOC_GLOBAL_PARAM_LIST_DIMENSION       ((uint32)8U)

/*-----------------------------------------------/
/  DMA HARDWARE INSTANCES                        /
/-----------------------------------------------*/
#define DMA_HW_INST_0                                 ((uint8)(0U))

/* @section [global]
 * 1534 ++ 
 * :The macro '%1s' is declared but not used within this project.
 * REASON: These macros are reserved for user usage.
 */
/*-----------------------------------------------/
/  DMA HARDWARE CHANNELS                         /
/-----------------------------------------------*/
#define DMA_HW_CH_0                                   ((uint8)(0U))
#define DMA_HW_CH_1                                   ((uint8)(1U))
#define DMA_HW_CH_2                                   ((uint8)(2U))
#define DMA_HW_CH_3                                   ((uint8)(3U))
#define DMA_HW_CH_4                                   ((uint8)(4U))
#define DMA_HW_CH_5                                   ((uint8)(5U))
#define DMA_HW_CH_6                                   ((uint8)(6U))
#define DMA_HW_CH_7                                   ((uint8)(7U))
#define DMA_HW_CH_8                                   ((uint8)(8U))
#define DMA_HW_CH_9                                   ((uint8)(9U))
#define DMA_HW_CH_10                                  ((uint8)(10U))
#define DMA_HW_CH_11                                  ((uint8)(11U))
#define DMA_HW_CH_12                                  ((uint8)(12U))
#define DMA_HW_CH_13                                  ((uint8)(13U))
#define DMA_HW_CH_14                                  ((uint8)(14U))
#define DMA_HW_CH_15                                  ((uint8)(15U))

/*-----------------------------------------------/
/  DMA CHANNEL PRIORITY                          /
/-----------------------------------------------*/
#define DMA_HW_LEVEL_PRIO0                               ((uint8)(0U))
#define DMA_HW_LEVEL_PRIO1                               ((uint8)(1U))
#define DMA_HW_LEVEL_PRIO2                               ((uint8)(2U))
#define DMA_HW_LEVEL_PRIO3                               ((uint8)(3U))
/* 1534--*/

#ifdef __cplusplus
}
#endif

#endif  /* #ifndef DMA_HW_CFG_DEFINES_H. */

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
