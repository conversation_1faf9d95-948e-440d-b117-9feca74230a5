#ifndef POD_MOTOR_H
#define POD_MOTOR_H
/// ---------- begin of guard: <includes> -----------------------------------
#include "motor.h"
#include "hsm.h"
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
typedef enum {
    CONTROL_BACK_REST,
    CONTROL_SEAT_LIFT,
    CONTROL_SEAT_SLIDE,
    CONTROL_FRONT_BACK,
    CONTROL_LEG_LIFT,
    CONTROL_LEG_STRETCH,

} eMotorControl;

typedef struct MotorEvent {
    eMotorControl control;
    tMotorOption option;
} tMotorEvent;
/// ---------- end of guard:   <header> -------------------------------------

void pod_motor_idle_entry(tHsmGenCurr* const ev);
void pod_motor_moving_entry(tHsmGenCurr* const ev);
void pod_motor_on_init(tHsmGenCurr* const ev);
void pod_motor_on_seat_events(tHsmGenCurr* const ev);
void pod_motor_on_tm10ms(tHsmGenCurr* const ev);

void pod_motor_on_motor1_start(tHsmGenCurr* const ev);
void pod_motor_on_motor1_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor1_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor1_idled(tHsmGenCurr* const ev);

#ifdef SEAT_SIDE_RIGHT

void pod_motor_on_motor2_start(tHsmGenCurr* const ev);
void pod_motor_on_motor2_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor2_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor2_idled(tHsmGenCurr* const ev);

void pod_motor_on_motor3_start(tHsmGenCurr* const ev);
void pod_motor_on_motor3_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor3_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor3_idled(tHsmGenCurr* const ev);

#endif

void pod_motor_on_motor4_start(tHsmGenCurr* const ev);
void pod_motor_on_motor4_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor4_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor4_idled(tHsmGenCurr* const ev);

void pod_motor_on_motor5_start(tHsmGenCurr* const ev);
void pod_motor_on_motor5_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor5_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor5_idled(tHsmGenCurr* const ev);

void pod_motor_on_motor6_start(tHsmGenCurr* const ev);
void pod_motor_on_motor6_stopping(tHsmGenCurr* const ev);
void pod_motor_on_motor6_stopped(tHsmGenCurr* const ev);
void pod_motor_on_motor6_idled(tHsmGenCurr* const ev);


void pod_motor_stalled_entry(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_MOTOR_H