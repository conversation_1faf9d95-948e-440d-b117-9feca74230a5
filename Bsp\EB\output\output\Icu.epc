<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Icu</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Icu</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/LS_LSE14M01I0R0/Icu</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-POST-BUILD</IMPLEMENTATION-CONFIG-VARIANT>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>122</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>110</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>IcuConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuMaxChannel</DEFINITION-REF>
                  <VALUE>12</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_0</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall1/IcuPwmGpcmChannels_Hall1_B_PWM4_CH4_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall2/IcuPwmGpcmChannels_Hall2_B_PWM4_CH2_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>8</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall3/IcuPwmGpcmChannels_Hall3_B_PWM1_CH1_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_3</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>9</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall4/IcuPwmGpcmChannels_Hall4_B_PWM1_CH5_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_4</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>10</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall5/IcuPwmGpcmChannels_Hall5_B_PWM5_CH3_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_5</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>11</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall6/IcuPwmGpcmChannels_Hall6_B_PWM3_CH2_NoUse</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall1/IcuPwmGpcmChannels_Hall1_A_PWM3_CH7</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall2/IcuPwmGpcmChannels_Hall2_A_PWM3_CH6</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL3</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall3/IcuPwmGpcmChannels_Hall3_A_PWM1_CH7</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL4</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall4/IcuPwmGpcmChannels_Hall4_A_PWM1_CH6</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL5</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall5/IcuPwmGpcmChannels_Hall5_A_PWM5_CH0</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuChannel_HALL6</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelId</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuDefaultStartEdge</DEFINITION-REF>
                      <VALUE>ICU_RISING_EDGE</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuMeasurementMode</DEFINITION-REF>
                      <VALUE>ICU_MODE_SIGNAL_MEASUREMENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuWakeupCapability</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Icu/Icu/IcuConfigSet/IcuPwmChannels_Hall6/IcuPwmGpcmChannels_Hall6_A_PWM5_CH1</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuSignalMeasurement</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuChannel/IcuSignalMeasurement/IcuSignalMeasurementProperty</DEFINITION-REF>
                          <VALUE>ICU_DUTY_CYCLE</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>14</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall1_A_PWM3_CH7</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_13</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH7</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall1_B_PWM4_CH4_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_27</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM4_CH4</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>15</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>9</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall2_A_PWM3_CH6</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_12</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH6</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall2_B_PWM4_CH2_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_11</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM4_CH2</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall3</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall3_A_PWM1_CH7</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_0_CH_13</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM1_CH7</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall3_B_PWM1_CH1_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_0_CH_1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM1_CH1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall4</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall4_A_PWM1_CH6</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_0_CH_12</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM1_CH6</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall4_B_PWM1_CH5_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_0_CH_11</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM1_CH5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall5</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>22</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall5_A_PWM5_CH0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_9</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM5_CH0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall5_B_PWM5_CH3_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_7</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH3</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>IcuPwmChannels_Hall6</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmId</DEFINITION-REF>
                      <VALUE>23</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>4294967294</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmPrescaler</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall6_A_PWM5_CH1</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>400</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_8</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM5_CH1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>IcuPwmGpcmChannels_Hall6_B_PWM3_CH2_NoUse</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPad</DEFINITION-REF>
                          <VALUE>PORT_2_CH_6</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuConfigSet/IcuPwmChannels/IcuPwmGpcmChannels/IcuPwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH2</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>IcuGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuGeneral/IcuDevErrorDetect</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuGeneral/IcuMulticoreSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuGeneral/IcuReportWakeupSource</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>IcuOptionalApis</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuDeInitApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuDisableWakeupApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuEdgeCountApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuEdgeDetectApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuEnableWakeupApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuGetDutyCycleValuesApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuGetInputStateApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuGetTimeElapsedApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuGetVersionInfoApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuSetModeApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuSignalMeasurementApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuTimestampApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Icu/IcuOptionalApis/IcuWakeupFunctionalityApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
