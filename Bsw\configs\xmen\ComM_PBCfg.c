/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : ComM_PBCfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:14 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/*=================================================[inclusions]=======================================================*/
#include "ComM_Cfg.h"
#include "ComM_PBCfg.h"
#include "CanSM.h"
#include "LinSM.h"
/*==========================================[external function definitions]===========================================*/
BEGIN_C_DECLS
/*=================================================[internal data]====================================================*/
#define COMM_START_SEC_CONST_PBCFG_8
#include "ComM_MemMap.h"
COMM_CONFIG_LOCAL CONST(uint8, COMM_CONST) ComM_UserDirectMapChanelTable[2] = {0u,1u};
COMM_CONFIG_LOCAL CONST(uint8, COMM_CONST) ComM_UserMapAllChanelTable[2] = {
    0u,1u,
};
COMM_CONFIG_LOCAL CONST(uint8, COMM_CONST) ComM_UserInChanelIdexTable[2] = {0u,0u};
#define COMM_STOP_SEC_CONST_PBCFG_8
#include "ComM_MemMap.h"

#define COMM_START_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
COMM_CONFIG_LOCAL CONST(ComM_UserConfigType, COMM_CONST) ComM_UserCfg[COMM_USER_NUMBER] = {
    /* ComMUser_0 */
    {
        0u,/*userId*/
        &ComM_UserDirectMapChanelTable[0],/*directChList*/
        1u,/*directChListNum*/
        &ComM_UserMapAllChanelTable[0],/*mapAllChList*/
        1u,/*mapAllChListNum*/
        &ComM_UserInChanelIdexTable[0],/*userInChIdex*/
    },
    /* ComMUser_DSCU */
    {
        1u,/*userId*/
        &ComM_UserDirectMapChanelTable[1],/*directChList*/
        1u,/*directChListNum*/
        &ComM_UserMapAllChanelTable[1],/*mapAllChList*/
        1u,/*mapAllChListNum*/
        &ComM_UserInChanelIdexTable[1],/*userInChIdex*/
    },
};
#define COMM_STOP_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"

#define COMM_START_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
COMM_CONFIG_LOCAL CONST(ComM_BusSmConfigType, COMM_CONST) ComM_BusSmCfgTable[2] = {
    {
        CanSM_RequestComMode,/*busSm_RequestComMode*/
        CanSM_GetCurrentComMode, /*busSm_GetCurrentComMode*/
    },
    {
        LinSM_RequestComMode,/*busSm_RequestComMode*/
        LinSM_GetCurrentComMode, /*busSm_GetCurrentComMode*/
    },
};
#define COMM_STOP_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
#define COMM_START_SEC_VAR_NO_INIT_8
#include "ComM_MemMap.h"
COMM_CONFIG_LOCAL VAR(uint8, AUTOMATIC) ComM_ReqMaskBuf_Channel_CanController_0[1];
COMM_CONFIG_LOCAL VAR(uint8, AUTOMATIC) ComM_ReqMaskBuf_ComMChannel_DSCU[1];
#define COMM_STOP_SEC_VAR_NO_INIT_8
#include "ComM_MemMap.h"

#define COMM_START_SEC_CONST_PBCFG_8
#include "ComM_MemMap.h"

COMM_CONFIG_LOCAL CONST(uint8, COMM_CONST) ComM_DcmNotifyId[1]={0x0u};
#define COMM_STOP_SEC_CONST_PBCFG_8
#include "ComM_MemMap.h"

#define COMM_START_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
COMM_CONFIG_LOCAL CONST(ComM_ChannelConfigType, COMM_CONST) ComM_ChannelConfig[COMM_CHANNEL_NUMBER] = {
    /* Channel_CanController_0 */
    {
        0u,/*inerChIdx*/
        COMM_BUS_TYPE_CAN,/*busType*/
        0u,/*busIndex*/
        &ComM_BusSmCfgTable[0],/*busSmCfgType*/
        COMM_NM_VARIANT_NONE,/*nmVariant*/
        1u,/*dcmInReqIdx*/
        &ComM_DcmNotifyId[0],/*dcmNotifyIdPtr*/
        0x0u,/*nmLightTimeout*/
        {0x0},/*cfgMask*/
        &ComM_ReqMaskBuf_Channel_CanController_0[0],/*requestMask*/
        1u, /*requestMaskLen*/
    },
    /* ComMChannel_DSCU */
    {
        1u,/*inerChIdx*/
        COMM_BUS_TYPE_LIN,/*busType*/
        1u,/*busIndex*/
        &ComM_BusSmCfgTable[1],/*busSmCfgType*/
        COMM_NM_VARIANT_LIGHT,/*nmVariant*/
        1u,/*dcmInReqIdx*/
        NULL_PTR,/*dcmNotifyIdPtr*/
        0x1388u,/*nmLightTimeout*/
        {0x0},/*cfgMask*/
        &ComM_ReqMaskBuf_ComMChannel_DSCU[0],/*requestMask*/
        1u, /*requestMaskLen*/
    },
};
#define COMM_STOP_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"

/*=================================================[external data]====================================================*/
#define COMM_START_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
CONST(ComM_ConfigType, COMM_CONST) ComM_Config = {  /* PRQA S 1531 */ /* MISRA Rule 8.7 */
    ComM_ChannelConfig,/*chCfgPtr*/
    ComM_UserCfg,/*userCfgPtr*/
    COMM_USER_NUMBER,/*userCfgNum*/
};
#define COMM_STOP_SEC_CONST_PBCFG_UNSPECIFIED
#include "ComM_MemMap.h"
END_C_DECLS

