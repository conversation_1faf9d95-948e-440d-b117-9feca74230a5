/**
 * 
 * @file Icu_PBcfg.h
 * @brief ICU config file
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, W<PERSON><PERSON>H<PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef ICU_PBCFG_H
#define ICU_PBCFG_H

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ICU_PBCFG_VENDOR_ID                    (110u)
#define ICU_PBCFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define ICU_PBCFG_AR_RELEASE_MINOR_VERSION     (4u)
#define ICU_PBCFG_AR_RELEASE_REVISION_VERSION  (0u)
#define ICU_PBCFG_SW_MAJOR_VERSION             (1u)
#define ICU_PBCFG_SW_MINOR_VERSION             (0u)
#define ICU_PBCFG_SW_PATCH_VERSION             (0u)

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define ICU_CONFIG_PB \
        extern const Icu_ConfigType Icu_Config;

#ifdef __cplusplus
}
#endif


#endif /* ICU_PBCFG_H */

