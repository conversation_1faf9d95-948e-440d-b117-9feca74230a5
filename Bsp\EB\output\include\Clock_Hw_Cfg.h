/**
 * *************************************************************************
 * @file   Clock_Hw_Cfg.h
 * @brief  This file is the header containing all the necessary information for CLOCK
 *            module configuration(s).
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

#ifndef CLOCK_HW_CFG_H
#define CLOCK_HW_CFG_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Clock_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CLOCK_HW_CFG_VENDOR_ID                       (110U)
#define CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define CLOCK_HW_CFG_SW_MAJOR_VERSION                (1U)
#define CLOCK_HW_CFG_SW_MINOR_VERSION                (0U)
#define CLOCK_HW_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Clock_Hw_Cfg.h file and Clock_Hw_PBcfg.h file are of the same vendor */
#if (CLOCK_HW_CFG_VENDOR_ID != CLOCK_HW_PBCFG_VENDOR_ID)
    #error "Clock_Hw_Cfg.h and Clock_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Clock_Hw_Cfg.h file and Clock_Hw_PBcfg.h file are of the same Autosar version */
#if ((CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION != CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION != CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION != CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_Cfg.h and Clock_Hw_PBcfg.h are different"
#endif

/* Check if Clock_Hw_Cfg.h file and Clock_Hw_PBcfg.h file are of the same Software version */
#if ((CLOCK_HW_CFG_SW_MAJOR_VERSION != CLOCK_HW_PBCFG_SW_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_SW_MINOR_VERSION != CLOCK_HW_PBCFG_SW_MINOR_VERSION) || \
     (CLOCK_HW_CFG_SW_PATCH_VERSION != CLOCK_HW_PBCFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Clock_Hw_Cfg.h and Clock_Hw_PBcfg.h are different"
#endif
/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
/**
* @brief            Pre-processor switch for enabling the default error detection and reporting to the DET.
*                   The detection of default errors is configurable (ON / OFF) at pre-compile time.
* @implements CLOCK_HW_DEV_ERROR_DETECT_Define
*/
#define CLOCK_HW_DEV_ERROR_DETECT         (STD_OFF)

#define CLOCK_HW_TIMEOUT_TYPE                (OSIF_COUNTER_DUMMY)

#define CLOCK_HW_TIMEOUT_VALUE_US            (50000U)
/**
* @brief        Support for User mode.
*               If this parameter has been configured to 'TRUE' the Clock can be executed from both supervisor and user mode.
*/
#define CLOCK_HW_ENABLE_USER_MODE_SUPPORT  (STD_OFF)

/** Check the driver user mode is enabled only when the MCAL_ENABLE_USER_MODE_SUPPORT is enabled */
#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
  #if (STD_ON == CLOCK_HW_ENABLE_USER_MODE_SUPPORT)
    #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Clock in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined.
  #endif /* (STD_ON == CLOCK_HW_ENABLE_USER_MODE_SUPPORT) */
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
                                             ENUMS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */
#endif /* CLOCK_HW_CFG_H */


