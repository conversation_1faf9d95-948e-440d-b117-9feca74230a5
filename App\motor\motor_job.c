#include "motor_job.h"
#include "log.h"

static tMotorJobQueue job_queue = {0};

usize motor_job_queue_size(void)
{
    return job_queue.front == -1 ? 0 : (job_queue.back - job_queue.front + 1) % MOTOR_JOB_QUEUE_SIZE;
}
      
bool motor_job_queue_is_empty(void)
{
    return job_queue.front == -1;
}

bool motor_job_queue_is_full(void)
{
    return job_queue.front == (job_queue.back + 1) % MOTOR_JOB_QUEUE_SIZE
        || (job_queue.front == 0 && job_queue.back == MOTOR_JOB_QUEUE_SIZE - 1);
}

void motor_job_init(void)
{
    memset(job_queue.jobs, 0, sizeof(job_queue.jobs));
    job_queue.front = -1;
    job_queue.back = -1;
}

void motor_job_push(tMotorJob* job)
{
        // overflow check
    if (motor_job_queue_is_full()) {
        return;
    }
    if (job_queue.front == -1) {
        job_queue.front = 0;
    }
    job_queue.back = (job_queue.back + 1) % MOTOR_JOB_QUEUE_SIZE;
    job_queue.jobs[job_queue.back].motor = job->motor;
    job_queue.jobs[job_queue.back].position = job->position;
    job_queue.jobs[job_queue.back].kind = job->kind;
    job_queue.jobs[job_queue.back].go_on = job->go_on;
    job_queue.jobs[job_queue.back].wait = job->wait;
}

tMotorJob* motor_job_pop(void)
{
    if (motor_job_queue_is_empty()) {
        return NULL;
    }
    // check array bounds
    if (job_queue.front < 0 || job_queue.front >= MOTOR_JOB_QUEUE_SIZE) {
        return NULL;
    }
    tMotorJob* job = &job_queue.jobs[job_queue.front];
    if (job_queue.front == job_queue.back) {
        job_queue.front = -1;
        job_queue.back = -1;
    } else {
        job_queue.front = (job_queue.front + 1) % MOTOR_JOB_QUEUE_SIZE;
    }
    return job;
}

static void motor_job_clean(tMotorJob *job)
{
    job->kind = JOB_NONE;
    job->motor = NULL;
    job->position = 0;
    job->wait = 0;
}

static void motor_job_dispatch(tMotorJob *job)
{
    switch (job->kind) {
    case JOB_MOVE: {
        println(LogInfo, "job: move %d to %d", job->motor->type, job->position);
        bool started = motor_move_to(job->motor, job->position);
        if (started) {
            motion_start();
        }
        break;
    }
    case JOB_FORCE: {
        println(LogInfo, "job: force %d to %d", job->motor->type, job->position);
        if (job->position <= 0) {
            motor_force(job->motor, OPTION_NEG);
        } else {
            motor_force(job->motor, OPTION_POS);
        }
        motion_start();
        break;
    }
    case JOB_CLEAR: {
        motor_do_clear(job->motor);
        break;
    }
    case JOB_RANGE: {
        println(LogInfo, "job: calib range %d", job->motor->type);
        motor_calib_range(job->motor);
        break;
    }
    default:
        break;
    }

    motor_job_clean(job);
}

void do_motor_job_queue(void)
{
    static u8 idle_count = 0;
    static u8 wait_count = 0;
    static u8 remained_jobs = 0;
    if (motors_all_idle())
    {
        if (motor_job_queue_is_empty()) {
            return;
        }
        idle_count++;
        if (idle_count >= 10) { 
            idle_count = 0;

            if (wait_count > 0) {
                wait_count--;
                return;
            }
            tMotorJob* job = motor_job_pop();
            motor_job_dispatch(job);
            // max two jobs at the same time
            if (job->go_on) {
                tMotorJob* job1 = motor_job_pop();
                motor_job_dispatch(job1);
            }
            remained_jobs = motor_job_queue_size();
            // if last job has a wait property, should wait for several loops
            if (job->wait > 0) {
                wait_count = job->wait;
            }
        }
    } else {
        idle_count = 0;
    }
    
}
