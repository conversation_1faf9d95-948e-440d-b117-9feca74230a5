/**
* 
* @file    Port_PBcfg.c
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
* @section[blobal]
* Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
* Reason: The macro definition names are similar, the version number definition prefixes are the same, 
* and the keywords are different.
*
*
*
*/ 

#include "Port.h"

#ifdef __cplusplus
extern "C" {
#endif
/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_VENDOR_ID_PBCFG_C                       (110U)
#define PORT_AR_RELEASE_MAJOR_VERSION_PBCFG_C        (4U)
#define PORT_AR_RELEASE_MINOR_VERSION_PBCFG_C        (4U)
#define PORT_AR_RELEASE_REVISION_VERSION_PBCFG_C     (0U)
#define PORT_SW_MAJOR_VERSION_PBCFG_C                (1U)
#define PORT_SW_MINOR_VERSION_PBCFG_C                (0U)
#define PORT_SW_PATCH_VERSION_PBCFG_C                (0U)


/*=================================================================================================
*                                      FILE VERSION CHECKS
=================================================================================================*/
/* Check if the files Port_PBcfg.c and Port.h are of the same vendor */
#if (PORT_VENDOR_ID_PBCFG_C != PORT_VENDOR_ID)
    #error "Port_PBcfg.c and Port.h have different vendor ids"
#endif
/* Check if the files Port_PBcfg.c and Port.h are of the same Autosar version */
#if ((PORT_AR_RELEASE_MAJOR_VERSION_PBCFG_C    != PORT_AR_RELEASE_MAJOR_VERSION)  || \
     (PORT_AR_RELEASE_MINOR_VERSION_PBCFG_C    != PORT_AR_RELEASE_MINOR_VERSION)  || \
     (PORT_AR_RELEASE_REVISION_VERSION_PBCFG_C != PORT_AR_RELEASE_REVISION_VERSION)  \
    )
    #error "AutoSar Version Numbers of Port_PBcfg.c and Port.h are different"
#endif
/* Check if the files Port_PBcfg.c and Port.h are of the same software version */
#if ((PORT_SW_MAJOR_VERSION_PBCFG_C != PORT_SW_MAJOR_VERSION) || \
     (PORT_SW_MINOR_VERSION_PBCFG_C != PORT_SW_MINOR_VERSION) || \
     (PORT_SW_PATCH_VERSION_PBCFG_C != PORT_SW_PATCH_VERSION)    \
    )
    #error "Software Version Numbers of Port_PBcfg.c and Port.h are different"
#endif

/*=================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
=================================================================================================*/


/*=================================================================================================
*                                       LOCAL MACROS
=================================================================================================*/
/**
* @brief The number of configured Digital Filter Ports
*/
#define PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8         (0U)

/*=================================================================================================
*                                      LOCAL CONSTANTS
=================================================================================================*/


/*=================================================================================================
*                                      LOCAL VARIABLES
=================================================================================================*/


/*=================================================================================================
*                                      GLOBAL CONSTANTS
=================================================================================================*/

#define PORT_START_SEC_CONFIG_DATA_8
#include "Port_MemMap.h"
/**
* @brief Array containing list of partition which used in driver
*/
static const uint8 au8Port_PartitionList[PORT_MAX_PARTITION] =
{
    (uint8)1
};
#define PORT_STOP_SEC_CONFIG_DATA_8
#include "Port_MemMap.h"

#define PORT_START_SEC_CONFIG_DATA_32
#include "Port_MemMap.h"
/**
* @brief Array containing list of mapping information for mappable elements
*/
/* [cover SWSID = SWS_Port_00075]
The PORT Driver module shall provide atomic access to all ports and port pins.*/

static const uint32 au32Port_PinToPartitionMap[PORT_MAX_CONFIGURED_PADS_U16] =
{
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001,
    (uint32)0x00000001
};
#define PORT_STOP_SEC_CONFIG_DATA_32
#include "Port_MemMap.h"

#define PORT_START_SEC_CONFIG_DATA_16
#include "Port_MemMap.h"

#if (0UL != PORT_MAX_UNUSED_PADS_U16)
/**
* @brief NoDefaultPadsArray is an array containing Unimplemented pads and User pads
*/
static const uint16 Port_au16NoUnUsedPadsArrayDefault[PORT_MAX_UNUSED_PADS_U16]=
{
    (uint16)25,
    (uint16)26,
    (uint16)27,
    (uint16)28,
    (uint16)29,
    (uint16)30,
    (uint16)31,
    (uint16)38,
    (uint16)39,
    (uint16)50,
    (uint16)52,
    (uint16)53,
    (uint16)54,
    (uint16)55,
    (uint16)57,
    (uint16)59,
    (uint16)60,
    (uint16)61,
    (uint16)83,
    (uint16)87,
    (uint16)91,
    (uint16)92,
    (uint16)93,
    (uint16)94,
    (uint16)95,
    (uint16)114,
    (uint16)115,
    (uint16)118,
    (uint16)119,
    (uint16)120,
    (uint16)123,
    (uint16)124,
    (uint16)125,
    (uint16)126,
    (uint16)147,
    (uint16)148,
    (uint16)149,
    (uint16)150,
    (uint16)151,
    (uint16)152,
    (uint16)153
};
#endif

#define PORT_STOP_SEC_CONFIG_DATA_16
#include "Port_MemMap.h"

#define PORT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"
/**
 * @brief Default Configuration for Pins not initialized
 */
static const PorUnUsedPinConfig_t Port_UnUsedPin =
{
    /* @note: Configuration of Default pin */
    (uint32)0x00000001, (uint32)0x00000000, PORT_PIN_IN, (uint8)0U
};

/**
* @brief Pin default configuration data for configPB
*/
static const PortPinConfig_t Port_aPinConfigDefault[PORT_MAX_CONFIGURED_PADS_U16]=
{
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)141, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)96, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)133, (uint32)0x00000005, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)132, (uint32)0x00000005, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)97, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)105, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)110, (uint32)0x00000005, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)109, (uint32)0x00000005, (uint32)0x0000000d,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)140, (uint32)0x00000005, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)113, (uint32)0x00000005, (uint32)0x0000000c,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)32, (uint32)0x00000002, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)33, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)139, (uint32)0x00000004, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)138, (uint32)0x00000004, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)3, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)2, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)112, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)111, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)137, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)136, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)37, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)36, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)101, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)108, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)107, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)106, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)99, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)98, (uint32)0x00000002, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)65, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)130, (uint32)0x00000000, (uint32)0x0000000c,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)134, (uint32)0x00000000, (uint32)0x0000000c,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)15, (uint32)0x00000000, (uint32)0x0000000c,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)16, (uint32)0x00000000, (uint32)0x0000000c,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)64, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)81, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)80, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)79, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)78, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)35, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)7, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)6, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)48, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)47, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)46, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)45, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)67, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)102, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)49, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)41, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)71, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)14, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)142, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)66, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)104, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)44, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)40, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)74, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)131, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)103, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)17, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)100, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)1, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)75, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)77, (uint32)0x00000002, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)76, (uint32)0x00000002, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)13, (uint32)0x00000002, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)12, (uint32)0x00000002, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)73, (uint32)0x00000004, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)72, (uint32)0x00000004, (uint32)0x00000001,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)43, (uint32)0x00000001, (uint32)0x0000000d,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)42, (uint32)0x00000001, (uint32)0x0000000d,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)0, (uint32)0x00000001, (uint32)0x0000000d,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)70, (uint32)0x00000000, (uint32)0x00000001,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)8, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)9, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)144, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)128, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)129, (uint32)0x00000001, (uint32)0x00000000,(uint32)0x00000000,(uint8)1, PORT_PIN_OUT, TRUE, TRUE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)11, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)0, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value, Output Level, Direction , IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)34, (uint32)0x00000000, (uint32)0x00000000,(uint32)0x00000000,(uint8)2, PORT_PIN_IN, FALSE, FALSE, TRUE},
    /* PCR Id, IOSEL Value, IOCFG Value, DLP Value,Output Level, Direction, IsGpio, Direction Configurable, Mode Changeable */
    {(uint16)143, (uint32)0x00000001, (uint32)0x00000000, (uint32)0x00000000, (uint8)0,PORT_PIN_OUT, TRUE, TRUE, TRUE}

};

#if (0UL != PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8)
static const PortDigitalFilterConfig_t Port_aDigitalFilter[PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8]=
{

};
#endif /* (0UL != PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8).. */

/**
* @brief This table contains all the Configured Port Pin parameters and the
*        number of Port Pins configured by the tool for the post-build mode
* that will be used by the upper layers.
*/
/* [cover SWSID = SWS_Port_00003]
The Port Driver module environment may also uses the function Port_Init to initialize the driver software and reinitialize the ports 
and port pins to another configured state depending on the configuration set passed to this function.*/

const Port_ConfigType Port_Config =
{
    PORT_MAX_CONFIGURED_PADS_U16,
    PORT_MAX_UNUSED_PADS_U16,
#if (0UL != PORT_MAX_UNUSED_PADS_U16)
    Port_au16NoUnUsedPadsArrayDefault,
#else
    NULL_PTR,
#endif
    &Port_UnUsedPin,
    Port_aPinConfigDefault,
    PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8,
#if (0UL != PORT_MAX_CONFIGURED_DIGITAL_FILTER_PORTS_U8)
    Port_aDigitalFilter,
#else
    NULL_PTR,
#endif
    au32Port_PinToPartitionMap,
    au8Port_PartitionList,
    g_pin_mux_InitConfigArr
};

#define PORT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"
/*=================================================================================================
*                                      GLOBAL VARIABLES
=================================================================================================*/

/*=================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
=================================================================================================*/

/*=================================================================================================
*                                       LOCAL FUNCTIONS
=================================================================================================*/

/*=================================================================================================
*                                       GLOBAL FUNCTIONS
=================================================================================================*/


#ifdef __cplusplus
}
#endif
/** @} */

/* End of File */

