/**
 * file    Uart_Ipw_PBcfg.c
 * brief   UART driver for LES14XX
 * author  xiali
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Uart_Ipw_Cfg.h"
#ifdef UART_IPW_LPUART_HW_USING
#include "Uart_Hw_Cfg.h"
#endif
#ifdef UART_IPW_FLEXIO_HW_USING
#include "Flexio_Uart_Hw_Cfg.h"
#endif

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define UART_IPW_PBCFG_VENDOR_ID_C                     (110u)
#define UART_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define UART_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define UART_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define UART_IPW_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define UART_IPW_PBCFG_SW_MINOR_VERSION_C              (0u)
#define UART_IPW_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Uart_Ipw_Cfg.h */
#if (UART_IPW_PBCFG_VENDOR_ID_C != UART_IPW_CFG_VENDOR_ID)
    #error "Uart_Ipw_PBcfg.c and Uart_Ipw_Cfg.h have different vendor ids"
#endif
#if ((UART_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != UART_IPW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != UART_IPW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C != UART_IPW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Uart_Ipw_PBcfg.c and Uart_Ipw_Cfg.h are different"
#endif
#if ((UART_IPW_PBCFG_SW_MAJOR_VERSION_C != UART_IPW_CFG_SW_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_SW_MINOR_VERSION_C != UART_IPW_CFG_SW_MINOR_VERSION) || \
     (UART_IPW_PBCFG_SW_PATCH_VERSION_C != UART_IPW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_Ipw_PBcfg.c and Uart_Ipw_Cfg.h are different"
#endif

#ifdef UART_IPW_LPUART_HW_USING
/* Checks against Uart_Hw_Cfg.h */
#if (UART_IPW_PBCFG_VENDOR_ID_C != LPUART_UART_HW_CFG_VENDOR_ID)
    #error "Uart_Ipw_PBcfg.c and Uart_Hw_Cfg.h have different vendor ids"
#endif
#if ((UART_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LPUART_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LPUART_UART_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LPUART_UART_HW_CFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Uart_Ipw_PBcfg.c and Uart_Hw_Cfg.h are different"
#endif
#if ((UART_IPW_PBCFG_SW_MAJOR_VERSION_C != LPUART_UART_HW_CFG_SW_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_SW_MINOR_VERSION_C != LPUART_UART_HW_CFG_SW_MINOR_VERSION) || \
     (UART_IPW_PBCFG_SW_PATCH_VERSION_C != LPUART_UART_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_Ipw_PBcfg.c and Uart_Hw_Cfg.h are different"
#endif
#endif

#ifdef UART_IPW_FLEXIO_HW_USING
/* Checks against Flexio_Uart_Hw_Cfg.h */
#if (UART_IPW_PBCFG_VENDOR_ID_C != FLEXIO_UART_HW_CFG_VENDOR_ID)
    #error "Uart_Ipw_PBcfg.c and Flexio_Uart_Hw_Cfg.h have different vendor ids"
#endif
#if ((UART_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != FLEXIO_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != FLEXIO_UART_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C != FLEXIO_UART_HW_CFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Uart_Ipw_PBcfg.c and Flexio_Uart_Hw_Cfg.h are different"
#endif
#if ((UART_IPW_PBCFG_SW_MAJOR_VERSION_C != FLEXIO_UART_HW_CFG_SW_MAJOR_VERSION) || \
     (UART_IPW_PBCFG_SW_MINOR_VERSION_C != FLEXIO_UART_HW_CFG_SW_MINOR_VERSION) || \
     (UART_IPW_PBCFG_SW_PATCH_VERSION_C != FLEXIO_UART_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_Ipw_PBcfg.c and Flexio_Uart_Hw_Cfg.h are different"
#endif
#endif
/*==================================================================================================
                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"

#ifdef UART_IPW_LPUART_HW_USING
/**
* @brief   Export Lpuart Uart configurations.
*/
LPUART_UART_HW_CONFIG_EXT
#endif

#ifdef UART_IPW_FLEXIO_HW_USING
/**
* @brief   Export Flexio Uart configurations.
*/
FLEXIO_UART_HW_CONFIG_EXT
#endif
#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"
/*==================================================================================================
*                         LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                  LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/
#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"




/**
* @brief   The Ip configuration structure pointer
*/
static const Uart_Ipw_IpConfig_t Uart_Ipw_IpChnConfigPB_0 =
{

    /* Lpuart Ip configuration structure  */
    &Lpuart_Uart_Hw_xHwConfigPB_0,


    /* Not use Flexio Uart Ip configuration structure  */
    NULL_PTR

};

/**
* @brief    Hardware configuration for Uart Hardware - Configuration.
*/

const Uart_Ipw_HwConfig_t Uart_Ipw_xHwConfigPB_0 =
{
    /* Uart Hardware Channel.*/
    0U,
    /* Type of Hardware interface configured.*/
    LPUART_HW,
    /* User Callback */
    (Uart_Callback_t)BluetoothUartCallback,
    /* Pointer to the Ip configuration structure.*/
    &Uart_Ipw_IpChnConfigPB_0
};

#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"
/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/


#ifdef __cplusplus
}

/** @} */

#endif
