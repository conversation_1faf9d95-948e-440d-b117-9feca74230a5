#ifndef POD_CMD_H
#define POD_CMD_H
/// ---------- begin of guard: <includes> -----------------------------------
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
/// ---------- end of guard:   <header> -------------------------------------

void pod_cmd_on_bluetooth(tHsmGenCurr* const ev);
void pod_cmd_on_init(tHsmGenCurr* const ev);
void pod_cmd_on_io_key(tHsmGenCurr* const ev);
void pod_cmd_on_key(tHsmGenCurr* const ev);
void pod_cmd_on_lin(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_CMD_H