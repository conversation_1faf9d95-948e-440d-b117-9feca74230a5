/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Dcm_Callout.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:15 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#include "Dcm_Internal.h"
#include "UDS.h"
#include "DcmDsl_MsgManage.h"
#include "Dcm_CalloutBoot.h"
extern VAR(uint32, DCM_VAR_POWER_ON_INIT) Dcm_Timer;

/* PRQA S 2053++ */ /* MISRA Dir 4.4 */
/* BL_AppFlagType* BL_AppFlag = (BL_AppFlagType*) BL_APP_FLAG_ADDRESS; */
/* PRQA S 2053-- */ /* MISRA Dir 4.4 */

/* PRQA S 3673++ */ /* MISRA Rule 8.13 */
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType Dcm_SetProgConditions(
Dcm_OpStatusType OpStatus,
Dcm_ProgConditionsType * ProgConditions/* PRQA S 3334 */ /* MISRA Rule 5.3 */
)
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(ProgConditions);
/* PRQA S 2053++ */ /* MISRA Dir 4.4 */
    /*TODO: Set ReProgramingRequest Flag*/
    /*e.g*/
    /*    BL_AppFlag->FlBootMode = (uint32)FL_EXT_PROG_REQUEST_RECEIVED;
    BL_AppFlag->Busmode = BUS_MODE_ETH;*/
/* PRQA S 2053-- */ /* MISRA Dir 4.4 */
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Dcm_EcuStartModeType Dcm_GetProgConditions(/* PRQA S 3408 */ /* MISRA Rule 8.4 */
Dcm_ProgConditionsType * ProgConditions/* PRQA S 3334 */ /* MISRA Rule 5.3 */
)
{
    DCM_UNUSED(ProgConditions);
/* PRQA S 2053++ */ /* MISRA Rule 18.8 */
    /*TODO: check ApplUpdated*/
    /*e.g*/
    /*    if (BL_AppFlag->FlApplUpdate == (uint32)FL_APPL_UPDATED)
    {
        ProgConditions->ApplUpdated = TRUE;
        BL_AppFlag->FlApplUpdate = (uint32)0;
        return DCM_WARM_START;
    }*/
/* PRQA S 2053-- */ /* MISRA Rule 18.8 */
    return DCM_COLD_START;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

/*for 0x23 service to read data by memory address*/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Dcm_ReturnReadMemoryType Dcm_ReadMemory(Dcm_OpStatusType OpStatus,
        uint8 MemoryIdentifier,
        uint32 MemoryAddress,
        uint32 MemorySize,
        uint8* MemoryData,
        Dcm_NegativeResponseCodeType* ErrorCode)
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(MemoryIdentifier);
    DCM_UNUSED(MemoryAddress);
    DCM_UNUSED(MemorySize);
    DCM_UNUSED(MemoryData);
    DCM_UNUSED(ErrorCode);
    return DCM_READ_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
/*for 0x3D service to write data by memory address*/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Dcm_ReturnWriteMemoryType Dcm_WriteMemory(Dcm_OpStatusType OpStatus,
                                            uint8 MemoryIdentifier,
                                            uint32 MemoryAddress,
                                            uint32 MemorySize,
                                            uint8* MemoryData,
                                            Dcm_NegativeResponseCodeType* ErrorCode)
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(MemoryIdentifier);
    DCM_UNUSED(MemoryAddress);
    DCM_UNUSED(MemorySize);
    DCM_UNUSED(MemoryData);
    DCM_UNUSED(ErrorCode);
    return DCM_WRITE_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"
/*for 0x34 service to request download*/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType Dcm_ProcessRequestDownload(Dcm_OpStatusType OpStatus,
                                            uint8 DataFormatIdentifier,
                                            uint32 MemoryAddress,
                                            uint32 MemorySize,
                                            uint32* BlockLength,
                                            Dcm_NegativeResponseCodeType* ErrorCode)
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(DataFormatIdentifier);
    DCM_UNUSED(MemoryAddress);
    DCM_UNUSED(MemorySize);
    DCM_UNUSED(BlockLength);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

/* service to check DataFormatIdentifier, send NRC31 if return E_NOT_OK*/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
Std_ReturnType Dcm_DataFormatIdentifierCheck(uint8 DataFormatIdentifier)
{
    /** DO NOT CHANGE THIS COMMENT!
    * <USERBLOCK Dcm_DataFormatIdentifierCheck>
    */
    /* custom code.... */
    DCM_UNUSED(DataFormatIdentifier);
    return E_OK;
    /** DO NOT CHANGE THIS COMMENT!
    * </USERBLOCK>
    */
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

/*for 0x37 service to request transfer exit*/
#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
/* PRQA S 0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
Std_ReturnType Dcm_ProcessRequestTransferExit(Dcm_OpStatusType OpStatus,
                                                uint8* transferRequestParameterRecord,
                                                uint32 transferRequestParameterRecordSize,
                                                uint8* transferResponseParameterRecord,
                                                uint32* transferResponseParameterRecordSize,
                                                Dcm_NegativeResponseCodeType* ErrorCode)
/* PRQA S 0779-- */ /* MISRA Rule 1.3,Rule 5.2 */
{
    DCM_UNUSED(OpStatus);
    DCM_UNUSED(transferRequestParameterRecord);
    DCM_UNUSED(transferRequestParameterRecordSize);
    DCM_UNUSED(transferResponseParameterRecord);
    DCM_UNUSED(transferResponseParameterRecordSize);
    DCM_UNUSED(ErrorCode);
    return E_OK;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
void Rte_DcmControlCommunicationMode(NetworkHandleType DcmDspComMChannelId,Dcm_CommunicationModeType RequestedMode)
{
    DCM_UNUSED(DcmDspComMChannelId);
    switch(RequestedMode)
    {
        case DCM_ENABLE_RX_TX_NORM:
        break;
        case DCM_DISABLE_RX_TX_NM:
        break;
        case DCM_ENABLE_RX_TX_NM:
        break;
        case DCM_ENABLE_RX_TX_NORM_NM:
        break;
        case DCM_DISABLE_RX_TX_NORMAL:
        break;
        case DCM_DISABLE_RX_TX_NORM_NM:
        break;
        case DCM_ENABLE_RX_DISABLE_TX_NORM:
        break;
        case DCM_ENABLE_RX_DISABLE_TX_NM:
        break;
        case DCM_ENABLE_RX_DISABLE_TX_NORM_NM:
        break;
        case DCM_DISABLE_RX_ENABLE_TX_NORM:
        break;
        case DCM_DISABLE_RX_ENABLE_TX_NM:
        break;
        case DCM_DISABLE_RX_ENABLE_TX_NORM_NM:
        break;
        default:
        break;
    }
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
void Rte_EnableAllDtcsRecord(void)
{
   /*The update of the DTC status bit information shall continue once a ControlDTCSetting request is performed
     with sub-function set to on or a session layer timeout occurs (server transitions to defaultSession. */
    (void)Dem_DcmEnableDTCSetting(DEM_DTC_GROUP_ALL_DTCS, DEM_DTC_KIND_ALL_DTCS);
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
/* Tm and Os implementation for timer. If DcmTimerType is set to callout, need to implement manually */
/* PRQA S 3432++ */ /* MISRA Rule 20.7 */
FUNC(void, DCM_CODE) Dcm_ResetTime(P2VAR(uint32, AUTOMATIC, DCM_VAR) TimerPtr)
/* PRQA S 3432-- */ /* MISRA Rule 20.7 */
{
    *TimerPtr = Dcm_Timer;
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

#define DCM_START_SEC_CODE
#include "Dcm_MemMap.h"
/* Tm and Os implementation for timer. If DcmTimerType is set to callout, need to implement manually */
/* PRQA S 3432++ */ /* MISRA Rule 20.7 */
FUNC(void, DCM_CODE) Dcm_GetTimeSpan(uint32 TimerPtr,P2VAR(uint32, AUTOMATIC, DCM_VAR) TimeSpanPtr)
/* PRQA S 3432-- */ /* MISRA Rule 20.7 */
{
    if (TimerPtr > Dcm_Timer)
    {
        *TimeSpanPtr = (DCM_TIME_OVERFLOW - TimerPtr) + Dcm_Timer;
    }
    else
    {
        *TimeSpanPtr = Dcm_Timer - TimerPtr;
    }
}
#define DCM_STOP_SEC_CODE
#include "Dcm_MemMap.h"

/* PRQA S 3673-- */ /* MISRA Rule 8.13 */
