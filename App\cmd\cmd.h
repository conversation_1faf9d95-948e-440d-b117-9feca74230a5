#ifndef CMD_H
#define <PERSON><PERSON>_H

#include "types.h"

#define DEFAULT_FAN_PWM 0x4000u
#define DEFAULT_HEATER_PWM 0x4000u
#define DEFAULT_LEFT_WAIST_PWM 0x4000u
#define DEFAULT_RIGHT_WAISTPWM 0x4000u
#define MAX_PWM 0x8000u

typedef enum {
    CMD_NONE,

    // motor related
    CMD_BACK_REST,
    CMD_SEAT_LIFT,
    C<PERSON>_SEAT_SLIDE,
    CMD_FRONT_BACK,
    CMD_LEG_LIFT,
    CMD_LEG_STRETCH,

    // others
    CMD_ZERO_GRAVITY,

    C<PERSON>_MASSAGE,
    CMD_HEATER,
    CMD_FAN,
    C<PERSON>_WAIST,
    C<PERSON>_MASSAGE_MODE,
    C<PERSON>_MASSAGE_INTENSITY,
    C<PERSON>_BACK_FAN_SWITCH,
    CMD_BACK_FAN_LEVEL,
    CMD_SEAT_FAN_SWITCH,
    C<PERSON>_SEAT_FAN_LEVEL,
    <PERSON><PERSON>_BACK_HEATER,
    C<PERSON>_BACK_HEATER_LEVEL,
    CMD_SEAT_HEATER,
    CMD_SEAT_HEATER_LEVEL,
    CMD_ALL_RESET,
    CMD_CALIBRATION,
    CMD_MAX,

} tCmdKind;

typedef enum {
    ACTION_NONE,
    ACTION_POS,
    ACTION_NEG,
    ACTION_OFF,
} tCmdAction;

typedef enum {
    WAIST_NONE,
    WAIST_UP,
    WAIST_DOWN,
    WAIST_FRONT,
    WAIST_BACK,
} tWaistAction;

typedef struct Cmd {
    tCmdKind kind;
    tCmdAction action;
    u8 level;
} tCmd;


/// @brief 简单电机控制
/// @param cmd 
extern void do_simple_motor(tCmd cmd);

/// @brief 一键零重力控制
/// @param cmd 
extern void do_zero_gravity(tCmd cmd);

/// @brief 座椅按摩
extern void do_massage(tCmd cmd);
extern void do_massage_mode(tCmd cmd);
extern void do_massage_intensity(tCmd cmd);

/// @brief 座椅加热
extern void do_heater_back(tCmd cmd);
extern void do_heater_seat(tCmd cmd);
extern void do_heater_all(tCmd cmd);
extern void do_heater_seat_level(tCmd cmd);
extern void do_heater_back_level(tCmd cmd);

/// @brief 座椅通风
extern void do_fan_back(tCmd cmd);
extern void do_fan_seat(tCmd cmd);
extern void do_fan_all(tCmd cmd);
extern void do_fan_back_level(tCmd cmd);
extern void do_fan_seat_level(tCmd cmd);

/// @brief 左侧腰托
extern void do_waist(tCmd cmd);

#endif
