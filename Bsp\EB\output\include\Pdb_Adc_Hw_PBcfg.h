/**
 * file    Pdb_Adc_Hw_PBcfg.c
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef PDB_ADC_HW_PBCFG_H
#define PDB_ADC_HW_PBCFG_H

/**
*   @file
*
*   @addtogroup pdb_adc_hw_config Pdb Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/
#include "Pdb_Adc_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PDB_ADC_HW_VENDOR_ID_PBCFG                     (110u)
#define PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG      (4u)
#define PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG      (4u)
#define PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG   (0u)
#define PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG              (1u)
#define PDB_ADC_HW_SW_MINOR_VERSION_PBCFG              (0u)
#define PDB_ADC_HW_SW_PATCH_VERSION_PBCFG              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Pdb_Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same vendor */
#if (PDB_ADC_HW_VENDOR_ID_PBCFG != PDB_ADC_HW_VENDOR_ID_TYPES)
    #error "Pdb_Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h have different vendor ids"
#endif

/* Check if Pdb_Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same Autosar version */
#if ((PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_TYPES) || \
     (PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_TYPES) || \
     (PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_TYPES) \
    )
    #error "AutoSar Version Numbers of Pdb_Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/* Check if Pdb_Adc_Hw_PBcfg.h file and Pdb_Adc_Hw_Types.h file are of the same Software version */
#if ((PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG != PDB_ADC_HW_SW_MAJOR_VERSION_TYPES) || \
     (PDB_ADC_HW_SW_MINOR_VERSION_PBCFG != PDB_ADC_HW_SW_MINOR_VERSION_TYPES) || \
     (PDB_ADC_HW_SW_PATCH_VERSION_PBCFG != PDB_ADC_HW_SW_PATCH_VERSION_TYPES) \
    )
  #error "Software Version Numbers of Pdb_Adc_Hw_PBcfg.h and Pdb_Adc_Hw_Types.h are different"
#endif

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                      EXTERN DECLARATIONS
==================================================================================================*/
#define ADC_START_SEC_CODE
#include "Adc_MemMap.h"



#define ADC_STOP_SEC_CODE
#include "Adc_MemMap.h"
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"




            
        
/**
* @brief          PDB Hw channel Config for Hardware Unit ADC0 for configuration variant .
*/
extern const Pdb_Adc_Hw_ChanConfig_t PdbAdcHwChConfig_0[2];    
    
/**
* @brief          PDB Hw Config for Hardware Unit ADC0 for configuration variant .
*/
extern const Pdb_Adc_Hw_Config_t PdbAdcHwConfig_0;


            
        
/**
* @brief          PDB Hw channel Config for Hardware Unit ADC1 for configuration variant .
*/
extern const Pdb_Adc_Hw_ChanConfig_t PdbAdcHwChConfig_1[2];    
    
/**
* @brief          PDB Hw Config for Hardware Unit ADC1 for configuration variant .
*/
extern const Pdb_Adc_Hw_Config_t PdbAdcHwConfig_1;



#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PDB_ADC_HW_PBCFG_H */

