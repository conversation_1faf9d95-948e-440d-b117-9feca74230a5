#include <math.h>
#include "motor.h"
#include "log.h"
#include "Pwm_Cfg.h"
#include "motor_job.h"
#include "mem.h"
#include "os_pod.h"
#include "pod_motor.h"
#include "motor_stall.h"


tMotorSeat motors_seat;
tMotor motors[MOTOR_MAX] = {0};
tMotorType CURRENT_MOTOR = MOTOR_1_BACK_REST;
static u32 DEFAULT_PWM = 0x8000;
static u32 zero_gravity_pos[MOTOR_MAX] = {0};
static u32 default_pos[MOTOR_MAX] = {0};
extern void pwm_out_control(tMotor* motor, uint32_t pwm);

const u32 default_ranges[MOTOR_MAX] = {
#ifdef SEAT_SIDE_RIGHT
    [MOTOR_1_BACK_REST] = 1090,
    [MOTOR_2_SEAT_LIFT] = 880,
    [MOTOR_3_SEAT_SLIDE] = 299,
    [MOTOR_4_FRONT_BACK] = 653,
    [MOTOR_5_LEG_LIFT] = 658,
    [MOTOR_6_LEG_STRETCH] = 320,
#else
    [MOTOR_1_BACK_REST] = 914,
    [MOTOR_4_FRONT_BACK] = 372,
    [MOTOR_5_LEG_LIFT] = 655,
    [MOTOR_6_LEG_STRETCH] = 320,
#endif
};

static const u32 speed_rates[MOTOR_MAX] = {
    [MOTOR_1_BACK_REST] = 45,
#ifdef SEAT_SIDE_RIGHT
    [MOTOR_2_SEAT_LIFT] = 25,
    [MOTOR_3_SEAT_SLIDE] = 25,
#endif
    [MOTOR_4_FRONT_BACK] = 30,
    [MOTOR_5_LEG_LIFT] = 45,
    [MOTOR_6_LEG_STRETCH] = 35,
};

tMotor motor_new(tMotorType type)
{
    return (tMotor) {
        .type = type,
    };
}

// NOTE: zero gravity position depedens on range of motor, 
// so when a calibration is done,
// zero positions should also be updated
static void init_zero_gravity_pos(void)
{
    zero_gravity_pos[MOTOR_1_BACK_REST] = motors[MOTOR_1_BACK_REST].config.range * 5.0 / 6;
#ifdef SEAT_SIDE_RIGHT
    zero_gravity_pos[MOTOR_2_SEAT_LIFT] = motors[MOTOR_2_SEAT_LIFT].config.range * 6.0 / 10;
    zero_gravity_pos[MOTOR_3_SEAT_SLIDE] = motors[MOTOR_3_SEAT_SLIDE].config.range * 3.0 / 4;
#endif
    zero_gravity_pos[MOTOR_4_FRONT_BACK] = motors[MOTOR_4_FRONT_BACK].config.range * 4.0 / 5;
    zero_gravity_pos[MOTOR_5_LEG_LIFT] = motors[MOTOR_5_LEG_LIFT].config.range * 5.0 / 6;
    zero_gravity_pos[MOTOR_6_LEG_STRETCH] = motors[MOTOR_6_LEG_STRETCH].config.range - POS_PROXIMITY;
}

static void init_default_pos(void)
{
    default_pos[MOTOR_1_BACK_REST] = motors[MOTOR_1_BACK_REST].config.range * 1.0 / 5;
#ifdef SEAT_SIDE_RIGHT
    default_pos[MOTOR_2_SEAT_LIFT] = motors[MOTOR_2_SEAT_LIFT].config.range * 1.0 / 6;
    default_pos[MOTOR_3_SEAT_SLIDE] = motors[MOTOR_3_SEAT_SLIDE].config.range * 0 + POS_PROXIMITY;
#endif
    default_pos[MOTOR_4_FRONT_BACK] = motors[MOTOR_4_FRONT_BACK].config.range * 1.0 / 2;
    default_pos[MOTOR_5_LEG_LIFT] = motors[MOTOR_5_LEG_LIFT].config.range * 0 + POS_PROXIMITY;
    default_pos[MOTOR_6_LEG_STRETCH] = motors[MOTOR_6_LEG_STRETCH].config.range * 0 + POS_PROXIMITY;
}

static void init_configs(void)
{
    motors[MOTOR_1_BACK_REST].config = (tMotorConfig) {
        // 靠背实际方向：A展开，B收起；我们定义0位置为收起，因此向Foword对应展开，Backword对应收起。
#ifdef SEAT_SIDE_RIGHT
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B,
        .stall_voltage = 1400,
#else
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A,
        .stall_voltage = 800,
#endif
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL1,
        .gpmc_id = 0,
        .range = default_ranges[MOTOR_1_BACK_REST],
        .rated_speed = speed_rates[MOTOR_1_BACK_REST],
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
    };

#ifdef SEAT_SIDE_RIGHT
    motors[MOTOR_2_SEAT_LIFT].config = (tMotorConfig) {
        // 座椅升降实际方向：A抬升，B下降。我们定义0位置为最低，因此Forward对应抬升，Backward对应下降
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_A,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_B,
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL2,
        .gpmc_id = 1,
        .range = default_ranges[MOTOR_2_SEAT_LIFT],
        .rated_speed = speed_rates[MOTOR_2_SEAT_LIFT],
        .stall_voltage = 1395,
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
    };

    motors[MOTOR_3_SEAT_SLIDE].config = (tMotorConfig) {
        // 座椅侧滑实际方向：A向右，B向左
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_B,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_A,
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL3,
        .gpmc_id = 2,
        .range = default_ranges[MOTOR_3_SEAT_SLIDE],
        .rated_speed = speed_rates[MOTOR_3_SEAT_SLIDE],
        .stall_voltage = 1350,
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
    };
#endif

    motors[MOTOR_4_FRONT_BACK].config = (tMotorConfig) {
        // 座椅实际方向：A前，B后
#ifdef SEAT_SIDE_RIGHT
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_B,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_A,
#else
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_A,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_B,
#endif
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL4,
        .gpmc_id = 8,
        .range = default_ranges[MOTOR_4_FRONT_BACK],
        .rated_speed = speed_rates[MOTOR_4_FRONT_BACK],
        .stall_voltage = 1400,
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
    };

    motors[MOTOR_5_LEG_LIFT].config = (tMotorConfig) {
        // 腿托上下实际方向：A开，B收; Forward对应打开
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_A,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_B,
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL5,
        .gpmc_id = 9,
        .range = default_ranges[MOTOR_5_LEG_LIFT],
        .rated_speed = speed_rates[MOTOR_5_LEG_LIFT],
        .stall_voltage = 1400,
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
    };

    motors[MOTOR_6_LEG_STRETCH].config = (tMotorConfig) {
        .channel_forward = PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_A,
        .channel_backward = PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_B,
        .channel_hall = IcuConf_IcuChannel_IcuChannel_HALL6,
        .gpmc_id = 12,
        .range = default_ranges[MOTOR_6_LEG_STRETCH],
        .rated_speed = speed_rates[MOTOR_6_LEG_STRETCH],
        .stall_voltage = 700,
        .speed_pid.kp = 200,
        .speed_pid.ki = 40,
        .speed_pid.kd = 0,
        .position_pid.kp = 8,
        .position_pid.ki = 0,
        .position_pid.kd = 0,
        .openloop = true,
    };

}

static void init_stall_configs(void)
{
    for (tU8 id = MOTOR_1_BACK_REST; id < MOTOR_MAX; id++) {
        motors[id].type = id;
        motors[id].driver.stall_flag = NO_STALL;
        monitor_init(&motors[id].stall_monitor, id);
    }

    // specific monitor init
#ifdef SEAT_SIDE_RIGHT
    motors[MOTOR_1_BACK_REST].stall_monitor.stall_inc = 4;
    motors[MOTOR_1_BACK_REST].stall_monitor.dft_threshold = 20;
    motors[MOTOR_2_SEAT_LIFT].stall_monitor.stall_inc = 3;
    motors[MOTOR_2_SEAT_LIFT].stall_monitor.dft_threshold = 12;
    motors[MOTOR_2_SEAT_LIFT].stall_monitor.unleash_threshold = 1314;
#endif
    motors[MOTOR_5_LEG_LIFT].stall_monitor.stall_inc = 3;
    motors[MOTOR_6_LEG_STRETCH].stall_monitor.dft_threshold = 21;
}

void motors_init(void)
{
    // TODO: determin seat by macro
    motors_seat = FRONT_DRIVER;
    init_stall_configs();
    init_configs();

    for (tU8 id = MOTOR_1_BACK_REST; id < MOTOR_MAX; id++) {
        pid_init(&motors[id]);
    }

    init_zero_gravity_pos();
    init_default_pos();
}

// cancel stall flag if move direction is opposite
void motor_unleash(tMotor* motor)
{
    // if ((motor->driver.stall_flag == BACKWARD_STALL && motor->driver.dir == DIR_FORWARD)
        // || (motor->driver.stall_flag == FORWARD_STALL && motor->driver.dir == DIR_BACKWARD)) {
    if ((motor->driver.stall_flag != NO_STALL)) {
        motor->driver.stall_flag = NO_STALL;
    }
    monitor_unleash(&motor->stall_monitor);
}

// void clear_encoder_position(tMotor* motor)
// {
//     motor->encoder.position = 0;
//     motor->encoder.position_last = 0;
// }

static void motor_new_job(tMotor* motor, tMotorJobKind kind, i32 position, bool go_on)
{
    tMotorJob job = {
        .kind = kind,
        .motor = motor,
        .position = position,
        .go_on = go_on,
    };
    motor_job_push(&job);
}

static void motor_new_slow_job(tMotor* motor, tMotorJobKind kind, i32 position, u8 wait)
{
    tMotorJob job = {
        .kind = kind,
        .motor = motor,
        .position = position,
        .go_on = false,
        .wait = wait
    };
    motor_job_push(&job);
}

static bool motor_move(tMotor* motor, i32 position)
{
    motor->position_pid.target = position;
    motor->driver.target_dir = position > (i32) motor->encoder.position ? TARGET_DIR_FORWARD: TARGET_DIR_BACKWARD;
    motor_unleash(motor);

    // send event for motors[i] to start
    switch (motor->type) {
    case MOTOR_1_BACK_REST:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor1Start), motor->type, 0);
        break;
#ifdef SEAT_SIDE_RIGHT
    case MOTOR_2_SEAT_LIFT:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor2Start), motor->type, 0);
        break;
    case MOTOR_3_SEAT_SLIDE:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor3Start), motor->type, 0);
        break;
#endif
    case MOTOR_4_FRONT_BACK:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor4Start), motor->type, 0);
        break;
    case MOTOR_5_LEG_LIFT:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor5Start), motor->type, 0);
        break;
    case MOTOR_6_LEG_STRETCH:
        os_submit_event_u16_from_isr(EV(Motor, Motor6Start), motor->type, 0);
        break;
    default:
        break;
    }
    return true;
}

bool motor_move_to(tMotor* motor, i32 position)
{
    return motor_move(motor, position);
}

bool motor_force(tMotor* motor, tMotorOption option)
{
    // return motor_move_by(motor, option == OPTION_POS ? DIR_FORWARD : DIR_BACKWARD, 0xFFFF);
    return motor_move(motor, option == OPTION_POS ? OVER_RANGE : -OVER_RANGE);
}


void motor_stop(tMotor* motor)
{
    motor->position_pid.target = motor->encoder.position;
    motor->speed_pid.target = 0;
    // motor->driver.target_dir = TARGET_DIR_NONE;

    // send event for motors[i] to stop
    switch (motor->type) {
    case MOTOR_1_BACK_REST:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor1Stop), motor->type, 0);
        break;
#ifdef SEAT_SIDE_RIGHT
    case MOTOR_2_SEAT_LIFT:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor2Stop), motor->type, 0);
        break;
    case MOTOR_3_SEAT_SLIDE:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor3Stop), motor->type, 0);
        break;
#endif
    case MOTOR_4_FRONT_BACK:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor4Stop), motor->type, 0);
        break;
    case MOTOR_5_LEG_LIFT:
        os_submit_event_u16_from_isr(EV(TnMotor, Motor5Stop), motor->type, 0);
        break;
    case MOTOR_6_LEG_STRETCH:
        os_submit_event_u16_from_isr(EV(Motor, Motor6Stop), motor->type, 0);
        break;
    default:
        break;
    }
}

void motor_brake(tMotor* motor)
{
    if (motor->config.openloop) {
        motor->driver.pwm = 0;
        pwm_out_control(motor, 0);
        return;
    }
    motor->driver.pwm = 0;
    motor->speed_pid.target = 0;
    motor->position_pid.target = motor->encoder.position;
    motor->position_pid.real = motor->encoder.position;
}

void motor_record(tMotor* motor)
{
    i32 pos = motor->encoder.position;
    mem_collect_motor(motor);
}

void motor_go(tMotor* motor, tMotorOption option)
{
    // TODO: notify control module to actually control the motor
    switch (option) {
    case OPTION_OFF: {
        println(LogInfo, "motor_go: motor off");
        motor_stop(motor);
        break;
    }
    case OPTION_POS: {
        motor_move_to(motor, motor->config.range - POS_PROXIMITY);
        break;
    }
    case OPTION_NEG: {
        motor_move_to(motor, POS_PROXIMITY);
        break;
    }
    }
}

tMotor* motor_find(tMotorType type)
{
    // find motor in seat_motors
    // NOTE: assume that each type has its own location in the array
    switch (type) {
    case MOTOR_1_BACK_REST:
#ifdef SEAT_SIDE_RIGHT
    case MOTOR_2_SEAT_LIFT:
    case MOTOR_3_SEAT_SLIDE:
#endif
    case MOTOR_4_FRONT_BACK:
    case MOTOR_5_LEG_LIFT:
    case MOTOR_6_LEG_STRETCH:
        return &motors[type];
    default:
        println(LogError, "motor_find: motor not found");
        return NULL;
    }
}

void motor_stall_callback(tMotor* motor) {
    println(LogInfo, "motor stalled!");

    if (motor->clear == CLEAR_SHRINK) {
        motor->clear = CLEAR_MIN;
        motor_do_clear(motor);
    }

    if (motor->calib.state == CALIB_EXPAND) {
        motor->calib.state = CALIB_MAX;
        motor_calib_range(motor);
    }
}



/// Simple one by one movement
void motors_move_positive(u32* pos)
{
    // 1. move slide and front back
#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_MOVE, pos[MOTOR_3_SEAT_SLIDE], true);
#endif
    motor_new_job(motor_find(MOTOR_4_FRONT_BACK), JOB_MOVE, pos[MOTOR_4_FRONT_BACK], false);

#ifdef SEAT_SIDE_RIGHT
    // 2. move seat lift and move back rest
    motor_new_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_MOVE, pos[MOTOR_2_SEAT_LIFT], true);
#endif
    motor_new_job(motor_find(MOTOR_1_BACK_REST), JOB_MOVE, pos[MOTOR_1_BACK_REST], false);

    // 3. move legs
    motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_MOVE, pos[MOTOR_5_LEG_LIFT], true);
    motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_MOVE, pos[MOTOR_6_LEG_STRETCH], false);
}

void motors_move_negative(u32* pos)
{
    motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_MOVE, pos[MOTOR_5_LEG_LIFT], true);
    motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_MOVE, pos[MOTOR_6_LEG_STRETCH], false);

    // move back rest and leg lift
#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_MOVE, pos[MOTOR_2_SEAT_LIFT], false);
#endif
    motor_new_job(motor_find(MOTOR_1_BACK_REST), JOB_MOVE, pos[MOTOR_1_BACK_REST], false);

    // move slide and front back
#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_MOVE, pos[MOTOR_3_SEAT_SLIDE], true);
    motor_new_job(motor_find(MOTOR_4_FRONT_BACK), JOB_MOVE, pos[MOTOR_4_FRONT_BACK], false);
#else
    motor_new_job(motor_find(MOTOR_4_FRONT_BACK), JOB_MOVE, pos[MOTOR_4_FRONT_BACK], false);
#endif

}

static void motor_clear_job(tMotor* motor)
{
    tMotorJob job = {
        .kind = JOB_CLEAR,
        .motor = motor,
        .position = 0,
    };
    motor_job_push(&job);
}

static void motor_clear_job2(tMotor* motor1, tMotor* motor2)
{
    tMotorJob job1 = {
        .kind = JOB_CLEAR,
        .motor = motor1,
        .position = 0,
        .go_on = true,
    };
    motor_job_push(&job1);

    tMotorJob job2 = {
        .kind = JOB_CLEAR,
        .motor = motor1,
        .position = 0,
    };
    motor_job_push(&job2);
}

void motors_clear(void)
{
    motor_clear_job2(
        &motors[MOTOR_6_LEG_STRETCH],
        &motors[MOTOR_5_LEG_LIFT]
    );

#ifdef SEAT_SIDE_RIGHT
    motor_clear_job2(
        &motors[MOTOR_2_SEAT_LIFT],
        &motors[MOTOR_1_BACK_REST]
    );
#else
    motor_clear_job(&motors[MOTOR_1_BACK_REST]);
#endif
#ifdef SEAT_SIDE_RIGHT
    motor_clear_job2(
        &motors[MOTOR_3_SEAT_SLIDE],
        &motors[MOTOR_4_FRONT_BACK]
    );
#else
    motor_clear_job(&motors[MOTOR_4_FRONT_BACK]);
#endif
}

void motors_shrink_force(void)
{
    motor_new_slow_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_FORCE, 0, 10);
    motor_new_slow_job(motor_find(MOTOR_5_LEG_LIFT), JOB_FORCE, 0, 10);

#ifdef SEAT_SIDE_RIGHT
    motor_new_slow_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_FORCE, 0, 20);
#endif
    motor_new_slow_job(motor_find(MOTOR_1_BACK_REST), JOB_FORCE, 0, 20);

#ifdef SEAT_SIDE_RIGHT
    motor_new_slow_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_FORCE, 0, 10);
#endif 
    motor_new_slow_job(motor_find(MOTOR_4_FRONT_BACK), JOB_FORCE, 0, 10);
}

void motors_shrink_fast(void)
{
    motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_MOVE, 0, true);
    motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_MOVE, 0, false);

#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_MOVE, 0, true);
#endif
    motor_new_job(motor_find(MOTOR_1_BACK_REST), JOB_MOVE, 0, false);

#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_MOVE, 0, true);
#endif 
    motor_new_job(motor_find(MOTOR_4_FRONT_BACK), JOB_MOVE, 0, false);
}

void motors_expand(void)
{
    u32 max_pos[MOTOR_MAX] = {0};
    for (usize i = 0; i < MOTOR_MAX; i++) {
        max_pos[i] = motors[i].config.range;
    }
    motors_move_positive(max_pos);
}

void motors_zero_recover(void)
{
    motors_move_negative(default_pos);
}

void motors_move_default(void)
{
    motors_move_positive(default_pos);
}

void motors_calib_s1_back(void)
{
    motor_new_job(motor_find(MOTOR_1_BACK_REST), JOB_RANGE, 0, false);
    motor_new_job(motor_find(MOTOR_1_BACK_REST), JOB_MOVE, 0, false);
}

void motors_calib_s2_lift(void)
{
#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_RANGE, 0, true);
    // motor_new_job(motor_find(MOTOR_2_SEAT_LIFT), JOB_MOVE, 0, false);
#endif
}

void motors_calib_s3_front(void)
{
    motor_new_job(motor_find(MOTOR_4_FRONT_BACK), JOB_RANGE, 0, false);
}

void motors_calib_s4_legs(void)
{

    motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_RANGE, 0, false);
    // motor_new_slow_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_RANGE, 0, 5);
    motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_RANGE, 0, false);

    // motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_RANGE, 0, true);
    // motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_RANGE, 0, false);
    
    motor_new_job(motor_find(MOTOR_6_LEG_STRETCH), JOB_MOVE, 0, false);
    motor_new_job(motor_find(MOTOR_5_LEG_LIFT), JOB_MOVE, 0, false);
}

void motors_calib_s5_slide(void)
{
#ifdef SEAT_SIDE_RIGHT
    motor_new_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_RANGE, 0, false);
    motor_new_job(motor_find(MOTOR_3_SEAT_SLIDE), JOB_MOVE, 0, false);
#endif
}


void motors_calib_batch(void)
{
    // 1. shrink to min position and clear
    motors_shrink_force();

    // 2. calib back seat
    motors_calib_s1_back();

    // 3. calib seat lift
    motors_calib_s2_lift();

    // 4. calib front back
    motors_calib_s3_front();

    // 5. calib legs
    motors_calib_s4_legs();

    // 6. calib seat slide
    motors_calib_s5_slide();

//     // 5. calib legs
//     // 6. calib seat slide

    // fin. move to default positions
    motors_move_default();
}

void motors_zero_gravity(void)
{
    motors_move_positive(zero_gravity_pos);
}

void motor_do_clear(tMotor* motor)
{
    switch (motor->clear) {
    case CLEAR_IDLE: {
        // set a very large destination
        // calculate distance
        u32 distance = 0xFFFF;
        // tries to go to minimum position until stalled
        motor->driver.dir = DIR_BACKWARD;
        // clear_encoder_position(motor);
        motor->position_pid.real = 0;
        motor->position_pid.target = distance;
        motor->driver.stall_flag = NO_STALL;
        motor_unleash(motor);

        // calib
        motor->clear = CLEAR_SHRINK;
        motion_start();
        break;
    }
    case CLEAR_SHRINK: {
        // do nothing. wait for stall event to change state to CLEAR_MIN
        break;
    }
    case CLEAR_MIN: { // 走到了最小点
        motor->calib.range = default_ranges[motor->type];
        u32 distance = 0xFFFF;

        // 把位置设成0
        // motor->mem.position = 0;
        motor->clear = CLEAR_IDLE;
        break;
    }
    }
}

void motor_calib_reset(tMotor* motor) {
    motor->calib.state = CALIB_IDLE;
}

void motor_clear_position(tMotor* motor)
{
    motor->encoder.position = 0;
    motor->position_pid.real = 0;
    motor->position_pid.target = 0;
}

void motor_calib_range(tMotor* motor)
{
    switch (motor->calib.state) {
    case CALIB_IDLE: {
        // reset current position to 0;
        motor->calib.range = 0;
        motor_clear_position(motor);
        // tries to go to maximum position until stalled
        motor_force(motor, OPTION_POS);
        motor->calib.state = CALIB_EXPAND;
        break;
    }
    case CALIB_MAX: { // 走到了最大点
        u32 range = motor->encoder.position;
        motor->calib.range = range;
        motor->calib.state = CALIB_IDLE;
        println(LogInfo, "Calib_range: motors[%d] range: [%d]", motor->type, range);

        motor_stop(motor);
        // 记忆到MEM和NvM中
        motor->config.range = range;
        mem_collect_motor(motor);
        break;
    }
    }
}

void motor_calib(tMotor* motor)
{
    motor_calib_reset(motor);
    motor_new_slow_job(motor, JOB_FORCE, -OVER_RANGE, 10);
    motor_new_slow_job(motor, JOB_RANGE, 0, 20);
    motor_new_slow_job(motor, JOB_MOVE, POS_PROXIMITY, 10);
}

// bool motor_is_active(tMotor* motor)
// {

//     bool is_pwm_on = motor->driver.pwm > 0x100;
//     if (is_pwm_on) {
//         return true;
//     }

//     bool is_stall = motor->driver.stall_flag != NO_STALL;
//     if (is_stall) {
//         return false;
//     }

//     if (!motor->config.openloop) {
//         bool is_near_target = motor->position_pid.target - motor->position_pid.real < 0.00001f;
//         return !is_near_target;
//     } else {
//         return false;
//     }
//     // return motor->encoder.speed > 0.00001f;
// }

void motor_stall_clear(tMotor* motor)
{
    monitor_clear(motor->stall_monitor);
    motor->driver.stall_flag = NO_STALL;
}

void motor_set_stall(tMotor* motor) {
    if (motor->driver.dir == DIR_FORWARD) {
        motor->driver.stall_flag = FORWARD_STALL;
    } else {
        motor->driver.stall_flag = BACKWARD_STALL;
    }
    // stop the motor
    motor_stop(motor);
    motor_stall_callback(motor);
    // clear counters
    motor->stall_monitor.stuck_count = 0;
    motor->stall_monitor.stall_count = 0;
    // inform hsm to go to STALL state
//     switch (motor->type) {
//     case MOTOR_1_BACK_REST:
//         os_submit_event_u16_from_isr(EV(Motor, Motor1Stall), motor->type, 0);
//         break;
// #ifdef SEAT_SIDE_RIGHT
//     case MOTOR_2_SEAT_LIFT:
//         os_submit_event_u16_from_isr(EV(Motor, Motor2Stall), motor->type, 0);
//         break;
//     case MOTOR_3_SEAT_SLIDE:
//         os_submit_event_u16_from_isr(EV(Motor, Motor3Stall), motor->type, 0);
//         break;
// #endif
//     case MOTOR_4_FRONT_BACK:
//         os_submit_event_u16_from_isr(EV(Motor, Motor4Stall), motor->type, 0);
//         break;
//     case MOTOR_5_LEG_LIFT:
//         os_submit_event_u16_from_isr(EV(Motor, Motor5Stall), motor->type, 0);
//         break;
//     case MOTOR_6_LEG_STRETCH:
//         os_submit_event_u16_from_isr(EV(Motor, Motor6Stall), motor->type, 0);
//         break;
//     default:
//         break;
//     }
}

void motor_stall_detect(tMotor* motor, bool stall, bool stuck)
{
    if (monitor_detect(&motor->stall_monitor, stall, stuck)) {
        motor_set_stall(motor);
    }
}

static tHsmStateIndex motor_states[MOTOR_MAX] = {0};

bool motor_is_stopped(tMotor* motor)
{
    tMotorType type = motor->type;
    tHsmTreeIndex treeIndex = (tHsmTreeIndex)type + 1;
    tHsmStateIndex state = hsm_get_current_state(&hsm_motor, treeIndex);
    motor_states[type] = state;

    switch (motor->type) {
    case MOTOR_1_BACK_REST:
        return state == StateMotor1Idle || state == StateMotor1Stopping || state == StateMotor1Stalled || state == StateMotor1Stopped;
#ifdef SEAT_SIDE_RIGHT
    case MOTOR_2_SEAT_LIFT:
        return state == StateMotor2Idle || state == StateMotor2Stopping || state == StateMotor2Stalled || state == StateMotor2Stopped;
    case MOTOR_3_SEAT_SLIDE:
        return state == StateMotor3Idle || state == StateMotor3Stopping || state == StateMotor3Stalled || state == StateMotor3Stopped;
#endif
    case MOTOR_4_FRONT_BACK:
        return state == StateMotor4Idle || state == StateMotor4Stopping || state == StateMotor4Stalled || state == StateMotor4Stopped;
    case MOTOR_5_LEG_LIFT:
        return state == StateMotor5Idle || state == StateMotor5Stopping || state == StateMotor5Stalled || state == StateMotor5Stopped;
    case MOTOR_6_LEG_STRETCH:
        return state == StateMotor6Idle || state == StateMotor6Stopping || state == StateMotor6Stalled || state == StateMotor6Stopped;
    default:
        return false;
    }
}

bool motor_is_idle(tMotor* motor)
{
    tMotorType type = motor->type;
    tHsmTreeIndex treeIndex = (tHsmTreeIndex)type + 1;
    tHsmStateIndex state = hsm_get_current_state(&hsm_motor, treeIndex);
    motor_states[type] = state;

    switch (motor->type) {
    case MOTOR_1_BACK_REST:
        return state == StateMotor1Idle;
#ifdef SEAT_SIDE_RIGHT
    case MOTOR_2_SEAT_LIFT:
        return state == StateMotor2Idle;
    case MOTOR_3_SEAT_SLIDE:
        return state == StateMotor3Idle;
#endif
    case MOTOR_4_FRONT_BACK:
        return state == StateMotor4Idle;
    case MOTOR_5_LEG_LIFT:
        return state == StateMotor5Idle;
    case MOTOR_6_LEG_STRETCH:
        return state == StateMotor6Idle;
    default:
        return false;
    }
}

bool motors_all_idle(void)
{
    for (usize i = 0; i < MOTOR_MAX; i++) {
        if (!motor_is_idle(&motors[i])) {
            return false;
        }
    }
    return true;
}
