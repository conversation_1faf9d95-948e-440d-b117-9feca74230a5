/**
 * *************************************************************************
 * @file   Ram_Hw_Cfg_Defines.h
 * @brief  Code template for Post-Build(PB) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
 
#ifndef RAM_HW_CFG_DEFINES_H
#define RAM_HW_CFG_DEFINES_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RAM_HW_CFG_DEFINES_VENDOR_ID                        (110U)
#define RAM_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION         (4U)
#define RAM_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION         (4U)
#define RAM_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION      (0U)
#define RAM_HW_CFG_DEFINES_SW_MAJOR_VERSION                 (1U)
#define RAM_HW_CFG_DEFINES_SW_MINOR_VERSION                 (0U)
#define RAM_HW_CFG_DEFINES_SW_PATCH_VERSION                 (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/

/**
* @brief            Pre-processor switch to enable/disable the API Ram_Hw_GetRamState.
*/
#define RAM_HW_GET_RAM_STATE_API        (STD_ON)

/**
* @brief            HW sseries used.
*/
#define RAM_HW_LSE

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */
#endif /* #ifndef RAM_HW_CFG_DEFINES_H */

