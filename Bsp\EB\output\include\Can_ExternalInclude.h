/**
 * file    Can_Cfg.h
 * brief   Can External Include header file.
 * author  <PERSON><PERSON>
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef CAN_EXTERNALINCLUDE_H
#define CAN_EXTERNALINCLUDE_H

#include "Std_Types.h"

#include "ErrorCalloutHandler.h"

#ifdef __cplusplus
extern "C"{
#endif

#define CAN_ERROR_CALLOUT_FUNCTION ErrorCalloutHandler

#ifdef __cplusplus
}
#endif
#endif
