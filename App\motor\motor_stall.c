#include "motor_stall.h"
#include "log.h"

void monitor_init(tStallMonitor* monitor, u8 mid) {
    monitor->mid = mid;
    monitor->stall_count = 0;
    monitor->idle_count = 0;
    monitor->threshold = STALL_THRESHOLD;
    monitor->stall_inc = DEFAULT_STALL_INC;
    monitor->dft_threshold = STALL_THRESHOLD;
    monitor->unleash_threshold = STALL_THRESHOLD_UNLEASH;
}

void monitor_clear(tStallMonitor* monitor) {
    monitor->stall_count = 0;
    monitor->idle_count = 0;
    monitor->stuck_count = 0;
}

void monitor_unleash(tStallMonitor* monitor) {
    monitor->stall_count = 0;
    monitor->idle_count = 0;
    println(LogInfo, "stall unleashed: motor [%d], stall_count: [%d]", monitor->mid, monitor->stall_count);
    monitor->threshold = monitor->unleash_threshold;
}

bool monitor_detect(tStallMonitor* monitor, bool stall, bool stuck)
{
    // detect for high currents
    if (stall) {
        monitor->stall_count += monitor->stall_inc;
        // monitor->idle_count = 0;
    } else {
        if (monitor->stall_count <= 1) {
            monitor->stall_count = 0;
        } else {
            monitor->stall_count--;
        }
        // if (monitor->stall_count == 0) {
        monitor->idle_count++;
        // if there's no stall for a long time, exit unleash mode
        if (monitor->idle_count >= 200 && monitor->threshold == monitor->unleash_threshold) {
            println(LogInfo, "stall unleash ends: motors[%d], idle_count: [%d]", monitor->mid, monitor->idle_count);
            monitor->threshold = monitor->dft_threshold;
            monitor->idle_count = 0;
        }
        // }
    }
    if (monitor->stall_count >= monitor->threshold) {
        println(LogInfo, "stall detected: motor [%d], stall_count: [%d]", monitor->mid, monitor->stall_count);
        monitor->stall_count = 0;
        return true;
    }

    return false;

    // ADDITIONAL PROTECT, time based
    // detect for stuck situation: the motor is outputing a PWM, but not moving for a certain time
    // if ((motor->encoder.speed < 0.01f) && (motor->driver.pwm >= 0x1000)) {
    // if (stuck) {
    //     monitor->stuck_count++;
    //     if (monitor->stuck_count > STUCK_THRESHOLD) {
    //         monitor->stuck_count = 0;
    //         return true;
    //     } else {
    //         return false;
    //     }
    // }
    // return false;
}
