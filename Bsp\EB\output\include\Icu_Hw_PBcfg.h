/**
 * 
 * @file Icu_Hw_PBcfg.h
 * @brief ICU config file
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef ICU_HW_PBCFG_H
#define ICU_HW_PBCFG_H

#include "Icu_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif



/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ICU_HW_PBCFG_VENDOR_ID                    (110u)
#define ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION     (4u)
#define ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION  (0u)
#define ICU_HW_PBCFG_SW_MAJOR_VERSION             (1u)
#define ICU_HW_PBCFG_SW_MINOR_VERSION             (0u)
#define ICU_HW_PBCFG_SW_PATCH_VERSION             (0u)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/

/* Check if source file and ICU header file are of the same vendor */
#if (ICU_HW_PBCFG_VENDOR_ID != ICU_HW_TYPES_VENDOR_ID)
    #error "Icu_Hw_PBcfg.c and Icu_Hw_Types.h have different vendor IDs"
#endif
/* Check if source file and ICU header file are of the same AutoSar version */
#if ((ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != ICU_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION != ICU_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION != ICU_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Types.h are different"
#endif
/* Check if source file and ICU header file are of the same Software version */
#if ((ICU_HW_PBCFG_SW_MAJOR_VERSION != ICU_HW_TYPES_SW_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_SW_MINOR_VERSION != ICU_HW_TYPES_SW_MINOR_VERSION) || \
     (ICU_HW_PBCFG_SW_PATCH_VERSION != ICU_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Types.h are different"
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

#define ICU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"
extern const Icu_Hw_ChannelConfig_t Icu_Hw_IpChannelConfig_PB[12U];

#define ICU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"


#ifdef __cplusplus
}
#endif

#endif /* ICU_HW_PBCFG_H */
