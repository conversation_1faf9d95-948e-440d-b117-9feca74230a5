#ifndef MOTOR_STALL_H
#define MOTOR_STALL_H
#include "types.h"

#define STALL_THRESHOLD 15
#define STALL_THRESHOLD_UNLEASH 141
#define DEFAULT_STALL_INC 2
#define STUCK_THRESHOLD 20

typedef enum {
    STALL_IDLE,
    STALL_UNLEASH,
    STALL_STALL,
} tStallState;

typedef struct {
    u32 mid;
    u32 stall_count;
    u32 idle_count;
    u32 stuck_count;
    u32 threshold;
    u32 dft_threshold;
    u32 unleash_threshold;
    u8 stall_inc;
} tStallMonitor;

void monitor_init(tStallMonitor* monitor, u8 mid);
bool monitor_detect(tStallMonitor* monitor, bool stall, bool stuck);
void monitor_unleash(tStallMonitor* monitor);

#endif