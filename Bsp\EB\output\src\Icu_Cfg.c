/**
 * 
 * @file Icu_Cfg.c
 * @brief AUTOSAR Icu - contains the data exported by the Icu module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 
#include "Icu.h"
#include "Icu_Hw.h"

#ifdef __cplusplus
extern "C"{
#endif

#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
#include "Dma.h"
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define ICU_VENDOR_ID_PCCFG_C                       (110u)
#define ICU_AR_RELEASE_MAJOR_VERSION_PCCFG_C        (4u)
#define ICU_AR_RELEASE_MINOR_VERSION_PCCFG_C        (4u)
#define ICU_AR_RELEASE_REVISION_VERSION_PCCFG_C     (0u)
#define ICU_SW_MAJOR_VERSION_PCCFG_C                (1u)
#define ICU_SW_MINOR_VERSION_PCCFG_C                (0u)
#define ICU_SW_PATCH_VERSION_PCCFG_C                (0u)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
/* Check Icu_Cfg.c against Icu.h file versions */
#if (ICU_VENDOR_ID_PCCFG_C != ICU_VENDOR_ID)
    #error "Icu_Cfg.c and Icu.h have different vendor IDs"
#endif

#if ((ICU_AR_RELEASE_MAJOR_VERSION_PCCFG_C != ICU_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_AR_RELEASE_MINOR_VERSION_PCCFG_C != ICU_AR_RELEASE_MINOR_VERSION) || \
     (ICU_AR_RELEASE_REVISION_VERSION_PCCFG_C != ICU_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_Cfg.c and Icu.h are different"
#endif

#if ((ICU_SW_MAJOR_VERSION_PCCFG_C != ICU_SW_MAJOR_VERSION) || \
     (ICU_SW_MINOR_VERSION_PCCFG_C != ICU_SW_MINOR_VERSION) || \
     (ICU_SW_PATCH_VERSION_PCCFG_C != ICU_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Cfg.c and Icu.h are different"
#endif

/* Check Icu_Cfg.c against Icu_Hw.h file versions */
#if (ICU_VENDOR_ID_PCCFG_C != ICU_HW_VENDOR_ID)
    #error "Icu_Cfg.c and Icu_Hw.h have different vendor IDs"
#endif

#if ((ICU_AR_RELEASE_MAJOR_VERSION_PCCFG_C != ICU_HW_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_AR_RELEASE_MINOR_VERSION_PCCFG_C != ICU_HW_AR_RELEASE_MINOR_VERSION) || \
     (ICU_AR_RELEASE_REVISION_VERSION_PCCFG_C != ICU_HW_AR_RELEASE_REVISION_VERSION))
  #error "AutoSar Version Numbers of Icu_Cfg.c and Icu_Hw.h are different"
#endif

#if ((ICU_SW_MAJOR_VERSION_PCCFG_C != ICU_HW_SW_MAJOR_VERSION) || \
     (ICU_SW_MINOR_VERSION_PCCFG_C != ICU_HW_SW_MINOR_VERSION) || \
     (ICU_SW_PATCH_VERSION_PCCFG_C != ICU_HW_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Cfg.c and Icu_Hw.h are different"
#endif

#if (ICU_TIMESTAMP_USES_DMA == STD_ON)
/* Check Icu_Cfg.c against CDD_Dma.h file versions */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((ICU_AR_RELEASE_MAJOR_VERSION_PCCFG_C != DMA_AR_RELEASE_MAJOR_VERSION) || \
         (ICU_AR_RELEASE_MINOR_VERSION_PCCFG_C != DMA_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Icu_Cfg.c and CDD_Dma.h are different"
    #endif
#endif
#endif /* ICU_TIMESTAMP_USES_DMA == STD_ON */

#ifdef __cplusplus
}
#endif


