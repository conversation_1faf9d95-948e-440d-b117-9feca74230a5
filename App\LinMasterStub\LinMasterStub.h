#ifndef LINMASTERSTUB_H
#define LINMASTERSTUB_H

#include "ComStack_Types.h"
#include "Lin_GeneralTypes.h"
//#include "EcuM.h"

//Std_ReturnType LinIf_CheckWakeup(EcuM_WakeupSourceType WakeupSource);

//void LinIf_WakeupConfirmation(EcuM_WakeupSourceType WakeupSource);
extern Std_ReturnType LinIf_HeaderIndication(NetworkHandleType Channel, Lin_PduType* PduPtr);
extern void LinIf_RxIndication(NetworkHandleType Channel, uint8* Lin_SduPtr);
extern void LinIf_TxConfirmation(NetworkHandleType Channel);
extern void LinIf_LinErrorIndication(NetworkHandleType Channel, Lin_SlaveErrorType ErrorStatus);


#endif