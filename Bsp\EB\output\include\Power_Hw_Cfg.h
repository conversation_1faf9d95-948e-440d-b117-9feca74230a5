/**
 * *************************************************************************
 * @file   Power_Hw_Cfg.h
 * @brief  Code template for Post-Build(PB) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
 
#ifndef POWER_HW_CFG_H
#define POWER_HW_CFG_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Power_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define POWER_HW_CFG_VENDOR_ID                       (110U)
#define POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define POWER_HW_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define POWER_HW_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define POWER_HW_CFG_SW_MAJOR_VERSION                (1U)
#define POWER_HW_CFG_SW_MINOR_VERSION                (0U)
#define POWER_HW_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Power_Hw_Cfg.h file and Power_Hw_PBcfg.h file are of the same vendor */
#if (POWER_HW_CFG_VENDOR_ID != POWER_HW_PBCFG_VENDOR_ID)
    #error "Power_Hw_Cfg.h and Power_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Power_Hw_Cfg.h file and Power_Hw_PBcfg.h file are of the same Autosar version */
#if ((POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION != POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_MINOR_VERSION != POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_REVISION_VERSION != POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_Cfg.h and Power_Hw_PBcfg.h are different"
#endif

/* Check if Power_Hw_Cfg.h file and Power_Hw_PBcfg.h file are of the same Software version */
#if ((POWER_HW_CFG_SW_MAJOR_VERSION != POWER_HW_PBCFG_SW_MAJOR_VERSION) || \
     (POWER_HW_CFG_SW_MINOR_VERSION != POWER_HW_PBCFG_SW_MINOR_VERSION) || \
     (POWER_HW_CFG_SW_PATCH_VERSION != POWER_HW_PBCFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Power_Hw_Cfg.h and Power_Hw_PBcfg.h are different"
#endif
/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
#define POWER_HW_DEV_ERROR_DETECT         (STD_OFF)

#define POWER_HW_TIMEOUT_TYPE                (OSIF_COUNTER_DUMMY)

#define POWER_HW_TIMEOUT_VALUE_US            (50000U)

/**
* @brief            ISR Mcu_CmuClockFail_ISR is/isn't available
*/
#define POWER_HW_SLEEP_WAKEUP_ISR_NOTIFICATION_USE       (STD_OFF)








/**
* @brief        Support for User mode.
*               If this parameter has been configured to 'TRUE' the Power can be executed from both supervisor and user mode.
*/
#define POWER_HW_ENABLE_USER_MODE_SUPPORT  (STD_OFF)

/** Check the driver user mode is enabled only when the MCAL_ENABLE_USER_MODE_SUPPORT is enabled */
#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
  #if (STD_ON == POWER_HW_ENABLE_USER_MODE_SUPPORT)
    #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Power in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined.
  #endif /* (STD_ON == POWER_HW_ENABLE_USER_MODE_SUPPORT) */
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */

/**
* @brief            Create defines with the values assigned to Mcu Reset Reason configurations.
*                   These values can be  retrieved from Mcu_GetResetReason Api.
*/


#define McuConf_McuResetReasonConf_MCU_MULTI_RST   ((uint8)0U)


#define McuConf_McuResetReasonConf_MCU_MDM_DAP_RST   ((uint8)1U)


#define McuConf_McuResetReasonConf_MCU_CPU_LOCK_RST   ((uint8)2U)


#define McuConf_McuResetReasonConf_MCU_HRCER_RST   ((uint8)3U)


#define McuConf_McuResetReasonConf_MCU_XTAL_ERR_RST   ((uint8)4U)


#define McuConf_McuResetReasonConf_MCU_PLLER_RST   ((uint8)5U)


#define McuConf_McuResetReasonConf_MCU_SW_RST   ((uint8)6U)


#define McuConf_McuResetReasonConf_MCU_WDG_RST   ((uint8)7U)


#define McuConf_McuResetReasonConf_MCU_LVD2_RST   ((uint8)8U)


#define McuConf_McuResetReasonConf_MCU_LVD1_RST   ((uint8)9U)


#define McuConf_McuResetReasonConf_MCU_BO_RST   ((uint8)10U)


#define McuConf_McuResetReasonConf_MCU_PIN_RST   ((uint8)11U)


#define McuConf_McuResetReasonConf_MCU_STBY_RST   ((uint8)12U)


#define McuConf_McuResetReasonConf_MCU_POR_RST   ((uint8)13U)


#define McuConf_McuResetReasonConf_MCU_RESET_UNDEFINED   ((uint8)14U)


/***********************************************************/
/*             RCM_GetResetRawValue Defines             */
/***********************************************************/
/** @brief This is the corresponding bit of Stop Acknowledge Error reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_SACKERR      ((uint32)0x00002000U)
/** @brief This is the corresponding bit of MDM-AP System Reset Request in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_MDM_AP       ((uint32)0x00000800U)
/** @brief This is the corresponding bit of Software reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_SW           ((uint32)0x00000400U)
/** @brief This is the corresponding bit of Stop Core Lockup reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_LOCKUP       ((uint32)0x00000200U)
/** @brief This is the corresponding bit of JTAG generated reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_JTAG         ((uint32)0x00000100U)
/** @brief This is the corresponding bit of Power-on reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_POR          ((uint32)0x00000080U)
/** @brief This is the corresponding bit of external pin reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_PIN          ((uint32)0x00000040U)
/** @brief This is the corresponding bit of watchdog reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_WDOG         ((uint32)0x00000020U)
/** @brief This is the corresponding bit of CMU Loss-of-Lock reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_CMU_LOC      ((uint32)0x00000010U)
/** @brief This is the corresponding bit of Loss-of-Lock reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_LOL          ((uint32)0x00000008U)
/** @brief This is the corresponding bit of Loss-of-Clock reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_LOC          ((uint32)0x00000004U)
/** @brief This is the corresponding bit of Low-Voltage Detect reset or High-Voltage Detect reset in the returned value of Mcu_GetResetRawReason */
#define MCU_RAW_RESET_LVD          ((uint32)0x00000002U)

/*==================================================================================================
                                             ENUMS
==================================================================================================*/
/**
* @brief            The type Power_Hw_ResetType, represents the different reset that a specified POWER_HW can have.
* @details          The POWER_HW shall provide at least the values MCU_POWER_ON_RESET and MCU_RESET_UNDEFINED for the enumeration Power_Hw_ResetType.
*
* @implements Power_Hw_ResetType_Enumeration
*/
typedef enum
{
    MCU_MULTI_RST = McuConf_McuResetReasonConf_MCU_MULTI_RST,            /**< @brief Stop Acknowledge Error reset . RCM_SRS[SACKERR]. */
    MCU_MDM_DAP_RST = McuConf_McuResetReasonConf_MCU_MDM_DAP_RST,                              /**< @brief MDM-AP System Reset Request . RCM_SRS[MDM_AP]. */
    MCU_CPU_LOCK_RST = McuConf_McuResetReasonConf_MCU_CPU_LOCK_RST,                                                    /**< @brief Software reset . RCM_SRS[SW]. */
    MCU_HRCER_RST = McuConf_McuResetReasonConf_MCU_HRCER_RST,                                  /**< @brief Core Lockup reset . RCM_SRS[LOCKUP]. */
    MCU_XTAL_ERR_RST = McuConf_McuResetReasonConf_MCU_XTAL_ERR_RST,                                                /**< @brief JTAG generated reset . RCM_SRS[JTAG]. */
    MCU_PLLER_RST = McuConf_McuResetReasonConf_MCU_PLLER_RST,                                        /**< @brief Power-on reset. RCM_SRS[POR]. */
    MCU_SW_RST = McuConf_McuResetReasonConf_MCU_SW_RST,                                /**< @brief External Reset Pin. RCM_SRS[PIN]. */
    MCU_WDG_RST = McuConf_McuResetReasonConf_MCU_WDG_RST,                                        /**< @brief Watchdog reset. RCM_SRS[Watchdog]. */
    MCU_LVD2_RST = McuConf_McuResetReasonConf_MCU_LVD2_RST,                      /**< @brief CMU Loss-of-Clock Reset. RCM_SRS[CMU_LOC]. */
    MCU_LVD1_RST = McuConf_McuResetReasonConf_MCU_LVD1_RST,                                /**< @brief Loss-of-Lock Reset. RCM_SRS[LOL]. */
    MCU_BO_RST = McuConf_McuResetReasonConf_MCU_BO_RST,                              /**< @brief Loss-of-Clock Reset. RCM_SRS[LOC]. */
    MCU_PIN_RST = McuConf_McuResetReasonConf_MCU_PIN_RST,    /**< @brief Low-Voltage Detect Reset or High-Voltage Detect Reset. RCM_SRS[LVD]. */
    MCU_STBY_RST = McuConf_McuResetReasonConf_MCU_STBY_RST,                                      /**< @brief No reset reason found */
    MCU_POR_RST = McuConf_McuResetReasonConf_MCU_POR_RST,                          /**< @brief More than one reset events are logged except "Power on event" */
    MCU_RESET_UNDEFINED = McuConf_McuResetReasonConf_MCU_RESET_UNDEFINED                                       /**< @brief Undefined reset source. */

} Power_Hw_ResetType;

/**
* @brief            Type of parameter value of the function Mcu_SRAMRetentionConfig.
* @details          The type of Power_Hw_SRAMRetenConfigType is an enumeration with the following values:
*                       MCU_SRAML_RETEN, MCU_SRAMU_RETEN, MCU_SRAMLU_RETEN, MCU_NO_SRAMLU_RETEN.
*/
typedef enum
{
    MCU_SRAML_RETEN = (uint32)0x00100000U,   /**< @brief SRAML will be retain only. */
    MCU_SRAMU_RETEN = (uint32)0x00200000U,   /**< @brief SRAMU will be retain only. */
    MCU_SRAMLU_RETEN = (uint32)0x00000000U,   /**< @brief Both SRAML and SRAMU will be retain. */
    MCU_NO_SRAMLU_RETEN = (uint32)0x00300000U,   /**< @brief Both SRAML and SRAMU will not be retain. */
} Power_Hw_SRAMRetenConfigType;












#ifdef __cplusplus
}
#endif

/** @} */
#endif /* #ifndef POWER_HW_CFG_H */

