/**
 * *************************************************************************
 * @file   Power_Hw_Cfg.c
 * @brief  Code template for Post-Compile(PC) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Power_Hw_Cfg.h"
#include "Power_Hw_Types.h"
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define POWER_HW_CFG_VENDOR_ID_C                         (110U)
#define POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION_C          (4U)
#define POWER_HW_CFG_AR_RELEASE_MINOR_VERSION_C          (4U)
#define POWER_HW_CFG_AR_RELEASE_REVISION_VERSION_C       (0U)
#define POWER_HW_CFG_SW_MAJOR_VERSION_C                  (1U)
#define POWER_HW_CFG_SW_MINOR_VERSION_C                  (0U)
#define POWER_HW_CFG_SW_PATCH_VERSION_C                  (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Power_Hw_Cfg.c file and Power_Hw_Cfg.h file are of the same vendor */
#if (POWER_HW_CFG_VENDOR_ID_C != POWER_HW_CFG_VENDOR_ID)
    #error "Power_Hw_Cfg.c and Power_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Power_Hw_Cfg.c file and Power_Hw_Cfg.h file are of the same Autosar version */
#if ((POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_MINOR_VERSION_C != POWER_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_REVISION_VERSION_C != POWER_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_Cfg.c and Power_Hw_Cfg.h are different"
#endif

/* Check if Power_Hw_Cfg.c file and Power_Hw_Cfg.h file are of the same Software version */
#if ((POWER_HW_CFG_SW_MAJOR_VERSION_C != POWER_HW_CFG_SW_MAJOR_VERSION) || \
     (POWER_HW_CFG_SW_MINOR_VERSION_C != POWER_HW_CFG_SW_MINOR_VERSION) || \
     (POWER_HW_CFG_SW_PATCH_VERSION_C != POWER_HW_CFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Power_Hw_Cfg.c and Power_Hw_Cfg.h are different"
#endif

/* Check if Power_Hw_Cfg.c file and Power_Hw_Types.h file are of the same vendor */
#if (POWER_HW_CFG_VENDOR_ID_C != POWER_HW_TYPES_VENDOR_ID)
    #error "Power_Hw_Cfg.c and Power_Hw_Types.h have different vendor ids"
#endif

/* Check if Power_Hw_Cfg.c file and Power_Hw_Types.h file are of the same Autosar version */
#if ((POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != POWER_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_MINOR_VERSION_C != POWER_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_REVISION_VERSION_C != POWER_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_Cfg.c and Power_Hw_Types.h are different"
#endif

/* Check if Power_Hw_Cfg.c file and Power_Hw_Types.h file are of the same Software version */
#if ((POWER_HW_CFG_SW_MAJOR_VERSION_C != POWER_HW_TYPES_SW_MAJOR_VERSION) || \
     (POWER_HW_CFG_SW_MINOR_VERSION_C != POWER_HW_TYPES_SW_MINOR_VERSION) || \
     (POWER_HW_CFG_SW_PATCH_VERSION_C != POWER_HW_TYPES_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Power_Hw_Cfg.c and Power_Hw_Types.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Power_Hw_Cfg.c file and Std_Types.h file are of the same Autosar version */
#if ((POWER_HW_CFG_AR_RELEASE_MAJOR_VERSION_C    != STD_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_CFG_AR_RELEASE_MINOR_VERSION_C    != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Power_Hw_Cfg.c and Std_Types.h are different"
#endif
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

