/// # Properties
name: "SCU001"
version: "0.0.1"
index: ["soutek"]

/// # Ports

// Default port is win32, no need to write it down
// port("win32") {
//     builder: "cmake"
//     platform: "win32"
// }

// self defined ports
port(lanshan) {
    at: "build/lanshan/project"
    builder: "iar"
    platform: "lse1480"
}

var kernel_config = {}

if port == "win32" {
    kernel_config =  {
        heap: "heap_5"
        port: "msvc/x64"
        config: "config/WIN32"
    }
}

if port == "lanshan" {
    kernel_config =  {
        heap: "heap_4"
        port: "IAR/ARM_CM4F"
        config: "config/LS1480"
    }

    // C Defines used in #ifdef
    defines: [
        "SEAT_SIDE_RIGHT"
        // "SEAT_SIDE_LEFT"
        // "USE_DEBUG_KEYS"
        "DRIVER_8_WAY_CONTROLLER"
        "USE_KERNEL"
        "RTT_LOG"
        "USING_OS_AUTOSAROS"
        "USE_XMEN"
        // for IRQ table
        "CONFIG_CHIP_VERSION_LSE1480"
        // "FLS_PRECOMPILE_SUPPORT"
        "USE_AUTO_POD"
        "USE_DEBUG_FUNCS"
        // "HSM_STATE_LOG"
    ]

    // MCAL
    device("Lanshan-LSE1480-MCAL") {
        at: "Bsp/Mcal"
        selects: ["Adc", "Can", "Lin", "Mcu", "Port", "Pwm", "Det", "Dio", "Dma", "Icu", "Gpt", "Fls", "Fee", "Uart"]
        version: "3.5.0"
    }

    // MCAL configs generated by EB
    bag(EB) {
        at: "Bsp/EB/output"
    }

    dep(xmen) {
        at: "Bsw/xmen"
        config: "Bsw/configs/xmen"
        can: "Bsp/Mcal/Can/include"
    }

    bag(lseconfig) {
        at: "Bsw/configs/lse"
    }
}

/// # Dependencies
dep(osal) {
    at: "Bsw/osal"
    use_rtt: true
    config: "App/os"
    modules: [
        "App/os/power"
        "App/os/bluetooth"
        "App/os/cmd"
        "App/os/lin"
        "App/os/motor"
        "App/os/timer"
    ]
    kernel: kernel_config
}

/// # Applications
app(SCU001) {
    at: "App"
    // set additional binary output to App.hex; possible formats: ["srec", "hex", "txt", "bin", "sim"]
    if port == "lanshan" {
        extra_output: "hex"
    }
    dirs: ["key", "cmd", "mem", "motor", "Dcm", "CanStub", "LinMasterStub", "debug"]
    links: ["Lanshan-LSE1480-MCAL", "osal", "xmen", "kernel", "log", "common", "EB", "lseconfig"]
}
