<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr name="ImporterExporterAdditions">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ImporterExporterAdditions"/>
    <d:var name="Version_SystemDescriptionExporter" value="1">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="Version_SystemDescriptionImporter" value="4">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="Version_AutosarImporter" value="2">
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
  </d:ctr>
  <d:ctr name="ECUCNature">
    <a:a name="DEF" value="XPath:/PreferencesSchema/ECUCNature"/>
    <d:var name="ReleaseVersion" >
      <a:a name="IMPORTER_INFO" value="@DEF"/>
    </d:var>
    <d:var name="ECUId" value="Lse146_100"/>
    <d:var name="ConfigurationPath" value="config"/>
    <d:var name="GenerationPath" value="output"/>
    <d:var name="UnixLF" value="FALSE"/>
    <d:var name="UnixLFConfigData" value="FALSE"/>
    <d:var name="DisableMinListChildCreation" value="TRUE"/>
    <d:var name="ProjectSpecificSettings" value="FALSE"/>
    <d:var name="ProjectSpecificSettingsConfigurationProject" value="FALSE"/>
    <d:var name="Target" value="CORTEXM"/>
    <d:var name="Derivate" value="LSE14XX"/>
    <d:var name="DefaultPreConfiguration" value="McuPreConfiguration"/>
    <d:var name="DefaultRecConfiguration" value="PortRecConfiguration_JtagPins"/>
    <d:ctr name="ConfigTime">
      <a:a name="ENABLE" value="false"/>
      <d:var name="PublishedInformation" value="FALSE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PreCompile" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="Link" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
      <d:var name="PostBuild" value="TRUE">
        <a:a name="IMPORTER_INFO" value="@DEF"/>
      </d:var>
    </d:ctr>
  </d:ctr>
  <d:ctr name="General">
    <a:a name="DEF" value="XPath:/PreferencesSchema/General"/>
    <d:var name="Version" value="29.0.0"/>
    <d:var name="ReleaseVersion" value="*"/>
    <d:var name="ModelExtenderCompatibility" value="TRUE"/>
    <d:lst name="ModelExtender" type="MAP">
      <d:ctr name="ModelEcuConfiguration"/>
      <d:ctr name="SystemModel2"/>
    </d:lst>
    <d:var name="System" value=""/>
    <d:var name="EcuInstance" value=""/>
    <d:lst name="ModuleConfigurations" type="MAP">
      <d:ctr name="Adc">
        <d:var name="ModuleId" value="Adc_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 0000"/>
        <d:var name="ConfigurationFileURL" value="config\Adc.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Can">
        <d:var name="ConfigurationFileURL" value="config\Can.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Can_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 P04_D2404"/>
      </d:ctr>
      <d:ctr name="Dio">
        <d:var name="ConfigurationFileURL" value="config\Dio.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Dio_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 00"/>
      </d:ctr>
      <d:ctr name="Dma">
        <d:var name="ConfigurationFileURL" value="config\Dma.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Dma_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 00"/>
      </d:ctr>
      <d:ctr name="Fee">
        <d:var name="ModuleId" value="Fee_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 00"/>
        <d:var name="ConfigurationFileURL" value="config\Fee.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Fls">
        <d:var name="ModuleId" value="Fls_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 0"/>
        <d:var name="ConfigurationFileURL" value="config\Fls.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Gpt">
        <d:var name="ModuleId" value="Gpt_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 P04_D2404"/>
        <d:var name="ConfigurationFileURL" value="config\Gpt.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Icu">
        <d:var name="ConfigurationFileURL" value="config\Icu.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Icu_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 000"/>
      </d:ctr>
      <d:ctr name="Lin">
        <d:var name="ConfigurationFileURL" value="config\Lin.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Lin_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 00000"/>
      </d:ctr>
      <d:ctr name="Mcu">
        <d:var name="ConfigurationFileURL" value="config\Mcu.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Mcu_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="TRUE"/>
        <d:var name="SoftwareVersion" value="1.0.0 000"/>
      </d:ctr>
      <d:ctr name="Port">
        <d:var name="ConfigurationFileURL" value="config\Port.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" value="PortRecConfiguration_JtagPins"/>
        <d:var name="ModuleId" value="Port_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 0"/>
      </d:ctr>
      <d:ctr name="Pwm">
        <d:var name="ModuleId" value="Pwm_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 000"/>
        <d:var name="ConfigurationFileURL" value="config\Pwm.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
      </d:ctr>
      <d:ctr name="Resource">
        <d:var name="ConfigurationFileURL" value="config\Resource.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Resource_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 000"/>
      </d:ctr>
      <d:ctr name="Uart">
        <d:var name="ConfigurationFileURL" value="config\Uart.xdm"/>
        <d:var name="ConfigurationFormat" value="xdm"/>
        <d:var name="GenerationPath" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="PreConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="RecConfig" >
          <a:a name="IMPORTER_INFO" value="@DEF"/>
        </d:var>
        <d:var name="ModuleId" value="Uart_LS_LSE14M01I0R0"/>
        <d:var name="Enabled" value="TRUE"/>
        <d:var name="Generate" value="TRUE"/>
        <d:var name="LoadExistingConfigurationFile" value="FALSE"/>
        <d:var name="SoftwareVersion" value="1.0.0 00000"/>
      </d:ctr>
    </d:lst>
    <d:lst name="AutoconfigureTriggers" type="MAP"/>
    <d:lst name="WizardConfigurations" type="MAP"/>
  </d:ctr>

</datamodel>
