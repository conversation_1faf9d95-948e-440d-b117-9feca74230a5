/**
* 
* @file    Dio_Cfg.c
* @brief   Digital Input/Output for LSE14XX
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
*
* @section[blobal]
* Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
* Reason: The macro definition names are similar, the version number definition prefixes are the same, 
* and the keywords are different.
*
* @section[blobal]
* Violations MISRA C 2012 Advisory Rule 8.4 , A compatible declaration shall be visible when an object 
* or function with external linkage is defined.
* Reason: Dio module is not initialized, it is placed in the Port module, Dio Config is defined as 
* Dio_ConfigPC reference, there is no risk.
*
*
*/ 

/*=================================================================================================
*                                        INCLUDE FILES
=================================================================================================*/

#include "Dio.h"

#ifdef __cplusplus
extern "C" {
#endif

#if (defined(DIO_PRECOMPILE_SUPPORT) || defined(DIO_LINKTIME_SUPPORT))
/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/
#define DIO_VENDOR_ID_CFG_C                   (110U)
#define DIO_AR_RELEASE_MAJOR_VERSION_CFG_C    (4U)  
#define DIO_AR_RELEASE_MINOR_VERSION_CFG_C    (4U)     
#define DIO_AR_RELEASE_REVISION_VERSION_CFG_C (0U)  
#define DIO_SW_MAJOR_VERSION_CFG_C            (1U)
#define DIO_SW_MINOR_VERSION_CFG_C            (0U)
#define DIO_SW_PATCH_VERSION_CFG_C            (0U)


/*=================================================================================================
*                                     FILE VERSION CHECKS
=================================================================================================*/
    /* Check if Dio_Cfg.c and Dio.h files are of the same Autosar version.*/
#if ((DIO_AR_RELEASE_MAJOR_VERSION_CFG_C != DIO_AR_RELEASE_MAJOR_VERSION)  ||  \
     (DIO_AR_RELEASE_MINOR_VERSION_CFG_C != DIO_AR_RELEASE_MINOR_VERSION)  ||  \
     (DIO_AR_RELEASE_REVISION_VERSION_CFG_C != DIO_AR_RELEASE_REVISION_VERSION)\
    )
    #error "AutoSar Version Numbers of Dio_Cfg.c and Dio.h are different"
#endif
    /* Check if Dio_Cfg.c and Dio.h files are of the same Software version.*/
#if ((DIO_SW_MAJOR_VERSION_CFG_C != DIO_SW_MAJOR_VERSION) || \
     (DIO_SW_MINOR_VERSION_CFG_C != DIO_SW_MINOR_VERSION) || \
     (DIO_SW_PATCH_VERSION_CFG_C != DIO_SW_PATCH_VERSION)    \
    )
    #error "Software Version Numbers of Dio_Cfg.c and Dio.h are different"
#endif
    /* Check if Dio_Cfg.c and Dio.h files are of the same vendor.*/
#if ((DIO_VENDOR_ID_CFG_C != DIO_VENDOR_ID))
    #error "VENDOR ID for Dio_Cfg.c and Dio.h is different"
#endif
/*=================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
=================================================================================================*/


/*=================================================================================================
*                                       LOCAL MACROS
=================================================================================================*/


/*=================================================================================================
*                                      LOCAL CONSTANTS
=================================================================================================*/


/*=================================================================================================
*                                      LOCAL VARIABLES
=================================================================================================*/


/*=================================================================================================
*                                      GLOBAL CONSTANTS
=================================================================================================*/
#define DIO_START_SEC_CONST_32
#include "Dio_MemMap.h"

/**
* @brief Array containing list of mapping channel for partition
*/
const uint32 Dio_au32ChannelToPartitionMap[DIO_CHANNEL_PARTITION_U16] =
{
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001
};

/**
* @brief Array containing list of mapping port for partition
*/
const uint32 Dio_au32PortToPartitionMap[DIO_PORT_PARTITION_U16] =
{
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001,
    (uint32)0x0000000000000001
};


/**
* @brief          Platform implemented port pins representation.
* @details        Array of bit maps reflecting the available pins in each port.
*                 The number of elements in the array corresponds to the number of
*                 ports implemented in the platform package. Rightmost bit of each
*                 array element corresponds to channel 0 of the associated port,
*                 while leftmost bit of each array element corresponds to channel 31
*                 of the associated port. A 0 bit means that the correspondent channel
*                 is not available in the platform package, while a bit of 1 means
*                 that the correspondent channel is available in the platform package.
*
* @api
*/
const Dio_PortLevelType Dio_aAvailablePinsForWrite[DIO_NUM_PORTS_U16] =
{
    (Dio_PortLevelType)0xFE03FFFFUL,
    (Dio_PortLevelType)0x3AF7FFFFUL,
    (Dio_PortLevelType)0xF88FFFFFUL,
    (Dio_PortLevelType)0x79CFFFFFUL,
    (Dio_PortLevelType)0x03F9FFFFUL
};

const Dio_PortLevelType Dio_aAvailablePinsForRead[DIO_NUM_PORTS_U16] =
{
    (Dio_PortLevelType)0xFE03FFFFUL,
    (Dio_PortLevelType)0x3AF7FFFFUL,
    (Dio_PortLevelType)0xF88FFFFFUL,
    (Dio_PortLevelType)0x79CFFFFFUL,
    (Dio_PortLevelType)0x03F9FFFFUL
};


#define DIO_STOP_SEC_CONST_32
#include "Dio_MemMap.h"

#define DIO_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dio_MemMap.h"

/* ========== DioConfig ========== */
/**
* @brief          Data structure for configuration DioConfig.
*/
const Dio_ConfigType Dio_Config =
{
    (uint8)0x0,
    NULL_PTR,
    Dio_au32ChannelToPartitionMap,
    Dio_au32PortToPartitionMap
};

#define DIO_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Dio_MemMap.h"

/*=================================================================================================
*                                      GLOBAL VARIABLES
=================================================================================================*/


/*=================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
=================================================================================================*/


/*=================================================================================================
*                                       LOCAL FUNCTIONS
=================================================================================================*/


/*=================================================================================================
*                                       GLOBAL FUNCTIONS
=================================================================================================*/

#endif /* (defined(DIO_PRECOMPILE_SUPPORT) || defined(DIO_LINKTIME_SUPPORT)) .. */

#ifdef __cplusplus
}
#endif

/** @} */

