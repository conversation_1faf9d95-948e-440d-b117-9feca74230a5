/**
 * file    Flexio_Uart_Hw_Cfg.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_UART_HW_CFG_H
#define FLEXIO_UART_HW_CFG_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Flexio_Uart_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_UART_HW_CFG_VENDOR_ID                     (110U)
#define FLEXIO_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION      (4U)
#define FLEXIO_UART_HW_CFG_AR_RELEASE_MINOR_VERSION      (4U) 
#define FLEXIO_UART_HW_CFG_AR_RELEASE_REVISION_VERSION   (0U) 
#define FLEXIO_UART_HW_CFG_SW_MAJOR_VERSION              (1U)
#define FLEXIO_UART_HW_CFG_SW_MINOR_VERSION              (0U)
#define FLEXIO_UART_HW_CFG_SW_PATCH_VERSION              (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Flexio_Uart_Hw_PBcfg.h */
#if (FLEXIO_UART_HW_CFG_VENDOR_ID != FLEXIO_UART_HW_PBCFG_VENDOR_ID)
    #error "Flexio_Uart_Hw_Cfg.h and Flexio_Uart_Hw_PBcfg.h have different vendor ids"
#endif
#if ((FLEXIO_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION    != FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_CFG_AR_RELEASE_MINOR_VERSION    != FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_UART_HW_CFG_AR_RELEASE_REVISION_VERSION != FLEXIO_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Flexio_Uart_Hw_Cfg.h and Flexio_Uart_Hw_PBcfg.h are different"
#endif
#if ((FLEXIO_UART_HW_CFG_SW_MAJOR_VERSION != FLEXIO_UART_HW_PBCFG_SW_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_CFG_SW_MINOR_VERSION != FLEXIO_UART_HW_PBCFG_SW_MINOR_VERSION) || \
     (FLEXIO_UART_HW_CFG_SW_PATCH_VERSION != FLEXIO_UART_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Flexio_Uart_Hw_Cfg.h and Flexio_Uart_Hw_PBcfg.h are different"
#endif


/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/
#define FLEXIO_UART_HW_CONFIG_EXT \
    FLEXIO_UART_HW_CONFIG_PB
#define UART_IPW_CODE
#define UART_IPW_VAR
/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/**
* @brief    Declare callback function if it is used by user
*/

#define UART_START_SEC_CODE
#include "Uart_MemMap.h"

/* Define User callback function */
extern FUNC(void, UART_IPW_CODE) Uart_Ipw_FlexioTransferCallback(CONST(uint8, UART_IPW_VAR) u8HwChannel, CONST(FlexioUartHwEvent_t, UART_IPW_VAR) Event, P2VAR(void, AUTOMATIC, UART_IPW_VAR) UserData);

#define UART_STOP_SEC_CODE
#include "Uart_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_UART_HW_CFG_H */
