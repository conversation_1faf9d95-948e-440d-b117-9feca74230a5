/**
 * *************************************************************************
 * @file Clock_Hw_PBcfg.h
 * @brief This file is used for dynamic code generation
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/


#ifndef CLOCK_HW_PBCFG_H
#define CLOCK_HW_PBCFG_H

/**
 * *************************************************************************
 * @file   Clock_Hw_PBcfg.h
 * @brief This file is used for Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Clock_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CLOCK_HW_PBCFG_VENDOR_ID                       (110U)
#define CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION        (4U)
#define CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION     (0U)
#define CLOCK_HW_PBCFG_SW_MAJOR_VERSION                (1U)
#define CLOCK_HW_PBCFG_SW_MINOR_VERSION                (0U)
#define CLOCK_HW_PBCFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Clock_Hw_Types.h file are of the same vendor */
#if (CLOCK_HW_PBCFG_VENDOR_ID != CLOCK_HW_TYPES_VENDOR_ID)
    #error "Clock_Hw_PBcfg.h and Clock_Hw_Types.h have different vendor ids"
#endif

/* Check if header file and Clock_Hw_Types.h file are of the same Autosar version */
#if ((CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != CLOCK_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION != CLOCK_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION != CLOCK_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_PBcfg.h and Clock_Hw_Types.h are different"
#endif

/* Check if header file and Clock_Hw_Types.h file are of the same Software version */
#if ((CLOCK_HW_PBCFG_SW_MAJOR_VERSION != CLOCK_HW_TYPES_SW_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_MINOR_VERSION != CLOCK_HW_TYPES_SW_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_PATCH_VERSION != CLOCK_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_PBcfg.h and Clock_Hw_Types.h are different"
#endif

#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
extern const Clock_Hw_ClockConfigType_t Mcu_aClockConfigPB[];
extern const Clock_Hw_FlsConfigType_t Flashtim_Hw_Param_ConfigPB;

#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */
#endif /* CLOCK_HW_PBCFG_H */


