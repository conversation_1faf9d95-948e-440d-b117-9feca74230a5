#include "os_pod.h"
#include "pod_cmd.h"
/// ---------- begin of guard: <includes> -------------------------------------
#include "log.h"
#include "types.h"
#include "cmd.h"

// key related
#include "key.h"
#include "iokey.h"

// lin related
#include "pod_lin.h"
#include "lin_events.h"
#include "Com_Cfg.h"

// bluetooth related
#include "pod_bluetooth.h"
/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------

static char* cmd_to_str(tCmd *cmd)
{
    switch(cmd->kind) {
    case CMD_NONE:
        return "CMD_NONE";
    case CMD_BACK_REST:
        return "CMD_BACK_REST";
    case CMD_SEAT_LIFT:
        return "CMD_SEAT_LIFT";
    case CMD_SEAT_SLIDE:
        return "CMD_SEAT_SLIDE";
    case CMD_FRONT_BACK:
        return "CMD_FRONT_BACK";
    case CMD_LEG_LIFT:
        return "CMD_LEG_LIFT";
    case CMD_LEG_STRETCH:
        return "CMD_LEG_STRETCH";
    case CMD_ZERO_GRAVITY:
        return "CMD_ZERO_GRAVITY";
    case CMD_MASSAGE:
        return "CMD_MASSAGE";
    case CMD_HEATER:
        return "CMD_HEATER";
    case CMD_FAN:
        return "CMD_FAN";
    case CMD_WAIST:
        return "CMD_WAIST";
    case CMD_MASSAGE_MODE:
        return "CMD_MASSAGE_MODE";
    case CMD_MASSAGE_INTENSITY:
        return "CMD_MASSAGE_INTENSITY";
    case CMD_BACK_FAN_SWITCH:
        return "CMD_BACK_FAN_SWITCH";
    case CMD_BACK_FAN_LEVEL:
        return "CMD_BACK_FAN_LEVEL";
    case CMD_SEAT_FAN_SWITCH:
        return "CMD_SEAT_FAN_SWITCH";
    case CMD_SEAT_FAN_LEVEL:
        return "CMD_SEAT_FAN_LEVEL";
    case CMD_BACK_HEATER:
        return "CMD_BACK_HEATER";
    }
}

tCmdAction action_with_option(tCmdAction action, u8 keyOption)
{
    return keyOption == KEY_OPTION_PRESSED ? action : ACTION_OFF;
}


/// @brief 测试用按键板（16个键，另外还有2个零重力键）
/// 按键左侧 | 按键右侧
/// ---     | ---
/// 座椅向前 | 座椅向后
/// 靠背向前 | 靠背向后
/// 座椅上升 | 座椅下降
/// 座椅左滑 | 座椅右滑
/// 加热(no) | 按摩（no）
/// 通风（no) | 零重力（no)
/// 腿托上升 | 腿托下降
/// 腿托伸展 | 腿托收缩
/// 零重力开 | 零重力关
static tCmd key_to_cmd(u8 keyId, u8 keyOption)
{
    tCmd cmd = {0};
    switch (keyId) {
#ifdef USE_DEBUG_KEYS
    case KEY_ID_SEAT_FRONT_BACK_POS:
        cmd.kind = CMD_FRONT_BACK;
        // 座椅前后电机：正向向后
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
    case KEY_ID_SEAT_FRONT_BACK_NEG:
        cmd.kind = CMD_FRONT_BACK;
        // 座椅前后电机：反向向前
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_BACK_REST_POS:
        cmd.kind = CMD_BACK_REST;
        // 靠背电机：正向是靠背向前，即收起来坐直
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
    case KEY_ID_BACK_REST_NEG:
        cmd.kind = CMD_BACK_REST;
        // 靠背电机：反向是靠背向后，即展开躺平
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_SEAT_UP_DOWN_POS:
        cmd.kind = CMD_SEAT_LIFT;
        // 座椅抬升电机：正向是抬升
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_SEAT_UP_DOWN_NEG:
        cmd.kind = CMD_SEAT_LIFT;
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
    case KEY_ID_LEG_FRONT_BACK_POS:
        // NOTE: 腿托按键换成操纵侧滑，腿托改为用废弃的腰托按键
        cmd.kind = CMD_SEAT_SLIDE;
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_LEG_FRONT_BACK_NEG:
        // NOTE: 腿托按键换成操纵侧滑，腿托改为用废弃的腰托按键
        cmd.kind = CMD_SEAT_SLIDE;
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
    case KEY_ID_HEATER:
        // TODO: temp: reset mcu
        // Mcu_PerformReset();
        // cmd.kind = CMD_HEATER;
        // cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_MASSAGE:
        cmd.kind = CMD_MASSAGE;
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_FAN:
        cmd.kind = CMD_FAN;
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_ZERO_GRAVITY_TMP:
        cmd.kind = CMD_ZERO_GRAVITY;
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_LEFT_WAIST_POS:
        // 废弃腰托按键，改为腿托抬升
        cmd.kind = CMD_LEG_LIFT;
        // 腿托抬升电机：正向为抬升
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_LEFT_WAIST_NEG:
        // 废弃腰托按键，改为腿托下降
        cmd.kind = CMD_LEG_LIFT;
        // 腿托抬升电机：反向为下降
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
    case KEY_ID_RIGHT_WAIST_POS:
        // 废弃腰托按键，改为腿托伸展
        cmd.kind = CMD_LEG_STRETCH;
        cmd.action = action_with_option(ACTION_POS, keyOption);
        break;
    case KEY_ID_RIGHT_WAIST_NEG:
        // 废弃腰托按键，改为腿托伸展
        cmd.kind = CMD_LEG_STRETCH;
        cmd.action = action_with_option(ACTION_NEG, keyOption);
        break;
#endif
    case KEY_ID_ZERO_GRAVITY_POS:
        cmd.kind = CMD_ZERO_GRAVITY;
        if (keyOption == KEY_OPTION_PRESSED) {
            cmd.action = ACTION_POS;
        } else if (keyOption == KEY_OPTION_RELEASED) {
            cmd.action = ACTION_NONE;
        }
        break;
    case KEY_ID_ZERO_GRAVITY_NEG:
        cmd.kind = CMD_ZERO_GRAVITY;
        if (keyOption == KEY_OPTION_PRESSED) {
            cmd.action = ACTION_NEG;
        } else if (keyOption == KEY_OPTION_RELEASED) {
            cmd.action = ACTION_NONE;
        }
        break;
    default:
        cmd.kind = CMD_NONE;
        cmd.action = ACTION_NONE;
        break;
    }
    return cmd;
}

static tCmdAction iokey_to_action(tBool keyState)
{
    return keyState == true ? ACTION_POS : ACTION_OFF;
}

static tCmd iokey_to_cmd(u8 keyId, u8 keyState)
{
    tCmd cmd = {0};
    switch (keyId) {
    case IO_KEY_FAN:
        cmd.kind = CMD_FAN;
        cmd.action = iokey_to_action(keyState);
        break;
    case IO_KEY_MASSAGE:
        cmd.kind = CMD_MASSAGE;
        cmd.action = iokey_to_action(keyState);
        break;
    case IO_KEY_HEAT:
        cmd.kind = CMD_HEATER;
        cmd.action = iokey_to_action(keyState);
        break;
    }
    return cmd;
}

static tCmd lin_signal_to_cmd(u8 signalId, u8 action)
{
    tCmd cmd = {0};

    bool is_reverse = false;

    switch (signalId) {
    case DSS_ForwardBack_SwitchActiveSts_DSS_DSCU:
        cmd.kind = CMD_FRONT_BACK;
        is_reverse = true;
        break;
    case DSS_SeatBack_SwitchActiveSts_DSS_DSCU:
        cmd.kind = CMD_BACK_REST;
        is_reverse = true;
        break;
    case DSS_LegRest_SwitchActiveSts_DSS_DSCU:
        cmd.kind = CMD_LEG_LIFT;
        break;
    case DSS_LegRest_SwitchActiveSts_FB_DSS_DSCU:
        cmd.kind = CMD_LEG_STRETCH;
        break;
    case PSS_ForwardBack_SwitchActiveSts_PSS_DSCU:
        cmd.kind = CMD_FRONT_BACK;
        break;
    case PSS_SeatBack_SwitchActiveSts_PSS_DSCU:
        cmd.kind = CMD_BACK_REST;
        break;
    case PSS_LegRest_SwitchActiveSts_PSS_DSCU:
        cmd.kind = CMD_LEG_LIFT;
        break;
    case PSS_LegRest_SwitchActiveSts_FB_PSS_DSCU:
        cmd.kind = CMD_LEG_STRETCH;
        break;
    default:
        break;
    }

    if (cmd.kind != CMD_MAX) {
        switch (action) {
        case LIN_ACTION_POS:
            cmd.action = is_reverse ? ACTION_NEG : ACTION_POS;
            break;
        case LIN_ACTION_NEG:
            cmd.action = is_reverse ? ACTION_POS : ACTION_NEG;
            break;
        case LIN_ACTION_NONE:
            cmd.action = ACTION_OFF;
            break;
        default:
            break;
        }
    }

    return cmd;
}

static tCmd ble_to_cmd(u8 key, u8 value)
{
    tCmd cmd;
    cmd.action = ACTION_OFF;
    cmd.kind = CMD_MAX;
    cmd.level = 0;

    switch (key) {
    case SEAT_BLUETOOTH_COMMAND_SEAT_FORWARD:         //座椅前
        cmd.kind = CMD_FRONT_BACK;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG; // 座椅向前是收缩
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_BACK:            //座椅后
        cmd.kind = CMD_FRONT_BACK;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS; // 座椅向后是扩展
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_LEFT:            //座椅左
        cmd.kind = CMD_SEAT_SLIDE;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_RIGHT:           //座椅右
        cmd.kind = CMD_SEAT_SLIDE;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_UP:              //座椅上
        cmd.kind = CMD_SEAT_LIFT;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_DOWN:            //座椅下
        cmd.kind = CMD_SEAT_LIFT;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_UP:              //靠背前
        cmd.kind = CMD_BACK_REST;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG; // 靠背前是收缩
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_DOWN:            //靠背后
        cmd.kind = CMD_BACK_REST;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS; // 靠背后是展开
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LEG_REST_UP:          //腿托上
        cmd.kind = CMD_LEG_LIFT;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LEG_REST_DOWN:        //腿托下
        cmd.kind = CMD_LEG_LIFT;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LEG_REST_FORWARD:     //腿托伸出
        cmd.kind = CMD_LEG_STRETCH;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LEG_REST_BACK:        //腿托收回
        cmd.kind = CMD_LEG_STRETCH;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_NEG;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_UP:       //腰托上
        cmd.kind = CMD_WAIST;
        if (value == 1) {
            cmd.action = WAIST_UP;
        } else {
            cmd.action = WAIST_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_DOWN:     //腰托下
        cmd.kind = CMD_WAIST;
        if (value == 1) {
            cmd.action = WAIST_DOWN;
        } else {
            cmd.action = WAIST_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_FORWARD:  //腰托挤出
        cmd.kind = CMD_WAIST;
        if (value == 1) {
            cmd.action = WAIST_FRONT;
        } else {
            cmd.action = WAIST_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_BACK:     //腰托收回
        cmd.kind = CMD_WAIST;
        if (value == 1) {
            cmd.action = WAIST_BACK;
        } else {
            cmd.action = WAIST_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_MASSAGE_SWITCH:       //按摩开关
        cmd.kind = CMD_MASSAGE;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_MASSAGE_MODE:         //按摩模式
        cmd.kind = CMD_MASSAGE_MODE;
        cmd.action = ACTION_POS;
        cmd.level = value;
        break;
    case SEAT_BLUETOOTH_COMMAND_MASSAGE_INTENSITY:    //按摩强度
        /*
            上位机发送强度1 2 3
            lin矩阵中定义：
            0x0:无请求
            0x1:关闭
            0x2:低（1档）
            0x3:中（2档）
            0x4:高（3档）
        */
        cmd.kind = CMD_MASSAGE_INTENSITY;
        cmd.action = ACTION_POS;
        cmd.level = value+1;
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_FAN_SWITCH:      //风扇-靠背-开关
        cmd.kind = CMD_BACK_FAN_SWITCH;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        } else {
            cmd.action = ACTION_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_FAN_INTENSITY:   //风扇-靠背-档位
        cmd.kind = CMD_BACK_FAN_LEVEL;
        cmd.action = ACTION_POS;
        cmd.level = value;
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_FAN_SWITCH:      //风扇-座椅-开关
        cmd.kind = CMD_SEAT_FAN_SWITCH;
        if (value == 0) {
            cmd.action = ACTION_OFF;
        } else if (value == 1) {
            cmd.action = ACTION_POS;
        } else {
            cmd.action = ACTION_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_FAN_INTENSITY:   //风扇-座椅-档位
        cmd.kind = CMD_SEAT_FAN_LEVEL;
        cmd.action = ACTION_POS;
        cmd.level = value;
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_HEAT_SWITCH:     //加热-靠背-开关
        if(value==0){
            cmd.action=ACTION_OFF;
        }else{
            cmd.action=ACTION_POS;
        }
        cmd.kind = CMD_BACK_HEATER;
        break;
    case SEAT_BLUETOOTH_COMMAND_BACK_HEAT_INTENSITY:  //加热-靠背-强度
        //加热没有挡位
        cmd.kind = CMD_BACK_HEATER_LEVEL;
        cmd.action = ACTION_POS;
        cmd.level = value;
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_HEAT_SWITCH:     //加热-座椅-开关
        if(value==0){
            cmd.action=ACTION_OFF;
        }else{
            cmd.action=ACTION_POS;
        }
        cmd.kind = CMD_SEAT_HEATER;
        break;
    case SEAT_BLUETOOTH_COMMAND_SEAT_HEAT_INTENSITY:  //加热-座椅-强度
        //加热没有挡位
        cmd.kind = CMD_SEAT_HEATER_LEVEL;
        cmd.action = ACTION_POS;
        cmd.level = value;
        break;
    case SEAT_BLUETOOTH_COMMAND_ZERO_GRAVITY:         //一键零重力
        cmd.kind = CMD_ZERO_GRAVITY;
        if (value == 0) {
            cmd.action = ACTION_NEG; // NEG表示恢复之前位置
        } else if (value == 1) {
            cmd.action = ACTION_POS; // POS表示展开成零重力
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_ALL_RESET:            //一键全部复位
        cmd.kind = CMD_ALL_RESET;
        if (value == 1) {
            cmd.action = ACTION_POS;
        } else {
            cmd.action = ACTION_NONE;
        }
        break;
    case SEAT_BLUETOOTH_COMMAND_CALIBRATION:
        cmd.kind = CMD_CALIBRATION;
        if (value == 1) {
            cmd.action = ACTION_POS;
        } else {
            cmd.action = ACTION_NONE;
        }
        break;
    default:
        break;
    }
    return cmd;
}

// static void reset_all(void)
// {
//     // close fan
//     do_fan_all((tCmd){CMD_FAN, ACTION_OFF, 0});
//     // close heater
//     do_heater_all((tCmd){CMD_HEATER, ACTION_OFF, 0});
//     // close massage
//     do_massage((tCmd){CMD_MASSAGE, ACTION_OFF, 0});
//     // close waist
//     do_waist((tCmd){CMD_WAIST, WAIST_BACK, 0});
//     do_waist((tCmd){CMD_WAIST, WAIST_DOWN, 0});
//     // shrink seat
//     motors_shrink_fast();
// }


static void dispatch_cmd(tCmd cmd)
{
    // Some cmds go to motor
    switch (cmd.kind) {
    case CMD_BACK_REST:
    case CMD_SEAT_LIFT:
    case CMD_SEAT_SLIDE:
    case CMD_FRONT_BACK:
    case CMD_LEG_LIFT:
    case CMD_LEG_STRETCH:
        // submit to motor
        do_simple_motor(cmd);
        break;
    case CMD_ZERO_GRAVITY:
        do_zero_gravity(cmd);
        break;
    case CMD_MASSAGE:
        do_massage(cmd);
        break;
    case CMD_MASSAGE_MODE:
        do_massage_mode(cmd);
        break;
    case CMD_MASSAGE_INTENSITY:
        do_massage_intensity(cmd);
        break;
    case CMD_HEATER:
        do_heater_all(cmd);
        break;
    case CMD_FAN:
        do_fan_all(cmd);
        break;
    case CMD_BACK_FAN_SWITCH:
        do_fan_back(cmd);
        break;
    case CMD_BACK_FAN_LEVEL:
        do_fan_back_level(cmd);
        break;
    case CMD_SEAT_FAN_SWITCH:
        do_fan_seat(cmd);
        break;
    case CMD_SEAT_FAN_LEVEL:
        do_fan_seat_level(cmd);
        break;
    case CMD_WAIST:
        do_waist(cmd);
        break;
    case CMD_BACK_HEATER:
        do_heater_back(cmd);
        break;
    case CMD_SEAT_HEATER:
        do_heater_seat(cmd);
        break;
    case CMD_BACK_HEATER_LEVEL:
        do_heater_back_level(cmd);
        break;
    case CMD_SEAT_HEATER_LEVEL:
        do_heater_seat_level(cmd);
        break;
    case CMD_ALL_RESET:
        motors_zero_recover();
        break;
    case CMD_CALIBRATION:
        if (cmd.action == ACTION_POS) {
            motors_calib_batch();
        }
        break;
    default:
        // TODO: go to LIN or other destines
        break;
    }

    println(LogInfo, "OS_MOTOR: motor go %d\n", cmd.action);
}
/// ---------- end of guard:   <header> ---------------------------------------

void pod_cmd_on_bluetooth(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_cmd_on_bluetooth> -----------------------------------------
    u8 key = (u8) ev->as.u16[0];
    u8 value = (u8) ev->as.u16[1];

    tCmd cmd = ble_to_cmd(key, value);

    dispatch_cmd(cmd);
/// ---------- end of guard:   <pod_cmd_on_bluetooth> -----------------------------------------
}

void pod_cmd_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_cmd_on_init> -----------------------------------------
/// ---------- end of guard:   <pod_cmd_on_init> -----------------------------------------
}

void pod_cmd_on_io_key(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_cmd_on_io_key> -----------------------------------------
    u8 keyId = (u8) ev->as.u16[0];
    tBool keyState = (tBool) ev->as.u16[1];

    println(LogInfo, "Got IO Key: %d:%d", keyId, keyState);
    tCmd cmd = iokey_to_cmd(keyId, keyState);
    println(LogInfo, "IOKey to Cmd: %s", cmd_to_str(&cmd));

    dispatch_cmd(cmd);
/// ---------- end of guard:   <pod_cmd_on_io_key> -----------------------------------------
}

void pod_cmd_on_key(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_cmd_on_key> -----------------------------------------
    u8 keyId = (u8) ev->as.u16[0];
    u8 keyOption = (u8) ev->as.u16[1];

    println(LogInfo, "Got Key: %d:%d", keyId, keyOption);

    tCmd cmd = key_to_cmd(keyId, keyOption);

    println(LogInfo, "Key to Cmd: %s", cmd_to_str(&cmd));

    dispatch_cmd(cmd);
/// ---------- end of guard:   <pod_cmd_on_key> -----------------------------------------
}

void pod_cmd_on_lin(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_cmd_on_lin> -----------------------------------------
    u8 signalId = (u8) ev->as.u16[0];
    u8 action = (u8) ev->as.u16[1];

    println(LogInfo, "Got Lin: %d:%d", signalId, action);

    tCmd cmd = lin_signal_to_cmd(signalId, action);

    println(LogInfo, "Key to Cmd: %s", cmd_to_str(&cmd));

    dispatch_cmd(cmd);
/// ---------- end of guard:   <pod_cmd_on_lin> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------