/**
 * @file    Fls_Cfg.c
 * @brief   Flash driver for LES14XX
 * <AUTHOR>
 * @date    2025.5.29
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved.
 */




/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fls.h"


#ifdef __cplusplus
extern "C"{
#endif


#ifdef FLS_PRECOMPILE_SUPPORT

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_VENDOR_ID_PCFG_C                      (110u)
#define FLS_AR_RELEASE_MAJOR_VERSION_PCFG_C       (4u)
#define FLS_AR_RELEASE_MINOR_VERSION_PCFG_C       (4u)
#define FLS_AR_RELEASE_REVISION_VERSION_PCFG_C    (0u)
#define FLS_SW_MAJOR_VERSION_PCFG_C               (1u)
#define FLS_SW_MINOR_VERSION_PCFG_C               (0u)
#define FLS_SW_PATCH_VERSION_PCFG_C               (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Fls header file are of the same vendor */
#if (FLS_VENDOR_ID_PCFG_C != FLS_VENDOR_ID)
    #error "Fls_Cfg.c and Fls.h have different vendor ids"
#endif
/* Check if current file and Fls header file are of the same Autosar version */
#if ((FLS_AR_RELEASE_MAJOR_VERSION_PCFG_C    != FLS_AR_RELEASE_MAJOR_VERSION) || \
     (FLS_AR_RELEASE_MINOR_VERSION_PCFG_C    != FLS_AR_RELEASE_MINOR_VERSION) || \
     (FLS_AR_RELEASE_REVISION_VERSION_PCFG_C != FLS_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Fls_Cfg.c and Fls.h are different"
#endif
/* Check if current file and Fls header file are of the same Software version */
#if ((FLS_SW_MAJOR_VERSION_PCFG_C != FLS_SW_MAJOR_VERSION) || \
     (FLS_SW_MINOR_VERSION_PCFG_C != FLS_SW_MINOR_VERSION) || \
     (FLS_SW_PATCH_VERSION_PCFG_C != FLS_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Fls_Cfg.c and Fls.h are different"
#endif


/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/
#define FLS_START_SEC_CODE
#include "Fls_MemMap.h"
/* Declaration of Fls notification function (Fee_JobEndNotification())*/
extern void Fee_JobEndNotification( void );

/* Declaration of Fls notification function (Fee_JobErrorNotification())*/
extern void Fee_JobErrorNotification( void );


#define FLS_STOP_SEC_CODE
#include "Fls_MemMap.h"


/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

#define FLS_START_SEC_CONFIG_DATA_8
#include "Fls_MemMap.h"

/* aFlsSectorFlags */
static const uint8  FlsConfigSet_aFlsSectorFlags[9U] =
{
            
    
     0U, /* FlsSector_0 */

            
    
     0U, /* FlsSector_1 */

            
    
     0U, /* FlsSector_2 */

            
    
     0U, /* FlsSector_3 */

            
    
     0U, /* FlsSector_4 */

            
    
     0U, /* FlsSector_5 */

            
    
     0U, /* FlsSector_6 */

            
    
     0U, /* FlsSector_7 */

            
    
     0U /* FlsSector_8 */

};







#define FLS_STOP_SEC_CONFIG_DATA_8
#include "Fls_MemMap.h"

#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

/* aFlsSectorEndAddr */
static const Fls_AddressType FlsConfigSet_aFlsSectorEndAddr[9U] =
{
            (Fls_AddressType)8191U,  /* FlsSector_0 */
            (Fls_AddressType)16383U,  /* FlsSector_1 */
            (Fls_AddressType)24575U,  /* FlsSector_2 */
            (Fls_AddressType)32767U,  /* FlsSector_3 */
            (Fls_AddressType)40959U,  /* FlsSector_4 */
            (Fls_AddressType)49151U,  /* FlsSector_5 */
            (Fls_AddressType)57343U,  /* FlsSector_6 */
            (Fls_AddressType)65535U,  /* FlsSector_7 */
            (Fls_AddressType)73727U   /* FlsSector_8 */
};

/* paSectorSize */
static const Fls_LengthType FlsConfigSet_aFlsSectorSize[9U] =
{
            (Fls_LengthType)8192U,  /* FlsSector_0 */
            (Fls_LengthType)8192U,  /* FlsSector_1 */
            (Fls_LengthType)8192U,  /* FlsSector_2 */
            (Fls_LengthType)8192U,  /* FlsSector_3 */
            (Fls_LengthType)8192U,  /* FlsSector_4 */
            (Fls_LengthType)8192U,  /* FlsSector_5 */
            (Fls_LengthType)8192U,  /* FlsSector_6 */
            (Fls_LengthType)8192U,  /* FlsSector_7 */
            (Fls_LengthType)8192U   /* FlsSector_8 */
};

/* paSectorPageSize */
static const Fls_LengthType FlsConfigSet_aFlsSectorPageSize[9U] =
{
            (Fls_LengthType)16U,  /* FlsSector_0 */
            (Fls_LengthType)16U,  /* FlsSector_1 */
            (Fls_LengthType)16U,  /* FlsSector_2 */
            (Fls_LengthType)16U,  /* FlsSector_3 */
            (Fls_LengthType)16U,  /* FlsSector_4 */
            (Fls_LengthType)16U,  /* FlsSector_5 */
            (Fls_LengthType)16U,  /* FlsSector_6 */
            (Fls_LengthType)16U,  /* FlsSector_7 */
            (Fls_LengthType)16U   /* FlsSector_8 */
};

/* Info structure (reg prot, ecc trigger, etc) for each internal flash sector. */

    
        static const FlsInternalSectorInfo_t FlsSector_0_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x000000UL),  /* pSectorStartAddressPtr */
    0U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_1_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x002000UL),  /* pSectorStartAddressPtr */
    1U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_2_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x004000UL),  /* pSectorStartAddressPtr */
    2U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_3_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x006000UL),  /* pSectorStartAddressPtr */
    3U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_4_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x008000UL),  /* pSectorStartAddressPtr */
    4U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_5_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x00A000UL),  /* pSectorStartAddressPtr */
    5U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_6_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x00C000UL),  /* pSectorStartAddressPtr */
    6U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_7_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x00E000UL),  /* pSectorStartAddressPtr */
    7U  /* Sector location to calculate cfgCRC */

        };
        
    
    
        static const FlsInternalSectorInfo_t FlsSector_8_FlsConfigSet_sInternalSectorInfo =
        {
            
                (FLS_HW_D_FLASH_BASE_ADDR + 0x010000UL),  /* pSectorStartAddressPtr */
    8U  /* Sector location to calculate cfgCRC */

        };
        
    
/* FLASH physical sectorization description */
static const FlsInternalSectorInfo_t * const FlsConfigSet_aSectorList[9U] =
{
                            &FlsSector_0_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P000 */
                        &FlsSector_1_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P001 */
                        &FlsSector_2_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P002 */
                        &FlsSector_3_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P003 */
                        &FlsSector_4_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P004 */
                        &FlsSector_5_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P005 */
                        &FlsSector_6_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P006 */
                        &FlsSector_7_FlsConfigSet_sInternalSectorInfo        ,      /* FLS_DATA_ARRAY_0_BLOCK_1_P007 */
                        &FlsSector_8_FlsConfigSet_sInternalSectorInfo               /* FLS_DATA_ARRAY_0_BLOCK_1_P008 */
    };


/* paSectorHwAddress */
static const Fls_AddressType  FlsConfigSet_paSectorHwAddress[9U] =
{
                (Fls_AddressType)0U,  /* FlsSector_0 */
                (Fls_AddressType)0U,  /* FlsSector_1 */
                (Fls_AddressType)0U,  /* FlsSector_2 */
                (Fls_AddressType)0U,  /* FlsSector_3 */
                (Fls_AddressType)0U,  /* FlsSector_4 */
                (Fls_AddressType)0U,  /* FlsSector_5 */
                (Fls_AddressType)0U,  /* FlsSector_6 */
                (Fls_AddressType)0U,  /* FlsSector_7 */
                (Fls_AddressType)0U   /* FlsSector_8 */
};



/**
* @brief        Structure used to set function pointers notification, working mode
*/
/* Fls module initialization data Fls_Config*/
const Fls_ConfigType Fls_Config  =
{
#if (STD_ON == FLS_AC_LOAD_ON_JOB_START)
            (FlsAcErasePtr_t)NULL_PTR,                                        /* FlsAcErase */

            (FlsAcWritePtr_t)NULL_PTR,                                        /* FlsAcWrite */

#endif
            
            
    
                        
    
            NULL_PTR,                                                         /* FlsACCallback */



        
            
    
                        
    
            &Fee_JobEndNotification,                                          /* FlsJobEndNotification */



        
            
    
                        
    
            &Fee_JobErrorNotification,                                        /* FlsJobErrorNotification */



        
            
    
                        
    
            NULL_PTR,                                                         /* FlashCompleteCallback */



        
            
    
                        
    
            NULL_PTR,                                                         /* FlashECCCallback */



        
            
    
                        
    
            NULL_PTR,                                                         /* FlashConflictCallback */



	
        
    
                        
    
            MEMIF_MODE_SLOW,                                                  /* FlsDefaultMode */


        
    
                        
    
            1048576U,                                                         /* FlsMaxReadFastMode */


        
    
                        
    
            1024U,                                                            /* FlsMaxReadNormalMode */


        
    
                        
    
            256U,                                                             /* FlsMaxWriteFastMode */


        
    
                        
    
            32U,                                                              /* FlsMaxWriteNormalMode */


        
    
                        
    
            9U,                                                               /* FlsSectorCount */


        
    
                        
    
            &FlsConfigSet_aFlsSectorEndAddr,                                  /* (*paSectorEndAddr)[] */


        
    
                        
    
            &FlsConfigSet_aFlsSectorSize,                                     /* (*paSectorSize)[] */


        
    
                        
    
            &FlsConfigSet_aSectorList,                                        /* (*pSectorList)[] */


        
    
                        
    
            &FlsConfigSet_aFlsSectorFlags,                                    /* (*paSectorFlags)[] */


        
    
                        
    
            &FlsConfigSet_aFlsSectorPageSize,                                 /* (*paSectorPageSize)[] */


        
    
                        
    
            &FlsConfigSet_paSectorHwAddress,                                  /* (*paSectorHwAddress)[] */


        
    
                        
    
            &FlsConfigSet_InitCfg,                                            /* FlsInternalConfig */


        
    
                        
    
            &Fls_HwPartitionConfig,                                           /* FlsHwPartitionConfig */


        
    
                        
    
            &Fls_MacPartitionConfig,                                          /* FlsMacPartitionConfig */


        
    
                        
    
            &Fls_SecKeyPartitionConfig,                                       /* FlsSecPartitionConfig */



};


#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"





#endif /* FLS_PRECOMPILE_SUPPORT */
#ifdef __cplusplus
}
#endif

/** @}*/
