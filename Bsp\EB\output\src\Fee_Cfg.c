/**
 * file     Fee_Cfg.c
 * brief    Specification of Flash EEPROM Emulation
 * author   Huhl <<EMAIL>>
 * date     2024.5.25
 * version  1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved
 */

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fee.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define FEE_VENDOR_ID_CFG_C                      (110U)
#define FEE_AR_RELEASE_MAJOR_VERSION_CFG_C       (4U)
#define FEE_AR_RELEASE_MINOR_VERSION_CFG_C       (4U)
#define FEE_AR_RELEASE_REVISION_VERSION_CFG_C    (0U)
#define FEE_SW_MAJOR_VERSION_CFG_C               (1U)
#define FEE_SW_MINOR_VERSION_CFG_C               (0U)
#define FEE_SW_PATCH_VERSION_CFG_C               (0U)
/* 0791 --*/

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and Fee header file are of the same vendor */
#if (FEE_VENDOR_ID_CFG_C != FEE_VENDOR_ID)
    #error "Fee_Cfg.c and Fee.h have different vendor ids"
#endif
/* Check if current file and Fee header file are of the same Autosar version */
#if ((FEE_AR_RELEASE_MAJOR_VERSION_CFG_C    != FEE_AR_RELEASE_MAJOR_VERSION) || \
     (FEE_AR_RELEASE_MINOR_VERSION_CFG_C    != FEE_AR_RELEASE_MINOR_VERSION) || \
     (FEE_AR_RELEASE_REVISION_VERSION_CFG_C != FEE_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Fee_Cfg.c and Fee.h are different"
#endif
/* Check if current file and Fee header file are of the same Software version */
#if ((FEE_SW_MAJOR_VERSION_CFG_C != FEE_SW_MAJOR_VERSION) || \
     (FEE_SW_MINOR_VERSION_CFG_C != FEE_SW_MINOR_VERSION) || \
     (FEE_SW_PATCH_VERSION_CFG_C != FEE_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Fee_Cfg.c and Fee.h are different"
#endif



/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/





#define FEE_START_SEC_VAR_INIT_UNSPECIFIED
#include "Fee_MemMap.h"

/* Sectors information of group FeeSectorGroup_0 */
/* Sector information of group FeeSector_0 */
static FeeFlsSectorRtmInfo_t Fee_FeeSectorGroup_0_FeeSector_0[2U] =
{
    {
        0U,  /* Fls start address */
        8192U  /* Size */
    },
    {
        8192U,  /* Fls start address */
        8192U  /* Size */
    }
};

/* Sector information of group FeeSector_1 */
static FeeFlsSectorRtmInfo_t Fee_FeeSectorGroup_0_FeeSector_1[2U] =
{
    {
        16384U,  /* Fls start address */
        8192U  /* Size */
    },
    {
        24576U,  /* Fls start address */
        8192U  /* Size */
    }
};



/* Fee Sectors information of group FeeSectorGroup_0 */
static FeeSectorRuntimeInfo_t Fee_FeeSectorGroup_0_SectorInfo[2U] =
{
    /* FeeSector_0 */
    {
        16384U, /* Sector length */
        2U,  /* Number of Fls Sectors*/
        Fee_FeeSectorGroup_0_FeeSector_0  /* Sector set */
    },
    /* FeeSector_1 */
    {
        16384U, /* Sector length */
        2U,  /* Number of Fls Sectors*/
        Fee_FeeSectorGroup_0_FeeSector_1  /* Sector set */
    }
};


/* List of pointer for information accross all sector groups */
FeeSectorRuntimeInfo_t *Fee_SectorRuntimeInfo[FEE_NUM_OF_SECTOR_GROUPS] =
{
    Fee_FeeSectorGroup_0_SectorInfo   /* FeeSectorGroup_0 */
};

#define FEE_STOP_SEC_VAR_INIT_UNSPECIFIED
#include "Fee_MemMap.h"

#define FEE_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

const FeeSectorToRecover_t Fee_SectorToRecover[FEE_NUMBER_OF_SECTORS_TO_RECOVER] =
{
    /* FeeSectorToRecover_0 */
    {
        &(Fee_FeeSectorGroup_0_FeeSector_1[0U]),  /* Sector to recover */
        8192U  /* Sector size */
    }
};

#define FEE_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"



#define FEE_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"


/* Configuration of Sector group FeeSectorGroup_0 */
static const FeeSector_t Fee_FeeSectorGroup_0[2U] =
{
    /* FeeSector_0 */
    {
        0U  /* Start address */
    },
    /* FeeSector_1 */
    {
        16384U  /* Start address */
    }
};

/* Configuration of Sector group set */
const FeeSectorGroup_t Fee_SectorGrps[FEE_NUM_OF_SECTOR_GROUPS] =
{
    
    /* FeeSectorGroup_0 */
    {
        Fee_FeeSectorGroup_0, /* Fee Sector set */
        2U, /* Number of Fee Sector */
        0U /* Size of the reserved area */
    }
};

/* Configuration of Fee blocks */
const FeeBlockConfig_t Fee_BlockConfig[FEE_NUM_OF_CFG_BLOCKS] =
{
    /* FeeBlockConfiguration_Motor */
    {
        FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_Motor, /* FeeBlockNumber symbol */
        100U, /* FeeBlockSize */
        0U, /* Fee Sector Group */
        (boolean)FALSE,   /* FeeImmediateData */
#if (FEE_SWAP_FOREIGN_BLOCKS_ENABLED == STD_ON)
        FEE_PROJECT_APPLICATION /* Fee Block Assignment to a project */
#else
        FEE_PROJECT_RESERVED
#endif
    }
};

#define FEE_STOP_SEC_CONFIG_DATA_UNSPECIFIED
/* @section [global]
 * 5087 ++
 * This attribute syntax is a language extension.
 * REASON: Variables and text need to be placed in the specified location
*/

#include "Fee_MemMap.h" 
/* 5087 -- */
#ifdef __cplusplus
}
#endif
/** @}*/
