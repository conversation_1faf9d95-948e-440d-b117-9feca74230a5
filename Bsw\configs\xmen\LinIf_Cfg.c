/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : LinIf_Cfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:15 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
//#include "Compiler.h"
#include "LinIf.h"
#include "LinSM_Cbk.h"
#include "PduR_LinIf.h"
//#include "EcuM.h"
#include "LinIf_Cfg.h"
#include "Com_Cfg.h"
#include "Lin.h"

#define LINIF_START_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "LinIf_MemMap.h"
static VAR(boolean, LINIF_VAR) LinIf_TransmitPendingData[7] = {0};
#define LINIF_STOP_SEC_VAR_POWER_ON_INIT_UNSPECIFIED
#include "LinIf_MemMap.h"

#define LINIF_START_SEC_CONST_UNSPECIFIED
#include "LinIf_MemMap.h"

static CONST(LinIf_TxPduType, LINIF_CONST) LinIf_TxPduData[] =
{

    {
        PduR_LinIfTxConfirmation,  /* LinIfTxConfirmationUL */
        LINIF_TXPDU_DSM_LIN1,  /* LinIfTxPduId */
        PduR_LinIfTriggerTransmit, /* LinIfTxTriggerTransmitUL */
        LINIF_UL_PDUR,             /* LinIfUserTxUL */
        PDUR_DESTPDU_DSM_LIN1   ,     /* LinIfTxPduRef */
        FALSE,                       /* LinIfContainResponseErrorSignal */
    },
};

static CONST(LinIf_RxPduType, LINIF_CONST) LinIf_RxPduData[] =
{
    {
        PduR_LinIfRxIndication, /* LinIfRxIndicationUL */
        LINIF_UL_PDUR,          /* LinIfUserRxIndicationUL */
        PDUR_SRCPDU_MLCU_DSCU_1,                      /* LinIfRxPduRef */
    },
    {
        PduR_LinIfRxIndication, /* LinIfRxIndicationUL */
        LINIF_UL_PDUR,          /* LinIfUserRxIndicationUL */
        PDUR_SRCPDU_MLCU_DSCU_2,                      /* LinIfRxPduRef */
    },
    {
        PduR_LinIfRxIndication, /* LinIfRxIndicationUL */
        LINIF_UL_PDUR,          /* LinIfUserRxIndicationUL */
        PDUR_SRCPDU_DSS_DSCU,                      /* LinIfRxPduRef */
    },
    {
        PduR_LinIfRxIndication, /* LinIfRxIndicationUL */
        LINIF_UL_PDUR,          /* LinIfUserRxIndicationUL */
        PDUR_SRCPDU_PSS_DSCU,                      /* LinIfRxPduRef */
    },
};

static CONST(LinIf_PduDirectionType, LINIF_CONST) LinIf_PduDirectionData[] =
{
    /* index 0 */
    {
        LINIF_TX_PDU,        /* LinIfPduDirectionId */

        NULL_PTR,            /* LinIfRxPdu */

        &LinIf_TxPduData[0],             /* LinIfTxPdu */
    },
    /* index 1 */
    {
        LINIF_RX_PDU,        /* LinIfPduDirectionId */

        &LinIf_RxPduData[0],            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
    /* index 2 */
    {
        LINIF_RX_PDU,        /* LinIfPduDirectionId */

        &LinIf_RxPduData[1],            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
    /* index 3 */
    {
        LINIF_TX_PDU,        /* LinIfPduDirectionId */

        NULL_PTR,            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
    /* index 4 */
    {
        LINIF_RX_PDU,        /* LinIfPduDirectionId */

        NULL_PTR,            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
    /* index 5 */
    {
        LINIF_RX_PDU,        /* LinIfPduDirectionId */

        &LinIf_RxPduData[2],            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
    /* index 6 */
    {
        LINIF_RX_PDU,        /* LinIfPduDirectionId */

        &LinIf_RxPduData[3],            /* LinIfRxPdu */

        NULL_PTR,             /* LinIfTxPdu */
    },
};

static CONST(LinIf_FrameType, LINIF_CONST) LinIf_FrameData[] =
{
  /* index 0 */
  {
    LINIF_ENHANCED,             /* LinIfChecksumType */
    0x10,                       /* LinIfFrameId  */
    0x50,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_UNCONDITIONAL,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[0], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[0], /* LinIfIsTransmitPending */
  },
  /* index 1 */
  {
    LINIF_ENHANCED,             /* LinIfChecksumType */
    0x1a,                       /* LinIfFrameId  */
    0x1a,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_UNCONDITIONAL,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[1], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[1], /* LinIfIsTransmitPending */
  },
  /* index 2 */
  {
    LINIF_ENHANCED,             /* LinIfChecksumType */
    0x2a,                       /* LinIfFrameId  */
    0x6a,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_UNCONDITIONAL,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[2], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[2], /* LinIfIsTransmitPending */
  },
  /* index 3 */
  {
    LINIF_CLASSIC,             /* LinIfChecksumType */
    0x3c,                       /* LinIfFrameId  */
    0x3c,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_MRF,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[3], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[3], /* LinIfIsTransmitPending */
  },
  /* index 4 */
  {
    LINIF_CLASSIC,             /* LinIfChecksumType */
    0x3d,                       /* LinIfFrameId  */
    0x7d,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_SRF,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[4], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[4], /* LinIfIsTransmitPending */
  },
  /* index 5 */
  {
    LINIF_ENHANCED,             /* LinIfChecksumType */
    0x2b,                       /* LinIfFrameId  */
    0x2b,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    2,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_UNCONDITIONAL,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[5], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[5], /* LinIfIsTransmitPending */
  },
  /* index 6 */
  {
    LINIF_ENHANCED,             /* LinIfChecksumType */
    0x2c,                       /* LinIfFrameId  */
    0xec,                       /* LinIfProtectedId */
    255,                            /* LinIfFrameIndex */
    8,                          /* LinIfLength */
    0xffu,                       /* LinIfFrameIdAssociatedWithEvent */
    LINIF_UNCONDITIONAL,        /* LinIfFrameType */
    NULL_PTR,                   /* LinIfFixedFrameSdu */
    &LinIf_PduDirectionData[6], /* LinIfPduDirection */
    0,                          /* LinIfNumOfSubstitutionFrame */
    NULL_PTR,                    /* LinIfSubstitutionFrames */
    &LinIf_TransmitPendingData[6], /* LinIfIsTransmitPending */
  },
};
static CONST(LinIf_EntryType, LINIF_CONST) LinIf_EntryData[] =
{
  {
    5,  /* LinIfDelay */
    0,  /* LinIfEntryIndex */
    0,  /* LinIfCollisionResolvingRef */
    0,   /* LinIfFrameRef */
  },
  {
    5,  /* LinIfDelay */
    1,  /* LinIfEntryIndex */
    0,  /* LinIfCollisionResolvingRef */
    1,   /* LinIfFrameRef */
  },
  {
    5,  /* LinIfDelay */
    2,  /* LinIfEntryIndex */
    0,  /* LinIfCollisionResolvingRef */
    2,   /* LinIfFrameRef */
  },
  {
    5,  /* LinIfDelay */
    3,  /* LinIfEntryIndex */
    0,  /* LinIfCollisionResolvingRef */
    5,   /* LinIfFrameRef */
  },
  {
    5,  /* LinIfDelay */
    4,  /* LinIfEntryIndex */
    0,  /* LinIfCollisionResolvingRef */
    6,   /* LinIfFrameRef */
  },
};
static CONST(LinIf_ScheduleTableType, LINIF_CONST) LinIf_ScheduleTableData[] =
{
  /* NULL SCHEDULE */
  {
    LINIF_START_FROM_BEGINNING, /* LinIfResumePosition */
    LINIF_RUN_ONCE,             /* LinIfRunMode */
    LINIF_NULL_SCHEDULE_INDEX,  /* LinIfScheduleTableIndex */
    NULL_PTR,                   /* LinIfEntry */
    0,                           /* LinIfNumOfEntry */
  },
  {
    LINIF_START_FROM_BEGINNING, /* LinIfResumePosition */
    LINIF_RUN_CONTINUOUS,             /* LinIfRunMode */
    LinIfScheduleTable_schedule_1,       /* LinIfScheduleTableIndex */
    &LinIf_EntryData[0],                   /* LinIfEntry */
    5,                           /* LinIfNumOfEntry */
  },
};
static CONST(LinIf_LinDriverChannelRef, LINIF_CONST) LinIf_LinDriverChannelRefData[] =
{
    {
        0,                   /* LinChannelIdRef */
        0,                   /* LinDriverId */
        0,  /* WakeUpSource */
    },
};
static CONST(LinIf_MasterType, LINIF_CONST) LinIf_MasterData[] =
{
    {
        0, /* LinIfJitter */
    },

};
static CONST(LinIf_NodeType, LINIF_CONST) LinIf_NodeTypeData[] =
{
    {
    LINIF_MASTER,                   /* LinIfNodeType */
    &LinIf_MasterData[0],       /* LinIfMaster */
    NULL_PTR,                        /* LinIfSlave */
    },
};

static CONST(LinIf_ChannelType, LINIF_CONST) LinIf_ChannelData[LINIF_NUMBER_OF_CHANNELS] =
{
  {
    2000u,                                 /* LinIfBusIdleTimeoutCnt */
    LINIF_UL_LINSM,                     /* LinIfGotoSleepConfirmationUL */
    LinSM_GotoSleepConfirmation,        /* GotoSleepConfirmation */
    LINIF_UL_LINSM,                     /* LinIfGotoSleepIndicationUL */
    NULL_PTR,        /* GotoSleepIndication */
    0,                                /* LinIfMaxFrameCnt */
    FALSE,                               /* LinIfScheduleChangeNextTimeBase */
    LINIF_UL_LINSM,                     /* LinIfScheduleRequestConfirmationUL */
    LinSM_ScheduleRequestConfirmation,  /* ScheduleRequestConfirmation */
    LINIF_STARTUP_NORMAL,               /* LinIfStartupState */
    LINIF_UL_LINSM,                     /* LinIfWakeupConfirmationUL */
    LinSM_WakeupConfirmation,           /* WakeupConfirmation */
    &LinIf_LinDriverChannelRefData[0],  /* LinIfChannelRef */
    1,                                  /* LinIfComMNetworkHandleRef */
    7,                                  /* LinIfNumOfFrame */
    0,                                  /* LinIfFrameIndexOffset */
    &LinIf_FrameData[0],                /* LinIfFrame */
    &LinIf_NodeTypeData[0],               /* LinIfNodeType */
    1,                                  /* LinIfNumOfSchedule */
    1,                                  /* LinIfScheduleIndexOffset */
    &LinIf_ScheduleTableData[1],        /* LinIfScheduleTable */
    NULL_PTR,                            /* LinIfTransceiverDrvConfig */
  },
};

CONST(LinIf_ConfigType, LINIF_CONST) LinIf_PCConfig =
{
  2,                                   /* LinIfTimeBase */
  0,                                    /* LinIfNumOfSubstitution */
  NULL_PTR,                             /* LinIfSubstitution */
  2,                                    /* LinIfNumOfTxPdu */
  LinIf_TxPduData,                      /* LinIfTxPdu */
  LinIf_FrameData,                      /* LinIfFrame */
  LinIf_ChannelData,                     /* LinIfChannel */
};

static CONST(LinTp_ChannelConfigType, LINIF_CONST) LinTp_ChannelConfigData[] =
{
    {
        0,     /* LinTpLinDriverChannelRef */
        TRUE,  /* LinTpDropNotRequestedNad */
        1,      /* LinTpChannelRef */
    },
};

static CONST(LinTp_RxNSduType, LINIF_CONST) LinTp_RxNSduData[] =
{
};

static CONST(LinTp_TxNSduType, LINIF_CONST) LinTp_TxNSduData[] =
{
};

CONST(LinTp_ConfigType, LINIF_CONST) LinTp_PCConfig =
{
    5,                          /* LinTpMaxNumberOfRespPendingFrames */
    0,                          /* LinTpMaxRxNSduCnt */
    0,                          /* LinTpNumOfRxNSdu */
    0,                          /* LinTpMaxTxNSduCnt */
    0,                          /* LinTpNumOfTxNSdu */
    1000,                       /* LinTpP2MaxCnt */
    250,                        /* LinTpP2TimingCnt */
    LinTp_ChannelConfigData,    /* LinTpChannelConfig */
    LinTp_RxNSduData,           /* LinTpRxNSdu */
    LinTp_TxNSduData,            /* LinTpTxNSdu */
};
CONST(Lin_DriverApiType, LINIF_CONST) Lin_DriverApi[LINIF_NUMBER_OF_DRIVERS] =
{
    {
        Lin_GetStatus,            /* LinGetStatus */
        Lin_GoToSleep,             /* LinGoToSleep */
        Lin_GoToSleepInternal,    /* LinGoToSleepInternal */
        Lin_SendFrame,               /* LinSendFrame */
        Lin_Wakeup,               /* LinWakeup */
        Lin_WakeupInternal,       /* LinWakeupInternal */
        Lin_CheckWakeup,     /* LinCheckWakeup */
    },
};

#define LINIF_STOP_SEC_CONST_UNSPECIFIED
#include "LinIf_MemMap.h"
