#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
//#include "check_example.h"
#include "log.h"
#include "osal.h"

#ifdef WIN32

#else // LSE14xx

#include "lse14xx.h"
#include "Mcu.h"
#include "Port.h"
#include "Dio.h"
#include "Can.h"
#include "CanIf_Cfg.h"
#include "Lin.h"
#include "Pwm.h"
#include "Adc.h"
#include "Dma.h"
#include "Icu.h"
#include "Gpt.h"
#include "Fls.h"
#include "Fls_Cfg.h"
#include "Fee.h"
#include "core_cm4.h"

#include "CanIf_Cfg.h"
#include "CanIf.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Dcm.h"
#include "Dem.h"
#include "Com.h"
#include "CanSM.h"
#include "ComM.h"
#include "ComM_PBCfg.h"
#include "ComM_Gent.h"
#include "encoder.h"

#include "Dcm_Cfg.h"
#include "LinIf.h"
#include "LinSM.h"
#include "PduR.h"
#include "SchM_Com.h"
#include "SchM_Dcm.h"
#include "SchM_Dem.h"
#include "SchM_LinIf.h"
#include "SchM_LinSM.h"
#include "key.h"
#include "Uart.h"
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define VOL_ADC_FACTOR (3.3*10/4096)*((680+68)/68)

/*==================================================================================================
*                                      EXTERN DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/
uint32  Adc0_SoftOneShotResultBuff[ADC_CFGSET_GROUP_0_CHANNELS] =  {0u};
uint32  Adc1_SoftOneShotResultBuff[ADC_CFGSET_GROUP_1_CHANNELS] =  {0u};  
Adc_ValueGroupType    Adc0_SoftOneShotResultBuff_temp[ADC_CFGSET_GROUP_0_CHANNELS];
Adc_ValueGroupType    Adc1_SoftOneShotResultBuff_temp[ADC_CFGSET_GROUP_1_CHANNELS];

Std_ReturnType Can_SetControllerMode_Std_ReturnType0;
Std_ReturnType Can_SetControllerMode_Std_ReturnType1;
Std_ReturnType Can_SetControllerMode_Std_ReturnType2;
Std_ReturnType Can_SetControllerMode_Std_ReturnType3;

extern tMotor motors[MOTOR_MAX];
uint32 accVol10x;
uint32 seatTemp;

static const uint16 adcTempTab[] =
{
    3897,3886,3874,3862,3850,3837,3824,3809,3795,3780,
    3764,3748,3731,3713,3695,3676,3657,3637,3616,3595,
    3572,3550,3526,3502,3478,3452,3426,3400,3372,3345,
    3316,3287,3256,3226,3195,3163,3130,3097,3064,3030,
    2995,2961,2925,2890,2853,2817,2780,2743,2705,2667,
    2629,2591,2552,2514,2475,2436,2397,2358,2319,2280,
    2241,2202,2163,2125,2086,2048,2010,1972,1934,1897,
    1860,1823,1786,1750,1714,1679,1644,1610,1576,1542,
    1509,1476,1444,1412,1381,1350,1320,1290,1261,1232,
    1204,1176,1148,1122,1095,1070,1045,1020,996,972,
    949,926,904,883,862,842,822,802,783,764,746,728,
    711,694,677,661,645,630,615,600,586,572,558,545,
    532,519,507,495,483,472,461,450,439,429,419,409,
    399,390,381,372,363,355,347,339,331,324,316,309,
    302,295,289,282,276,270,264,258,252,246,241,236,
    231,225,221,216,211,207,
};

static sint8 AdcToTemperature(uint16 adcValue)
{
    sint8 ret;
    uint8 left,right,mid;
    uint16 const *pTab = &adcTempTab[0];

    if (adcValue >= pTab[0])
    {
        ret = -40;
    }
    else if (adcValue <= pTab[COUNT_OF(adcTempTab) - 1])
    {
        mid = (uint8)COUNT_OF(adcTempTab) - 1 - 40;
        ret = (sint8)mid;
    }
    else
    {
        left = 0;
        right = (uint8)COUNT_OF(adcTempTab) - 1;

        while(left < right)  
        {
            mid = (left + right) / 2;

            if (adcValue == pTab[mid])
            {
                break;
            }  
            else if ((adcValue < pTab[mid]) && (adcValue > pTab[mid + 1]))
            {
                mid = ((pTab[mid] - adcValue) < (adcValue - pTab[mid + 1])) ? mid : (mid + 1);
                break;
            }
            else
            {
                if (adcValue > pTab[mid])
                {
                    right = mid;
                }
                else
                {
                    left = mid;
                }
            }
        }
        ret = (sint8)mid - 40;
    }

    return ret;
}

// FUNC(void, CANIF_CODE) CanIf_RxIndication
// (
//     const Can_HwType* Mailbox,
//     const PduInfoType* PduInfoPtr
// )
// {
//     (void)Mailbox;
//     (void)PduInfoPtr;
//     Can_PduType pdu;
//     pdu.id = Mailbox->CanId & 0xFFFFFFFE;
//     pdu.length = PduInfoPtr->SduLength;
//     pdu.sdu = PduInfoPtr->SduDataPtr;
//     pdu.swPduHandle = 1;
//     Can_Write(Mailbox->Hoh + 4, &pdu);
// }

FUNC(void, CANIF_CODE) CanIf_CurrentIcomConfiguration
(
    uint8 ControllerId,
    IcomConfigIdType ConfigurationId,
    IcomSwitch_ErrorType Error
)
{
    (void)ControllerId;
    (void)ConfigurationId;
    (void)Error;
}

Std_ReturnType Can_Write_Std_ReturnType4;
Std_ReturnType Can_Write_Std_ReturnType5;
Std_ReturnType Can_Write_Std_ReturnType6;
Std_ReturnType Can_Write_Std_ReturnType7;

uint32 tick_ctr = 0;
uint32 flag = false;
boolean send_flag = true;

uint32 Can_SetControllerMode_Flag = 0;
Can_ControllerStateType Can_ControllerStateType_Can0;

void Can_SysTick_Handler(void);
void canif_task_handler(void) {
    // PduInfoType pduInfor;
    // uint8 data[64u] = {0x00u, 0x01u, 0x02u, 0, 0x03u, 0x04u};
    // pduInfor.SduLength = 64u;
    // // CanTxMsgGetData(PDUR_CANIF_CD_1_0X3BF, data); //FDJ
    // pduInfor.SduDataPtr = &data[0];
    // /* PduR Id */
    // CanIf_Transmit(PDUR_CANIF_CD_1_0X3BF, &pduInfor);
    flag = true;
    //Can_SysTick_Handler();
}

void Can_SysTick_Handler(void)
{
    tick_ctr++;

    if (flag == true)
    {
        /* Period 1ms */

        uint8 data[64] = {0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x9};
        Can_PduType_t pdu;
        pdu.id = 0x123;
        pdu.length = 8;
        pdu.sdu = &data[0];
        pdu.swPduHandle = 1;
        Can_Write_Std_ReturnType4 = Can_Write(CanConf_CanHardwareObject_0_CanHardwareObject_Transmit, &pdu);

        if (Can_SetControllerMode_Flag == 1)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_SLEEP);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 2)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 3)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STOPPED);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 4)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, 4);
            Can_SetControllerMode_Flag = 0;
        }
        Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
    }
}

void Enable_ISR(IRQn_Type source)
{
    uint32_t irq_prio = 0xE0 >> (8U - __NVIC_PRIO_BITS);
    NVIC_SetPriority(source, irq_prio);
    NVIC_ClearPendingIRQ(source);
    NVIC_EnableIRQ(source);
}

int xmen_init(void)
{
    /*----------------------------lin start----------------------------*/
    Com_IpduGroupVector ipduGroupVector = {0};
    Com_IpduGroupIdType com_RxPduGroup_LinController_0 = ComIPduGroup_DSCU_Rx ;
    Com_IpduGroupIdType com_TxPduGroup_LinController_0 = ComIPduGroup_DSCU_Tx ;

    LinIf_Init(&LinIf_PCConfig);
    LinSM_Init(&LinSM_PCConfig);
    LinTp_Init(&LinTp_PCConfig);
    PduR_Init(&PduR_PBConfigData);
    Com_Init(&Com_PBConfigData);

    Com_SetIpduGroup(ipduGroupVector, com_RxPduGroup_LinController_0, TRUE);
    Com_SetIpduGroup(ipduGroupVector, com_TxPduGroup_LinController_0, TRUE);
    Com_ReceptionDMControl(ipduGroupVector);
    Com_IpduGroupControl(ipduGroupVector, TRUE);

    ComM_Init(&ComM_Config);
    ComM_RequestComMode(ComM_Config.userCfgPtr->userId, COMM_FULL_COMMUNICATION);
    //ComM_CommunicationAllowed(ComM_Config.chCfgPtr->inerChIdx,TRUE); //bswm module need this
    //Dcm_Init(&Dcm_Cfg);
    //Dem_PreInit();
    //Dem_Init(&DemPbCfg);
    /*----------------------------lin end----------------------------*/

    /*
    CanIf_Init(&CanIf_InitCfgSet);
    Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
    // CanIf_SetControllerMode( 0, CANIF_CS_STARTED );
    CanIf_SetPduMode( 0, CANIF_ONLINE );

    // CanSM_Init(&CanSM_Config);
    PduR_Init(&PduR_PBConfigData);
    Com_Init(&Com_PBConfigData);
    // Init Com group
    Com_IpduGroupVector ipduGroupVector={0};
    Com_SetIpduGroup(ipduGroupVector, Com_RxPduGroup_CanController_0, TRUE);
    Com_SetIpduGroup(ipduGroupVector, Com_TxPduGroup_CanController_0, TRUE);
    Com_ReceptionDMControl(ipduGroupVector);
    Com_IpduGroupControl(ipduGroupVector,TRUE);

    // ComM init
    ComM_Init(&ComM_Config);
    ComM_RequestComMode(0, COMM_FULL_COMMUNICATION);
    // ComM_CommunicationAllowed(ComMUser_0, TRUE);

    // RttLogPrintf("BswInit: Com Init end\n");

    CanTp_Init(&CanTp_Config);
    Dcm_Init(&Dcm_Cfg);
    Dem_PreInit();
    Dem_Init(&DemPbCfg);
    */
    return 0;
}

int mcal_dio_init(void)
{
    Dio_WriteChannel(DioConf_DioChannel_PTE15_Motor_PWR_EN, STD_HIGH);
    // 等待12V电容充电；否则电驱的初始化时序会有问题
    for(int i=0;i<(150000);i++)__NOP();

    //Dio_WriteChannel(DioConf_DioChannel_PTE16_MCU_HOLD, STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PTE13_CAN_EN, STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PTD0_CAN_STB, STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PTD9_LIN_SLP, STD_HIGH);

    Dio_WriteChannel(DioConf_DioChannel_PTE14_DRV8145_NSLEEP_1,  STD_LOW);
#ifdef SEAT_SIDE_RIGHT
    Dio_WriteChannel(DioConf_DioChannel_PTC2_DRV8145_NSLEEP_2,  STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTD8_DRV8145_NSLEEP_3,  STD_LOW);
#endif
    Dio_WriteChannel(DioConf_DioChannel_PB12_DRV8145_NSLEEP_4,  STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PB8_DRV8145_NSLEEP_5,  STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTC10_DRV8145_NSLEEP_6,  STD_LOW);
    for(int i=0;i<(200);i++)__NOP();
    Dio_WriteChannel(DioConf_DioChannel_PTE14_DRV8145_NSLEEP_1,  STD_HIGH);
#ifdef SEAT_SIDE_RIGHT
    Dio_WriteChannel(DioConf_DioChannel_PTC2_DRV8145_NSLEEP_2,  STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PTD8_DRV8145_NSLEEP_3,  STD_HIGH);
#endif
    Dio_WriteChannel(DioConf_DioChannel_PB12_DRV8145_NSLEEP_4,  STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PB8_DRV8145_NSLEEP_5,  STD_HIGH);
    Dio_WriteChannel(DioConf_DioChannel_PTC10_DRV8145_NSLEEP_6,  STD_HIGH);
    for(int i=0;i<(80*60);i++);

    Dio_WriteChannel(DioConf_DioChannel_PTE3_DRV8145_DRVOFF_1, STD_LOW);
#ifdef SEAT_SIDE_RIGHT
    Dio_WriteChannel(DioConf_DioChannel_PTD7_DRV8145_DRVOFF_2, STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTA17_DRV8145_DRVOFF_3, STD_LOW);
#endif
    Dio_WriteChannel(DioConf_DioChannel_PTD4_DRV8145_DRVOFF_4, STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTA1_DRV8145_DRVOFF_5, STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTC11_DRV8145_DRVOFF_6, STD_LOW);
    for(int i=0;i<800*100;i++);
    
    Dio_WriteChannel(DioConf_DioChannel_PTE1_UART0_EN, STD_HIGH);

    return 0;
}

int mcal_pwm_init(void)
{
    /*Pwm initialization*/

    Pwm_Init(&Pwm_Config);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B,0x0000);

#ifdef SEAT_SIDE_RIGHT
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_B,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_B,0x0000);
#endif
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_B,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_B,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_A,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_B,0x0000);
    
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Fan1,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Fan2,0x0000);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Heat1,0);
    Pwm_SetDutyCycle(PwmConf_PwmChannel_PwmChannel_Heat2,0);
    Dio_WriteChannel(DioConf_DioChannel_PTA8_VB_12V_EN, STD_LOW);
    Dio_WriteChannel(DioConf_DioChannel_PTA9_AIR_12V_EN, STD_LOW);

    return 0;
}

int mcal_adc_init(void)
{
    Enable_ISR(ADC0_IRQn);
    Enable_ISR(ADC1_IRQn);
    Enable_ISR(PDB0_IRQn);
    Enable_ISR(PDB1_IRQn);
    Enable_ISR(DMA_CH0_IRQn);
    Enable_ISR(DMA_CH1_IRQn);

    Dma_Init(&Dma_Config);
    Adc_Init(&Adc_Config);
    Adc_EnableGroupNotification(Adc0_HardOneShot);
    Adc_EnableGroupNotification(Adc1_HardOneShot);
    DMA_SetDmaChannelCommand(0u, DMA_CH_START_SERVICE);
    DMA_SetDmaChannelCommand(1u, DMA_CH_START_SERVICE);
    (void)Adc_SetupResultBuffer(Adc0_HardOneShot, &Adc0_SoftOneShotResultBuff_temp[0]);
    (void)Adc_SetupResultBuffer(Adc1_HardOneShot, &Adc1_SoftOneShotResultBuff_temp[0]);
    Adc_EnableHardwareTrigger(Adc0_HardOneShot);
    Adc_EnableHardwareTrigger(Adc1_HardOneShot);
}

int mcal_icu_init(void)
{   
    Icu_Init(&Icu_Config);
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL1);
#ifdef SEAT_SIDE_RIGHT
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL2);
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL3);
#endif
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL4);
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL5);
    Icu_StartSignalMeasurement(IcuConf_IcuChannel_IcuChannel_HALL6);
}

int mcal_gpt_init(void)
{
    Enable_ISR(PIT_CH0_IRQn);

    Gpt_Init(&Gpt_Config);
    Gpt_EnableNotification(GptConf_GptChannelConfiguration_GptChannelConfiguration_0);
    Gpt_StartTimer(GptConf_GptChannelConfiguration_GptChannelConfiguration_0, 400000u);//产生5ms（400000u/80M）周期中断
}

int mcal_fls_init(void)
{
    Fls_Init(NULL_PTR);
}

int mcal_uart_init(void)
{   
    Enable_ISR(UART0_TX_IRQn);
    Enable_ISR(UART0_RX_IRQn);
    Enable_ISR(UART0_ERR_IRQn);
    Enable_ISR(UART0_TIMEOUT_IRQn);
    Enable_ISR(UART0_WAKE_IRQn);
    Uart_Init(&Uart_xConfig);
}

int mcal_fee_init(void)
{
    Fee_Init(NULL_PTR);
    
    /*Perform init Fee driver*/
    MemIf_StatusType status = MEMIF_IDLE;
    do
    {
        Fls_MainFunction();
        Fee_MainFunction();
        status = Fee_GetStatus();
    } while (status != MEMIF_IDLE);
}

int mcal_init(void)
{
    Mcu_Init(&Mcu_Config);
    Mcu_SetMode(McuModeSettingConf_0);
    Mcu_InitClock(McuClockSettingConfig_0);
    while ( MCU_PLL_LOCKED != Mcu_GetPllStatus() )
    {
        /* Busy wait until the System PLL is locked */
    }
    Mcu_DistributePllClock();

    Port_Init(&Port_Config);

    mcal_dio_init();
    mcal_pwm_init();
    mcal_adc_init();
    mcal_icu_init();
    mcal_gpt_init();
    mcal_fls_init();
    mcal_uart_init();
    mcal_fee_init();
    
    return 0;
}


int mcal_can_init(void)
{
    Enable_ISR(CAN0_LINE0_IRQn);    /* 39 CAN0_INT0_IRQHandler  CAN0 */
    Enable_ISR(CAN0_LINE1_IRQn);    /* 40 CAN0_INT1_IRQHandler  CAN0 */
    Enable_ISR(CAN0_DMU_IRQn);      /* 41 CAN0_DMU_IRQHandler   CAN0 */

    Can_Init(&CanConfigSet_0);

    Can_DeInit();

    Can_Init(&CanConfigSet_0);

    Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
    return 0;
}

int mcal_lin_init(void)
{
    Enable_ISR(LIN1_IRQn);
    Enable_ISR(LIN2_IRQn);

    Lin_Init(&Lin_Config);

    (void)Lin_WakeupInternal(LIN_CHANNEL_ID_0);
    (void)Lin_CheckWakeup(LIN_CHANNEL_ID_0);
    //(void)Lin_WakeupInternal(LIN_CHANNEL_ID_1);
    //(void)Lin_CheckWakeup(LIN_CHANNEL_ID_1);
    return 0;
}

extern void vTaskStartScheduler(void);

void Dma_Adc0_CallBack(uint32 val)
{
  DMA_SetDmaChannelCommand(0u, DMA_CH_START_SERVICE);
  uint32 adc0Val[ADC_CFGSET_GROUP_0_CHANNELS];
  for(int i=0; i<ADC_CFGSET_GROUP_0_CHANNELS; i++)
  {
    adc0Val[i] = Adc0_SoftOneShotResultBuff[i];
  }
  accVol10x = adc0Val[0]*VOL_ADC_FACTOR;

  motors[MOTOR_1_BACK_REST].driver.ntc = adc0Val[1];
  motors[MOTOR_1_BACK_REST].driver.temp = AdcToTemperature(adc0Val[1]);
#ifdef SEAT_SIDE_RIGHT
  motors[MOTOR_2_SEAT_LIFT].driver.ntc = adc0Val[2];
  motors[MOTOR_2_SEAT_LIFT].driver.temp = AdcToTemperature(adc0Val[2]);
  motors[MOTOR_3_SEAT_SLIDE].driver.ntc = adc0Val[3];
  motors[MOTOR_3_SEAT_SLIDE].driver.temp = AdcToTemperature(adc0Val[3]);
#endif
  motors[MOTOR_4_FRONT_BACK].driver.ntc = adc0Val[4];
  motors[MOTOR_4_FRONT_BACK].driver.temp = AdcToTemperature(adc0Val[4]);
  motors[MOTOR_5_LEG_LIFT].driver.ntc = adc0Val[5];
  motors[MOTOR_5_LEG_LIFT].driver.temp = AdcToTemperature(adc0Val[5]);
  motors[MOTOR_6_LEG_STRETCH].driver.ntc = adc0Val[6];
  motors[MOTOR_6_LEG_STRETCH].driver.temp = AdcToTemperature(adc0Val[6]);

  motors[MOTOR_1_BACK_REST].driver.current_feedback = adc0Val[7];

#ifdef SEAT_SIDE_RIGHT
  motors[MOTOR_2_SEAT_LIFT].driver.current_feedback = adc0Val[8];
#endif

    seatTemp = AdcToTemperature(adc0Val[9]);
}

void Dma_Adc1_CallBack(uint32 val)
{
  DMA_SetDmaChannelCommand(1u, DMA_CH_START_SERVICE);
  uint32 adc1Val[ADC_CFGSET_GROUP_1_CHANNELS];
  for(int i=0; i<ADC_CFGSET_GROUP_1_CHANNELS; i++)
  {
    adc1Val[i] = Adc1_SoftOneShotResultBuff[i];
  }

#ifdef SEAT_SIDE_RIGHT
  motors[MOTOR_3_SEAT_SLIDE].driver.current_feedback = adc1Val[5];
#endif
  motors[MOTOR_4_FRONT_BACK].driver.current_feedback = adc1Val[6];
  motors[MOTOR_5_LEG_LIFT].driver.current_feedback = adc1Val[7];
  motors[MOTOR_6_LEG_STRETCH].driver.current_feedback = adc1Val[8];

  Key_ProcessHandle(&adc1Val[0]);
}

void dwt_init(void)
{
    //使能DWT计数器
    CoreDebug->DEMCR |= CoreDebug_DEMCR_TRCENA_Msk; // Enable the trace in core debug
    DWT->CYCCNT = 0; // Reset the counter
    DWT->CTRL |= DWT_CTRL_CYCCNTENA_Msk; // Enable the counter 
}

int main(void)
{
    RttLogInit();
    

    mcal_init();
    mcal_can_init();
    mcal_lin_init();

    dwt_init();
    xmen_init();
    motors_init();

    __enable_irq();

    // os_start should be the last statement
    os_start();
    return 0;
}


#ifdef __cplusplus
}
#endif

/** @} */
