/**
 * @file     Lin_PBcfg.h
 * @brief    Lin_PBcfg Software h files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 **/

#ifndef LIN_PBCFG_H
#define LIN_PBCFG_H



/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Lin_Types.h"


#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LIN_PBCFG_VENDOR_ID                     (110u)
#define LIN_PBCFG_AR_RELEASE_MAJOR_VERSION      (4u)
#define LIN_PBCFG_AR_RELEASE_MINOR_VERSION      (4u)
#define LIN_PBCFG_AR_RELEASE_REVISION_VERSION   (0u)
#define LIN_PBCFG_SW_MAJOR_VERSION              (1u)
#define LIN_PBCFG_SW_MINOR_VERSION              (0u)
#define LIN_PBCFG_SW_PATCH_VERSION              (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/


/* Checks against Lin_Types.h */
#if (LIN_PBCFG_VENDOR_ID != LIN_TYPES_VENDOR_ID)
    #error "Lin_PBcfg.h and Lin_Types.h have different vendor ids"
#endif
#if ((LIN_PBCFG_AR_RELEASE_MAJOR_VERSION   != LIN_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_MINOR_VERSION   != LIN_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_REVISION_VERSION!= LIN_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_PBcfg.h and Lin_Types.h are different"
#endif
#if ((LIN_PBCFG_SW_MAJOR_VERSION!= LIN_TYPES_SW_MAJOR_VERSION) || \
     (LIN_PBCFG_SW_MINOR_VERSION!= LIN_TYPES_SW_MINOR_VERSION) || \
     (LIN_PBCFG_SW_PATCH_VERSION!= LIN_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lin_PBcfg.h and Lin_Types.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define LIN_CONFIG_PB \
extern const Lin_ConfigType Lin_Config;
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_PBCFG_H */

