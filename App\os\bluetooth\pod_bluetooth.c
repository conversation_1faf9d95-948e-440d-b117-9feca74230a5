#include "os_pod.h"
#include "pod_bluetooth.h"
/// ---------- begin of guard: <includes> -------------------------------------
#include "log.h"
#include "types.h"
#include "Uart.h"
/// ---------- end of guard:   <includes> -------------------------------------

/// ---------- begin of guard: <header> ---------------------------------------
int counter=0;
uint8 readBuff[20];
uint8 asyncReadFlag=true;
uint8 btCmdLength=15;

// 从 str中提取数字，以 delimiter 为分隔符，最多查找 maxFindLen 位，结果存入 result 中
// 返回值：返回数字的长度，如果长度超过 maxFindLen，则返回 -1
static int str2int(char *str,char delimiter,int maxFindLen,int* result){
    *result=0;
    int i=0;
    while(*str!=delimiter){
        *result=(*result)*10+(*str-'0');
        i++;
        str++;
        if(i>=maxFindLen){
            return -1;
        }
    }
    return i;
}

void BluetoothUartCallback(uint8 HwInstance, Uart_Event_t Event){
    int key;
    int value;
    int index;
    if(Event==UART_EVENT_END_TRANSFER){
        log_printf_blocking("receive cmd %s",readBuff);
        //没有regex工具，简单的检查一下
        if(strncmp((char*)readBuff,"key",3)==0){
            index=str2int((char*)readBuff+3,':',3,&key);
            index=str2int((char*)readBuff+3+index+1,'\n',3,&value);
            log_printf_blocking("key:%d,value:%d\n",key,value);
            os_submit_event_u16_from_isr(EV(Cmd, BluetoothEV), key, value);
        }else{
            log_printf_blocking("format invalid\n");
        }
        asyncReadFlag=true;
    }
}
/// ---------- end of guard:   <header> ---------------------------------------

void pod_bluetooth_on_init(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_bluetooth_on_init> -----------------------------------------
    log_printf_blocking("os bluetooth root entry\n");
    os_timer_start(TmBluetooth10ms);
    memset(readBuff,0xff,sizeof(readBuff));
/// ---------- end of guard:   <pod_bluetooth_on_init> -----------------------------------------
}

void pod_bluetooth_on_tm10ms(tHsmGenCurr* const ev)
{
/// ---------- begin of guard: <pod_bluetooth_on_tm10ms> -----------------------------------------
    if(counter++>=100){
        counter=0;
        //Uart_SyncSend(0,"key1:0\n",6,timeout);
    }

    if(asyncReadFlag){
        if(E_OK==Uart_AsyncReceive(0,readBuff,btCmdLength)){
            asyncReadFlag=false;
        }
    }
/// ---------- end of guard:   <pod_bluetooth_on_tm10ms> -----------------------------------------
}


/// ---------- begin of guard: <tail> -----------------------------------------
/// ---------- end of guard:   <tail> -----------------------------------------