/**
 * file    Dma_Cfg.c
 * brief   dma cfg file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Cfg.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
* @page misra_violations MISRA-C:2012 violations
**/

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/* @section [global]
 * 0791 ++ 
 * #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct 
 */
#define DMA_CFG_VENDOR_ID_C                     (110U)
#define DMA_CFG_AR_RELEASE_MAJOR_VERSION_C      (4U)
#define DMA_CFG_AR_RELEASE_MINOR_VERSION_C      (4U)
#define DMA_CFG_AR_RELEASE_REVISION_VERSION_C   (0U)
#define DMA_CFG_SW_MAJOR_VERSION_C              (1U)
#define DMA_CFG_SW_MINOR_VERSION_C              (0U)
#define DMA_CFG_SW_PATCH_VERSION_C              (0U)
/* 0791 -- */
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if Dma_Cfg.c file and Dma_Cfg.h file are of the same vendor */
#if (DMA_CFG_VENDOR_ID_C != DMA_CFG_VENDOR_ID)
    #error "Dma_Cfg.c and Dma_Cfg.h have different vendor ids"
#endif

/* Check if Dma_Cfg.c file and Dma_Cfg.h file are of the same Autosar version */
#if ((DMA_CFG_AR_RELEASE_MAJOR_VERSION_C != DMA_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_MINOR_VERSION_C != DMA_CFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_REVISION_VERSION_C != DMA_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Cfg.c and Dma_Cfg.h are different"
#endif

/* Check if Dma_Cfg.c file and Dma_Cfg.h file are of the same Software version */
#if ((DMA_CFG_SW_MAJOR_VERSION_C != DMA_CFG_SW_MAJOR_VERSION) || \
     (DMA_CFG_SW_MINOR_VERSION_C != DMA_CFG_SW_MINOR_VERSION) || \
     (DMA_CFG_SW_PATCH_VERSION_C != DMA_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Cfg.c and Dma_Cfg.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                      LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
