/**
 * *************************************************************************
 * @file   Power_Hw_Cfg_Defines.h
 * @brief  Code template for Post-Build(PB) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
 
#ifndef POWER_HW_CFG_DEFINES_H
#define POWER_HW_CFG_DEFINES_H

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define POWER_HW_CFG_DEFINES_VENDOR_ID                        (110U)
#define POWER_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION         (4U)
#define POWER_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION         (4U)
#define POWER_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION      (0U)
#define POWER_HW_CFG_DEFINES_SW_MAJOR_VERSION                 (1U)
#define POWER_HW_CFG_DEFINES_SW_MINOR_VERSION                 (0U)
#define POWER_HW_CFG_DEFINES_SW_PATCH_VERSION                 (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
                                           DEFINES AND MACROS
==================================================================================================*/
/**
* @brief            Derivative used.
*/
#define POWER_HW_LSE14XX


#define POWER_HW_HSRUN_SUPPORT    STD_OFF


/**
* @brief            Support for Low Power mode.
*/
#define POWER_HW_ENTER_LOW_POWER_MODE   (STD_OFF)


/**
* @brief
*/
#define POWER_HW_PERFORM_RESET_API   (STD_ON)


/**
* @brief            ISR Mcu_ResetAlternate_ISR is/isn't available (STD_ON/STD_OFF)
*/
#define POWER_HW_RESET_ALTERNATE_ISR_USED            (STD_OFF)


/**
* @brief            ISR Mcu_PMC_UnderOverVoltage_ISR is/isn't available (STD_ON/STD_OFF)
*/
#define POWER_HW_VOLTAGE_ERROR_ISR_USED              (STD_OFF)

/**
* @brief            Enable the usage of Non-Autosar Peripheral CMU.
*/
#define MCU_ENABLE_CMU_PERIPHERAL    (STD_OFF)

/**
* @brief            This parameter shall be set True, if the H/W does not have a PLL or the PLL circuitry is enabled after the power on without S/W intervention.
*/
#define POWER_HW_NO_PLL   (STD_OFF)


/**
* @brief           This define controls the availability of function Mcu_SleepOnExit
*/
#define POWER_HW_SLEEPONEXIT_SUPPORT    (STD_ON)


/**
* @brief            Low Voltage Detect Status and Control 1 Register support (PMC_LVDSC1).
*/
#define POWER_HW_PMC_LVDSC1_SUPPORT     (STD_ON)

/**
* @brief            PMC_AE Configuration Register (CONFIG) and PMC_AE Monitor Register (MONITOR).
*/
#define POWER_HW_PMC_AE_SUPPORT     (STD_OFF)

/**
* @brief            AEC Configuration Register (RSTGEN_CFG).
*/
#define POWER_HW_AEC_SUPPORT     (STD_OFF)

/**
* @brief            Enable the usage of Non-Autosar API Mcu_GetPowerMode_State() for getting infos system platform configuration.
*/
#define POWER_HW_POWERMODE_STATE_API   (STD_OFF)

/**
* @brief            If this parameter is set to TRUE, the System Mode Controller (SMC) initialization has to be disabled in the MCU driver.
*/
#define POWER_MODE_CHANGE_NOTIFICATION   (STD_OFF)

/**
* @brief            Enable the usage of Non-Autosar API  Power_Hw_SRAMRetentionConfig().
*/
#define POWER_HW_SRAM_RETEN_CONFIG_API    (STD_OFF)

/**
* @brief            Enable the usage of Non-Autosar API  Power_Hw_PmcAeConfig().
*/
#define POWER_HW_PMCAECONFIG_API     (STD_OFF)

/**
* @brief            Enable the usage of Non-Autosar API  Power_Hw_AecResetConfig().
*/

#define POWER_HW_AECRESETCONFIG_API     (STD_OFF)



/**
* @brief            If this parameter is set to TRUE, the Reset Control Module (RMC) initialization has to be disabled from the MCU driver.
*/
#define POWER_HW_DISABLE_RCM_INIT   (STD_OFF)

/**
* @brief            If this parameter is set to TRUE, the Power Management Controller (PMC) initialization has to be disabled from the MCU driver.
*/
#define POWER_HW_DISABLE_PMC_INIT      (STD_OFF)

/**
* @brief            If this parameter is set to TRUE, the System Mode Controller (SMC) initialization has to be disabled from the MCU driver.
*/
#define POWER_HW_DISABLE_SMC_INIT   (STD_OFF)
/*==================================================================================================
                                             ENUMS
==================================================================================================*/
/**
* @brief            Supported software RESET name.
*/
#define POWER_HW_HAS_RST_PWM01         1U
#define POWER_HW_HAS_RST_PWM23         2U
#define POWER_HW_HAS_RST_SPI0         3U
#define POWER_HW_HAS_RST_SPI1         4U
#define POWER_HW_HAS_RST_SPI2         5U
#define POWER_HW_HAS_RST_UART0         6U
#define POWER_HW_HAS_RST_UART1         7U
#define POWER_HW_HAS_RST_RTC         8U
#define POWER_HW_HAS_RST_PIT         9U
#define POWER_HW_HAS_RST_STM         10U
#define POWER_HW_HAS_RST_EWM         11U
#define POWER_HW_HAS_RST_ADC0         12U
#define POWER_HW_HAS_RST_ADC1         13U
#define POWER_HW_HAS_RST_CMP         14U
#define POWER_HW_HAS_RST_PDB0         15U
#define POWER_HW_HAS_RST_PDB1         16U
#define POWER_HW_HAS_RST_CAN         17U
#define POWER_HW_HAS_RST_GPIO         18U
#define POWER_HW_HAS_RST_IO         19U
#define POWER_HW_HAS_RST_UART2         20U
#define POWER_HW_HAS_RST_CRC         21U
#define POWER_HW_HAS_RST_SENSOR         22U
#define POWER_HW_HAS_RST_FLEXIO         23U
#define POWER_HW_HAS_RST_I2C0         24U
#define POWER_HW_HAS_RST_I2C1         25U
#define POWER_HW_HAS_RST_LIN0         26U
#define POWER_HW_HAS_RST_LIN1         27U
#define POWER_HW_HAS_RST_LIN2         28U
#define POWER_HW_HAS_RST_LIN3         29U
#define POWER_HW_HAS_RST_TRGMUX         30U
#define POWER_HW_HAS_RST_WDT         31U
#define POWER_HW_HAS_RST_WKTM         32U
#define POWER_HW_RST_FEATURE_PRODUCERS_NO         33U

/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */
#endif /* #ifndef POWER_HW_CFG_DEFINES_H */


