#ifndef LPUART_UART_HW_DEFINES_H
#define LPUART_UART_HW_DEFINES_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LPUART_UART_HW_DEFINES_VENDOR_ID                     (110u)
#define LPUART_UART_HW_DEFINES_AR_RELEASE_MAJOR_VERSION      (4u)
#define LPUART_UART_HW_DEFINES_AR_RELEASE_MINOR_VERSION      (4u)
#define LPUART_UART_HW_DEFINES_AR_RELEASE_REVISION_VERSION   (0u)
#define LPUART_UART_HW_DEFINES_SW_MAJOR_VERSION              (1u)
#define LPUART_UART_HW_DEFINES_SW_MINOR_VERSION              (0u)
#define LPUART_UART_HW_DEFINES_SW_PATCH_VERSION              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((LPUART_UART_HW_DEFINES_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (LPUART_UART_HW_DEFINES_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of Lpuart_Uart_Hw_CfgDefines.h and Mcal.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/


/* @brief Development error detection */
/* [cover SWSID=SWS_Uart_00100] Disabling functionality by configuration should always be possible in pre-compile variant only. */
#define LPUART_UART_HW_DEV_ERROR_DETECT             (STD_OFF)

/* @brief Total number of channels configured for Lpuart*/
#define LPUART_UART_HW_NUMBER_OF_INSTANCES          (1U)

/* @brief Uart Osif source counter. This parameter is used to select between different OsIf counter implementation */
#define LPUART_UART_HW_TIMEOUT_TYPE                 (OSIF_COUNTER_DUMMY)

/* @brief Number of loops before returning LPUART_STATUS_TIMEOUT.*/
#define LPUART_UART_HW_TIMEOUT_VALUE_US             (1000U)

/* @brief LPUART DMA support */
/* [cover SWSID=SWS_Uart_00100] Disabling functionality by configuration should always be possible in pre-compile variant only. */
#define LPUART_UART_HW_HAS_DMA_ENABLED              (STD_ON)

/* @brief Support for User mode. If this parameter has been configured to TRUE, the Uart driver can be executed from both supervisor and user mode. */
#define LPUART_UART_HW_ENABLE_USER_MODE_SUPPORT     (STD_OFF)



/* @brief Support for Internal Loopback. If this parameter has been configured to TRUE, the Uart driver will be executed in Loopback mode. */
#define LPUART_UART_HW_ENABLE_INTERNAL_LOOPBACK  (STD_OFF)

#if (STD_ON == LPUART_UART_HW_ENABLE_INTERNAL_LOOPBACK)
    /* @brief Array of instances that have loopback mode enabled. */
    #define LPUART_UART_HW_ENABLE_INTERNAL_LOOPBACK_PER_INSTANCE  {(boolean) FALSE,(boolean) FALSE,(boolean) FALSE }
#endif

/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LPUART_UART_HW_DEFINES_H */
