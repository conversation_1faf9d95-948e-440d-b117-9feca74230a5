/**
 * *************************************************************************
 * @file   Ram_Hw_Cfg.c
 * @brief  Code template for Post-Compile(PC) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/
 
/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Ram_Hw_Cfg.h"
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RAM_HW_CFG_VENDOR_ID_C                         (110U)
#define RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION_C          (4U)
#define RAM_HW_CFG_AR_RELEASE_MINOR_VERSION_C          (4U)
#define RAM_HW_CFG_AR_RELEASE_REVISION_VERSION_C       (0U)
#define RAM_HW_CFG_SW_MAJOR_VERSION_C                  (1U)
#define RAM_HW_CFG_SW_MINOR_VERSION_C                  (0U)
#define RAM_HW_CFG_SW_PATCH_VERSION_C                  (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Ram_Hw_Cfg.c file and Ram_Hw_Cfg.h file are of the same vendor */
#if (RAM_HW_CFG_VENDOR_ID_C != RAM_HW_CFG_VENDOR_ID)
    #error "Ram_Hw_Cfg.c and Ram_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Ram_Hw_Cfg.c file and Ram_Hw_Cfg.h file are of the same Autosar version */
#if ((RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (RAM_HW_CFG_AR_RELEASE_MINOR_VERSION_C != RAM_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (RAM_HW_CFG_AR_RELEASE_REVISION_VERSION_C != RAM_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Ram_Hw_Cfg.c and Ram_Hw_Cfg.h are different"
#endif

/* Check if Ram_Hw_Cfg.c file and Ram_Hw_Cfg.h file are of the same Software version */
#if ((RAM_HW_CFG_SW_MAJOR_VERSION_C != RAM_HW_CFG_SW_MAJOR_VERSION) || \
     (RAM_HW_CFG_SW_MINOR_VERSION_C != RAM_HW_CFG_SW_MINOR_VERSION) || \
     (RAM_HW_CFG_SW_PATCH_VERSION_C != RAM_HW_CFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Ram_Hw_Cfg.c and Ram_Hw_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Ram_Hw_Cfg.c file and Std_Types.h file are of the same Autosar version */
#if ((RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION_C    != STD_AR_RELEASE_MAJOR_VERSION) || \
     (RAM_HW_CFG_AR_RELEASE_MINOR_VERSION_C    != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Ram_Hw_Cfg.c and Std_Types.h are different"
#endif
#endif
/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

