<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Pwm</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Pwm</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/LS_LSE14M01I0R0/Pwm</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-POST-BUILD</IMPLEMENTATION-CONFIG-VARIANT>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>121</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>110</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>PwmConfigSet</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmMaxChannel</DEFINITION-REF>
                  <VALUE>16</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Fan1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>12</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_Fan/PwmGpcmChannels_Fan_A_PTA3_PWM3_CH1</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Fan2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>13</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_Fan/PwmGpcmChannels_Fan_B_PTA2_PWM3_CH0</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Heat1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>14</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_Heat/PwmGpcmChannels_Heat_A_PTE11_PWM2_CH5</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Heat2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>15</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_Heat/PwmGpcmChannels_Heat_B_PTE10_PWM2_CH4</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor1_BackRest_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_0/PwmGpcmCh0_A_PTD16_PWM0_CH1</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor1_BackRest_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_0/PwmGpcmCh0_B_PTD15_PWM0_CH0</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor2_SeatLift_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_1/PwmGpcmCh1_A_PTE9_PWM0_CH7</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor2_SeatLift_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>3</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_1/PwmGpcmCh1_B_PTE8_PWM0_CH6</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor3_SeatSlide_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>4</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_2/PwmGpcmCh2_A_PTB5_PWM0_CH5</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor3_SeatSlide_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>5</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_2/PwmGpcmCh2_B_PTB4_PWM0_CH4</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor4_FrontBack_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>6</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_3/PwmGpcmCh8_A_PTD5_PWM2_CH3</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor4_FrontBack_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>7</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_3/PwmGpcmCh8_B_PTD12_PWM2_CH2</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor5_LegLift_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>8</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_4/PwmGpcmCh9_A_PTD11_PWM2_CH1</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor5_LegLift_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>9</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_4/PwmGpcmCh9_B_PTD10_PWM2_CH0</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor6_LegStretch_A</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>10</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_5/PwmGpcmCh12_A_PTD3_PWM3_CH5</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannel_Motor6_LegStretch_B</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelClass</DEFINITION-REF>
                      <VALUE>PWM_VARIABLE_PERIOD</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelId</DEFINITION-REF>
                      <VALUE>11</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmDutycycleDefault</DEFINITION-REF>
                      <VALUE>16384</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmIdleState</DEFINITION-REF>
                      <VALUE>PWM_LOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-FLOAT-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodDefault</DEFINITION-REF>
                      <VALUE>1.0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPeriodInTicks</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmPolarity</DEFINITION-REF>
                      <VALUE>PWM_HIGH</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <REFERENCE-VALUES>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-CHOICE-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmChannelRef</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Pwm/Pwm/PwmConfigSet/PwmChannels_5/PwmGpcmCh12_B_PTD2_PWM3_CH4</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                    <ECUC-REFERENCE-VALUE>
                      <DEFINITION-REF DEST="ECUC-REFERENCE-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannel/PwmMcuClockReferencePoint</DEFINITION-REF>
                      <VALUE-REF DEST="ECUC-CONTAINER-VALUE">/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm</VALUE-REF>
                    </ECUC-REFERENCE-VALUE>
                  </REFERENCE-VALUES>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_0</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh0_A_PTD16_PWM0_CH1</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD16</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh0_B_PTD15_PWM0_CH0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD15</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_1</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh1_A_PTE9_PWM0_CH7</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTE9</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH7</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh1_B_PTE8_PWM0_CH6</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTE8</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH6</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_2</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>2</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh2_A_PTB5_PWM0_CH5</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTB5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh2_B_PTB4_PWM0_CH4</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTB4</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM0_CH4</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_3</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>8</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh8_A_PTD5_PWM2_CH3</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH3</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh8_B_PTD12_PWM2_CH2</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD12</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH2</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_4</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>9</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh9_A_PTD11_PWM2_CH1</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD11</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh9_B_PTD10_PWM2_CH0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD10</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_5</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>12</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>500</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh12_A_PTD3_PWM3_CH5</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD3</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmCh12_B_PTD2_PWM3_CH4</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTD2</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH4</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_Fan</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>13</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmChannels_Fan_A_PTA3_PWM3_CH1</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTA3</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH1</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmChannels_Fan_B_PTA2_PWM3_CH0</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTA2</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM3_CH0</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>PwmChannels_Heat</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnaCmpvalue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmChnbCmpvalue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnaCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNA_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpChnbCtrl</DEFINITION-REF>
                      <VALUE>PWM_CMP_CHNB_ORIGINAL</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpLoad</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCmpMode</DEFINITION-REF>
                      <VALUE>PWM_CMP_INDEPENDENT</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmCountMode</DEFINITION-REF>
                      <VALUE>UP_CONTINUOUS</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmDieWidthValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExtClockSource</DEFINITION-REF>
                      <VALUE>EXT_CLK_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmExternalClockSelect</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmId</DEFINITION-REF>
                      <VALUE>10</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmInitCountValue</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPeriodCountValue</DEFINITION-REF>
                      <VALUE>100000</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrdLoadMode</DEFINITION-REF>
                      <VALUE>SHADOW</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmPrescaler</DEFINITION-REF>
                      <VALUE>PWM_CLK_DIV_8</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmChannels_Heat_A_PTE11_PWM2_CH5</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_A</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTE11</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH5</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>PwmGpcmChannels_Heat_B_PTE10_PWM2_CH4</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels</DEFINITION-REF>
                      <PARAMETER-VALUES>
                        <ECUC-NUMERICAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmFilterWidth</DEFINITION-REF>
                          <VALUE>0</VALUE>
                        </ECUC-NUMERICAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmGpcmChannel</DEFINITION-REF>
                          <VALUE>CH_B</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPad</DEFINITION-REF>
                          <VALUE>PTE10</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                        <ECUC-TEXTUAL-PARAM-VALUE>
                          <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmConfigSet/PwmChannels/PwmGpcmChannels/PwmPortMux</DEFINITION-REF>
                          <VALUE>PWM2_CH4</VALUE>
                        </ECUC-TEXTUAL-PARAM-VALUE>
                      </PARAMETER-VALUES>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>PwmGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmDevErrorDetect</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmEnableDualClockMode</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmEnableUserModeSupport</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmIndex</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmLowPowerStatesSupport</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmGeneral/PwmNotificationSupported</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>PwmOptionalApis</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmDeInitApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmDisableTrigger</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmEnableTrigger</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmGetChannelStateApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmGetOutputState</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmResetCounter</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmSetChannelDeadTime</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmSetDutyCycle</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmSetOutputToIdle</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmSetPeriodAndDuty</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Pwm/PwmOptionalApis/PwmVersionInfoApi</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
