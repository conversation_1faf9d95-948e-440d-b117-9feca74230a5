<?xml version="1.0" encoding="UTF-8"?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://autosar.org/schema/r4.0 autosar_4-2-2.xsd">
	<AR-PACKAGES>
		<AR-PACKAGE>
			<SHORT-NAME>AUTOSAR</SHORT-NAME>
			<AR-PACKAGES>
				<AR-PACKAGE UUID="34052513-f359-44b1-9364-31fd1c44cd42">
					<SHORT-NAME>StdTypes</SHORT-NAME>
					<ELEMENTS>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>SInt8</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/sint8</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt8_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>SInt16</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/sint16</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt16_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>SInt32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/sint32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="1f371ba3-895a-4adc-937e-de99882ff6f4">
							<SHORT-NAME>UInt16</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/uint16</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt16_ConStr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>UInt32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/uint32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>UInt64</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/uint64</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt64_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>Float32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/float32</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/Float32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>Float64</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/StdTypes/BaseTypes/float64</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/Float64_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_Boolean</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_SINT8</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt8_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_SINT16</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt16_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_SINT32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/SInt32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_UINT8</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt8_ConStr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_UINT16</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt16_ConStr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_UINT32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_Float32</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/Float32_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<APPLICATION-PRIMITIVE-DATA-TYPE>
							<SHORT-NAME>Appl_Float64</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/Float64_Constr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</APPLICATION-PRIMITIVE-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE UUID="336c6943-3c45-4195-9247-2b53ace85e8c">
							<SHORT-NAME>Boolean</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/SwBaseTypes/boolean</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
						<IMPLEMENTATION-DATA-TYPE>
							<SHORT-NAME>UInt8</SHORT-NAME>
							<CATEGORY>VALUE</CATEGORY>
							<SW-DATA-DEF-PROPS>
								<SW-DATA-DEF-PROPS-VARIANTS>
									<SW-DATA-DEF-PROPS-CONDITIONAL>
										<BASE-TYPE-REF DEST="SW-BASE-TYPE">/AUTOSAR/SwBaseTypes/uint8</BASE-TYPE-REF>
										<SW-CALIBRATION-ACCESS>NOT-ACCESSIBLE</SW-CALIBRATION-ACCESS>
										<DATA-CONSTR-REF DEST="DATA-CONSTR">/AUTOSAR/StdTypes/DataConstrs/UInt8_ConStr</DATA-CONSTR-REF>
									</SW-DATA-DEF-PROPS-CONDITIONAL>
								</SW-DATA-DEF-PROPS-VARIANTS>
							</SW-DATA-DEF-PROPS>
						</IMPLEMENTATION-DATA-TYPE>
					</ELEMENTS>
					<AR-PACKAGES>
						<AR-PACKAGE UUID="99219559-403e-451c-9cab-b98795374e41">
							<SHORT-NAME>BaseTypes</SHORT-NAME>
							<ELEMENTS>
								<SW-BASE-TYPE>
									<SHORT-NAME>sint8</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>sint8</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>sint16</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>16</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>sint16</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>sint32</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>VOID</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>sint32</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE UUID="2a80cacd-e086-414d-b759-e41f629eed44">
									<SHORT-NAME>uint16</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>16</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>uint16</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>uint32</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>uint32</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>uint64</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>uint64</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>float32</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>32</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>float32</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE>
									<SHORT-NAME>float64</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>64</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>float64</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE UUID="ea9afaaa-dd7b-4193-9278-a7a272921edf">
									<SHORT-NAME>uint8</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>8</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>NONE</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>uint8</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
								<SW-BASE-TYPE UUID="41a3c193-5372-43db-b9df-33823b187afa">
									<SHORT-NAME>boolean</SHORT-NAME>
									<CATEGORY>FIXED_LENGTH</CATEGORY>
									<BASE-TYPE-SIZE>1</BASE-TYPE-SIZE>
									<BASE-TYPE-ENCODING>BOOLEAN</BASE-TYPE-ENCODING>
									<NATIVE-DECLARATION>boolean</NATIVE-DECLARATION>
								</SW-BASE-TYPE>
							</ELEMENTS>
						</AR-PACKAGE>
						<AR-PACKAGE UUID="bb1be427-3c77-46d6-8fe0-914f083897c4">
							<SHORT-NAME>DataConstrs</SHORT-NAME>
							<ELEMENTS>
								<DATA-CONSTR>
									<SHORT-NAME>SInt8_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-128</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">127</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>SInt16_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-32768</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">32767</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>SInt32_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">-2147483648</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">2147483647</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR UUID="a86579e8-3932-48f6-b550-c106f379e64d">
									<SHORT-NAME>UInt16_ConStr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">65535</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>UInt32_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">4294967295</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>UInt64_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">18446744073709551615</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>Float32_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="INFINITE"/>
												<UPPER-LIMIT INTERVAL-TYPE="INFINITE"/>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR>
									<SHORT-NAME>Float64_Constr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="INFINITE"/>
												<UPPER-LIMIT INTERVAL-TYPE="INFINITE"/>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
								<DATA-CONSTR UUID="094ac182-6238-438d-8a6f-2de8dd8d1145">
									<SHORT-NAME>UInt8_ConStr</SHORT-NAME>
									<DATA-CONSTR-RULES>
										<DATA-CONSTR-RULE>
											<INTERNAL-CONSTRS>
												<LOWER-LIMIT INTERVAL-TYPE="CLOSED">0</LOWER-LIMIT>
												<UPPER-LIMIT INTERVAL-TYPE="CLOSED">255</UPPER-LIMIT>
											</INTERNAL-CONSTRS>
										</DATA-CONSTR-RULE>
									</DATA-CONSTR-RULES>
								</DATA-CONSTR>
							</ELEMENTS>
						</AR-PACKAGE>
					</AR-PACKAGES>
				</AR-PACKAGE>
				<AR-PACKAGE>
					<SHORT-NAME>ServiceComponents</SHORT-NAME>
				</AR-PACKAGE>
			</AR-PACKAGES>
		</AR-PACKAGE>
	</AR-PACKAGES>
</AUTOSAR>