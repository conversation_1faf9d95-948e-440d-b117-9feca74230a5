<?xml version="1.1" encoding="ASCII"?>
<cn.com.isoft.mal.model:BswBuilderModel xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:cn.com.isoft.mal.model="http:///cn/com/isoft/mal/model.ecore" name="BSW_Builder" projectName="AH8">
  <ecuConfigurationModels pathValue="BSW_Builder\AH8\ecuconfig.arxml" projectName="AH8">
    <moduleKindModels kindName="COM">
      <moduleModels moduleName="CanIf" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="CanSM" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="Com" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="ComM" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="EcuC" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="LinIf" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="LinSM" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="PduR" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
    </moduleKindModels>
    <moduleKindModels kindName="DIAG">
      <moduleModels moduleName="CanTp" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="Dcm" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="Dem" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
    </moduleKindModels>
    <moduleKindModels kindName="MCAL">
      <moduleModels moduleName="Can" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
      <moduleModels moduleName="Lin" projectName="AH8" pathValue="BSW_Builder\AH8\ecuconfig.arxml"/>
    </moduleKindModels>
  </ecuConfigurationModels>
</cn.com.isoft.mal.model:BswBuilderModel>
