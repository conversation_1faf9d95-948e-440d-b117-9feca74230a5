/**
 * file    EcuM_Cbk.h
 * brief   EcuM_Cbk.h Stub.
 * author  Lihao
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#if !defined(ECUM_CBK_H)
#define ECUM_CBK_H

/*==================[inclusions]============================================*/

#include <EcuM.h>

/*==================[macros]=========================*/
#define ECUM_CBK_AR_RELEASE_MAJOR_VERSION   4
#define ECUM_CBK_AR_RELEASE_MINOR_VERSION   4

/* defines that would be generated into EcuM_Cfg.h */
#define EcuMWakeupSource_0 0x20U
#define EcuMWakeupSource_1 0x40U
#define EcuMWakeupSource_2 0x80U
 
/*==================[type definitions]======================================*/

typedef uint32 EcuM_WakeupSourceType;

/*==================[external function declarations]=========================*/

void EcuM_CheckWakeup (EcuM_WakeupSourceType wakeupSource);
void EcuM_SetWakeupEvent (EcuM_WakeupSourceType wakeupSource);


#endif /* ECUM_CBK_H */
/*==================[end of file]===========================================*/
