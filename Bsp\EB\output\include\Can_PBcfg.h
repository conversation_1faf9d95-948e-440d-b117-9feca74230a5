/**
 * file    Can_PBcfg.h
 * brief   Can Post Build Configurations header file.
 * author  <PERSON><PERSON>
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef CAN_PBCFG_H
#define CAN_PBCFG_H

#include "Can_Types.h"
#include "Can_Cfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================[macros]================================================*/

/* CanConfigSet 0: CanConfigSet_0 */
/* CanController 0: CanController_0 */
    /** Part of the initializer to the constant array of Dedicated filter elements. */
#define CAN_CFG_RX_DEDICATED_BUFFER_FILTERS_00 \
    /* Array of Can_ControllerFilterType_t */\
    /*          CanIdValue of the Can filter.*/\
    /*          VAR(uint32, TYPEDEF) FilterIdValue;*/\
    /*          |            CanHwFilterMask of the Can filter. */\
    /*          |            VAR(uint32, TYPEDEF) FilterMaskValue; */\
    /*          |            | */\
    /*          V            V */\


/** Part of the initializer to the constant array of FIFO filter elements. */
#define CAN_CFG_RX_FIFO_FILTERS_00 \
    /* Array of Can_ControllerFilterType_t */\
    /*          CanIdValue of the Can filter.*/\
    /*          VAR(uint32, TYPEDEF) FilterIdValue;*/\
    /*          |            CanHwFilterMask of the Can filter. */\
    /*          |            VAR(uint32, TYPEDEF) FilterMaskValue; */\
    /*          |            | */\
    /*          V            V */\
    \
    {      0x400u,      0x400u }, /* Array index 0, standard identifier (11 bit), CAN_RX_FIFO0 */ \

/** Part of the initializer to the constant array of HRHs.
    The list must be sorted ascending by buffer index, FIFOs last. */
#define CAN_CFG_HRH_FILTERS_00 \
    /* Array of Can_RxHandleMappingType_t */\
    /* The index of the controller where the HRH is allocated. */\
    /* VAR(Can_ControllerIdType, TYPEDEF) ControllerIndex, */\
    /* |    */\
    /* |    The unique object identifier to identify the Hardware object. */\
    /* |    VAR(Can_HwHandleType, TYPEDEF) RxHwHandle */\
    /* |    |    */\
    /* |    |    The filter index which filter has detected the message. The filter    */\
    /* |    |    index has only 7 bit. The MSB is set for extended messages. With this */\
    /* |    |    index a search to the allocated HRH is possible, if the order of the  */\
    /* |    |    filters in Message RAM is in the order lower CAN IDs first and first  */\
    /* |    |    all standard IDs and then extended IDs. This order have to match to   */\
    /* |    |    the initialization order. */\
    /* |    |    VAR(Can_ControllerRxHandleType, TYPEDEF) RxHandle */\
    /* |    |    |    */\
    /* |    |    |    Flag indicating whether it is an RxHandler dedicated to pretended */\
    /* |    |    |    networking mode. */\
    /* |    |    |    VAR(boolean, TYPEDEF) IcomDedicatedEnable */\
    /* |    |    |    |  */\
    /* V    V    V    V  */\
    { 0u,  0u,  0u, FALSE}, /* Array index 0, standard filter, CAN_RX_FIFO0 */\

/** Part of the initializer to the constant array of baudrates. */
#define CAN_CFG_BAUDRATES_00 \
    /* Array of Can_ControllerBaudrateConfigType_t */\
    /* The value by which the CAN prescaler output frequency is divided for generating */\
    /* the bit time quanta. (I.e. there is a global CAN prescaler maintained by */\
    /* Can_ModuleManager, and an additional individual prescaler per CAN controller.) */\
    /* The bit time is built up from a multiple of this quanta. */\
    /* Valid values for the Baud Rate Prescaler are 1 to 1024. */\
    /* CONST(uint16, TYPEDEF) NominalBaudratePrescaler; */\
    /*    |      */\
    /*    |      The value of the baudrate in kbps. */\
    /*    |      CONST(uint16, TYPEDEF) BaudrateValue; */\
    /*    |      |    */\
    /*    |      |    (Re) Synchronization Jump Width: Valid values are 1 to 16. */\
    /*    |      |    CONST(uint8, TYPEDEF) SyncJumpWidth; */\
    /*    |      |    |    */\
    /*    |      |    |    Time segment before sample point: Valid values are 2 to 64. */\
    /*    |      |    |    CONST(uint8, TYPEDEF) TSeg1; */\
    /*    |      |    |    |    */\
    /*    |      |    |    |    Time segment after sample point: Valid values are 1 to 16. */\
    /*    |      |    |    |    CONST(uint8, TYPEDEF) TSeg2; */\
    /*    |      |    |    |    |      Switches support of CAN-FD features for this CAN object on or off.           */\
    /*    |      |    |    |    |      TRUE := Valid CAN-FD baudrate settings available and should be used for this */\
    /*    |      |    |    |    |      CAN object.                                                                  */\
    /*    |      |    |    |    |      FALSE := No valid CAN-FD baudrate settings available. Standard CAN should be */\
    /*    |      |    |    |    |      used for this CAN object. */\
    /*    |      |    |    |    |      CONST(boolean, TYPEDEF) FdEnabled; */\
    /*    |      |    |    |    |      |    (Re) Synchronization Jump Width for CAN FD: Valid values are 1 to 16. */\
    /*    |      |    |    |    |      |    CONST(uint8, TYPEDEF) FdSyncJumpWidth; */\
    /*    |      |    |    |    |      |    |    Time segment for CAN FD before sample point: Valid values are 2 to 64. */\
    /*    |      |    |    |    |      |    |    CONST(uint8, TYPEDEF) FdTSeg1; */\
    /*    |      |    |    |    |      |    |    |    Time segment for CAN FD after sample point: Valid values are 1 to 16. */\
    /*    |      |    |    |    |      |    |    |    CONST(uint8, TYPEDEF) FdTSeg2; */\
    /*    |      |    |    |    |      |    |    |    |      Specifies if the bit rate switching shall be used for transmissions. */\
    /*    |      |    |    |    |      |    |    |    |      FALSE := CAN FD frames shall be sent without bit rate switching */\
    /*    |      |    |    |    |      |    |    |    |      TRUE := CAN FD frames shall be sent with bit rate switching */\
    /*    |      |    |    |    |      |    |    |    |      CONST(boolean, TYPEDEF) FdTxBitRateSwitch; */\
    /*    |      |    |    |    |      |    |    |    |      |     Specifies whether transceiver compensation is enabled or not. */\
    /*    |      |    |    |    |      |    |    |    |      |     FALSE := CAN FD frames transceiver compensation is not enabled */\
    /*    |      |    |    |    |      |    |    |    |      |     TRUE := CAN FD frames transceiver compensation is enabled  */\
    /*    |      |    |    |    |      |    |    |    |      |     CONST(boolean, TYPEDEF) TrCVDelayCompensationEnable; */\
    /*    |      |    |    |    |      |    |    |    |      |     |      The value of the delay compensation SSP offset in tq. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      CONST(uint8, TYPEDEF)   TrcvDelayCompensationOffset;*/\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     The value of the Delay compensation filtering window length in tq. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     CONST(uint8, TYPEDEF) TrcvDelayCompensationFilterWindowLength;*/\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      The value of the CAN FD baudrate in kbps. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      CONST(uint16, TYPEDEF) FdBaudrateValue; */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      The value by which the CAN prescaler output frequency for Data section if bit */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      rate switching is set is divided for generating the bit time quanta. (I.e.    */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      there is a global CAN prescaler maintained by Can_ModuleManager, and an       */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      additional individual prescaler per CAN controller.) The bit time is built up */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      from a multiple of this quanta. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      Valid values for the Baud Rate Prescaler are 1 to 1024. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      CONST(uint16, TYPEDEF) DataBaudratePrescaler; */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      |       Unique identifier to identify one baud rate configuration. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      |       Valid range is 0..65535. */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      |       CONST(uint16, TYPEDEF) BaudrateConfigId; */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      |       |  */\
    /*    |      |    |    |    |      |    |    |    |      |     |      |     |      |      |       |  */\
    /*    V      V    V    V    V      V    V    V    V      V     V      V     V      V      V       V*/\
    {    2u,  500u,  4u, 60u, 17u,  TRUE,  3u, 30u,  7u,  TRUE, FALSE,   0u,   0u, 2000u,    1u,     0u }, \

/** Pointer to the base address of the Message RAM. It must point to the hardware
    base address, even if the beginning of the message RAM is not used by this
    controller.

    The macro can be overridden by the integrator via command line parameter.
    Doing so is at own risk! */
#ifndef CAN_CFG_MESSAGE_RAM_BASE_PTR_00
    #define CAN_CFG_MESSAGE_RAM_BASE_PTR_00 (P2VAR(uint8, TYPEDEF, TYPEDEF))0x50004000u
#else
    /* Deviation from MISRA-C:2004 Rule 19.16:
      Unrecognized preprocessing directive has been ignored because of conditional inclusion directives.
      Justification: The preprocessing directive is a warning that indicates a special build 
      configuration which is not suitable for production software. */
    #warning CAN_CFG_MESSAGE_RAM_BASE_PTR_00 is overridden by the environment. This is not intended for production software!
#endif

/** Message RAM size */
#define CAN_CFG_MESSAGE_RAM_SIZE_00 0x3100u

/** Base address of the CAN FD controller's registers.
    
    The macro can be overridden by the integrator via command line parameter.
    Doing so is at own risk! */
#ifndef CAN_CFG_REGISTER_BASE_ADDRESS_00
    #define CAN_CFG_REGISTER_BASE_ADDRESS_00 0x50000000u
#else
    /* Deviation from MISRA-C:2004 Rule 19.16:
      Unrecognized preprocessing directive has been ignored because of conditional inclusion directives.
      Justification: The preprocessing directive is a warning that indicates a special build 
      configuration which is not suitable for production software. */
    #warning CAN_CFG_REGISTER_BASE_ADDRESS_00 is overridden by the environment. This is not intended for production software!
#endif


/*  Message RAM (0x50004000) linking table:

    Item                     | Size in elements | Start offset | Size in bytes
    -------------------------+------------------+--------------+--------------
    Standard (11bit) filters |                1 |          0x0 |           0x4
    Extended (29bit) filters |                0 |          0x4 |           0x0
    RX FIFO 0                |               16 |          0x4 |         0x480
    RX FIFO 1                |                0 |        0x484 |           0x0
    RX dedicated buffers     |                0 |        0x484 |           0x0
    Event FIFO               |                8 |        0x484 |          0x40
    TX dedicated buffers     |                0 |        0x4c4 |           0x0
    TX FIFO                  |                8 |        0x4c4 |         0x240
 */

/** Part of the initializer to the constant array of controllers. */
#define CAN_CFG_CONTROLLER_00 \
{\
    /* Pointer to an array of baudrates. */\
    .BaudratesCfgPtr = &(Can_Baudrates_0[0u]), /* CONSTP2(Can_ControllerBaudrateConfigType_t, TYPEDEF, TYPEDEF) */\
    \
    /* Id of the controller. */\
    .ControllerId = 0u, /* CONST(Can_ControllerIdType, TYPEDEF) */\
    \
    /* Pointer to the default baudrate setting. */\
    .DefaultBaudratePtr = &(Can_Baudrates_0[0u]), /* CONSTP2(Can_ControllerBaudrateConfigType_t, TYPEDEF, TYPEDEF) */\
    \
    /* Configuration of the TX Event FIFO. The FIFO element size is ignored, because  */\
    /* it is fixed to 8 bytes. */\
    .EventFifo = \
    {\
        /* The number of elements belonging to the buffer or FIFO, i.e. the buffer/FIFO */\
        /* size in elements. */\
        .ElementCount = 8u, /* CONST(uint8, TYPEDEF) */\
        \
        /* The size of each field in the buffer or FIFO. */\
        .ElementSize = CAN_CONTROLLER_BUFFER_SIZE_8, /* CONST(Can_ControllerBufferSizeType, TYPEDEF) */\
        \
        /* Start address of the buffer or FIFO in bytes relative (offset) to the CAN */\
        /* message RAM base address. */\
        .StartAddress = 0x484u /* CONST(uint16, TYPEDEF) */\
    }, /* CONST(Can_ControllerBufferCfgType_t, TYPEDEF) */\
    /* Address offset to the message RAM base address where the filters for extended */\
    /* messages shall be located. */\
    .FiltersExtendedAddressOffset = 0x4u, /* CONST(uint16, TYPEDEF) */\
    \
    /* Address offset to the message RAM base address where the filters for standard */\
    /* messages shall be located. */\
    .FiltersStandardAddressOffset = 0x0u, /* CONST(uint16, TYPEDEF) */\
    \
    /* Specifies if bus-off handling shall be done in interrupt mode (TRUE) or polling */\
    /* mode (FALSE). */\
    .InterruptEnableBusOff = TRUE, /* CONST(boolean, TYPEDEF) */\
    \
    /* Specifies if RX handling shall be done in interrupt mode (TRUE) or polling mode */\
    /* (FALSE). */\
    .InterruptEnableRx = TRUE, /* CONST(boolean, TYPEDEF) */\
    \
    /* Specifies if TX handling shall be done in interrupt mode (TRUE) or polling mode */\
    /* (FALSE). */\
    .InterruptEnableTx = TRUE, /* CONST(boolean, TYPEDEF) */\
    \
    /* Specifies if wakeup handling shall be done in interrupt mode (TRUE) or polling */\
    /* mode (FALSE). */\
    .InterruptEnableWakeup = TRUE, /* CONST(boolean, TYPEDEF) */\
    \
    /* Pointer to the base address of the Message RAM. It must point to the hardware */\
    /* base address, even if the beginning of the message RAM is not used by this */\
    /* controller. */\
    .MessageRamBasePtr = CAN_CFG_MESSAGE_RAM_BASE_PTR_00, /* CONSTP2VAR(uint8, TYPEDEF, TYPEDEF) */\
    \
    /* Message RAM size in 1 Can Instance. It is not Message RAM size occupied */\
    /* by 1 channel. */\
    .MessageRamSize = CAN_CFG_MESSAGE_RAM_SIZE_00, /* CONST(uint32, TYPEDEF) */\
    \
    /* Number of baudrates in the array of baudrates pointed to by BaudratesCfgPtr. */\
    .NumberOfBaudrates = 1u, /* CONST(uint8, TYPEDEF) */\
    \
    /* Base address of the CAN FD controller's registers. */\
    .RegisterBaseAddress = CAN_CFG_REGISTER_BASE_ADDRESS_00, /* CONST(uint32, TYPEDEF) */\
    \
    /* Configuration of the dedicated RX buffers. The ElementCount equals the number */\
    /* of filter settings in the filter settings list. */\
    .RxBuffers = \
    {\
        /* The number of elements belonging to the buffer or FIFO, i.e. the buffer/FIFO */\
        /* size in elements. */\
        .ElementCount = 0u, /* CONST(uint8, TYPEDEF) */\
        \
        /* The size of each field in the buffer or FIFO. */\
        .ElementSize = CAN_CONTROLLER_BUFFER_SIZE_64, /* CONST(Can_ControllerBufferSizeType, TYPEDEF) */\
        \
        /* Start address of the buffer or FIFO in bytes relative (offset) to the CAN */\
        /* message RAM base address. */\
        .StartAddress = 0x484u /* CONST(uint16, TYPEDEF) */\
    }, /* CONST(Can_ControllerBufferCfgType_t, TYPEDEF) */\
    /* Pointer to an array of CAN IDs. There is one ID per dedicated RX buffer; the */\
    /* order is the same as the buffers (same index).  */\
    .RxBuffersFiltersPtr = (P2CONST(Can_ControllerFilterType_t, AUTOMATIC, AUTOMATIC))NULL_PTR, /* P2CONST(Can_ControllerFilterType_t, TYPEDEF, TYPEDEF) */\
    /* Number of RxBuffers filters stored in array RxBuffersFiltersPtr. */\
    .RxBuffersFilterElementCount = 0u, /* CONST(uint8, TYPEDEF) */\
    /* Configuration of the RX FIFO 0. */\
    .RxFifo0 = \
    {\
        /* The number of elements belonging to the buffer or FIFO, i.e. the buffer/FIFO */\
        /* size in elements. */\
        .ElementCount = 16u, /* CONST(uint8, TYPEDEF) */\
        \
        /* The size of each field in the buffer or FIFO. */\
        .ElementSize = CAN_CONTROLLER_BUFFER_SIZE_64, /* CONST(Can_ControllerBufferSizeType, TYPEDEF) */\
        \
        /* Start address of the buffer or FIFO in bytes relative (offset) to the CAN */\
        /* message RAM base address. */\
        .StartAddress = 0x4u /* CONST(uint16, TYPEDEF) */\
    }, /* CONST(Can_ControllerBufferCfgType_t, TYPEDEF) */\
    /* Pointer to an array of different Can filter settings for the RX FIFO 0 */\
    .RxFifo0FiltersPtr = &(Can_RxFifoFilters_0[0]), /* P2CONST(Can_ControllerFilterType_t, TYPEDEF, TYPEDEF) */\
    /* Number of RxFifo0 filters stored in array RxFifo0FiltersPtr. */\
    .RxFifo0FilterElementCount = 1u, /* CONST(uint8, TYPEDEF) */\
    /* Configuration of the RX FIFO 1. */\
    .RxFifo1 = \
    {\
        /* The number of elements belonging to the buffer or FIFO, i.e. the buffer/FIFO */\
        /* size in elements. */\
        .ElementCount = 0u, /* CONST(uint8, TYPEDEF) */\
        \
        /* The size of each field in the buffer or FIFO. */\
        .ElementSize = CAN_CONTROLLER_BUFFER_SIZE_64, /* CONST(Can_ControllerBufferSizeType, TYPEDEF) */\
        \
        /* Start address of the buffer or FIFO in bytes relative (offset) to the CAN */\
        /* message RAM base address. */\
        .StartAddress = 0x484u /* CONST(uint16, TYPEDEF) */\
    }, /* CONST(Can_ControllerBufferCfgType_t, TYPEDEF) */\
    /* Pointer to an array of different Can filter settings for the RX FIFO 1 */\
    .RxFifo1FiltersPtr = (P2CONST(Can_ControllerFilterType_t, AUTOMATIC, AUTOMATIC))NULL_PTR, /* P2CONST(Can_ControllerFilterType_t, TYPEDEF, TYPEDEF) */\
    \
    /* Number of RxFifo1 filters stored in array RxFifo1FiltersPtr. */\
    .RxFifo1FilterElementCount = 0u, /* CONST(uint8, TYPEDEF) */\
    /* Configuration of the dedicated TX buffers. */\
    .TxBuffers = \
    {\
        /* The number of elements belonging to the buffer or FIFO, i.e. the buffer/FIFO */\
        /* size in elements. */\
        .ElementCount = 0u, /* CONST(uint8, TYPEDEF) */\
        \
        /* The size of each field in the buffer or FIFO. */\
        .ElementSize = CAN_CONTROLLER_BUFFER_SIZE_64, /* CONST(Can_ControllerBufferSizeType, TYPEDEF) */\
        \
        /* Start address of the buffer or FIFO in bytes relative (offset) to the CAN */\
        /* message RAM base address. */\
        .StartAddress = 0x4c4u /* CONST(uint16, TYPEDEF) */\
    }, /* CONST(Can_ControllerBufferCfgType_t, TYPEDEF) */\
    \
    /*Number of hths in the transmit TxDedicated Buffer*/\
    .TxDedicatedBufferHthCount = 0u, /*CONST(uint8, TYPEDEF) */\
    \
    /* Configuration of the TX FIFO element count. The FIFO element size and start */\
    /* offset are shared with the dedicated TX Buffers. */\
    .TxFifoElementCount = 8u, /* CONST(uint8, TYPEDEF) */\
    \
    /* The wakeup source id that is passed to EcuM_CheckWakeup. */\
    .WakeupSource = 0u, /* CONST(uint32, TYPEDEF) */\
    \
    /* Channel number excluding CanInstance number. */\
    \
    .ChannelNumber = 0u, /* CONST(uint8, TYPEDEF) */\
    /** Indicates whether ECC error detection can be enabled. */\
    \
    .EccPresentEnable = TRUE, /* CONST(boolean, TYPEDEF) */\
    /* Indicate initial running mode. */\
    \
    .RunningMode = CAN_CONTROLLER_NORMAL, /* CONST(Can_ControllerRunningModeType, TYPEDEF) */\
    \
    /* CAN auto resend feature disable. */\
    .CanAutoResendDisable = FALSE, /* CONST(boolean, TYPEDEF) */\
    \
    /* CAN Controller timestamp feature enable. */\
    .CanTimestampEnable = FALSE, /* CONST(boolean, TYPEDEF) */\
    \
    /* CAN Controller Tx FIFO DMA feature enable. */\
    .CanDMA_TxFIFO_Enable = FALSE, /* CONST(boolean, TYPEDEF) */\
    \
    /* CAN Controller DMA Tx FIFO channel. */\
    .CanDMA_TxFIFO_Channel = DMA_CHANNEL_INVALID, /* CONST(uint32, TYPEDEF) */\
    \
    /* CAN Controller DMA Tx FIFO Hw channel. */\
    .CanDMA_TxFIFO_HwChannel = DMA_CHANNEL_INVALID, /* CONST(uint32, TYPEDEF) */\
    \
    /* CAN Controller Rx FIFO0 DMA feature enable. */\
    .CanDMA_RxFIFO0_Enable = FALSE, /* CONST(boolean, TYPEDEF) */\
    \
    /* CAN Controller DMA Rx FIFO0 channel. */\
    .CanDMA_RxFIFO0_Channel = DMA_CHANNEL_INVALID, /* CONST(uint32, TYPEDEF) */\
    \
    /* CAN Controller DMA Rx FIFO0 Hw channel. */\
    .CanDMA_RxFIFO0_HwChannel = DMA_CHANNEL_INVALID, /* CONST(uint32, TYPEDEF) */\
    \
    /* CAN Controller Rx FIFO1 DMA feature enable. */\
    .CanDMA_RxFIFO1_Enable = FALSE, /* CONST(boolean, TYPEDEF) */\
    \
    /* CAN Controller DMA Rx FIFO1 channel. */\
    .CanDMA_RxFIFO1_Channel = DMA_CHANNEL_INVALID, /* CONST(uint32, TYPEDEF) */\
    \
    /* CAN Controller DMA Rx FIFO0 Hw channel. */\
    .CanDMA_RxFIFO1_HwChannel = DMA_CHANNEL_INVALID /* CONST(uint32, TYPEDEF) */\
},\


/** Initializer to the constant array of baudrates. */
#define CAN_CFG_BAUDRATES_0 \
{\
    CAN_CFG_BAUDRATES_00\
}

/** Initializer to the constant array of controllers. */
#define CAN_CFG_CONTROLLERS_0 \
{\
    CAN_CFG_CONTROLLER_00\
}

/** Initializer to the constant array of RX dedicated buffer Filters. */
#define CAN_CFG_RX_DEDICATED_BUFFER_FILTERS_0 \
{\
}

/** Initializer to the constant array of RX FIFO Filters. */
#define CAN_CFG_RX_FIFO_FILTERS_0 \
{\
    CAN_CFG_RX_FIFO_FILTERS_00\
}

/** Initializer to the constant array of HRHs.
    The list must be sorted ascending by controller index and for the
    same controller index ascending by buffer index, FIFOs last. */
#define CAN_CFG_HRH_FILTERS_0 \
{\
    CAN_CFG_HRH_FILTERS_00\
}

/** Initializer to the constant array of HTHs. 
    The list must be sorted ascending by CanObjectId. */
#define CAN_CFG_HTHS_0 \
{\
    /* Can_TxHandleMappingType_t */\
    /* The index of the controller where the HTH is allocated. */\
    /* VAR(Can_ControllerIdType, TYPEDEF) ControllerIndex, */\
    /* |       The index pointed to right position in the Message Queue. */\
    /* |       VAR(uint32, TYPEDEF) QueueStartIndex, */\
    /* |       |       The length of the Object message queue.*/\
    /* |       |       VAR(uint32, TYPEDEF) QueueLength, */\
    /* |       |       |      Enables the possibility to request the data for this HW object with the */\
    /* |       |       |      CanIf_TriggerTransmit from CanIf instead of getting the data by call of */\
    /* |       |       |      Can_Write. */\
    /* |       |       |      VAR(boolean, TYPEDEF) TriggerTransmitEnable, */\
    /* |       |       |      |     Value to initialize unused bytes in a transmit message, when the     */\
    /* |       |       |      |     PduInfo->SduLength does not match possible DLC values and the driver */\
    /* |       |       |      |     have to use the next higher valid DLC for transmission. */\
    /* |       |       |      |     VAR(uint8, TYPEDEF) PaddingValue, */\
    /* |       |       |      |     |     This is the index of the buffer in scope of the controller, */\
    /* |       |       |      |     |     where the HTH is allocated. */\
    /* |       |       |      |     |     0..CAN_CONTROLLER_TX_BUFFER_MAX is a dedicated buffer, */\
    /* |       |       |      |     |     CAN_CONTROLLER_TX_FIFO is the FIFO. */\
    /* |       |       |      |     |     VAR(uint8, TYPEDEF) TxHandle */\
    /* |       |       |      |     |     |                         Enable the store of Tx Event, */\
    /* |       |       |      |     |     |                         VAR(boolean, TYPEDEF) TxEventStoreEnable, */\
    /* |       |       |      |     |     |                         |*/\
    /* |       |       |      |     |     |                         |*/\
    /* V       V       V      V     V     V                         V*/\
    { 0u,     0u,     8u, FALSE,   0u, CAN_CONTROLLER_TX_FIFO,   TRUE }, /* Array index 0, TX FIFO */\
}


/** Initializer to the constant array of Can_TimestampCapture. */
#define CAN_CFG_TIMESTAMP_CAPTURE_0 \
{0u,0u}

/** Initializer to the constant array of Can_TimestampValue. */
#define CAN_CFG_TIMESTAMP_VALUE_0 \
{0u,0u}

/** Initializer to the constant array of CanIcomConfig. */
#define CAN_CFG_ICOMS_0 \
{\
}

/** Initializer to the constant array of CanIcomRxMessageConfig. */
#define CAN_CFG_ICOM_RX_MESSAGES_0 \
{\
}

/** Initializer to the constant array of CanIcomRxMessageSignalConfig. */
#define CAN_CFG_ICOM_RX_MESSAGE_SIGNALS_0 \
{\
}

/** The number of baudrates that are configured. */
#define CAN_CFG_NUMBER_OF_BAUDRATES_0 1u

/** The number of controllers that are configured. */
#define CAN_CFG_NUMBER_OF_CONTROLLERS_0 1u

/** The number of filters for RX dedicated CAN objects. */
#define CAN_CFG_NUMBER_OF_RX_DEDICATED_BUFFER_FILTERS_0 0u

/** The number of filters for RX FIFO CAN objects. */
#define CAN_CFG_NUMBER_OF_RX_FIFO_FILTERS_0 1u

/** The number of configured HRH filters. */
#define CAN_CFG_NUMBER_OF_HRH_FILTERS_0 1u

/** The number of HRHs are configured. */
#define CAN_CFG_NUMBER_OF_HRHS_0 1u

/** The number of HTHs are configured. */
#define CAN_CFG_NUMBER_OF_HTHS_0 1u

/** The number of HWOs are configured. */
#define CAN_CFG_NUMBER_OF_HOHS_0 2u

/** Number of TX buffers that are available in hardware over all CAN controllers. */
#define CAN_CFG_NUMBER_OF_TX_BUFFERS_0 8u

/** The number of CanIcomConfigs are configured. */
#define CAN_CFG_NUMBER_OF_ICOMS_0 0u

/** The number of CanIcomRxMessageConfigs are configured. */
#define CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0 0u

/** The number of CanIcomRxMessageSignalConfigs are configured. */
#define CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGE_SIGNALS_0 0u


/** The number of CanConfigSet */
#define CAN_CFG_NUMBER_OF_SET_CONFIGS 1u

/* Array of interrupt to controller list. */
#define CAN_CFG_INTERRUPT_TO_CONTROLLER_LIST_0 \
{\
    CanConf_CanController_0_CanController_0, \
}

/* Number of interrupt to controller list. */
#define CAN_CFG_NUMBER_OF_INTERRUPT_TO_CONTROLLER_LISTS_0  1u


#define CAN_START_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"

extern CONST(Can_ConfigType, CAN_CONST) Can_Config[CAN_CFG_NUMBER_OF_SET_CONFIGS];

#define CAN_STOP_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"

#define CAN_START_SEC_CODE
#include "Can_MemMap.h"

/**********************************************************************************************************************************
 * @brief This function checks the pointer of the configuration set.
 *
 * @param [in]  ConfigPtr  Pointer of data stored configuration set information.
 * 
 * @return If parameter ConfigPtr is a valid config set pointer, 
 *         return TRUE, else return FALSE. 
 *********************************************************************************************************************************/
FUNC(boolean, CAN_CODE) Can_CheckConfigPtr
(
    P2CONST(Can_ConfigType, AUTOMATIC, CAN_APPL_CONST) ConfigPtr
);

#define CAN_STOP_SEC_CODE
#include "Can_MemMap.h"

#ifdef __cplusplus
}
#endif
#endif

