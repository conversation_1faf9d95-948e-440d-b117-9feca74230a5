/**
* 
* @file    Port_Cfg.c
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
*
* @section[blobal]
* Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
* Reason: The macro definition names are similar, the version number definition prefixes are the same, 
* and the keywords are different.
*
*/ 

#include "Port.h"

#ifdef __cplusplus
extern "C" {
#endif
/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module's description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_VENDOR_ID_CFG_C                       (110U)
#define PORT_AR_RELEASE_MAJOR_VERSION_CFG_C        (4U)
#define PORT_AR_RELEASE_MINOR_VERSION_CFG_C        (4U)
#define PORT_AR_RELEASE_REVISION_VERSION_CFG_C     (0U)     
#define PORT_SW_MAJOR_VERSION_CFG_C                (1U)
#define PORT_SW_MINOR_VERSION_CFG_C                (0U)
#define PORT_SW_PATCH_VERSION_CFG_C                (0U)


/*=================================================================================================
*                                      FILE VERSION CHECKS
=================================================================================================*/
/* Check if the files Port_Cfg.c and Port.h are of the same vendor */
#if (PORT_VENDOR_ID_CFG_C != PORT_VENDOR_ID)
    #error "Port_Cfg.c and Port.h have different vendor ids"
#endif
/* Check if the files Port_Cfg.c and Port.h are of the same Autosar version */
#if ((PORT_AR_RELEASE_MAJOR_VERSION_CFG_C    != PORT_AR_RELEASE_MAJOR_VERSION)  || \
     (PORT_AR_RELEASE_MINOR_VERSION_CFG_C    != PORT_AR_RELEASE_MINOR_VERSION)  || \
     (PORT_AR_RELEASE_REVISION_VERSION_CFG_C != PORT_AR_RELEASE_REVISION_VERSION)  \
    )
    #error "AutoSar Version Numbers of Port_Cfg.c and Port.h are different"
#endif
/* Check if the files Port_Cfg.c and Port.h are of the same software version */
#if ((PORT_SW_MAJOR_VERSION_CFG_C != PORT_SW_MAJOR_VERSION) || \
     (PORT_SW_MINOR_VERSION_CFG_C != PORT_SW_MINOR_VERSION) || \
     (PORT_SW_PATCH_VERSION_CFG_C != PORT_SW_PATCH_VERSION)    \
    )
    #error "Software Version Numbers of Port_Cfg.c and Port.h are different"
#endif

/*=================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
=================================================================================================*/


/*=================================================================================================
*                                       LOCAL MACROS
=================================================================================================*/


/*=================================================================================================
*                                      LOCAL CONSTANTS
=================================================================================================*/


/*=================================================================================================
*                                      LOCAL VARIABLES
=================================================================================================*/


/*=================================================================================================
*                                      GLOBAL CONSTANTS
=================================================================================================*/

#define PORT_START_SEC_CONST_16
#include "Port_MemMap.h"

#if (STD_ON == PORT_SET_PIN_MODE_API)
/**
* @brief Port Pin description data
*/
const uint16 Port_au16PinDescription[8][9] =
{
            

/*for Port_144LQFP*/

    /*  Mode PORT_ALT0_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_ADC0_SE0_CMP0_IN0 |
         PORT1_ADC0_SE1_CMP0_IN1 |
         PORT2_ADC1_SE0 |
         PORT3_ADC1_SE1 |
         PORT4_DISABLED |
         PORT5_DISABLED |
         PORT6_ADC0_SE2 |
         PORT7_ADC0_SE3 |
         PORT8_DISABLED |
         PORT9_DISABLED |
         PORT10_DISABLED |
         PORT11_DISABLED |
         PORT12_DISABLED |
         PORT13_DISABLED |
         PORT14_DISABLED |
         PORT15_ADC1_SE12 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  16 ...  31 : PORT16_ADC1_SE13 |
         PORT17_DISABLED */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_ADC0_SE4_ADC1_SE14 |
         PORT33_ADC0_SE5_ADC1_SE15 |
         PORT34_ADC0_SE6 |
         PORT35_ADC0_SE7 |
         PORT36_DISABLED |
         PORT37_DISABLED |
         PORT38_XTAL |
         PORT39_EXTAL |
         PORT40_DISABLED |
         PORT41_DISABLED |
         PORT42_DISABLED |
         PORT43_DISABLED |
         PORT44_ADC1_SE7 |
         PORT45_ADC1_SE8_ADC0_SE8 |
         PORT46_ADC1_SE9_ADC0_SE9 |
         PORT47_ADC1_SE14 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  48 ...  63 : PORT48_ADC1_SE15 |
         PORT49_DISABLED */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  64 ...  79 : PORT64_ADC0_SE8 |
         PORT65_ADC0_SE9 |
         PORT66_ADC0_SE10_CMP0_IN5 |
         PORT67_ADC0_SE11_CMP0_IN4 |
         PORT68_CMP0_IN2 |
         PORT69_DISABLED |
         PORT70_ADC1_SE4 |
         PORT71_ADC1_SE5 |
         PORT72_DISABLED |
         PORT73_DISABLED |
         PORT74_DISABLED |
         PORT75_DISABLED |
         PORT76_DISABLED |
         PORT77_DISABLED |
         PORT78_ADC0_SE12 |
         PORT79_ADC0_SE13 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  80 ...  95 : PORT80_ADC0_SE14 |
         PORT81_ADC0_SE15 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  96 ... 111 : PORT96_DISABLED |
         PORT97_DISABLED |
         PORT98_ADC1_SE2 |
         PORT99_ADC1_SE3 |
         PORT100_ADC1_SE6 |
         PORT101_DISABLED |
         PORT102_CMP0_IN7 |
         PORT103_CMP0_IN6 |
         PORT104_DISABLED |
         PORT105_DISABLED |
         PORT106_DISABLED |
         PORT107_DISABLED |
         PORT108_DISABLED |
         PORT109_DISABLED |
         PORT110_DISABLED |
         PORT111_DISABLED */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 112 ... 127 : PORT112_DISABLED |
         PORT113_DISABLED */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_DISABLED |
         PORT129_DISABLED |
         PORT130_ADC1_SE10 |
         PORT131_DISABLED |
         PORT132_DISABLED |
         PORT133_DISABLED |
         PORT134_ADC1_SE11 |
         PORT136_CMP0_IN3 |
         PORT137_DISABLED |
         PORT138_DISABLED |
         PORT139_DISABLED |
         PORT140_DISABLED |
         PORT141_DISABLED |
         PORT142_DISABLED |
         PORT143_DISABLED */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_DISABLED */
    }
    ,
    /*  Mode PORT_GPIO_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_GPIO |
        PORT1_GPIO |
        PORT2_GPIO |
        PORT3_GPIO |
        PORT4_GPIO |
        PORT5_GPIO |
        PORT6_GPIO |
        PORT7_GPIO |
        PORT8_GPIO |
        PORT9_GPIO |
        PORT10_GPIO |
        PORT11_GPIO |
        PORT12_GPIO |
        PORT13_GPIO |
        PORT14_GPIO |
        PORT15_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  16 ...  31 : PORT16_GPIO |
        PORT17_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_GPIO |
        PORT33_GPIO |
        PORT34_GPIO |
        PORT35_GPIO |
        PORT36_GPIO |
        PORT37_GPIO |
        PORT38_GPIO |
        PORT39_GPIO |
        PORT40_GPIO |
        PORT41_GPIO |
        PORT42_GPIO |
        PORT43_GPIO |
        PORT44_GPIO |
        PORT45_GPIO |
        PORT46_GPIO |
        PORT47_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  48 ...  63 : PORT48_GPIO |
        PORT49_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  64 ...  79 : PORT64_GPIO |
        PORT65_GPIO |
        PORT66_GPIO |
        PORT67_GPIO |
        PORT68_GPIO |
        PORT69_GPIO |
        PORT70_GPIO |
        PORT71_GPIO |
        PORT72_GPIO |
        PORT73_GPIO |
        PORT74_GPIO |
        PORT75_GPIO |
        PORT76_GPIO |
        PORT77_GPIO |
        PORT78_GPIO |
        PORT79_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  80 ...  95 : PORT80_GPIO |
        PORT81_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  96 ... 111 : PORT96_GPIO |
        PORT97_GPIO |
        PORT98_GPIO |
        PORT99_GPIO |
        PORT100_GPIO |
        PORT101_GPIO |
        PORT102_GPIO |
        PORT103_GPIO |
        PORT104_GPIO |
        PORT105_GPIO |
        PORT106_GPIO |
        PORT107_GPIO |
        PORT108_GPIO |
        PORT109_GPIO |
        PORT110_GPIO |
        PORT111_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 112 ... 127 : PORT112_GPIO |
        PORT113_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_GPIO |
        PORT129_GPIO |
        PORT130_GPIO |
        PORT131_GPIO |
        PORT132_GPIO |
        PORT133_GPIO |
        PORT134_GPIO |
        PORT136_GPIO |
        PORT137_GPIO |
        PORT138_GPIO |
        PORT139_GPIO |
        PORT140_GPIO |
        PORT141_GPIO |
        PORT142_GPIO |
        PORT143_GPIO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_GPIO */

    }
    ,
    /*  Mode PORT_ALT2_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_PWM2_CH1 |
         PORT1_PWM1_CH1 |
         PORT2_PWM3_CH0 |
         PORT3_PWM3_CH1 |
         PORT4_UART2_TX |
         PORT6_PWM0_FLT1 |
         PORT7_PWM0_FLT2 |
         PORT8_UART2_RX |
         PORT9_LIN2_TX |
         PORT10_PWM1_CH4 |
         PORT11_PWM1_CH5 |
         PORT12_PWM1_CH6 |
         PORT13_PWM1_CH7 |
         PORT14_PWM0_FLT0 |
         PORT15_PWM1_CH2 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  16 ...  31 : PORT16_PWM1_CH3 |
         PORT17_PWM0_CH6 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_UART0_RX |
         PORT33_UART0_TX |
         PORT34_PWM1_CH0 |
         PORT35_PWM1_CH1 |
         PORT36_PWM0_CH4 |
         PORT37_PWM0_CH5 |
         PORT38_I2C0_SDA |
         PORT39_I2C0_SCL |
         PORT40_PWM3_CH0 |
         PORT41_PWM3_CH1 |
         PORT42_PWM3_CH2 |
         PORT43_PWM3_CH3 |
         PORT44_PWM0_CH0 |
         PORT45_PWM0_CH1 |
         PORT46_PWM0_CH2 |
         PORT47_PWM0_CH3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  48 ...  63 : PORT48_PWM0_CH4 |
         PORT49_PWM0_CH5 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  64 ...  79 : PORT64_PWM0_CH0 |
         PORT65_PWM0_CH1 |
         PORT66_PWM0_CH2 |
         PORT67_PWM0_CH3 |
         PORT68_PWM1_CH0 |
         PORT69_PWM2_CH0 |
         PORT70_UART1_RX |
         PORT71_UART1_TX |
         PORT72_UART1_RX |
         PORT73_UART1_TX |
         PORT74_PWM3_CH4 |
         PORT75_PWM3_CH5 |
         PORT76_PWM3_CH6 |
         PORT77_PWM3_CH7 |
         PORT78_PWM1_CH2 |
         PORT79_PWM1_CH3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  80 ...  95 : PORT80_PWM1_FLT2 |
         PORT81_PWM1_FLT3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  96 ... 111 : PORT96_PWM0_CH2 |
         PORT97_PWM0_CH3 |
         PORT98_PWM3_CH4 |
         PORT99_PWM3_CH5 |
         PORT100_PWM0_FLT3 |
         PORT101_PWM2_CH3 |
         PORT102_UART2_RX |
         PORT103_UART2_TX |
         PORT104_I2C1_SDA |
         PORT105_I2C1_SCL |
         PORT106_PWM2_CH0 |
         PORT107_PWM2_CH1 |
         PORT108_PWM2_CH2 |
         PORT109_PWM2_CH4 |
         PORT110_PWM2_CH5 |
         PORT111_PWM0_CH0 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 112 ... 127 : PORT112_PWM0_CH1 |
         PORT113_PWM0_FLT2 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_SPI0_SCLK |
         PORT129_SPI0_MISO |
         PORT130_SPI0_MOSI |
         PORT131_PWM0_FLT0 |
         PORT132_ETM_TRACE_D1 |
         PORT133_PWM_EXTCLK3 |
         PORT134_SPI0_SS_N2 |
         PORT136_PWM0_CH6 |
         PORT137_PWM0_CH7 |
         PORT138_CHIP_CLKOUT |
         PORT139_SPI2_SS_N0 |
         PORT140_PWM0_FLT3 |
         PORT141_PWM4_CH5 |
         PORT142_PWM0_FLT1 |
         PORT143_UART1_CTS */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_UART1_RTS */


    }
    ,
    /*  Mode PORT_ALT3_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_I2C0_SCL |
         PORT1_I2C0_SDA |
         PORT2_I2C0_SDA |
         PORT3_I2C0_SCL |
         PORT5_TCLK1 |
         PORT6_SPI1_SS_N1 |
         PORT7_PWM5_CH3 |
         PORT8_SPI2_MOSI |
         PORT9_SPI2_SS_N0 |
         PORT12_CAN1_RX |
         PORT13_CAN1_TX |
         PORT14_PWM3_FLT1 |
         PORT15_SPI0_SS_N3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  16 ...  31 : PORT16_SPI1_SS_N2 |
         PORT17_PWM3_FLT0 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_SPI0_SS_N0 |
         PORT33_SPI0_MOSI |
         PORT34_SPI0_SCLK |
         PORT35_SPI0_MISO |
         PORT36_SPI0_MOSI |
         PORT37_SPI0_SS_N1 |
         PORT38_LIN3_RX |
         PORT39_LIN3_TX |
         PORT41_I2C0_SCL |
         PORT42_I2C0_SDA |
         PORT44_PWM3_FLT2 |
         PORT45_PWM3_FLT1 |
         PORT46_SPI1_SCLK |
         PORT47_SPI1_MISO */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  48 ...  63 : PORT48_SPI1_MOSI |
         PORT49_SPI1_SS_N3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  64 ...  79 : PORT64_SPI2_MISO |
         PORT65_SPI2_MOSI |
         PORT66_CAN0_RX |
         PORT67_CAN0_TX |
         PORT68_RTC_CLK |
         PORT69_RTC_CLK |
         PORT70_CAN1_RX |
         PORT71_CAN1_TX |
         PORT72_PWM1_FLT0 |
         PORT73_PWM1_FLT1 |
         PORT75_PWM4_CH2 |
         PORT76_PWM2_CH6 |
         PORT77_PWM2_CH7 |
         PORT78_SPI2_SS_N0 |
         PORT79_SPI2_SCLK */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  80 ...  95 : PORT80_CAN2_RX |
         PORT81_CAN2_TX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads  96 ... 111 : PORT96_SPI1_SCLK |
         PORT97_SPI1_MISO |
         PORT98_SPI1_MOSI |
         PORT99_SPI1_SS_N0 |
         PORT100_PWM3_FLT3 |
         PORT101_LPIT_ALT2 |
         PORT102_CAN3_RX |
         PORT103_CAN3_TX |
         PORT105_FXIO_D0 |
         PORT106_PWM2_QD_PHB |
         PORT107_PWM2_QD_PHA |
         PORT109_UART1_RX |
         PORT110_UART1_TX |
         PORT111_ETM_TRACE_D3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 112 ... 127 : PORT112_ETM_TRACE_D2 |
         PORT113_UART2_RX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_PWM_EXTCLK2 |
         PORT130_LPIT_ALT0 |
         PORT131_UART2_RTS |
         PORT137_UART2_CTS |
         PORT138_SPI2_SS_N1 |
         PORT139_LPIT_ALT1 |
         PORT140_UART2_TX |
         PORT141_SPI2_SS_N2 |
         PORT143_SPI2_SCLK */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_SPI2_MISO |
         PORT149_CAN3_TX |
         PORT150_CAN3_RX |
         PORT152_CAN2_TX |
         PORT153_CAN2_RX */


    }
    ,
    /*  Mode PORT_ALT4_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_FXIO_D2 |
         PORT1_FXIO_D3 |
         PORT2_EWM_OUT_N |
         PORT3_EWM_IN |
         PORT4_CMP0_OUT |
         PORT6_PWM5_CH5 |
         PORT7_CLK_RTC_32K |
         PORT8_FXIO_D6 |
         PORT9_FXIO_D7 |
         PORT10_FXIO_D0 |
         PORT11_FXIO_D1 |
         PORT12_I2C1_SDA |
         PORT13_I2C1_SCL |
         PORT14_EWM_IN |
         PORT15_SPI2_SS_N3 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  16 ...  31 : PORT17_EWM_OUT_N */

        (uint16)( SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_LPIT_ALT3 |
         PORT33_PWM_EXTCLK0 |
         PORT37_SPI0_SS_N0 |
         PORT44_CAN2_RX |
         PORT45_CAN2_TX |
         PORT46_CAN3_RX |
         PORT47_CAN3_TX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads  48 ...  63 : PORT49_PWM5_FLT1 */

        (uint16)( SHL_PAD_U16(1)
                ),
        /* Pads  64 ...  79 : PORT64_PWM5_FLT3 |
         PORT65_PWM5_FLT2 |
         PORT66_UART0_RX |
         PORT67_UART0_TX |
         PORT70_PWM3_CH2 |
         PORT71_PWM3_CH3 |
         PORT72_PWM5_CH1 |
         PORT73_PWM5_CH0 |
         PORT76_UART2_CTS |
         PORT77_UART2_RTS */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) 
                ),
        /* Pads  80 ... 95 */

        (uint16)0x0000,
        
        /* Pads  96 ... 111 : PORT96_PWM2_CH0 |
         PORT97_PWM2_CH1 |
         PORT98_FXIO_D4 |
         PORT99_FXIO_D5 |
         PORT101_PWM2_FLT1 |
         PORT102_PWM2_FLT2 |
         PORT103_PWM2_FLT3 |
         PORT104_PWM2_FLT2 |
         PORT105_PWM2_FLT3 |
         PORT106_ETM_TRACE_D3 |
         PORT107_ETM_TRACE_D2 |
         PORT108_ETM_TRACE_D1 |
         PORT111_SPI0_SCLK */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 112 ... 127 : PORT112_SPI0_MISO |
         PORT113_PWM5_FLT1 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_I2C1_SDA |
         PORT129_I2C1_SCL |
         PORT130_PWM3_CH6 |
         PORT131_PWM2_FLT0 |
         PORT132_PWM2_CH2 |
         PORT133_PWM2_CH3 |
         PORT134_PWM3_CH7 |
         PORT138_PWM2_CH4 |
         PORT139_PWM2_CH5 |
         PORT140_PWM5_FLT0 |
         PORT141_PWM2_FLT0 |
         PORT142_PWM2_FLT1 |
         PORT143_PWM2_CH6 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_PWM2_CH7 */

    }
    ,
    /*  Mode PORT_ALT5_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT2_FXIO_D4 |
         PORT3_FXIO_D5 |
         PORT4_EWM_OUT_N |
         PORT6_LIN3_RX |
         PORT7_LIN3_TX |
         PORT8_PWM3_FLT3 |
         PORT9_PWM3_FLT2 |
         PORT11_CMP0_RRT */

        (uint16)( SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(11) 
                ),
        /* Pads  16 ...  31 : PORT17_PWM5_FLT0 */

        (uint16)( SHL_PAD_U16(1)
                ),
        /* Pads  32 ...  47 : PORT32_CAN0_RX |
         PORT33_CAN0_TX |
         PORT37_CHIP_CLKOUT |
         PORT38_CAN3_RX |
         PORT39_CAN3_TX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) 
                ),
        /* Pads 48 ... 63 */
        (uint16)0x0000,
        /* Pads  64 ...  79 : PORT66_LIN0_RX |
         PORT67_LIN0_TX |
         PORT68_EWM_IN |
         PORT70_LIN1_RX |
         PORT71_LIN1_TX |
         PORT72_LIN1_RX |
         PORT73_LIN1_TX |
         PORT74_PWM5_FLT3 |
         PORT75_PWM5_FLT2 */

        (uint16)( SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) 
                ),
        /* Pads  80 ... 95 */

        (uint16)0x0000,
        
        /* Pads  96 ... 111 : PORT96_ETM_TRACE_D0 |
         PORT98_FXIO_D6 |
         PORT99_FXIO_D7 |
         PORT102_LIN2_RX |
         PORT103_LIN2_TX |
         PORT104_FXIO_D1 |
         PORT109_LIN1_RX |
         PORT110_LIN1_TX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) 
                ),
        /* Pads 112 ... 127 : PORT112_CMP0_RRT |
         PORT113_LIN2_RX */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1)
                ),
        /* Pads 128 ... 143 : PORT128_SPI1_MOSI |
         PORT129_SPI1_SS_N0 |
         PORT132_CAN0_RX |
         PORT133_CAN0_TX |
         PORT138_PWM4_FLT3 |
         PORT139_PWM4_FLT2 |
         PORT140_LIN2_TX |
         PORT143_PWM4_FLT1 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_PWM4_FLT0 */

    }
    ,
    /*  Mode PORT_ALT6_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_UART0_CTS |
         PORT1_UART0_RTS |
         PORT2_UART0_RX |
         PORT3_UART0_TX |
         PORT6_UART1_CTS |
         PORT7_UART1_RTS |
         PORT8_PWM4_FLT1 |
         PORT9_PWM1_FLT3 |
         PORT11_PWM4_FLT2 |
         PORT14_PWM1_FLT0 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(14) 
                ),
                
        /* Pads 16 ... 31 
         PORT27_LIN0_TX |
         PORT28_LIN0_RX |
         PORT29_LIN2_TX |
         PORT30_LIN2_RX */
         
        (uint16)( SHL_PAD_U16(11) |
                  SHL_PAD_U16(12) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14) 
                ),
       
        /* Pads  32 ...  47 : PORT32_PWM4_CH6 |
         PORT33_PWM4_CH5 |
         PORT34_TRGMUX_IN3 |
         PORT35_TRGMUX_IN2 |
         PORT36_TRGMUX_IN1 |
         PORT37_TRGMUX_IN0 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5)
                ),
        /* Pads 48 ... 63 : PORT54_LIN1_TX |
         PORT55_LIN1_RX */

        (uint16)( SHL_PAD_U16(6) |
                  SHL_PAD_U16(7) 
                ),
        /* Pads  64 ...  79 : PORT64_PWM1_CH6 |
         PORT65_PWM1_CH7 |
         PORT66_ETM_TRACE_CHIP_CLKOUT |
         PORT72_UART0_CTS |
         PORT73_UART0_RTS |
         PORT74_TRGMUX_IN11 |
         PORT75_TRGMUX_IN10 |
         PORT78_TRGMUX_IN9 |
         PORT79_TRGMUX_IN8 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(14) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 80 ... 95 */
        (uint16)0x0000,
        /* Pads  96 ... 111 : PORT96_FXIO_D0 |
         PORT97_FXIO_D1 |
         PORT98_TRGMUX_IN5 |
         PORT99_TRGMUX_IN4 |
         PORT101_TRGMUX_IN7 |
         PORT103_ETM_TRACE_D0 |
         PORT104_PWM1_CH4 |
         PORT105_PWM1_CH5 |
         PORT106_CHIP_CLKOUT |
         PORT107_UART2_CTS |
         PORT108_UART2_RTS */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(7) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(12)
                ),
        /* Pads 112 ... 127 : PORT112_ETM_TRACE_CHIP_CLKOUT */

        (uint16)( SHL_PAD_U16(0)
                ),
        /* Pads 128 ... 143 : PORT128_PWM1_FLT2 |
         PORT129_PWM1_FLT1 |
         PORT130_UART1_CTS |
         PORT131_TRGMUX_IN6 |
         PORT132_FXIO_D6 |
         PORT133_FXIO_D7 |
         PORT134_UART1_RTS |
         PORT138_FXIO_D4 |
         PORT139_FXIO_D5 |
         PORT143_FXIO_D2 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(6) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_FXIO_D3 */

    }
    ,
    /*  Mode PORT_ALT7_FUNC_MODE: */
    {
        /* Pads   0 ...  15 : PORT0_TRGMUX_OUT3 |
         PORT1_TRGMUX_OUT0 |
         PORT2_LIN0_RX |
         PORT3_LIN0_TX |
         PORT4_JTAG_TMS |
         PORT5_RESET_b |
         PORT8_LIN2_RX |
         PORT9_PWM4_FLT0 |
         PORT10_JTAG_TDO |
         PORT13_SAI0_D0 */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(2) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(8) |
                  SHL_PAD_U16(9) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(13) 
                ),
        /* Pads 16 ... 31 */
        (uint16)0x0000,
        /* Pads  32 ...  47 : PORT32_LIN0_RX |
         PORT33_LIN0_TX  */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) 
                ),
        /* Pads 48 ... 63 */
        (uint16)0x0000,
        /* Pads  64 ...  79 : PORT68_JTAG_TCLK |
         PORT69_JTAG_TDI */

        (uint16)( SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) 
                ),
        /* Pads  80 ... 95 */

        (uint16)0x0000,
        
        /* Pads  96 ... 111 : PORT96_TRGMUX_OUT1 |
         PORT97_TRGMUX_OUT2 |
         PORT99_NMI_b |
         PORT109_RTC_CLK |
         PORT110_CHIP_CLKOUT */

        (uint16)( SHL_PAD_U16(0) |
                  SHL_PAD_U16(1) |
                  SHL_PAD_U16(3) |
                  SHL_PAD_U16(13) |
                  SHL_PAD_U16(14)
                ),
        /* Pads 112 ... 127 */
        (uint16)0x0000,
        /* Pads 128 ... 143 : PORT131_CMP0_OUT |
         PORT132_EWM_OUT_N |
         PORT133_EWM_IN |
         PORT138_TRGMUX_OUT4 |
         PORT139_TRGMUX_OUT5 |
         PORT143_TRGMUX_OUT6 */

        (uint16)( SHL_PAD_U16(3) |
                  SHL_PAD_U16(4) |
                  SHL_PAD_U16(5) |
                  SHL_PAD_U16(10) |
                  SHL_PAD_U16(11) |
                  SHL_PAD_U16(15)
                ),
        /* Pads 144 ... 159 : PORT144_TRGMUX_OUT7 */

    }


    };
#endif

#define PORT_STOP_SEC_CONST_16
#include "Port_MemMap.h"
/*=================================================================================================
*                                      GLOBAL VARIABLES
=================================================================================================*/

/*=================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
=================================================================================================*/

/*=================================================================================================
*                                       LOCAL FUNCTIONS
=================================================================================================*/

/*=================================================================================================
*                                       GLOBAL FUNCTIONS
=================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

/* End of File */
