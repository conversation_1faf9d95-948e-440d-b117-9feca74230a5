<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Port" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Port" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Port"/>
              <a:a name="IMPORTER_INFO" value="@REC"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@REC"/>
              </d:var>
              <d:ctr name="PortConfigSet" type="IDENTIFIABLE">
                <a:a name="IMPORTER_INFO" value="@REC"/>
                <d:ctr name="NotUsedPortPin" type="IDENTIFIABLE">
                  <d:var name="PortPinMode" type="ENUMERATION" value="GPIO">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="PortPinDirection" type="ENUMERATION" 
                         value="PORT_PIN_IN">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="PortPinLevelValue" type="ENUMERATION" 
                         value="PORT_PIN_LEVEL_LOW">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="PortPinDSE" type="ENUMERATION" value="PORT_DS0">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="PortPinPE" type="ENUMERATION" 
                         value="PullDisabled">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="PortPinPS" type="ENUMERATION" value="PullDown">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:lst name="PortContainer" type="MAP">
                  <d:ctr name="PortContainer_CAN" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="5"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTE13_CAN_EN" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="1"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="141"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD0_CAN_STB" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="2"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="96"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE5_CAN_TX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="3"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="133"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="CAN0_TX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE4_CAN_RX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="4"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="132"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="CAN0_RX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD1_CAN_ERR" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="5"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="97"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_LIN" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="5"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTD9_LIN_SLP" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="6">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="105"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD14_LIN1_TX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="7">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="110"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="LIN1_TX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD13_LIN1_RX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="8">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="109"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="LIN1_RX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTE12_LIN2_TX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="9">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="140"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="LIN2_TX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD17_LIN2_RX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="10">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="113"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="LIN2_RX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_UART" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="3"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTB0_USART0_RX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="11">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="32"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="UART0_RX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB1_USART0_TX" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="12">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="33"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="UART0_TX"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE1_USART0_EN" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="78"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="129"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_PWM" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="16"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTE11_HEAT_PWM" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="13">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="139"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH5"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE10_FAN_PWM" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="14">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="138"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH4"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA3_WAIST_PWM1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="15">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="3"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH1"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA2_WAIST_PWM2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="16">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="2"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH0"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD16_IN1_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="17">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="112"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH1"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD15_IN1_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="18">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="111"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH0"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE9_IN2_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="19">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="137"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH7"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE8_IN2_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="20">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="136"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH6"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB5_IN3_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="21">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="37"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH5"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB4_IN3_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="22">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="36"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM0_CH4"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD5_IN4_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="23">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="101"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH3"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD12_IN4_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="24">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="108"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH2"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD11_IN5_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="25">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="107"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH1"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD10_IN5_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="26">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="106"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM2_CH0"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD3_IN6_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="27">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="99"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH5"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW"/>
                      </d:ctr>
                      <d:ctr name="PTD2_IN6_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="28">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="98"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH4"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_ADC" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="18"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTC1_ADC_KL30_DET" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="29">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="65"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE9"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE2_KEY4_ADC" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="30">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="130"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE10"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE6_KEY3_ADC" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="31">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="134"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE11"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA15_KEY2_ADC" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="32">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="15"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE12"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA16_KEY1_ADC" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="33">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="16"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE13"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC0_NTC_AD1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="34">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="64"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE8"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC17_NTC_AD2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="35">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="81"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE15"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC16_NTC_AD3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="36">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="80"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE14"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC15_NTC_AD4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="37">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="79"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE13"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC14_NTC_AD5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="38">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="78"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE12"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB3_NTC_AD6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="39">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="35"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE7"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA7_IPROPI_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="40">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="7"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE3"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA6_IPROPI_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="41">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="6"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE2"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB16_IPROPI_3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="42">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="48"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE15"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB15_IPROPI_4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="43">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="47"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE14"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB14_IPROPI_5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="44">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="46"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE9_ADC0_SE9"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB13_IPROPI_6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="45">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="45"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE8_ADC0_SE8"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB2_NTC_DET" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="80"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="34"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC0_SE6"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_DRV8145" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="18"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTC3_NFAULT_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="46">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="67"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTD6_NFAULT_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="47">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="102"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB17_NFAULT_3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="48">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="49"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB9_NFAULT_4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="49">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="41"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC7_NFAULT_5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="50">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="71"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA14_NFAULT_6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="51">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="14"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE14_NSLEEP_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="52">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="142"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTC2_NSLEEP_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="53">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="66"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD8_NSLEEP_3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="54">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="104"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTB12_NSLEEP_4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="55">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="44"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTB8_NSLEEP_5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="56">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="40"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTC10_NSLEEP_6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="57">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="74"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTE3_DRVOFF_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="58">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="131"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD7_DRVOFF_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="59">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="103"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTA17_DRVOFF_3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="60">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="17"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTD4_DRVOFF_4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="61">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="100"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTA1_DRVOFF_5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="62">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="1"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                      <d:ctr name="PTC11_DRVOFF_6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="63">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="75"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_HIGH"/>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_HALL" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="6"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTC13_MCU_HALL_1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="64">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="77"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH7"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC12_MCU_HALL_2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="65">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="76"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM3_CH6"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA13_MCU_HALL_3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="66">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="13"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM1_CH7"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA12_MCU_HALL_4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="67">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="12"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM1_CH6"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC9_MCU_HALL_5" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="68">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="73"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM5_CH0"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC8_MCU_HALL_6" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="69">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="72"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="PWM5_CH1"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_KEY" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="4"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTB11_KEY1" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="70">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="43"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTB10_KEY2" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="71">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="42"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA0_KEY3" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="72">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="0"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullEnabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTC6_KEY4" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" value="true"/>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="73">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="70"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="ADC1_SE4"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled"/>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullUp"/>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_Conctrl" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="4"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTA8_VB_12V_EN" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="74">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="8"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA9_AIR_12V_EN" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="75">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="9"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE16_MCU_HOLD" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="76">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="144"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTE15_Motor_PWR_EN" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="81">
                          <a:a name="IMPORTER_INFO">
                            <a:v>@DEF</a:v>
                            <a:v>@CALC</a:v>
                          </a:a>
                        </d:var>
                        <d:var name="PortPinPcr" type="INTEGER" value="143"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="GPIO">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_OUT"/>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PortContainer_NoUse" type="IDENTIFIABLE">
                    <d:var name="PortNumberOfPortPins" type="INTEGER" value="2"/>
                    <d:lst name="PortPin" type="MAP">
                      <d:ctr name="PTE0" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="77"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="128"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="DISABLED"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PTA11" type="IDENTIFIABLE">
                        <d:var name="PortPinPFE" type="BOOLEAN" value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinInputEn" type="BOOLEAN" 
                               value="false">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirectionChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinModeChangeable" type="BOOLEAN" 
                               value="true">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinId" type="INTEGER" value="79"/>
                        <d:var name="PortPinPcr" type="INTEGER" value="11"/>
                        <d:var name="PortPinMode" type="ENUMERATION" 
                               value="DISABLED"/>
                        <d:var name="PinInputCon" type="ENUMERATION" 
                               value="SCHMITT">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDSE" type="ENUMERATION" 
                               value="PORT_DS0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPE" type="ENUMERATION" 
                               value="PullDisabled">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinPS" type="ENUMERATION" 
                               value="PullDown">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetDlp" type="ENUMERATION" 
                               value="PORT_KEEP_CONFIG">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinSetRetm" type="ENUMERATION" 
                               value="PORT_EXT_RTE">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinDirection" type="ENUMERATION" 
                               value="PORT_PIN_IN">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PinOutMode" type="ENUMERATION" 
                               value="PORT_PIN_OUT_PUSHUPULL">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PortPinLevelValue" type="ENUMERATION" 
                               value="PORT_PIN_LEVEL_LOW">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:lst name="UnTouchedPortPin" type="MAP">
                  <d:ctr name="PortPin_JTAG_TDI" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@REC"/>
                    <d:var name="PortPinPcr" type="INTEGER" value="69">
                      <a:a name="IMPORTER_INFO" value="@REC"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="PortPin_JTAG_TDO" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@REC"/>
                    <d:var name="PortPinPcr" type="INTEGER" value="10">
                      <a:a name="IMPORTER_INFO" value="@REC"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="PortPin_JTAG_TCK" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@REC"/>
                    <d:var name="PortPinPcr" type="INTEGER" value="68">
                      <a:a name="IMPORTER_INFO" value="@REC"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="PortPin_JTAG_TMS" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@REC"/>
                    <d:var name="PortPinPcr" type="INTEGER" value="4">
                      <a:a name="IMPORTER_INFO" value="@REC"/>
                    </d:var>
                  </d:ctr>
                  <d:ctr name="PortPin_Reset_b" type="IDENTIFIABLE">
                    <a:a name="IMPORTER_INFO" value="@REC"/>
                    <d:var name="PortPinPcr" type="INTEGER" value="5">
                      <a:a name="IMPORTER_INFO" value="@REC"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:lst name="DigitalFilter" type="MAP"/>
              </d:ctr>
              <d:ctr name="PortGeneral" type="IDENTIFIABLE">
                <d:var name="PortDevErrorDetect" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortHwDevErrorDetect" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortSetPinDirectionApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortSetPinModeApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortVersionInfoApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortSetPinModeDoesNotTouchGpioLevel" 
                       type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortSetAsUnusedPinApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortResetPinModeApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PortEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="124">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
