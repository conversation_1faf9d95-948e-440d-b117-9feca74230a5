/**
 * file    Flexio_Uart_Hw_PBcfg.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_UART_HW_PBCFG_H
#define FLEXIO_UART_HW_PBCFG_H

/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/
#include "Flexio_Uart_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define FLEXIO_UART_HW_PBCFG_VENDOR_ID                     (110U)
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION      (4U)
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION      (4U)
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION   (0U)
#define FLEXIO_UART_HW_PBCFG_SW_MAJOR_VERSION              (1U)
#define FLEXIO_UART_HW_PBCFG_SW_MINOR_VERSION              (0U)
#define FLEXIO_UART_HW_PBCFG_SW_PATCH_VERSION              (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Flexio_Uart_Hw_Types.h */
#if (FLEXIO_UART_HW_PBCFG_VENDOR_ID != FLEXIO_UART_HW_TYPES_VENDOR_ID)
    #error "Flexio_Uart_Hw_PBcfg.h and Flexio_Uart_Hw_Types.h have different vendor ids"
#endif
#if ((FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION   != FLEXIO_UART_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION   != FLEXIO_UART_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION!= FLEXIO_UART_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Flexio_Uart_Hw_PBcfg.h and Flexio_Uart_Hw_Types.h are different"
#endif
#if ((FLEXIO_UART_HW_PBCFG_SW_MAJOR_VERSION!= FLEXIO_UART_HW_TYPES_SW_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_SW_MINOR_VERSION!= FLEXIO_UART_HW_TYPES_SW_MINOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_SW_PATCH_VERSION!= FLEXIO_UART_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Flexio_Uart_Hw_PBcfg.h and Flexio_Uart_Hw_Types.h are different"
#endif


/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define FLEXIO_UART_HW_CONFIG_PB \

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_UART_HW_PBCFG_H */
