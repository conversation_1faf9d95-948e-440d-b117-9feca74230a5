/**
 * @file    Gpt_Cfg.h
 * @brief   Gpt Cfg Head file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 2.5, The macro '%1s' is declared but not used within this project.
 * REASON: The macro is not currently called at the Demo(User) or other Module
 */

#ifndef GPT_CFG_H
#define GPT_CFG_H

#include "Std_Types.h"

#include "Gpt_PBcfg.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define GPT_VENDOR_ID_CFG                    (110U)
#define GPT_AR_RELEASE_MAJOR_VERSION_CFG     (4U)
#define GPT_AR_RELEASE_MINOR_VERSION_CFG     (4U)
#define GPT_AR_RELEASE_REVISION_VERSION_CFG  (0U)
#define GPT_SW_MAJOR_VERSION_CFG             (1U)
#define GPT_SW_MINOR_VERSION_CFG             (0U)
#define GPT_SW_PATCH_VERSION_CFG             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and GPT configuration header file are of the same vendor */
#if (GPT_VENDOR_ID_PBCFG_H != GPT_VENDOR_ID_CFG)
    #error "Gpt_PBcfg.h and Gpt_Cfg.h have different vendor IDs"
#endif
    /* Check if header file and Gpt configuration header file are of the same Autosar version */
#if ((GPT_AR_RELEASE_MAJOR_VERSION_PBCFG_H != GPT_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (GPT_AR_RELEASE_MINOR_VERSION_PBCFG_H != GPT_AR_RELEASE_MINOR_VERSION_CFG) || \
     (GPT_AR_RELEASE_REVISION_VERSION_PBCFG_H != GPT_AR_RELEASE_REVISION_VERSION_CFG) \
    )
#error "AutoSar Version Numbers of Gpt_PBcfg.h and Gpt_Cfg.h are different"
#endif
/* Check if header file and Gpt configuration header file are of the same software version */
#if ((GPT_SW_MAJOR_VERSION_PBCFG_H != GPT_SW_MAJOR_VERSION_CFG) || \
     (GPT_SW_MINOR_VERSION_PBCFG_H != GPT_SW_MINOR_VERSION_CFG) || \
     (GPT_SW_PATCH_VERSION_PBCFG_H != GPT_SW_PATCH_VERSION_CFG) \
    )
#error "Software Version Numbers of Gpt_PBcfg.h and Gpt_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((GPT_AR_RELEASE_MAJOR_VERSION_CFG != STD_AR_RELEASE_MAJOR_VERSION) || \
         (GPT_AR_RELEASE_MINOR_VERSION_CFG != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Gpt_Cfg.h and Std_Types.h are different"
    #endif
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
 * @brief    GPT_DEV_ERROR_DETECT switch
 * @details  Enable/disable GPT_DEV_ERROR_DETECT.
 */
#define GPT_DEV_ERROR_DETECT (STD_ON)
/*================================================================================================*/
/**
 * @brief    GPT_DEINIT_API switch
 * @details  Enable/disable GPT_DEINIT_API.
 */
#define GPT_DEINIT_API (STD_ON)
/*================================================================================================*/
/**
 * @brief    GPT_TIME_ELAPSED_API switch
 * @details  Enable/disable GPT_VERSION_INFO_API.
 */
#define GPT_TIME_ELAPSED_API (STD_ON)
/*================================================================================================*/
/**
 * @brief    GPT_ENABLE_DISABLE_NOTIFICATION_API switch
 * @details  Enable/disable GPT_VERSION_INFO_API.
 */
#define GPT_ENABLE_DISABLE_NOTIFICATION_API (STD_ON)
/*================================================================================================*/
/**
 * @brief    GPT_WAKEUP_FUNCTIONALITY_API switch
 * @details  Enable/disable GPT_VERSION_INFO_API.
 */
#define GPT_WAKEUP_FUNCTIONALITY_API (STD_OFF)
/*================================================================================================*/
/**
 * @brief    GPT_TIME_REMAINING_API switch
 * @details  Enable/disable GPT_VERSION_INFO_API.
 */
#define GPT_TIME_REMAINING_API (STD_ON)
/*================================================================================================*/
/**
 * @brief    GPT_VERSION_INFO_API switch
 * @details  Enable/disable GPT_VERSION_INFO_API.
 */
#define GPT_VERSION_INFO_API (STD_ON)
/*================================================================================================*/

/**
 * @brief    GPT_CHANGE_NEXT_TIMEOUT_VALUE switch
 * @details  Enable/disable support for changing timeout value during timer running
 */
#define GPT_CHANGE_NEXT_TIMEOUT_VALUE     (STD_ON)

/*================================================================================================*/
/**
 * @brief    GPT_REPORT_WAKEUP_SOURCE switch
 * @details  Enable/disable GPT_REPORT_WAKEUP_SOURCE.
 */
#define GPT_REPORT_WAKEUP_SOURCE (STD_OFF)

/*================================================================================================*/
/**
 * @brief    GPT_CHAIN_MODE switch
 * @details  Enable/disable API for Chain Mode.
 */
#define GPT_CHAIN_MODE (STD_ON)

/*================================================================================================*/
/**
 * @brief    GPT_PRECOMPILE_SUPPORT Switch
 * @details  Enable/disable GPT_PRECOMPILE_SUPPORT.
 */
#define GPT_PRECOMPILE_SUPPORT (STD_OFF)
/*================================================================================================*/
/**
 * @brief       GPT_HW_CHANNEL_NUM
 * @details     The maximum number of HW channels. This is used to allocate memory space for channel runtime info.
 */
#define GPT_HW_CHANNEL_NUM  (1U)

/*================================================================================================*/
/**
 * @brief        GPT_NUM_CONFIG.
 * @details      Number of ChannelConfiguration
 */
#define GPT_NUM_CONFIG             (1U)

/*================================================================================================*/
/**
 * @brief        Define symbolic names of channels
 * @details      Symbolic names of channels.
 */
#define GptConf_GptChannelConfiguration_GptChannelConfiguration_0  (0U)
    

/*================================================================================================*/
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define GPT_CONFIG_EXT \
       GPT_CONFIG_PB
/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif  /* GPT_CFG_H */
