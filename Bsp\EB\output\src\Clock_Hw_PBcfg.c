/**
 * *************************************************************************
 * @file   Clock_Hw_PBcfg.c
 * @brief Code template for Post-Build(PB) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Clock_Hw_PBcfg.h"
#include "Std_Types.h"
#include "Clock_Hw.h"
#include "Clock_Hw_Private.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CLOCK_HW_PBCFG_VENDOR_ID_C                      (110U)
#define CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C       (4U)
#define CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C       (4U)
#define CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C    (0U)
#define CLOCK_HW_PBCFG_SW_MAJOR_VERSION_C               (1U)
#define CLOCK_HW_PBCFG_SW_MINOR_VERSION_C               (0U)
#define CLOCK_HW_PBCFG_SW_PATCH_VERSION_C               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_PBcfg.h file are of the same vendor */
#if (CLOCK_HW_PBCFG_VENDOR_ID_C != CLOCK_HW_PBCFG_VENDOR_ID)
    #error "Clock_Hw_PBcfg.c and Clock_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_PBcfg.h file are of the same Autosar version */
#if ((CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw_PBcfg.h are different"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_PBcfg.h file are of the same Software version */
#if ((CLOCK_HW_PBCFG_SW_MAJOR_VERSION_C != CLOCK_HW_PBCFG_SW_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_MINOR_VERSION_C != CLOCK_HW_PBCFG_SW_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_PATCH_VERSION_C != CLOCK_HW_PBCFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw_PBcfg.h are different"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw.h file are of the same vendor */
#if (CLOCK_HW_PBCFG_VENDOR_ID_C != CLOCK_HW_VENDOR_ID)
    #error "Clock_Hw_PBcfg.c and Clock_Hw.h have different vendor ids"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw.h file are of the same Autosar version */
#if ((CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw.h are different"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw.h file are of the same Software version */
#if ((CLOCK_HW_PBCFG_SW_MAJOR_VERSION_C != CLOCK_HW_SW_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_MINOR_VERSION_C != CLOCK_HW_SW_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_PATCH_VERSION_C != CLOCK_HW_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw.h are different"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_Private.h file are of the same vendor */
#if (CLOCK_HW_PBCFG_VENDOR_ID_C != CLOCK_HW_PRIVATE_VENDOR_ID)
    #error "Clock_Hw_PBcfg.c and Clock_Hw_Private.h have different vendor ids"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_Private.h file are of the same Autosar version */
#if ((CLOCK_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw_Private.h are different"
#endif

/* Check if Clock_Hw_PBcfg.c file and Clock_Hw_Private.h file are of the same Software version */
#if ((CLOCK_HW_PBCFG_SW_MAJOR_VERSION_C != CLOCK_HW_PRIVATE_SW_MAJOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_MINOR_VERSION_C != CLOCK_HW_PRIVATE_SW_MINOR_VERSION) || \
     (CLOCK_HW_PBCFG_SW_PATCH_VERSION_C != CLOCK_HW_PRIVATE_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_PBcfg.c and Clock_Hw_Private.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/


#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"










/* *************************************************************************
 * Configuration structure for Clock Configuration 0
 * ************************************************************************* */
 /*! @brief User Configuration structure clock_Cfg_0 */
const Clock_Hw_ClockConfigType_t Mcu_aClockConfigPB[2U] = {

    {
        0U,                                 /* clkConfigId */
        (NULL_PTR),                                 /* Register data if register value optimization is enabled */

        2U,                                 /* ircoscsCount */
        1U,                                 /* xoscsCount */
        1U,                                 /* pllsCount */
        12U,                                /* selectorsCount */
        20U,                                /* dividersCount */
        0U,                                 /* dividerTriggersCount */
        0U,                                /* fracDivsCount */
        0U,                                /* extClksCount */
        63U,                                 /* gatesCount */
        0U,                                 /* pcfsCount */
        3U,                                /* cmusCount  NumOfUnderClkMonitor */
        28U,                                /* configureFrequenciesCount */

        /* IRCOSC initialization. */
        {
            #if CLOCK_HW_IRCOSCS_NO > 0U
            {
                HRC_CLK,    /* Clock name associated to Ircosc */
                1U,                       /* Enable ircosc */
                0U,              /* Enable regulator */
                0U,                     										/* Ircosc range   GetIrcoscRange */
                0U,       /* Ircosc enable in VLP mode  */
                0U,           /* Ircosc enable in STOP mode  */
                1U,             /* Ircosc output is HRCRDY gated  */
            },
            #endif
            #if CLOCK_HW_IRCOSCS_NO > 1U
            {
                MRC_CLK,    /* Clock name associated to Ircosc */
                1U,                       /* Enable ircosc */
                0U,              /* Enable regulator */
                0U,                     										/* Ircosc range   GetIrcoscRange */
                0U,       /* Ircosc enable in VLP mode  */
                0U,           /* Ircosc enable in STOP mode  */
                0U,             /* Ircosc output is HRCRDY gated  */
            },
            #endif
        },

        /* XOSC initialization. */
        {
            #if CLOCK_HW_XOSCS_NO > 0U
            {
                SOSC_CLK,                    /* Clock name associated to xosc */
                16000000U,                    /* External oscillator frequency. */
                1U,                           /* Enable xosc. */
                0U,                                                             /* Startup stabilization time. */
                1U,                           /* XOSC bypass option */
                0U,                           /* Comparator enable */
                0U,                          /* Crystal overdrive protection */
                2U,                    /* High gain value */
                0U,       /* Monitor type GetXoscClockMonitorType*/
                0U,                          /* Automatic level controller */
                4U,                    /* Work current */
                INTER_1MO,                          /* Work rfsel */
                1U,                       /* Work rfen */
                CRYSTAL_TYPE,                       /* GetXoscAGCBypassEn */
                
            },
            #endif

        },

        /* PLL initialization. */
        {
            #if CLOCK_HW_PLLS_NO > 0U
            {
                SPLL_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
                SOSC_CLK,                    /*!<     inputReference           */
                0U,                           /*!<     bypass;                  */
                19U,                           /*!<     predivider;              */
                0U,                           /*!<     GetPlldivN;              */
                1U,                           /*!<     postdivider  GetPlldivP;              */              
                2U,                           /*!<     PLL-DIV1;              */
                2U,                           /*!<     PLL-DIV2;              */
                0U,                           /*!<     numeratorFracLoopDiv;    */
                0U,                           /*!<     mulFactorDiv;            */
                0U,                           /*!<     ModulationBypass;              */
                0U,                           /*!<     modulationType;          */
                0U,                           /*!<     modulationPeriod;        */
                0U,                           /*!<     incrementStep;           */
                0U,                           /*!<     sigmaDelta;              */
                0U,                           /*!<     ditherControl;           */
                0U,                           /*!<     ditherControlValue;      */
                CLOCK_HW_HAS_MONITOR_DISABLE,        /*!<     Monitor type             */
                {                             /*!<     Dividers */
                        0U,
                        0U,
                        0U,
                },
            },
            #endif

        },

        /* SELECTOR initialization. */
        {

            #if CLOCK_HW_SELECTORS_NO > 0U
            {
                SYS_PRE_CLK,             						/* Clock name associated to selector   "concat('SCS_',$NameSelector,'_CLK')"*/
                SPLL_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 1U
            {
                CLKOUT_CLK,                     /* Clock name associated to selector */
                SPLL_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 2U
            {
                LRC_CLK,                     /* Clock name associated to selector */
                LRC_128K_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 3U
            {
                SPI0_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 4U
            {
                SPI1_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 5U
            {
                SPI2_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 6U
            {
                PIT_CLK,                      /* Clock name associated to selector */
                TOP_PCLK_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 7U
            {
                RTC_CLK,                     /* Clock name associated to selector */
                LRC_32K_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 8U
            {
                CAN0_CLK,                      /* Clock name associated to selector */
                SPLL_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 9U
            {
                CAN1_CLK,                      /* Clock name associated to selector */
                SPLL_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 10U
            {
                CAN2_CLK,                      /* Clock name associated to selector */
                SPLL_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 11U
            {
                CAN3_CLK,                      /* Clock name associated to selector */
                SPLL_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 12U
            {
                WKTMR_CLK,                      /* Clock name associated to selector */
                LRC_128K_CLK,            /* Name of the selected input source */
            },
            #endif
            
        },

        /* DIVIDER initialization. */
        {
        
            #if CLOCK_HW_DIVIDERS_NO > 0U
            {
                MRC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 1U
            {
                MRC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 2U
            {
                HRC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 3U
            {
                HRC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 4U
            {
                OSC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 5U
            {
                OSC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 6U
            {
                SPLL_DIV1_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 7U
            {
                SPLL_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 8U
            {
                SYS_DIV_CLK,
                0U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 9U
            {
                GPIO_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 10U
            {
                IPT_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 11U
            {
                IPB_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 12U
            {
                TOP_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 13U
            {
                AFE_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 14U
            {
                CAN_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 15U
            {
                ADC1_DIV_CLK,
                7U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 16U
            {
                ADC0_DIV_CLK,
                7U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 17U
            {
                CLKOUT_CLK,
                4U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 18U
            {
                GPIO_DEBOUNCE_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
					#if CLOCK_HW_DIVIDERS_NO > 19U
					{
						EWM_FUN_CLK,
						3U,
						{
							1U,
						}
					},
					#endif
			   
        },

        /* DIVIDER TRIGGER Initialization. */
        {
            {
                RESERVED_CLK,               /* divider name */
                IMMEDIATE_DIVIDER_UPDATE,   /* trigger value */
                RESERVED_CLK,               /* input source name */
            },
        },

        /* FRACTIONAL DIVIDER initialization. */
        {
            {
                RESERVED_CLK,
                0U,
                {
                    0U,
                    0U,
                },
            },
        },

        /* EXTERNAL CLOCKS initialization. */
        {
            {
				CLOCK_IS_OFF,
				0U,
            },

        },

        /* CLOCK GATE initialization. */
        {



            #if CLOCK_HW_GATES_NO > 0U
            {
                RTC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 1U
            {
                RTC_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 2U
            {
                HSM_SLV_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 3U
            {
                HSM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 4U
            {
                FLASH_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 5U
            {
                GPIO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 6U
            {
                IO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 7U
            {
                PWM0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 8U
            {
                PWM1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 9U
            {
                PWM2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 10U
            {
                PWM3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 11U
            {
                LIN0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 12U
            {
                LIN1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 13U
            {
                LIN2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 14U
            {
                LIN3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 15U
            {
                SPI0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 16U
            {
                SPI0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 17U
            {
                SPI1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 18U
            {
                SPI1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 19U
            {
                SPI2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 20U
            {
                SPI2_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 21U
            {
                UART0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 22U
            {
                UART1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 23U
            {
                UART2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 24U
            {
                I2C0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 25U
            {
                I2C1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 26U
            {
                FLEXIO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 27U
            {
                PIT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 28U
            {
                PIT_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 29U
            {
                TRGMUX_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 30U
            {
                STM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 31U
            {
                STM_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 32U
            {
                WDT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 33U
            {
                CRC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 34U
            {
                SENSOR_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 35U
            {
                EWM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 36U
            {
                EWM_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 37U
            {
                ADC0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 38U
            {
                ADC0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 39U
            {
                ADC1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 40U
            {
                ADC1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 41U
            {
                CMP_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 42U
            {
                CMP_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 43U
            {
                PDB0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 44U
            {
                PDB0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 45U
            {
                PDB1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 46U
            {
                PDB1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 47U
            {
                CAN0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 48U
            {
                CAN0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 49U
            {
                CAN1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 50U
            {
                CAN1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 51U
            {
                CAN2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 52U
            {
                CAN2_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 53U
            {
                CAN3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 54U
            {
                CAN3_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 55U
            {
                CANSRAM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 56U
            {
                SRAM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 57U
            {
                MPU_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 58U
            {
                PMU_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 59U
            {
                WKTMR_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif











            #if CLOCK_HW_GATES_NO > 60U
            {
                LRC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif






            #if CLOCK_HW_GATES_NO > 61U
            {
                CLKOUT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif






            #if CLOCK_HW_GATES_NO > 62U
            {
                SOCCTRL_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif



            {
                RESERVED_CLK,           /* name */
                0U,                     /* enable */
            },
        },

        /* PCFS initialization. */
        {
            {
                RESERVED_CLK,
                0U,
                0U,
                RESERVED_CLK,
                0U,
            },
        },

        /* Clock monitor */ 
        {     

            #if CLOCK_HW_CMUS_NO > 0U
            {
                HRC_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU0_HRC_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				MRC_CLK,
				6U,
				41U,
				32U,
            },
            #endif

            #if CLOCK_HW_CMUS_NO > 1U
            {
                SPLL_VCO_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU1_SPLL_VCO_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				HRC_CLK,
				6U,
				40U,
				16U,
            },
            #endif

            #if CLOCK_HW_CMUS_NO > 2U
            {
                SOSC_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU2_SOSC_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				MRC_CLK,
				9U,
				12U,
				5U,
            },
            #endif
        },

        /* Specific peripheral initialization. */
        {
            0U,
            {
                {
                    RESERVED_VALUE,
                    0U,
                },
            },
        },
        
        /* Flash param initialization*/
        {
            {
                /* When erasing FLASH pages, the time from the upper edge of NVSTR to the lower edge of ERASE is established */
                (uint32) 1616000UL,
                /* When erasing FALSH MASS and BLOCK, the time from the upper edge of NVSTR to the lower edge of ERASE is established */
                (uint32) 3232000UL,
                /* When erasing FLASH MASS, hold time from ERASE bottom edge to NVSTR bottom edge */
                (uint16) 16160U,
                /* FLASH recovery time */
                (uint16) 1616U,
                 /* Hold time from FLASH ROG bottom edge to NVSTR bottom edge */
                (uint16) 808U,
                /* FLASH programming hold time, i.e. the hold time from SE bottom edge to PROG bottom edge */
                (uint16) 4U,
                /* FLASH YE programming time */
                (uint16) 1293U,
                /* FLASH NVSTR upper edge to YE upper edge hold time */
                (uint16) 324U,
                /* Establishment time from FLASH ROG/ERASE to NVSTR */
                (uint16) 1293U,
                /* FLASH read cycle time */
                (uint8)  6U,
                /* FLASH access time. From the upper edge of SE to the effective time of data */
                (uint8)  4U,
                /* FLASH SE positive edge width configuration */
                (uint8)  1U,
                /* FLASH RECALL to XE/YE hold time */
                (uint8)  4U,
                /* FLASH RECALL to SE lower edge holding time */
                (uint8)  20U,
                /* FLASH RECALL High Level Hold Time */
                (uint8)  17U,
                /* FLASH RECALL to SE upper edge establishment time */
                (uint8)  4U,
            }
        },
        /* Configured frequency values. */
        {
            {
                CLOCK_IS_OFF,
                0U,
            },
            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 1U
            {
                HRC_CLK,
                48000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 2U
            {
                SOSC_CLK,
                16000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 3U
            {
                MRC_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 4U
            {
                SPLL_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 5U
            {
                HRC_DIV1_CLK,
                48000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 6U
            {
                HRC_DIV2_CLK,
                24000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 7U
            {
                OSC_DIV1_CLK,
                16000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 8U
            {
                OSC_DIV2_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 9U
            {
                MRC_DIV1_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 10U
            {
                MRC_DIV2_CLK,
                4000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 11U
            {
                SPLL_DIV1_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 12U
            {
                SPLL_DIV2_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 13U
            {
                LRC_CLK,
                128000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 14U
            {
                RTC_CLK,
                32000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 15U
            {
                SYS_PRE_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 16U
            {
                SYS_DIV_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 17U
            {
                ADC0_DIV_CLK,
                20000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 18U
            {
                ADC1_DIV_CLK,
                20000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 19U
            {
                CAN_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 20U
            {
                AFE_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 21U
            {
                TOP_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 22U
            {
                IPB_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 23U
            {
                IPT_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 24U
            {
                GPIO_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 25U
            {
                PMU_SYS_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 26U
            {
                CLKOUT_CLK,
                40000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 27U
            {
                ADC_TRIG_CLK,
                160000000U,
            },
            #endif

        },

    },

    {
        1U,                                 /* clkConfigId */
        (NULL_PTR),                                 /* Register data if register value optimization is enabled */

        2U,                                 /* ircoscsCount */
        1U,                                 /* xoscsCount */
        1U,                                 /* pllsCount */
        12U,                                /* selectorsCount */
        20U,                                /* dividersCount */
        0U,                                 /* dividerTriggersCount */
        0U,                                /* fracDivsCount */
        0U,                                /* extClksCount */
        63U,                                 /* gatesCount */
        0U,                                 /* pcfsCount */
        3U,                                /* cmusCount  NumOfUnderClkMonitor */
        28U,                                /* configureFrequenciesCount */

        /* IRCOSC initialization. */
        {
            #if CLOCK_HW_IRCOSCS_NO > 0U
            {
                HRC_CLK,    /* Clock name associated to Ircosc */
                1U,                       /* Enable ircosc */
                0U,              /* Enable regulator */
                0U,                     										/* Ircosc range   GetIrcoscRange */
                0U,       /* Ircosc enable in VLP mode  */
                0U,           /* Ircosc enable in STOP mode  */
                1U,             /* Ircosc output is HRCRDY gated  */
            },
            #endif
            #if CLOCK_HW_IRCOSCS_NO > 1U
            {
                MRC_CLK,    /* Clock name associated to Ircosc */
                1U,                       /* Enable ircosc */
                0U,              /* Enable regulator */
                0U,                     										/* Ircosc range   GetIrcoscRange */
                0U,       /* Ircosc enable in VLP mode  */
                0U,           /* Ircosc enable in STOP mode  */
                0U,             /* Ircosc output is HRCRDY gated  */
            },
            #endif
        },

        /* XOSC initialization. */
        {
            #if CLOCK_HW_XOSCS_NO > 0U
            {
                SOSC_CLK,                    /* Clock name associated to xosc */
                8000000U,                    /* External oscillator frequency. */
                1U,                           /* Enable xosc. */
                0U,                                                             /* Startup stabilization time. */
                1U,                           /* XOSC bypass option */
                0U,                           /* Comparator enable */
                0U,                          /* Crystal overdrive protection */
                2U,                    /* High gain value */
                0U,       /* Monitor type GetXoscClockMonitorType*/
                0U,                          /* Automatic level controller */
                4U,                    /* Work current */
                INTER_1MO,                          /* Work rfsel */
                1U,                       /* Work rfen */
                CRYSTAL_TYPE,                       /* GetXoscAGCBypassEn */
                
            },
            #endif

        },

        /* PLL initialization. */
        {
            #if CLOCK_HW_PLLS_NO > 0U
            {
                SPLL_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
                MRC_CLK,                    /*!<     inputReference           */
                0U,                           /*!<     bypass;                  */
                39U,                           /*!<     predivider;              */
                0U,                           /*!<     GetPlldivN;              */
                1U,                           /*!<     postdivider  GetPlldivP;              */              
                1U,                           /*!<     PLL-DIV1;              */
                2U,                           /*!<     PLL-DIV2;              */
                0U,                           /*!<     numeratorFracLoopDiv;    */
                0U,                           /*!<     mulFactorDiv;            */
                0U,                           /*!<     ModulationBypass;              */
                0U,                           /*!<     modulationType;          */
                0U,                           /*!<     modulationPeriod;        */
                0U,                           /*!<     incrementStep;           */
                0U,                           /*!<     sigmaDelta;              */
                0U,                           /*!<     ditherControl;           */
                0U,                           /*!<     ditherControlValue;      */
                CLOCK_HW_HAS_MONITOR_DISABLE,        /*!<     Monitor type             */
                {                             /*!<     Dividers */
                        0U,
                        0U,
                        0U,
                },
            },
            #endif

        },

        /* SELECTOR initialization. */
        {

            #if CLOCK_HW_SELECTORS_NO > 0U
            {
                SYS_PRE_CLK,             						/* Clock name associated to selector   "concat('SCS_',$NameSelector,'_CLK')"*/
                SPLL_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 1U
            {
                CLKOUT_CLK,                     /* Clock name associated to selector */
                MRC_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 2U
            {
                LRC_CLK,                     /* Clock name associated to selector */
                LRC_128K_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 3U
            {
                SPI0_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 4U
            {
                SPI1_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 5U
            {
                SPI2_CLK,                      /* Clock name associated to selector */
                HRC_DIV1_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 6U
            {
                PIT_CLK,                      /* Clock name associated to selector */
                IPT_PCLK_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 7U
            {
                RTC_CLK,                     /* Clock name associated to selector */
                LRC_32K_CLK,                       /* Name of the selected input source */
            },
            #endif
        
            #if CLOCK_HW_SELECTORS_NO > 8U
            {
                CAN0_CLK,                      /* Clock name associated to selector */
                HRC_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 9U
            {
                CAN1_CLK,                      /* Clock name associated to selector */
                HRC_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 10U
            {
                CAN2_CLK,                      /* Clock name associated to selector */
                HRC_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 11U
            {
                CAN3_CLK,                      /* Clock name associated to selector */
                HRC_DIV2_CLK,            /* Name of the selected input source */
            },
            #endif
            
            #if CLOCK_HW_SELECTORS_NO > 12U
            {
                WKTMR_CLK,                      /* Clock name associated to selector */
                LRC_128K_CLK,            /* Name of the selected input source */
            },
            #endif
            
        },

        /* DIVIDER initialization. */
        {
        
            #if CLOCK_HW_DIVIDERS_NO > 0U
            {
                MRC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 1U
            {
                MRC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 2U
            {
                HRC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 3U
            {
                HRC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 4U
            {
                OSC_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 5U
            {
                OSC_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 6U
            {
                SPLL_DIV1_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 7U
            {
                SPLL_DIV2_CLK,
                2U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 8U
            {
                SYS_DIV_CLK,
                0U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 9U
            {
                GPIO_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 10U
            {
                IPT_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 11U
            {
                IPB_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 12U
            {
                TOP_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 13U
            {
                AFE_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 14U
            {
                CAN_PCLK_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 15U
            {
                ADC1_DIV_CLK,
                3U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 16U
            {
                ADC0_DIV_CLK,
                3U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 17U
            {
                CLKOUT_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
            #if CLOCK_HW_DIVIDERS_NO > 18U
            {
                GPIO_DEBOUNCE_CLK,
                1U,
                {
                    0U,
                }
            },
            #endif
        
					#if CLOCK_HW_DIVIDERS_NO > 19U
					{
						EWM_FUN_CLK,
						1U,
						{
							1U,
						}
					},
					#endif
			   
        },

        /* DIVIDER TRIGGER Initialization. */
        {
            {
                RESERVED_CLK,               /* divider name */
                IMMEDIATE_DIVIDER_UPDATE,   /* trigger value */
                RESERVED_CLK,               /* input source name */
            },
        },

        /* FRACTIONAL DIVIDER initialization. */
        {
            {
                RESERVED_CLK,
                0U,
                {
                    0U,
                    0U,
                },
            },
        },

        /* EXTERNAL CLOCKS initialization. */
        {
            {
				CLOCK_IS_OFF,
				0U,
            },

        },

        /* CLOCK GATE initialization. */
        {



            #if CLOCK_HW_GATES_NO > 0U
            {
                RTC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 1U
            {
                RTC_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 2U
            {
                HSM_SLV_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 3U
            {
                HSM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 4U
            {
                FLASH_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 5U
            {
                GPIO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 6U
            {
                IO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 7U
            {
                PWM0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 8U
            {
                PWM1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 9U
            {
                PWM2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 10U
            {
                PWM3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 11U
            {
                LIN0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 12U
            {
                LIN1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 13U
            {
                LIN2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 14U
            {
                LIN3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 15U
            {
                SPI0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 16U
            {
                SPI0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 17U
            {
                SPI1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 18U
            {
                SPI1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 19U
            {
                SPI2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 20U
            {
                SPI2_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 21U
            {
                UART0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 22U
            {
                UART1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 23U
            {
                UART2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 24U
            {
                I2C0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 25U
            {
                I2C1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 26U
            {
                FLEXIO_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 27U
            {
                PIT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 28U
            {
                PIT_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 29U
            {
                TRGMUX_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 30U
            {
                STM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 31U
            {
                STM_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 32U
            {
                WDT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 33U
            {
                CRC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 34U
            {
                SENSOR_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 35U
            {
                EWM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 36U
            {
                EWM_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 37U
            {
                ADC0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 38U
            {
                ADC0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 39U
            {
                ADC1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 40U
            {
                ADC1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 41U
            {
                CMP_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 42U
            {
                CMP_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 43U
            {
                PDB0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 44U
            {
                PDB0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 45U
            {
                PDB1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 46U
            {
                PDB1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 47U
            {
                CAN0_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 48U
            {
                CAN0_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 49U
            {
                CAN1_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 50U
            {
                CAN1_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 51U
            {
                CAN2_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 52U
            {
                CAN2_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 53U
            {
                CAN3_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 54U
            {
                CAN3_FUN_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 55U
            {
                CANSRAM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 56U
            {
                SRAM_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 57U
            {
                MPU_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 58U
            {
                PMU_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif







            #if CLOCK_HW_GATES_NO > 59U
            {
                WKTMR_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif











            #if CLOCK_HW_GATES_NO > 60U
            {
                LRC_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif






            #if CLOCK_HW_GATES_NO > 61U
            {
                CLKOUT_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif






            #if CLOCK_HW_GATES_NO > 62U
            {
                SOCCTRL_CLK,                  /*!<     name;                    */
                1U,                           /*!<     enable;                  */
            },
            #endif



            {
                RESERVED_CLK,           /* name */
                0U,                     /* enable */
            },
        },

        /* PCFS initialization. */
        {
            {
                RESERVED_CLK,
                0U,
                0U,
                RESERVED_CLK,
                0U,
            },
        },

        /* Clock monitor */ 
        {     

            #if CLOCK_HW_CMUS_NO > 0U
            {
                HRC_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU0_HRC_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				MRC_CLK,
				6U,
				39U,
				32U,
            },
            #endif

            #if CLOCK_HW_CMUS_NO > 1U
            {
                SPLL_VCO_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU1_SPLL_VCO_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				MRC_CLK,
				6U,
				124U,
				115U,
            },
            #endif

            #if CLOCK_HW_CMUS_NO > 2U
            {
                SOSC_CLK,            /* Clock name associated to clock monitor. */
                0U,                   /*Enable/disable clock monitor CMU2_SOSC_CLK */
                0,
				
                0U,
				0U,
				0U,
				0U,
				
				0,
                {                                               
                    0U,          /* Start index in register values array */
                    0U,          /* End index in register values array */
                },
				MRC_CLK,
				9U,
				12U,
				5U,
            },
            #endif
        },

        /* Specific peripheral initialization. */
        {
            0U,
            {
                {
                    RESERVED_VALUE,
                    0U,
                },
            },
        },
        
        /* Flash param initialization*/
        {
            {
                /* When erasing FLASH pages, the time from the upper edge of NVSTR to the lower edge of ERASE is established */
                (uint32) 1616000UL,
                /* When erasing FALSH MASS and BLOCK, the time from the upper edge of NVSTR to the lower edge of ERASE is established */
                (uint32) 3232000UL,
                /* When erasing FLASH MASS, hold time from ERASE bottom edge to NVSTR bottom edge */
                (uint16) 16160U,
                /* FLASH recovery time */
                (uint16) 1616U,
                 /* Hold time from FLASH ROG bottom edge to NVSTR bottom edge */
                (uint16) 808U,
                /* FLASH programming hold time, i.e. the hold time from SE bottom edge to PROG bottom edge */
                (uint16) 4U,
                /* FLASH YE programming time */
                (uint16) 1293U,
                /* FLASH NVSTR upper edge to YE upper edge hold time */
                (uint16) 324U,
                /* Establishment time from FLASH ROG/ERASE to NVSTR */
                (uint16) 1293U,
                /* FLASH read cycle time */
                (uint8)  6U,
                /* FLASH access time. From the upper edge of SE to the effective time of data */
                (uint8)  4U,
                /* FLASH SE positive edge width configuration */
                (uint8)  1U,
                /* FLASH RECALL to XE/YE hold time */
                (uint8)  4U,
                /* FLASH RECALL to SE lower edge holding time */
                (uint8)  20U,
                /* FLASH RECALL High Level Hold Time */
                (uint8)  17U,
                /* FLASH RECALL to SE upper edge establishment time */
                (uint8)  4U,
            }
        },
        /* Configured frequency values. */
        {
            {
                CLOCK_IS_OFF,
                0U,
            },
            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 1U
            {
                HRC_CLK,
                48000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 2U
            {
                SOSC_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 3U
            {
                MRC_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 4U
            {
                SPLL_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 5U
            {
                HRC_DIV1_CLK,
                48000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 6U
            {
                HRC_DIV2_CLK,
                24000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 7U
            {
                OSC_DIV1_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 8U
            {
                OSC_DIV2_CLK,
                4000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 9U
            {
                MRC_DIV1_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 10U
            {
                MRC_DIV2_CLK,
                4000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 11U
            {
                SPLL_DIV1_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 12U
            {
                SPLL_DIV2_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 13U
            {
                LRC_CLK,
                128000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 14U
            {
                RTC_CLK,
                32000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 15U
            {
                SYS_PRE_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 16U
            {
                SYS_DIV_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 17U
            {
                ADC0_DIV_CLK,
                40000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 18U
            {
                ADC1_DIV_CLK,
                40000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 19U
            {
                CAN_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 20U
            {
                AFE_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 21U
            {
                TOP_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 22U
            {
                IPB_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 23U
            {
                IPT_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 24U
            {
                GPIO_PCLK_CLK,
                80000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 25U
            {
                PMU_SYS_CLK,
                160000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 26U
            {
                CLKOUT_CLK,
                8000000U,
            },
            #endif

            #if CLOCK_HW_CONFIGURED_FREQUENCIES_NO > 27U
            {
                ADC_TRIG_CLK,
                160000000U,
            },
            #endif

        },

    },

};


#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

