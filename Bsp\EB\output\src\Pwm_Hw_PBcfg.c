/**
 * 
 * @file Pwm_Hw_PBcfg.c
 * @brief AUTOSAR Pwm - contains the data exported by the Pwm module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DAT<PERSON>, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, W<PERSON><PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 * @section[blobal]
 * Violations MISRA C 2012 Advisory Rule 5.4 , Macro identifiers shall be distinct.
 * Reason: The macro definition names are similar, the version number definition prefixes are the same, 
 * and the keywords are different.
 *
 * Violations MISRA C 2012 Advisory Rule 10.5 , The value of an expression should not 
 * be cast to an inappropriate essential type.
 * Reason: When configuring the mask event, because some of them fire more than one event 
 * at the same time, this mask event is represented by int, and because the structure is 
 * defined as an enumeration type, it needs to be cast.
 *
 */ 
 

#include "Std_Types.h"
#include "Pwm_Hw_PBcfg.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define PWM_HW_PBCFG_VENDOR_ID_C                    (110U)
#define PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C     (4U) 
#define PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C     (4U)  
#define PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C  (0U)  
#define PWM_HW_PBCFG_SW_MAJOR_VERSION_C             (1U)
#define PWM_HW_PBCFG_SW_MINOR_VERSION_C             (0U)
#define PWM_HW_PBCFG_SW_PATCH_VERSION_C             (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types.h file are of the same Autosar version */
    #if ((PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Pwm_Hw_PBcfg.c and Std_Types.h are different"
    #endif
#endif

/* Check if source file and PWM header file are of the same vendor */
#if (PWM_HW_PBCFG_VENDOR_ID_C != PWM_HW_TYPES_VENDOR_ID)
    #error "Pwm_Hw_PBcfg.c and Pwm_Hw_Types.h have different vendor IDs"
#endif
/* Check if source file and PWM header file are of the same AutoSar version */
#if ((PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != PWM_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != PWM_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != PWM_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_Hw_PBcfg.c and Pwm_Hw_Types.h are different"
#endif
/* Check if source file and PWM header file are of the same Software version */
#if ((PWM_HW_PBCFG_SW_MAJOR_VERSION_C != PWM_HW_TYPES_SW_MAJOR_VERSION) || \
     (PWM_HW_PBCFG_SW_MINOR_VERSION_C != PWM_HW_TYPES_SW_MINOR_VERSION) || \
     (PWM_HW_PBCFG_SW_PATCH_VERSION_C != PWM_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_Hw_PBcfg.c and Pwm_Hw_Types.h are different"
#endif

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/


/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h" 


/*
 *  @brief    PB PwmChannels_0 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_0_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 0U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM0_3_CH1,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM0_3_CH0,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_1 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_1_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 1U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM0_3_CH7,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM0_3_CH6,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_2 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_2_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 2U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM0_3_CH5,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM0_3_CH4,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_3 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_8_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 8U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM2_5_CH3,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM2_5_CH2,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_4 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_9_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 9U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM2_5_CH1,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM2_5_CH0,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_5 Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_12_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 0U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 500U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 500U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 500U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM0_3_CH5,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM0_3_CH4,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_Fan Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_13_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 1U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 100000U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 100000U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 100000U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM0_3_CH0,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM0_3_CH1,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};

/*
 *  @brief    PB PwmChannels_Heat Channels Configuration
 */
static const PwmGpcmOc_t Pwm_Hw_10_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    .GpcmNum = 10U,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    .CntMode = PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    .PrdLoadMode = PWM_PRD_LOAD_SHADOW,
    /* Compare load mode */
    .CmpLoadMode = PWM_CMP_LOAD_SHADOW,
    /* Comparison value atomic loading enabled */
    .CmpAtomLoadEnable = TRUE,
    /* Init value load enable */
    .PrdIniAtomLoadEnable = TRUE,
    /* Period value */
    .CntPrd = 100000U,
    /* Initial value */
    .CntIni = 0U,
    /* Clock source */
    .ClkSrc = PWM_SYS_CLK,
    /* Clock division */
    .ClkDiv = PWM_CLK_DIV_8,
    /* External clock source */
    .ExtClkSrc = PWM_EXT_CLK_0,
    /* Die Width Value */
    .DieWidth = 0U,
    /* Channel A control Value */
    .ChnaCtrl = PWM_CMP_CHNA_ORIGINAL,
    /* Channel B control Value */
    .ChnbCtrl = PWM_CMP_CHNB_ORIGINAL,
    /* Channel A Compare Value */
    .ChnaCmpvalue = 100000U,
    /* Channel B Compare Value */
    .ChnbCmpvalue = 100000U,
    /* Channel A is connected to the output PAD channel */
    .ChnaPadNum = PWM_XMUX_CMP_PWM2_5_CH5,
    /* Channel B is connected to the output PAD channel */
    .ChnbPadNum = PWM_XMUX_CMP_PWM2_5_CH4,
    /* Output mode selection */
    .CmpMode = PWM_CMP_INDEPENDENT,

};




/** 
 * @brief   Pwm channels IP related configuration array
 */

const PwmIpwChannelConfig_t Pwm_Hw_IpChannelConfig_PB[16U] =
{
    /** @brief PwmChannel_Motor1_BackRest_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_0_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor1_BackRest_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_0_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor2_SeatLift_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_1_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor2_SeatLift_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_1_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor3_SeatSlide_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_2_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor3_SeatSlide_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_2_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor4_FrontBack_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_8_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor4_FrontBack_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_8_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor5_LegLift_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_9_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor5_LegLift_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_9_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor6_LegStretch_A */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_12_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        1u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Motor6_LegStretch_B */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_12_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        1u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Fan1 */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_13_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        1u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Fan2 */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_13_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        1u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Heat1 */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_10_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_A,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    },
    /** @brief PwmChannel_Heat2 */
    {
        /** @brief IP type of this channel */
        PWM_HW_USED,
         /** @brief  Pointer to the Flexio ip channel configuration structure */
        
        /** @brief PWM IP channel pointer */
        &Pwm_Hw_10_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        PWM_GPCM_CHN_B,
        /** @brief Instance number */
        0u,
#if (PWM_FLEXIO_USED == STD_ON)
        /** @brief The PWM pass does not use this duty ratio, for write θu processing*/
        0u,
#endif
    }
};



#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

#ifdef __cplusplus
}
#endif


