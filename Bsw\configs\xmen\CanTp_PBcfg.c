/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : CanTp_PBcfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-03-01 14:41:59 
*  @date               : 2024-12-20 13:31:11 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 

/*=======[I N C L U D E S]================================*/
#include "CanTp.h"
#include "CanIf_Cfg.h"
#include "PduR_Cfg.h"

/*=======[I N T E R N A L   D A T A]=======================*/
/* PRQA S 0779,0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
/*CanTpChannel RxSdu address information*/

/* PRQA S 0779++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define CANTP_START_SEC_CONST_32
#include "CanTp_MemMap.h"
static CONST(uint32, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Nar = 70u;
static CONST(uint32, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Ncr = 150u;
#define CANTP_STOP_SEC_CONST_32
#include "CanTp_MemMap.h"
#define CANTP_START_SEC_CONST_8
#include "CanTp_MemMap.h"
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Bs = 8u;
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_RxWftMax = 255u;
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_STmin = 20u;

#define CANTP_STOP_SEC_CONST_8
#include "CanTp_MemMap.h"
#define CANTP_START_SEC_CONST_32
#include "CanTp_MemMap.h"
static CONST(uint32, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Nar = 70u;
static CONST(uint32, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Ncr = 150u;
#define CANTP_STOP_SEC_CONST_32
#include "CanTp_MemMap.h"
#define CANTP_START_SEC_CONST_8
#include "CanTp_MemMap.h"
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Bs = 8u;
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_RxWftMax = 255u;
static CONST(uint8, CANTP_CONST) CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_STmin = 20u;

#define CANTP_STOP_SEC_CONST_8
#include "CanTp_MemMap.h"

#define CANTP_START_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"
static CONST(CanTp_RxNSduType, CANTP_CONST) CanTp_Ch0RxNSdus[2] =
{
    {
        &CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Bs,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Nar,
        10u,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_Ncr,
        CANTP_STANDARD,
        PDUR_SRCPDU_CanTP_Phys_Diag_Request,
        CANTP_CanTP_Phys_Diag_Request,
        CANTP_CD_Phys_Diag_Rx,
        CANTP_CD_Diag_Tx,
        CANIF_TXPDU_CD_Diag_Tx,
        0u,
        64u,
        CANTP_PADDING_ON,
        CANTP_CANFD_PHYSICAL,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_RxWftMax,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Phys_Diag_Request_STmin,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        CANTP_CAN_FD,
    },
    {
        &CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Bs,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Nar,
        10u,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_Ncr,
        CANTP_STANDARD,
        PDUR_SRCPDU_CanTP_Func_Diag_Request,
        CANTP_CanTP_Func_Diag_Request,
        CANTP_Fun_Diag_Rx,
        0xFFu,
        0xFFu,
        0u,
        64u,
        CANTP_PADDING_ON,
        CANTP_CANFD_FUNCTIONAL,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_RxWftMax,
        &CanTp_Ch0RxSdu_CANTP_CanTP_Func_Diag_Request_STmin,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        CANTP_CAN_FD,
    },
};
#define CANTP_STOP_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"

/*CanTpChannel TxSdu address information*/
#define CANTP_START_SEC_CONST_32
#include "CanTp_MemMap.h"
static CONST(uint32, CANTP_CONST) CanTp_Ch0TxSdu_CANTP_CanTP_Diag_Resp_Nbs = 150u;
static CONST(uint32, CANTP_CONST) CanTp_Ch0TxSdu_CANTP_CanTP_Diag_Resp_Ncs = 150u;
#define CANTP_STOP_SEC_CONST_32
#include "CanTp_MemMap.h"

#define CANTP_START_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"
static CONST(CanTp_TxNSduType, CANTP_CONST) CanTp_Ch0TxNSdus[1] =
{
    {
        70u,
        &CanTp_Ch0TxSdu_CANTP_CanTP_Diag_Resp_Nbs,
        &CanTp_Ch0TxSdu_CANTP_CanTP_Diag_Resp_Ncs,
        CANTP_STANDARD,
        PDUR_DESTPDU_CanTP_Diag_Resp,
        CANTP_CanTP_Diag_Resp,
        CANTP_CD_Diag_Tx,
        CANTP_CD_Phys_Diag_Rx,
        CANIF_TXPDU_CD_Diag_Tx,
        0u,
        64u,
        CANTP_PADDING_ON,
        CANTP_PHYSICAL_TX,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        CANTP_CAN_FD,
    },
};
#define CANTP_STOP_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"
/* PRQA S 0779-- */ /* MISRA Rule 1.3,Rule 5.2 */

#define CANTP_START_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"
static CONST(CanTp_ChannelType, CANTP_CONST) CanTp_CfgChannel[CANTP_CHANNEL_NUMBER] =
{
    {
        CANTP_MODE_FULL_DUPLEX,
        FALSE,
        50u,
        5000u,
        0u,
        0x2u,
        &CanTp_Ch0RxNSdus[0],
        0x1u,
        &CanTp_Ch0TxNSdus[0],
    },
};
#define CANTP_STOP_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"

#define CANTP_START_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"
CONST(CanTp_ConfigType, CANTP_CONST) CanTp_Config =
{
    CANTP_CHANNEL_NUMBER,
    &CanTp_CfgChannel[0],
};
#define CANTP_STOP_SEC_CONST_UNSPECIFIED
#include "CanTp_MemMap.h"

/*=======[E X T E R N A L   D A T A]===========================*/
/*=======[E N D   O F   F I L E]==============================*/

