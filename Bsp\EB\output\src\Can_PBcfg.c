/**
 * file    Can_PBcfg.h
 * brief   Can Post Build Configurations source file.
 * author  <PERSON><PERSON>
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#include "Can_PBcfg.h"
#include "Can_Ipw.h"
#define CAN_START_SEC_VAR_CLEARED_UNSPECIFIED
#include "Can_MemMap.h"

/* CanConfigSet_0 */
/* This array manages PduId in Tx Buffer. */
#if (CAN_CFG_NUMBER_OF_TX_BUFFERS_0 > 0u)
static VAR(PduIdType, CAN_VAR_NO_INIT) Can_PduIdBuffer_0[CAN_CFG_NUMBER_OF_TX_BUFFERS_0];
#else
#define Can_PduIdBuffer_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_TX_BUFFERS_0 > 0u) */

/*This array store hth in Tx Buffer.*/
#if (CAN_CFG_NUMBER_OF_TX_BUFFERS_0 > 0u)
static VAR(Can_HwHandleType, CAN_VAR_NO_INIT) Can_PduHandleBuffer_0[CAN_CFG_NUMBER_OF_TX_BUFFERS_0];
#else
#define Can_PduHandleBuffer_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_TX_BUFFERS_0 > 0u) */

/* This array manages Can controller state. */
static VAR(Can_ControllerStateDataType_t, CAN_VAR_NO_INIT) Can_ControllerStates_0[CAN_CFG_NUMBER_OF_CONTROLLERS_0];

/* This array manages all CanIcomCounterValue. */
#if (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0 > 0u)
static VAR(uint16, CAN_VAR_NO_INIT) Can_IcomRxMessageCounters_0[CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0];
#else
#define Can_IcomRxMessageCounters_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0 > 0u) */


#define CAN_STOP_SEC_VAR_CLEARED_UNSPECIFIED
#include "Can_MemMap.h"


#define CAN_START_SEC_VAR_INIT_UNSPECIFIED
#include "Can_MemMap.h"


/*store timestamp capture status*/
static VAR(uint8, CAN_CONST) Can_TimestampCapture_0[CAN_CFG_NUMBER_OF_HOHS_0] = CAN_CFG_TIMESTAMP_CAPTURE_0;

/*store timestamp value*/
static VAR(uint32, CAN_CONST) Can_TimestampValue_0[CAN_CFG_NUMBER_OF_HOHS_0] = CAN_CFG_TIMESTAMP_VALUE_0;

#define CAN_STOP_SEC_VAR_INIT_UNSPECIFIED
#include "Can_MemMap.h"



#define CAN_START_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"

/* CanConfigSet_0 */

/* Provide all configured RX dedicated buffer filters. */
#if (CAN_CFG_NUMBER_OF_RX_DEDICATED_BUFFER_FILTERS_0 > 0u)
static CONST(Can_ControllerFilterType_t, CAN_CONST) Can_DedicatedRxFilters_0[CAN_CFG_NUMBER_OF_RX_DEDICATED_BUFFER_FILTERS_0] = CAN_CFG_RX_DEDICATED_BUFFER_FILTERS_0;
#else
#define Can_DedicatedRxFilters_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_RX_DEDICATED_BUFFER_FILTERS_0 > 0u) */

/* Provide all configured RX FIFO filters. */
#if (CAN_CFG_NUMBER_OF_RX_FIFO_FILTERS_0 > 0u)
static CONST(Can_ControllerFilterType_t, CAN_CONST) Can_RxFifoFilters_0[CAN_CFG_NUMBER_OF_RX_FIFO_FILTERS_0] = CAN_CFG_RX_FIFO_FILTERS_0;
#else
#define Can_RxFifoFilters_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_RX_FIFO_FILTERS_0 > 0u) */

/* Mapping of all HRHs (index to array) to the hardware (element content). */
#if (CAN_CFG_NUMBER_OF_HRH_FILTERS_0 > 0u)
static CONST(Can_RxHandleMappingType_t, CAN_CONST) Can_RxHandleMappings_0[CAN_CFG_NUMBER_OF_HRH_FILTERS_0] = CAN_CFG_HRH_FILTERS_0;
#else
#define Can_RxHandleMappings_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_HRH_FILTERS_0 > 0u) */

/* Provide all configured baud rates. */
static CONST(Can_ControllerBaudrateConfigType_t, CAN_CONST) Can_Baudrates_0[CAN_CFG_NUMBER_OF_BAUDRATES_0] = CAN_CFG_BAUDRATES_0;

/* Contains a list of all configured controllers. */
static CONST(Can_ControllerConfigType_t, CAN_CONST) Can_ControllerConfigs_0[CAN_CFG_NUMBER_OF_CONTROLLERS_0] = CAN_CFG_CONTROLLERS_0;

/* Contains a list of all CanIcomRxMessageSignalConfig */
#if (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGE_SIGNALS_0 > 0u)
static CONST(Can_ControllerIcomRxMessageSignalConfigType_t, CAN_CONST) Can_IcomRxMessageSignals_0[CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGE_SIGNALS_0] = CAN_CFG_ICOM_RX_MESSAGE_SIGNALS_0;
#else
#define Can_IcomRxMessageSignals_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGE_SIGNALS_0 > 0u) */

/* Contains a list of all CanIcomRxMessageConfig */
#if (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0 > 0u)
static CONST(Can_ControllerIcomRxMessageConfigType_t, CAN_CONST) Can_IcomRxMessages_0[CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0] = CAN_CFG_ICOM_RX_MESSAGES_0;
#else
#define Can_IcomRxMessages_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_ICOM_RX_MESSAGES_0 > 0u) */

/* Contains a list of all CanIcomConfig */
#if (CAN_CFG_NUMBER_OF_ICOMS_0 > 0u)
static CONST(Can_ControllerIcomConfigType_t, CAN_CONST) Can_ControllerIcomConfigs_0[CAN_CFG_NUMBER_OF_ICOMS_0] = CAN_CFG_ICOMS_0;
#else
#define Can_ControllerIcomConfigs_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_ICOMS_0 > 0u) */

/* Mapping of all HTHs (index to array) to the hardware (element content). */
#if (CAN_CFG_NUMBER_OF_HTHS_0 > 0u)
static CONST(Can_TxHandleMappingType_t, CAN_CONST) Can_TxHandleMappings_0[CAN_CFG_NUMBER_OF_HTHS_0] = CAN_CFG_HTHS_0;
#else
#define Can_TxHandleMappings_0 NULL_PTR
#endif /* (CAN_CFG_NUMBER_OF_HTHS_0 > 0u) */

/* This data list hardware assigned channel identifier for current config set. */
static CONST(Can_ControllerIdType, CAN_CONST) Can_InterruptToControllerList_0[CAN_CFG_NUMBER_OF_INTERRUPT_TO_CONTROLLER_LISTS_0] = CAN_CFG_INTERRUPT_TO_CONTROLLER_LIST_0;

/*==================[external data]==========================================*/
CONST(Can_ConfigType, CAN_CONST) Can_Config[CAN_CFG_NUMBER_OF_SET_CONFIGS] = 
{
    /* CanConfigSet_0 */
    {
        /* Pointer to an array of controller configs. */
        .Can_ControllerConfigsPtr       = Can_ControllerConfigs_0,
        /* Pointer to an array of controller state data */
        .Can_ControllerStatesPtr        = Can_ControllerStates_0,
        /* Pointer to an array of Rx handle mappings configs. */
        .Can_RxHandleMappingsPtr        = Can_RxHandleMappings_0,
        /* Pointer to an array of Tx handle mappings configs. */
        .Can_TxHandleMappingsPtr        = Can_TxHandleMappings_0,
        /* Pointer to an array of Tx message buffer. */
        .Can_PduIdBufferPtr             = Can_PduIdBuffer_0,
        /* Pointer to an array of Tx message buffer. */
        .Can_PduHandleBufferPtr         = Can_PduHandleBuffer_0,
        /* Pointer to an array of Icom controller configs. */
        .Can_ControllerIcomConfigsPtr   = Can_ControllerIcomConfigs_0,
        /* Pointer to an array of Icom Rx message counter. */
        .Can_IcomRxMessageCountersPtr   = Can_IcomRxMessageCounters_0,
        /* Pointer to an array of interrupt to controller list. */
        .Can_InterruptToControllerListPtr   = Can_InterruptToControllerList_0,
        /* Number of controllers in the array pointed to by Can_ControllerConfigs_0. */
        .NumberOfControllers            = CAN_CFG_NUMBER_OF_CONTROLLERS_0,
        /* Number of Rx handle mappings in the array pointed to by Can_RxHandleMappings_0. */
        .NumberOfRxHandleMappings       = CAN_CFG_NUMBER_OF_HRH_FILTERS_0,
        /* Number of Rx handles */
        .NumberOfRxHandles              = CAN_CFG_NUMBER_OF_HRHS_0,
        /* Number of Tx handle mappings in the array pointed to by Can_TxHandleMappings_0. */
        .NumberOfTxHandleMappings       = CAN_CFG_NUMBER_OF_HTHS_0,
        /* Number of Tx message buffer in the array pointed to by Can_PduIdBuffer_0. */
        .NumberOfTxBuffers              = CAN_CFG_NUMBER_OF_TX_BUFFERS_0,
        /* Number of Icom configuration in the array pointed to by Can_ControllerIcomConfigs_0. */
        .NumberOfIcomConfigs            = CAN_CFG_NUMBER_OF_ICOMS_0,
        /* Number of interrupt to controller list in the array pointed to by Can_InterruptToControllerList_0. */
        .NumberOfInterruptToControllerLists  = CAN_CFG_NUMBER_OF_INTERRUPT_TO_CONTROLLER_LISTS_0,
        /* Global timestamp capture status*/
        .Can_TimestampCapturePtr       = Can_TimestampCapture_0,
        /* Global internal timestamp value*/
        .Can_TimestampValuePtr = Can_TimestampValue_0
    } 
};

#define CAN_STOP_SEC_CONST_UNSPECIFIED
#include "Can_MemMap.h"

#define CAN_START_SEC_CODE
#include "Can_MemMap.h"

/**********************************************************************************************************************************
 * @brief This function checks the pointer of the configuration set.
 *
 * @param [in]  ConfigPtr  Pointer of data stored configuration set information.
 * 
 * @return If parameter ConfigPtr is a valid config set pointer, 
 *         return TRUE, else return FALSE. 
 *********************************************************************************************************************************/
FUNC(boolean, CAN_CODE) Can_CheckConfigPtr
(
    P2CONST(Can_ConfigType, AUTOMATIC, CAN_APPL_CONST) ConfigPtr
)
{
  /* set false to result */  
  boolean result = FALSE;
  /* counter for loop */
  uint8 index; 
  
  if (ConfigPtr != NULL_PTR)
  {
        for (index = 0u; index < CAN_CFG_NUMBER_OF_SET_CONFIGS; index++)
        {
            if (ConfigPtr == &Can_Config[index])
            {
                result = TRUE;
                break;
            }
        }
  }
  
  return result; 
}

#define CAN_STOP_SEC_CODE
#include "Can_MemMap.h"
