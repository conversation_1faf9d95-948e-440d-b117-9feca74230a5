/**
* 
* @file    Port_Hw_Cfg.h
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>HER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 


#ifndef PORT_HW_CFG_H
#define PORT_HW_CFG_H

#include "Port_Hw_Types.h"

#include "Port_Hw_PBcfg.h"


#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_HW_VENDOR_ID_CFG_H                       (110U)
#define PORT_HW_AR_RELEASE_MAJOR_VERSION_CFG_H        (4U)
#define PORT_HW_AR_RELEASE_MINOR_VERSION_CFG_H        (4U)
#define PORT_HW_AR_RELEASE_REVISION_VERSION_CFG_H     (0U)
#define PORT_HW_SW_MAJOR_VERSION_CFG_H                (1U)
#define PORT_HW_SW_MINOR_VERSION_CFG_H                (0U)
#define PORT_HW_SW_PATCH_VERSION_CFG_H                (0U)


/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if the files Port_Hw_Cfg.h and Port_Hw_Types.h are of the same vendor */
#if (PORT_HW_VENDOR_ID_CFG_H != PORT_HW_VENDOR_ID_TYPES_H)
    #error "Port_Hw_Cfg.h and Port_Hw_Types.h have different vendor ids"
#endif
/* Check if the files Port_Hw_Cfg.h and Port_Hw_Types.h are of the same Autosar version */
#if ((PORT_HW_AR_RELEASE_MAJOR_VERSION_CFG_H    != PORT_HW_AR_RELEASE_MAJOR_VERSION_TYPES_H)  || \
     (PORT_HW_AR_RELEASE_MINOR_VERSION_CFG_H    != PORT_HW_AR_RELEASE_MINOR_VERSION_TYPES_H)  || \
     (PORT_HW_AR_RELEASE_REVISION_VERSION_CFG_H != PORT_HW_AR_RELEASE_REVISION_VERSION_TYPES_H)  \
    )
    #error "AutoSar Version Numbers of Port_Hw_Cfg.h and Port_Hw_Types.h are different"
#endif
/* Check if the files Port_Hw_Cfg.h and Port_Hw_Types.h are of the same software version */
#if ((PORT_HW_SW_MAJOR_VERSION_CFG_H != PORT_HW_SW_MAJOR_VERSION_TYPES_H) || \
     (PORT_HW_SW_MINOR_VERSION_CFG_H != PORT_HW_SW_MINOR_VERSION_TYPES_H) || \
     (PORT_HW_SW_PATCH_VERSION_CFG_H != PORT_HW_SW_PATCH_VERSION_TYPES_H)    \
    )
    #error "Software Version Numbers of Port_Hw_Cfg.h and Port_Hw_Types.h are different"
#endif


/* Check if the files Port_Hw_Cfg.h and Port_Hw_PBcfg.h are of the same vendor */
#if (PORT_HW_VENDOR_ID_CFG_H != PORT_HW_VENDOR_ID_PBCFG_H)
    #error "Port_Hw_Cfg.h and Port_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if the files Port_Hw_Cfg.h and Port_Hw_PBcfg.h are of the same Autosar version */
#if ((PORT_HW_AR_RELEASE_MAJOR_VERSION_CFG_H    != PORT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H)  || \
     (PORT_HW_AR_RELEASE_MINOR_VERSION_CFG_H    != PORT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H)  || \
     (PORT_HW_AR_RELEASE_REVISION_VERSION_CFG_H != PORT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H)  \
    )
    #error "AutoSar Version Numbers of Port_Hw_Cfg.h and Port_Hw_PBcfg.h are different"
#endif
/* Check if the files Port_Hw_Cfg.h and Port_Hw_PBcfg.h are of the same software version */
#if ((PORT_HW_SW_MAJOR_VERSION_CFG_H != PORT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (PORT_HW_SW_MINOR_VERSION_CFG_H != PORT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (PORT_HW_SW_PATCH_VERSION_CFG_H != PORT_HW_SW_PATCH_VERSION_PBCFG_H)    \
    )
    #error "Software Version Numbers of Port_Hw_Cfg.h and Port_Hw_PBcfg.h are different"
#endif



/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/*! @brief Definitions for BOARD_InitPins Functional Group */

#define   PORT_INSTANCE_COUNT 5U


#define PORT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"









#define PORT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Port_MemMap.h"
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PORT_HW_CFG_H */
