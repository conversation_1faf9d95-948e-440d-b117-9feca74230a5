/**
 * @file    Gpt_Hw_PBcfg.c
 * @brief   Gpt Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 20.1, #include directives should only be preceded by preprocessor directives or comments.
 * REASON: Variables and text need to be placed in the specified location
 */
 
/*==================================================================================================
 *                              GENERATION MACRO DEFINTION
 *================================================================================================*/


#include "Gpt_Hw_PBcfg.h"
#include "Rtc_Gpt_Hw_PBcfg.h"
#include "Stm_Gpt_Hw_PBcfg.h"
#include "Pit_Gpt_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define GPT_HW_VENDOR_ID_PBCFG_C                    (110U)
#define GPT_HW_RELEASE_MAJOR_VERSION_PBCFG_C        (4U)
#define GPT_HW_RELEASE_MINOR_VERSION_PBCFG_C        (4U)
#define GPT_HW_RELEASE_REVISION_VERSION_PBCFG_C     (0U)
#define GPT_HW_SW_MAJOR_VERSION_PBCFG_C             (1U)
#define GPT_HW_SW_MINOR_VERSION_PBCFG_C             (0U)
#define GPT_HW_SW_PATCH_VERSION_PBCFG_C             (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#if (GPT_HW_VENDOR_ID_PBCFG_H != GPT_HW_VENDOR_ID_PBCFG_C)
    #error "Gpt_Hw_PBcfg.h and Gpt_Hw_PBcfg.c have different vendor ids"
#endif
/* Check if the header files are of the same Autosar version */
#if ((GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H != GPT_HW_RELEASE_MAJOR_VERSION_PBCFG_C) || \
     (GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H != GPT_HW_RELEASE_MINOR_VERSION_PBCFG_C) || \
     (GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H != GPT_HW_RELEASE_REVISION_VERSION_PBCFG_C) \
    )
    #error "AutoSar Version Numbers of Gpt_Hw_PBcfg.h and Gpt_Hw_PBcfg.c are different"
#endif
/* Check if the header files are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_H != GPT_HW_SW_MAJOR_VERSION_PBCFG_C) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_H != GPT_HW_SW_MINOR_VERSION_PBCFG_C) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_H != GPT_HW_SW_PATCH_VERSION_PBCFG_C) \
    )
    #error "Software Version Numbers of Gpt_Hw_PBcfg.h and Gpt_Hw_PBcfg.c are different"
#endif

#if (GPT_HW_VENDOR_ID_PBCFG_C != PIT_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if the header files are of the same Autosar version */
#if ((GPT_HW_RELEASE_MAJOR_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_MINOR_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_REVISION_VERSION_PBCFG_C != PIT_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if the header files are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_C != PIT_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_C != PIT_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_C != PIT_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Gpt_Hw_PBcfg.c and Pit_Gpt_Hw_PBcfg.h are different"
#endif

#if (GPT_HW_VENDOR_ID_PBCFG_C != STM_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if the header files are of the same Autosar version */
#if ((GPT_HW_RELEASE_MAJOR_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_MINOR_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_REVISION_VERSION_PBCFG_C != STM_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if the header files are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_C != STM_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_C != STM_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_C != STM_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Gpt_Hw_PBcfg.c and Stm_Gpt_Hw_PBcfg.h are different"
#endif

#if (GPT_HW_VENDOR_ID_PBCFG_C != RTC_HW_VENDOR_ID_PBCFG_H)
    #error "Gpt_Hw_PBcfg.c and Rtc_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if the header files are of the same Autosar version */
#if ((GPT_HW_RELEASE_MAJOR_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_MINOR_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_RELEASE_REVISION_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Gpt_Hw_PBcfg.c and Rtc_Hw_PBcfg.h are different"
#endif
/* Check if the header files are of the same Software version */
#if ((GPT_HW_SW_MAJOR_VERSION_PBCFG_C != RTC_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_MINOR_VERSION_PBCFG_C != RTC_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (GPT_HW_SW_PATCH_VERSION_PBCFG_C != RTC_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Gpt_Hw_PBcfg.c and Rtc_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

 /*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/
#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"

const Gpt_HwInstanceConfig_t Gpt_HwInstanceConfig_PB[1U]=
{
    {
        /* @brief HW type  */
        GPT_PIT_MODULE,
        /* @brief HW instance */
        0U
    }
};


/* channels of GPT */

/** 
 * brief   Gpt channels HW related configuration array
 */
const Gpt_HwChannelConfig_t Gpt_ChannelConfig_PB[1U] =
{
    /* @brief GptChannelConfiguration_0 */
    {
        /* @brief HW type  */
        GPT_PIT_MODULE,
        /* @brief HW instance */
        0U,
        /* @brief HW channel */
        0U,
        /* @brief HW channel pointer */
        {
            NULL_PTR,
            NULL_PTR,
            &PIT_0_ChannelConfig_PB[0U]
        }
    }

};

#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/


/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/


/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/

#ifdef __cplusplus
}
#endif/*Gpt_Hw_PBCFG_C*/

/** } */
