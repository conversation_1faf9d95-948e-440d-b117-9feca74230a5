/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : PduR_Cfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 15:42:14 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "PduR.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Com_Cbk.h"
#include "Dcm.h"
#include "Dcm_Cbk.h"
#include "LinIf.h"
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/
#define PDUR_CFG_C_AR_MAJOR_VERSION  4u
#define PDUR_CFG_C_AR_MINOR_VERSION  2u
#define PDUR_CFG_C_AR_PATCH_VERSION  2u

/*******************************************************************************
**                      Version Check                                         **
*******************************************************************************/

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

#define PDUR_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
CONST(PduRBswModuleType,PDUR_CONST)
PduR_BswModuleConfigData[PDUR_BSW_MODULE_SUM] = /* PRQA S 3408 */ /* MISRA Rule 8.4 */
{
    {
        PDUR_CANIF,
        NULL_PTR,
        CanIf_Transmit,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
    },
    {
        PDUR_COM,
        Com_TriggerTransmit,
        NULL_PTR,
        Com_TxConfirmation,
        Com_RxIndication,
        NULL_PTR,
        Com_CopyTxData,
        Com_TpTxConfirmation,
        Com_StartOfReception,
        Com_CopyRxData,
        Com_TpRxIndication,
    },
    {
        PDUR_CANTP,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        CanTp_Transmit,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
    },
    {
        PDUR_DCM,
        NULL_PTR,
        NULL_PTR,
        Dcm_TxConfirmation,
        NULL_PTR,
        NULL_PTR,
        Dcm_CopyTxData,
        Dcm_TpTxConfirmation,
        Dcm_StartOfReception,
        Dcm_CopyRxData,
        Dcm_TpRxIndication,
    },
    {
        PDUR_LINIF,
        NULL_PTR,
        LinIf_Transmit,
        NULL_PTR,
        NULL_PTR,
        LinTp_Transmit,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
        NULL_PTR,
    },
};
#define PDUR_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"

/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/
