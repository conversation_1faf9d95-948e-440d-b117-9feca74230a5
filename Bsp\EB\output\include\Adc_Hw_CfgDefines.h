/**
 * file    Adc_Hw_CfgDefines.h
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


#ifndef ADC_HW_CFGDEFINES_H
#define ADC_HW_CFGDEFINES_H

/**
*   @file
*
*   @addtogroup adc_hw_config Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                         INCLUDE FILES

==================================================================================================*/

/* Important Note: The header warapper file depends on header platform and can not be used independently.
*  Do not change #include order in this file */
#include "OsIf.h"


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                                SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ADC_HW_VENDOR_ID_CFGDEFINES                      (110u)
#define ADC_HW_AR_RELEASE_MAJOR_VERSION_CFGDEFINES       (4u)
#define ADC_HW_AR_RELEASE_MINOR_VERSION_CFGDEFINES       (4u)
#define ADC_HW_AR_RELEASE_REVISION_VERSION_CFGDEFINES    (0u)
#define ADC_HW_SW_MAJOR_VERSION_CFGDEFINES               (1u)
#define ADC_HW_SW_MINOR_VERSION_CFGDEFINES               (0u)
#define ADC_HW_SW_PATCH_VERSION_CFGDEFINES               (0u)
/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Adc_Hw_CfgDefines.h file and OsIf.h file are of the same Autosar version */
#if ((ADC_HW_AR_RELEASE_MAJOR_VERSION_CFGDEFINES != OSIF_AR_RELEASE_MAJOR_VERSION) || \
     (ADC_HW_AR_RELEASE_MINOR_VERSION_CFGDEFINES != OSIF_AR_RELEASE_MINOR_VERSION)    \
    )
    #error "AutoSar Version Numbers of Adc_Hw_CfgDefines.h and OsIf.h are different"
#endif
#endif


/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                        LOCAL MACROS
==================================================================================================*/

#define ADC_HW_DEV_ERROR_DETECT                (STD_OFF)
#define ADC_HW_TIMEOUT_TYPE                    (OSIF_COUNTER_DUMMY)
#define ADC_HW_TIMEOUT_VAL                     (100000UL)
#define ADC_HW_AIEN_INTERRUPT_ENABLE           (STD_OFF)


/*! @brief ADC default Sample Time from RM */
#define ADC_HW_DEFAULT_SAMPLE_TIME             (0x0CU)
/*! @brief ADC default Hold Time from RM */
#define ADC_HW_DEFAULT_Hold_TIME               (0x02U)
/*! @brief ADC default cal Hold Time from RM */
#define ADC_HW_DEFAULT_CalHold_TIME            (0x08U)
/*! @brief ADC default Vcm Time from RM */
#define ADC_HW_DEFAULT_Vcm_TIME                (0x04U)
/*! @brief ADC default User Gain from RM */
#define ADC_HW_DEFAULT_USER_GAIN               (0x04U)

/*! @brief ADC Max external channel ID */
#define FEATURE_ADC_MAX_EXT_CHAN_ID            (23U)
/*! @brief ADC has external channels */
#define FEATURE_ADC_HAS_CHANNEL_2              (0U)
#define FEATURE_ADC_HAS_CHANNEL_8              (0U)
#define FEATURE_ADC_HAS_CHANNEL_9              (0U)


/*==================================================================================================
*                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* ADC_HW_CFGDEFINES_H */

