/**
 * @file     Lin_Ipw_PBcfg.c
 * @brief    Lin_Ipw_PBcfg Software c files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 * 
 * section [global]
 * Violates MISRA C 2012 Advisory Rule 5.4, The macro definition name cannot be changed
 * reason: Macro definitions have similar names, version number definitions have the same prefix, but different keywords.
 * 
 * section [global]
 * Violates MISRA C 2012 Dir-1.1-C99 [L] Number of macro definitions exceeds 4095 - program does not conform strictly to ISO C99.
 * Reason: include this file is needed.
 * 
 **/


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Lin_Ipw_Cfg.h"
#include "Lin_Hw_Cfg.h"


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LIN_IPW_PBCFG_VENDOR_ID_C                     (110u)
#define LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define LIN_IPW_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define LIN_IPW_PBCFG_SW_MINOR_VERSION_C              (0u)
#define LIN_IPW_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lin_Ipw_Cfg.h */
#if (LIN_IPW_PBCFG_VENDOR_ID_C != LIN_IPW_CFG_VENDOR_ID)
    #error "Lin_Ipw_PBcfg.c and Lin_Ipw_Cfg.h have different vendor ids"
#endif
#if ((LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_IPW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_IPW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_IPW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_Ipw_PBcfg.c and Lin_Ipw_Cfg.h are different"
#endif
#if ((LIN_IPW_PBCFG_SW_MAJOR_VERSION_C != LIN_IPW_CFG_SW_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_MINOR_VERSION_C != LIN_IPW_CFG_SW_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_PATCH_VERSION_C != LIN_IPW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lin_Ipw_PBcfg.c and Lin_Ipw_Cfg.h are different"
#endif

/* Checks against Lin_Hw_Cfg.h */
#if (LIN_IPW_PBCFG_VENDOR_ID_C != LIN_HW_CFG_VENDOR_ID)
    #error "Lin_Ipw_PBcfg.c and Lin_Hw_Cfg.h have different vendor ids"
#endif
#if ((LIN_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_HW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_Ipw_PBcfg.c and Lin_Hw_Cfg.h are different"
#endif
#if ((LIN_IPW_PBCFG_SW_MAJOR_VERSION_C != LIN_HW_CFG_SW_MAJOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_MINOR_VERSION_C != LIN_HW_CFG_SW_MINOR_VERSION) || \
     (LIN_IPW_PBCFG_SW_PATCH_VERSION_C != LIN_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lin_Ipw_PBcfg.c and Lin_Hw_Cfg.h are different"
#endif

/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/
#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/**
* @brief   Export LIN configurations.
*/
LIN_IPW_CONFIG_EXT

/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/
/**
*
* @brief          Hardware configuration for Lin Hardware Instance LIN_HW_1 - Configuration:
*
*
*/
const LinHwConfig_t Lin_Ipw_pHwConfigPB_0 =
{
    LIN_HW_1,

#if (STD_ON == LIN_WAKEUP_DETECTION)
    (boolean)FALSE, /* Wakeup support disabled */
    (EcuM_WakeupSourceType)LIN_NONE_ECUM_WAKEUP_SOURCE_REF, /* None Wakeup Source was referred */
#endif

    LIN_MASTER_NODE, /* Lin Node Type */
    LIN_HARDWARE_CHANNEL, /*Hardware type*/
    {
        &Lin_Hw_pHwConfigPB_0
    }
};
/**
*
* @brief          Hardware configuration for Lin Hardware Instance LIN_HW_2 - Configuration:
*
*
*/
const LinHwConfig_t Lin_Ipw_pHwConfigPB_1 =
{
    LIN_HW_2,

#if (STD_ON == LIN_WAKEUP_DETECTION)
    (boolean)FALSE, /* Wakeup support disabled */
    (EcuM_WakeupSourceType)LIN_NONE_ECUM_WAKEUP_SOURCE_REF, /* None Wakeup Source was referred */
#endif

    LIN_MASTER_NODE, /* Lin Node Type */
    LIN_HARDWARE_CHANNEL, /*Hardware type*/
    {
        &Lin_Hw_pHwConfigPB_1
    }
};


#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"
/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */
