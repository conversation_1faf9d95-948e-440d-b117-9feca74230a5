/**
 * file    Flexio_Uart_Hw_CfgDefines.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_UART_HW_CFG_DEFINES_H
#define FLEXIO_UART_HW_CFG_DEFINES_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_UART_HW_CFG_DEFINES_VENDOR_ID                     (110U)
#define FLEXIO_UART_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION      (4U)
#define FLEXIO_UART_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION      (4U) 
#define FLEXIO_UART_HW_CFG_DEFINES_AR_RELEASE_REVISION_VERSION   (0U) 
#define FLEXIO_UART_HW_CFG_DEFINES_SW_MAJOR_VERSION              (1U) 
#define FLEXIO_UART_HW_CFG_DEFINES_SW_MINOR_VERSION              (0U)
#define FLEXIO_UART_HW_CFG_DEFINES_SW_PATCH_VERSION              (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((FLEXIO_UART_HW_CFG_DEFINES_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (FLEXIO_UART_HW_CFG_DEFINES_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of Flexio_Uart_Hw_CfgDefines.h and Mcal.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
 *                                     DEFINES AND MACROS
==================================================================================================*/
/* Macros that indicate FLEXIO channels used by UART */

/* Macros that indicate FLEXIO pins used by UART */

/* Macros that indicate FLEXIO channels used by UART */

/*==================================================================================================
*                                            ENUMS
==================================================================================================*/

/*==================================================================================================
*                               STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/



#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_UART_HW_CFG_DEFINES_H */
