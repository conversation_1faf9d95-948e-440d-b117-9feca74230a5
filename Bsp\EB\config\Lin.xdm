<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Lin" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Lin" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Lin"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="AutosarExt" type="IDENTIFIABLE">
                <d:var name="LinDisableDemReportErrorStatus" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinFrameTimeoutDisable" type="BOOLEAN" value="true"/>
                <d:var name="LinEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinStartTimerNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinStopTimerNotification" type="FUNCTION-NAME" 
                       value="NULL_PTR">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
              <d:ctr name="LinGeneral" type="IDENTIFIABLE">
                <d:var name="LinMulticoreSupport" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="LinIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinTimeoutMethod" type="ENUMERATION" 
                       value="OSIF_COUNTER_DUMMY">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinTimeoutDuration" type="INTEGER" value="1000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="LinVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:lst name="LinEcucPartitionRef"/>
              </d:ctr>
              <d:ctr name="LinDemEventParameterRefs" type="IDENTIFIABLE">
                <a:a name="ENABLE" value="false"/>
                <d:ref name="LIN_E_TIMEOUT" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                </d:ref>
              </d:ctr>
              <d:ctr name="LinGlobalConfig" type="IDENTIFIABLE">
                <d:lst name="LinChannel" type="MAP">
                  <d:ctr name="LinChannel_0" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="0"/>
                    <d:var name="LinNodeType" type="ENUMERATION" value="MASTER"/>
                    <d:var name="LinAutobaudFeature" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200"/>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LIN_HW_1"/>
                    <d:var name="LinTransferMode" type="ENUMERATION" 
                           value="LIN_INTERRUPT_MODE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinCrcMode" type="ENUMERATION" 
                           value="LIN_CLASSIC"/>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Lin"/>
                    <d:ref name="LinClockRef_Alternate" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false"/>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                  <d:ctr name="LinChannel_1" type="IDENTIFIABLE">
                    <d:var name="LinChannelId" type="INTEGER" value="1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@CALC</a:v>
                        <a:v>@DEF</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="LinNodeType" type="ENUMERATION" value="MASTER"/>
                    <d:var name="LinAutobaudFeature" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinChannelBaudRate" type="INTEGER" 
                           value="19200"/>
                    <d:var name="LinResponseTimeout" type="INTEGER" value="14">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinHeaderTimeout" type="INTEGER" value="44">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinHwChannel" type="ENUMERATION" 
                           value="LIN_HW_2"/>
                    <d:var name="LinTransferMode" type="ENUMERATION" 
                           value="LIN_INTERRUPT_MODE">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="LinCrcMode" type="ENUMERATION" 
                           value="LIN_CLASSIC"/>
                    <d:ref name="LinClockRef" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Lin"/>
                    <d:ref name="LinClockRef_Alternate" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:var name="LinChannelWakeupSupport" type="BOOLEAN" 
                           value="false"/>
                    <d:ref name="LinChannelEcuMWakeupSource" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:ref name="LinChannelEcucPartitionRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="82">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorApiInfix" type="STRING" value="">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
