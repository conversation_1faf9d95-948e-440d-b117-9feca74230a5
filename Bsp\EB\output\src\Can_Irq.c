/**
 * file    Can_Irq.h
 * brief   Can Irq Configurations.
 * author  <PERSON><PERSON>
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#include "Can.h"
#include "OsIf.h"


#ifndef CAN_IR_00
    #define CAN_IR_00 (*((volatile uint32*)0x50000000uL))
#else
    #warning CAN_IR_00 is overridden by the environment. This is not intended for production software!
#endif

#ifndef CAN_IR_01
    #define CAN_IR_01 (*((volatile uint32*)0x50001000uL))
#else
    #warning CAN_IR_01 is overridden by the environment. This is not intended for production software!
#endif

#ifndef CAN_IR_02
    #define CAN_IR_02 (*((volatile uint32*)0x50002000uL))
#else
    #warning CAN_IR_02 is overridden by the environment. This is not intended for production software!
#endif

#ifndef CAN_IR_03
    #define CAN_IR_03 (*((volatile uint32*)0x50003000uL))
#else
    #warning CAN_IR_03 is overridden by the environment. This is not intended for production software!
#endif

#define CAN_START_SEC_CODE
#include "Can_MemMap.h"


/**********************************************************************************************************************************
 * @brief Can_Interrupt_CAN00_Cat1
 *        Category 1 interrupt service routine for vector 39 of channel CAN00.
 *
 *********************************************************************************************************************************/
ISR(CAN0_LINE0_IRQHandler)
{
    Can0_InterruptHandlerLine0(CAN_INTERRUPT_39);
}

/**********************************************************************************************************************************
 * @brief Can_Interrupt_CAN00_Cat2
 *        Category 2 interrupt service routine for vector 39 of channel CAN00.
 *
 *********************************************************************************************************************************/
ISR(Can0_LINE1_IRQHandler)
{
    Can0_InterruptHandlerLine1(CAN_INTERRUPT_40);
}


#define CAN_STOP_SEC_CODE
#include "Can_MemMap.h"
