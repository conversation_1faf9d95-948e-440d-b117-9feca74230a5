<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<ODX MODEL-VERSION="2.2.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="odx.xsd">
<!--created by CANdelaStudio::ODXExport220.dll 11.0.101 on 2024-12-17T15:09:12+08:00-->
  <?CANdelaTemplateManufacturer 7?>
  <?CANdelaTemplateName CWC/HUD/NFC?>
  <?CANdelaTemplateVersion 15.0.1?>
  <?CANdelaProtocolStandard UDS?>
  <?ASAMOdxExport220.dll 11.0.101?>
  <DIAG-LAYER-CONTAINER ID="_300">
    <SHORT-NAME>PDB131113A</SHORT-NAME>
    <LONG-NAME>PDB131113A</LONG-NAME>
    <DESC>
      <p>
        <b>Notes for CANdesc Code Generation</b>
      </p>
      <p>
        <br/>
      </p>
      <p>
        <br/>
        The following diagnostic instance options can be configured in GENy (CANdesc) only:
      </p>
      <p>
        <br/>
      </p>
      <ul>
        <li>MaxAttemptsToDelay</li>
        <li>DelayTimeMs</li>
        <li>DelayTimeOnPowerOnMs</li>
      </ul>
      <p>The options are available as attributes in this file, but with category &quot;UDS&quot;, therefore they do not take effect in GENy.</p>
      <p>
        <br/>
        To configure the options for GENy already in the CANdela file, use the older Vector_UDS_1.5.cddt instead.
      </p>
      <p>
        <br/>
      </p>
      <p>
        <br/>
        User Defined Fault Memory:
      </p>
      <p>
        <br/>
      </p>
      <ul>
        <li>
          CANdesc 
          with Vector DEM checks the Memory Selection parameter, accepts only the first one defined in the CDD and rejects all other requests. It does not forward the Memory Selection to the Vector DEM but just asks for &quot;SecondaryMemory&quot;. 
        </li>
        <li>CANdesc without Vector DEM: it is the task of the main handler (application) to distinguish the User Defined Fault Memories. The main handler gets all parameters of the request.</li>
      </ul>
    </DESC>
    <ADMIN-DATA>
      <LANGUAGE>en-US</LANGUAGE>
      <DOC-REVISIONS>
        <DOC-REVISION>
          <TEAM-MEMBER-REF ID-REF="_302"/>
          <REVISION-LABEL>V1.0</REVISION-LABEL>
          <STATE>draft</STATE>
          <DATE>2024-08-06T16:27:18+08:00</DATE>
          <TOOL>CANdelaStudio 12 Release Admin</TOOL>
          <MODIFICATIONS>
            <MODIFICATION>
              <CHANGE>Update DTC&amp;DID&amp;VariantDTC</CHANGE>
              <REASON></REASON>
            </MODIFICATION>
          </MODIFICATIONS>
        </DOC-REVISION>
        <DOC-REVISION>
          <TEAM-MEMBER-REF ID-REF="_302"/>
          <REVISION-LABEL>V1.1</REVISION-LABEL>
          <STATE>draft</STATE>
          <DATE>2024-08-07T14:55:36+08:00</DATE>
          <TOOL>CANdelaStudio 12 Release Admin</TOOL>
          <MODIFICATIONS>
            <MODIFICATION>
              <CHANGE>Delete Old DID</CHANGE>
              <REASON></REASON>
            </MODIFICATION>
          </MODIFICATIONS>
        </DOC-REVISION>
        <DOC-REVISION>
          <TEAM-MEMBER-REF ID-REF="_302"/>
          <REVISION-LABEL>V1.2</REVISION-LABEL>
          <STATE>draft</STATE>
          <DATE>2024-08-08T10:44:42+08:00</DATE>
          <TOOL>CANdelaStudio 12 Release Admin</TOOL>
          <MODIFICATIONS>
            <MODIFICATION>
              <CHANGE>add Snapshot Record</CHANGE>
              <REASON></REASON>
            </MODIFICATION>
          </MODIFICATIONS>
        </DOC-REVISION>
        <DOC-REVISION>
          <TEAM-MEMBER-REF ID-REF="_302"/>
          <REVISION-LABEL>V1.3</REVISION-LABEL>
          <STATE>draft</STATE>
          <DATE>2024-08-08T10:54:02+08:00</DATE>
          <TOOL>CANdelaStudio 12 Release Admin</TOOL>
          <MODIFICATIONS>
            <MODIFICATION>
              <CHANGE>update RID information</CHANGE>
              <REASON></REASON>
            </MODIFICATION>
          </MODIFICATIONS>
        </DOC-REVISION>
        <DOC-REVISION>
          <TEAM-MEMBER-REF ID-REF="_302"/>
          <REVISION-LABEL>V1.77</REVISION-LABEL>
          <STATE>draft</STATE>
          <DATE>2024-08-09T14:21:10+08:00</DATE>
          <TOOL>CANdelaStudio 12 Release Admin</TOOL>
          <MODIFICATIONS>
            <MODIFICATION>
              <CHANGE>Update DID name（Old</CHANGE>
              <REASON></REASON>
            </MODIFICATION>
          </MODIFICATIONS>
        </DOC-REVISION>
      </DOC-REVISIONS>
    </ADMIN-DATA>
    <COMPANY-DATAS>
      <COMPANY-DATA ID="_301">
        <SHORT-NAME>BOE</SHORT-NAME>
        <LONG-NAME>BOE</LONG-NAME>
        <TEAM-MEMBERS>
          <TEAM-MEMBER ID="_302">
            <SHORT-NAME>YC</SHORT-NAME>
            <LONG-NAME>Z YC</LONG-NAME>
          </TEAM-MEMBER>
        </TEAM-MEMBERS>
      </COMPANY-DATA>
    </COMPANY-DATAS>
    <ECU-SHARED-DATAS>
      <ECU-SHARED-DATA ID="_303">
        <SHORT-NAME>ESD_PDB131113A_faultMemory</SHORT-NAME>
        <DIAG-DATA-DICTIONARY-SPEC>
          <DTC-DOPS>
            <DTC-DOP ID="_299">
              <SHORT-NAME>RecordDataType</SHORT-NAME>
              <LONG-NAME>RecordDataType</LONG-NAME>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>24</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DTCS>
                <DTC ID="_304">
                  <SHORT-NAME>DTC970016</SHORT-NAME>
                  <TROUBLE-CODE>9895958</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B170016</DISPLAY-TROUBLE-CODE>
                  <TEXT>Power Supply  is UnderVoltage - Circuit Voltage Below Threshold</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION ID="_305">
                        <SHORT-NAME>DTC_PRIORITY_VALUE</SHORT-NAME>
                        <LONG-NAME>DTC Priority</LONG-NAME>
                        <DESC>
                          <p>(DEM) This value influences the event displacement in case the respective memory is completely filled at the time of event confirmation. 
A lower number represents higher priority (default: 2. Use 1 for OBD)
Specifies DemEventClass.DemEventPriority.
(ref. to Dem104)</p>
                        </DESC>
                      </SDG-CAPTION>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION ID="_306">
                        <SHORT-NAME>DTC_AGING_SUPPORTED</SHORT-NAME>
                        <LONG-NAME>Aging Supported</LONG-NAME>
                        <DESC>
                          <p>(DEM) If this parameter is supported, the aging mechanism is enabled for this DTC.
Aging means that the DTC will be automatically removed from fault memory
if the monitor function was executed
and no failure was detected for a configurable number of operation cycles.
The default aging behaviour is MICROSAR value DEM_AGING_TYPE_1.</p>
                        </DESC>
                      </SDG-CAPTION>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION ID="_307">
                        <SHORT-NAME>DTC_AGING_COUNTER</SHORT-NAME>
                        <LONG-NAME>Aging Cycle Counter Threshold</LONG-NAME>
                        <DESC>
                          <p>(DEM) Number of aging cycles needed to unlearn/delete this DTC.
This parameter has only effect if “Aging supported“ is set to “supported”.</p>
                        </DESC>
                      </SDG-CAPTION>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION ID="_308">
                        <SHORT-NAME>DTC_SEVERITY_VALUE</SHORT-NAME>
                        <LONG-NAME>DTC Severity</LONG-NAME>
                        <DESC>
                          <p>(DEM, Tester) Reports the recommended action to be taken by the system (e.g. vehicle) operator
This parameter is reported by UDS service $19 09 and can serve as a filter for UDS service $19 08.
A missing configuration value (or parameter) is interpreted as noSeverity.
Specifies DemDTCClass.DemDTCSeverity; no further influence on the function of the DEM.</p>
                        </DESC>
                      </SDG-CAPTION>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION ID="_309">
                        <SHORT-NAME>DTC_FUNCTIONAL_UNIT_VALUE</SHORT-NAME>
                        <LONG-NAME>Functional Unit</LONG-NAME>
                        <DESC>
                          <p>(DEM, Tester) For use with UDS 19 08 and 19 09 only.
Identifies the corresponding basic vehicle / system function which monitors this DTC.
The default value is 0, use 0x33 for WWH-OBD.
This parameter is necessary for reporting severity information.
It will be returned in the UDS services $19 08 and  $19 09.
If these services are not used, this parameter serves no use at all.
Specifies DemDTCClass.DemDTCFunctionalUnit; no further influence on the function of the DEM.</p>
                        </DESC>
                      </SDG-CAPTION>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_310">
                  <SHORT-NAME>DTC970017</SHORT-NAME>
                  <TROUBLE-CODE>9895959</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B170017</DISPLAY-TROUBLE-CODE>
                  <TEXT>Power Supply  is OverVoltage - Circuit Voltage Above Threshold</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_311">
                  <SHORT-NAME>DTC980471</SHORT-NAME>
                  <TROUBLE-CODE>9962609</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180471</DISPLAY-TROUBLE-CODE>
                  <TEXT>Motor Stall can't move - Actuator Stuck</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_312">
                  <SHORT-NAME>DTC980496</SHORT-NAME>
                  <TROUBLE-CODE>9962646</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180496</DISPLAY-TROUBLE-CODE>
                  <TEXT>Motor Phase Lost - Component Internal Failure</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_313">
                  <SHORT-NAME>DTC980498</SHORT-NAME>
                  <TROUBLE-CODE>9962648</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180498</DISPLAY-TROUBLE-CODE>
                  <TEXT>Motor temperature Above Thresdold - Component or System Over Temperature</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_314">
                  <SHORT-NAME>DTC980A4B</SHORT-NAME>
                  <TROUBLE-CODE>9964107</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180A4B</DISPLAY-TROUBLE-CODE>
                  <TEXT>ECU's environment temperature Above Threshold - Over Temperature</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_315">
                  <SHORT-NAME>DTC980A91</SHORT-NAME>
                  <TROUBLE-CODE>9964177</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180A91</DISPLAY-TROUBLE-CODE>
                  <TEXT>ECU's environment temperature Below Threshold - Parametric</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_316">
                  <SHORT-NAME>DTC980B54</SHORT-NAME>
                  <TROUBLE-CODE>9964372</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>B180B54</DISPLAY-TROUBLE-CODE>
                  <TEXT>Calibration status is missing - Missing Calibration</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_317">
                  <SHORT-NAME>DTCC07388</SHORT-NAME>
                  <TROUBLE-CODE>12612488</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>U007388</DISPLAY-TROUBLE-CODE>
                  <TEXT>CAN BUS OFF - Bus off</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_318">
                  <SHORT-NAME>DTCC14687</SHORT-NAME>
                  <TROUBLE-CODE>12666503</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>U014687</DISPLAY-TROUBLE-CODE>
                  <TEXT>Lost Communication with GW - Missing Message</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
                <DTC ID="_319">
                  <SHORT-NAME>DTCD8E081</SHORT-NAME>
                  <TROUBLE-CODE>14213249</TROUBLE-CODE>
                  <DISPLAY-TROUBLE-CODE>U18E081</DISPLAY-TROUBLE-CODE>
                  <TEXT>Invalid Data Received From CCU - Invalid Serial Data Received</TEXT>
                  <SDGS>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_305"/>
                      <SD>2</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_306"/>
                      <SD>supported</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_307"/>
                      <SD>0</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_308"/>
                      <SD>noSeverity</SD>
                    </SDG>
                    <SDG>
                      <SDG-CAPTION-REF ID-REF="_309"/>
                      <SD>0x00</SD>
                    </SDG>
                  </SDGS>
                </DTC>
              </DTCS>
            </DTC-DOP>
          </DTC-DOPS>
        </DIAG-DATA-DICTIONARY-SPEC>
      </ECU-SHARED-DATA>
    </ECU-SHARED-DATAS>
    <BASE-VARIANTS>
      <BASE-VARIANT ID="PDB131113A">
        <SHORT-NAME>PDB131113A</SHORT-NAME>
        <LONG-NAME>PDB131113A</LONG-NAME>
        <DIAG-DATA-DICTIONARY-SPEC>
          <DTC-DOPS>
            <DTC-DOP ID="_282">
              <SHORT-NAME>RecordDataType</SHORT-NAME>
              <LONG-NAME>RecordDataType</LONG-NAME>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>24</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DTCS>
                <DTC-REF ID-REF="_304"/>
                <DTC-REF ID-REF="_310"/>
                <DTC-REF ID-REF="_311"/>
                <DTC-REF ID-REF="_312"/>
                <DTC-REF ID-REF="_313"/>
                <DTC-REF ID-REF="_314"/>
                <DTC-REF ID-REF="_315"/>
                <DTC-REF ID-REF="_316"/>
                <DTC-REF ID-REF="_317"/>
                <DTC-REF ID-REF="_318"/>
                <DTC-REF ID-REF="_319"/>
              </DTCS>
            </DTC-DOP>
          </DTC-DOPS>
          <DATA-OBJECT-PROPS>
            <DATA-OBJECT-PROP ID="_9">
              <SHORT-NAME>HexDump_1Byte</SHORT-NAME>
              <LONG-NAME>HexDump (1 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_10">
              <SHORT-NAME>HexDump_2Byte</SHORT-NAME>
              <LONG-NAME>HexDump (2 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>16</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_11">
              <SHORT-NAME>HexDump_4Byte</SHORT-NAME>
              <LONG-NAME>HexDump (4 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>32</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_13">
              <SHORT-NAME>TestFailed</SHORT-NAME>
              <LONG-NAME>Test failed</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_14">
              <SHORT-NAME>TestFailedThisMonitoringCycle</SHORT-NAME>
              <LONG-NAME>Test failed this monitoring cycle</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_15">
              <SHORT-NAME>PendingDtc</SHORT-NAME>
              <LONG-NAME>Pending DTC</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_16">
              <SHORT-NAME>ConfirmedDtc</SHORT-NAME>
              <LONG-NAME>Confirmed DTC</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_17">
              <SHORT-NAME>TestNotCompletedSinceLastClear</SHORT-NAME>
              <LONG-NAME>Test not completed since last clear</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_18">
              <SHORT-NAME>TestFailedSinceLastClear</SHORT-NAME>
              <LONG-NAME>Test failed since last clear</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_19">
              <SHORT-NAME>TestNotCompletedThisMonitoringCycle</SHORT-NAME>
              <LONG-NAME>Test not completed this monitoring cycle</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>false</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>true</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>1</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_37">
              <SHORT-NAME>AddressAndLengthFormatIdentifier</SHORT-NAME>
              <LONG-NAME>Address and Length Format Identifier</LONG-NAME>
              <DESC>
                <p>Examples for addressAndLengthFormatIdentifier parameter values regarding to ISO 14229-1 Annex G.1</p>
              </DESC>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 1 byte, memorySize 1 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 2 byte, memorySize 1 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 3 byte, memorySize 1 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>20</LOWER-LIMIT>
                      <UPPER-LIMIT>20</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 4 byte, memorySize 1 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>33</LOWER-LIMIT>
                      <UPPER-LIMIT>33</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 1 byte, memorySize 2 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 2 byte, memorySize 2 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>35</LOWER-LIMIT>
                      <UPPER-LIMIT>35</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 3 byte, memorySize 2 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 4 byte, memorySize 2 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 1 byte, memorySize 3 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>50</LOWER-LIMIT>
                      <UPPER-LIMIT>50</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 2 byte, memorySize 3 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 3 byte, memorySize 3 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>52</LOWER-LIMIT>
                      <UPPER-LIMIT>52</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 4 byte, memorySize 3 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>65</LOWER-LIMIT>
                      <UPPER-LIMIT>65</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 1 byte, memorySize 4 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>66</LOWER-LIMIT>
                      <UPPER-LIMIT>66</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 2 byte, memorySize 4 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>67</LOWER-LIMIT>
                      <UPPER-LIMIT>67</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 3 byte, memorySize 4 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>68</LOWER-LIMIT>
                      <UPPER-LIMIT>68</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 4 byte, memorySize 4 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>69</LOWER-LIMIT>
                      <UPPER-LIMIT>69</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Length: memoryAddress 5 byte, memorySize 4 byte</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>21</LOWER-LIMIT>
                    <UPPER-LIMIT>32</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>53</LOWER-LIMIT>
                    <UPPER-LIMIT>64</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>70</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_96">
              <SHORT-NAME>HexDump_6_Byte</SHORT-NAME>
              <LONG-NAME>HexDump (6 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_BYTEFIELD" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>48</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_BYTEFIELD"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_146">
              <SHORT-NAME>ASCII_21_Byte</SHORT-NAME>
              <LONG-NAME>ASCII (21 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="ISO-8859-1" BASE-DATA-TYPE="A_ASCIISTRING" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>168</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_161">
              <SHORT-NAME>HexDump_12_Byte</SHORT-NAME>
              <LONG-NAME>HexDump (12 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_BYTEFIELD" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>96</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_BYTEFIELD"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_163">
              <SHORT-NAME>HexDump_14_Byte</SHORT-NAME>
              <LONG-NAME>HexDump (14 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_BYTEFIELD" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>112</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_BYTEFIELD"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_165">
              <SHORT-NAME>HexDump_17_Byte</SHORT-NAME>
              <LONG-NAME>HexDump (17 Byte)</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_BYTEFIELD" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>136</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_BYTEFIELD"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_180">
              <SHORT-NAME>direction</SHORT-NAME>
              <LONG-NAME>direction</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <SHORT-LABEL>关</SHORT-LABEL>
                      <LOWER-LIMIT>0</LOWER-LIMIT>
                      <UPPER-LIMIT>0</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>close</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <SHORT-LABEL>开</SHORT-LABEL>
                      <LOWER-LIMIT>1</LOWER-LIMIT>
                      <UPPER-LIMIT>1</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>open</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>2</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_212">
              <SHORT-NAME>Reset_DOP</SHORT-NAME>
              <LONG-NAME>Reset_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>17</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_213">
              <SHORT-NAME>Request_DOP</SHORT-NAME>
              <LONG-NAME>Request_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>127</LOWER-LIMIT>
                      <UPPER-LIMIT>127</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>128</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_214">
              <SHORT-NAME>Send_DOP</SHORT-NAME>
              <LONG-NAME>Send_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>53</LOWER-LIMIT>
                      <UPPER-LIMIT>53</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Invalid key</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>54</LOWER-LIMIT>
                      <UPPER-LIMIT>54</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Exceed number of attempts</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>55</LOWER-LIMIT>
                      <UPPER-LIMIT>55</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Required time delay not expired</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>127</LOWER-LIMIT>
                      <UPPER-LIMIT>127</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>52</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>56</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>128</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_215">
              <SHORT-NAME>Request_DOP_1</SHORT-NAME>
              <LONG-NAME>Anfordern_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>55</LOWER-LIMIT>
                      <UPPER-LIMIT>55</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Required time delay not expired</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>127</LOWER-LIMIT>
                      <UPPER-LIMIT>127</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>54</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>56</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>128</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_216">
              <SHORT-NAME>Send_DOP_1</SHORT-NAME>
              <LONG-NAME>Senden_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>53</LOWER-LIMIT>
                      <UPPER-LIMIT>53</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Invalid key</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>54</LOWER-LIMIT>
                      <UPPER-LIMIT>54</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Exceed number of attempts</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>55</LOWER-LIMIT>
                      <UPPER-LIMIT>55</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Required time delay not expired</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>52</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>56</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>127</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_220">
              <SHORT-NAME>TABLE_KEY_DOP_2Byte</SHORT-NAME>
              <LONG-NAME>TABLE-KEY-DOP 2Byte</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>16</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>0</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>61839</LOWER-LIMIT>
                    <UPPER-LIMIT>61839</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>65282</LOWER-LIMIT>
                    <UPPER-LIMIT>65535</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_239">
              <SHORT-NAME>DOP_NRC_Identification_Read</SHORT-NAME>
              <LONG-NAME>DOP NRC Identification Read</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>20</LOWER-LIMIT>
                      <UPPER-LIMIT>20</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Response too long</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>21</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_240">
              <SHORT-NAME>RC_DOP</SHORT-NAME>
              <LONG-NAME>RESPONSE CODE_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>20</LOWER-LIMIT>
                      <UPPER-LIMIT>20</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Response too long</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>21</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_241">
              <SHORT-NAME>DOP_NRC_Identification_Write</SHORT-NAME>
              <LONG-NAME>DOP NRC Identification Write</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>114</LOWER-LIMIT>
                      <UPPER-LIMIT>114</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>General programming failure</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>113</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>115</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_250">
              <SHORT-NAME>DOP_NRC_Stored_Data_Read</SHORT-NAME>
              <LONG-NAME>DOP NRC Stored Data Read</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>20</LOWER-LIMIT>
                      <UPPER-LIMIT>20</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Response too long</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>21</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_251">
              <SHORT-NAME>DOP_NRC_Stored_Data_Write</SHORT-NAME>
              <LONG-NAME>DOP NRC Stored Data Write</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>114</LOWER-LIMIT>
                      <UPPER-LIMIT>114</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>General programming failure</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>113</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>115</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_258">
              <SHORT-NAME>DOP_NRC_IOControl_ReturnControl</SHORT-NAME>
              <LONG-NAME>DOP NRC IO Control ReturnControl</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>127</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_259">
              <SHORT-NAME>DOP_NRC_IOControl_Control</SHORT-NAME>
              <LONG-NAME>DOP NRC IO Control Control</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>127</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_261">
              <SHORT-NAME>TABLE_KEY_DOP_2Byte_1</SHORT-NAME>
              <LONG-NAME>TABLE-KEY-DOP 2Byte</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>16</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UINT32" DISPLAY-RADIX="HEX"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>58112</LOWER-LIMIT>
                    <UPPER-LIMIT>61439</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-VALID">
                    <LOWER-LIMIT>65283</LOWER-LIMIT>
                    <UPPER-LIMIT>65535</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_278">
              <SHORT-NAME>DOP_NRC_Routine_Control_Start</SHORT-NAME>
              <LONG-NAME>DOP NRC Routine Control Start</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>114</LOWER-LIMIT>
                      <UPPER-LIMIT>114</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>General programming failure</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>127</LOWER-LIMIT>
                      <UPPER-LIMIT>127</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>113</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>115</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>128</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_279">
              <SHORT-NAME>DOP_NRC_Routine_Control_RequestResults</SHORT-NAME>
              <LONG-NAME>DOP NRC Routine Control Request Results</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>36</LOWER-LIMIT>
                      <UPPER-LIMIT>36</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request sequence error</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>114</LOWER-LIMIT>
                      <UPPER-LIMIT>114</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>General programming failure</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>17</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>35</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>37</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>113</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>115</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>127</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_292">
              <SHORT-NAME>LocalTable</SHORT-NAME>
              <LONG-NAME>LocalTable</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>16777215</LOWER-LIMIT>
                      <UPPER-LIMIT>16777215</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>All groups</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-TYPE-ENCODING="NONE" BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>24</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16777214</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_293">
              <SHORT-NAME>ReadNumber_DOP</SHORT-NAME>
              <LONG-NAME>Read (number)_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_294">
              <SHORT-NAME>ReadAllIdentified_DOP</SHORT-NAME>
              <LONG-NAME>Read - DTCs by status mask_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>17</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_295">
              <SHORT-NAME>Clear_DOP</SHORT-NAME>
              <LONG-NAME>Clear DTC_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>18</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_296">
              <SHORT-NAME>TransferRequestParameterRecord</SHORT-NAME>
              <LONG-NAME>TransferRequestParameterRecord</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>IDENTICAL</CATEGORY>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_BYTEFIELD" TERMINATION="END-OF-PDU" xsi:type="MIN-MAX-LENGTH-TYPE">
                <MIN-LENGTH>1</MIN-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_BYTEFIELD"/>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_297">
              <SHORT-NAME>Request_DOP_2</SHORT-NAME>
              <LONG-NAME>Request_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>19</LOWER-LIMIT>
                      <UPPER-LIMIT>19</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Incorrect message length or invalid format</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>34</LOWER-LIMIT>
                      <UPPER-LIMIT>34</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Conditions not correct</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>51</LOWER-LIMIT>
                      <UPPER-LIMIT>51</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Security access denied</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>112</LOWER-LIMIT>
                      <UPPER-LIMIT>112</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Upload / Download not accepted</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>113</LOWER-LIMIT>
                      <UPPER-LIMIT>113</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Transfer data suspended</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>114</LOWER-LIMIT>
                      <UPPER-LIMIT>114</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>General programming failure</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>115</LOWER-LIMIT>
                      <UPPER-LIMIT>115</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Wrong block sequence counter</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>120</LOWER-LIMIT>
                      <UPPER-LIMIT>120</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request correctly received - response pending</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>126</LOWER-LIMIT>
                      <UPPER-LIMIT>126</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>127</LOWER-LIMIT>
                      <UPPER-LIMIT>127</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported in active session</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>20</LOWER-LIMIT>
                    <UPPER-LIMIT>33</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>35</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>50</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>52</LOWER-LIMIT>
                    <UPPER-LIMIT>111</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>116</LOWER-LIMIT>
                    <UPPER-LIMIT>119</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>121</LOWER-LIMIT>
                    <UPPER-LIMIT>125</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>128</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
            <DATA-OBJECT-PROP ID="_298">
              <SHORT-NAME>NRC_DOP</SHORT-NAME>
              <LONG-NAME>NEGATIVE RESPONSE CODES_DOP</LONG-NAME>
              <COMPU-METHOD>
                <CATEGORY>TEXTTABLE</CATEGORY>
                <COMPU-INTERNAL-TO-PHYS>
                  <COMPU-SCALES>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>17</LOWER-LIMIT>
                      <UPPER-LIMIT>17</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Service not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>18</LOWER-LIMIT>
                      <UPPER-LIMIT>18</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Subfunction not supported</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                    <COMPU-SCALE>
                      <LOWER-LIMIT>49</LOWER-LIMIT>
                      <UPPER-LIMIT>49</UPPER-LIMIT>
                      <COMPU-CONST>
                        <VT>Request out of range</VT>
                      </COMPU-CONST>
                    </COMPU-SCALE>
                  </COMPU-SCALES>
                </COMPU-INTERNAL-TO-PHYS>
              </COMPU-METHOD>
              <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                <BIT-LENGTH>8</BIT-LENGTH>
              </DIAG-CODED-TYPE>
              <PHYSICAL-TYPE BASE-DATA-TYPE="A_UNICODE2STRING"/>
              <INTERNAL-CONSTR>
                <SCALE-CONSTRS>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>0</LOWER-LIMIT>
                    <UPPER-LIMIT>16</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>19</LOWER-LIMIT>
                    <UPPER-LIMIT>48</UPPER-LIMIT>
                  </SCALE-CONSTR>
                  <SCALE-CONSTR VALIDITY="NOT-DEFINED">
                    <LOWER-LIMIT>50</LOWER-LIMIT>
                    <UPPER-LIMIT>255</UPPER-LIMIT>
                  </SCALE-CONSTR>
                </SCALE-CONSTRS>
              </INTERNAL-CONSTR>
            </DATA-OBJECT-PROP>
          </DATA-OBJECT-PROPS>
          <STRUCTURES>
            <STRUCTURE ID="_185">
              <SHORT-NAME>activeDiagnosticSessionDataIdentifier</SHORT-NAME>
              <LONG-NAME>activeDiagnosticSessionDataIdentifier</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Active_Diagnostic_Session</SHORT-NAME>
                  <LONG-NAME>Active_Diagnostic_Session</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_186">
              <SHORT-NAME>GAC_ECUSerialNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>GAC ECUSerialNumberDataIdentifier</LONG-NAME>
              <BYTE-SIZE>21</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Serial_Number</SHORT-NAME>
                  <LONG-NAME>Serial_Number</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_146"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_187">
              <SHORT-NAME>systemSupplier_ECU_Hardware_Version</SHORT-NAME>
              <LONG-NAME>systemSupplier ECU Hardware Version</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Supplier_Hardware_Version</SHORT-NAME>
                  <LONG-NAME>Supplier_Hardware_Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_163"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_188">
              <SHORT-NAME>systemSupplierECUSoftwareVersion</SHORT-NAME>
              <LONG-NAME>systemSupplierECUSoftwareVersion</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Supplier_Software_Version</SHORT-NAME>
                  <LONG-NAME>Supplier_Software_Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_163"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_189">
              <SHORT-NAME>Manufactory_mode</SHORT-NAME>
              <LONG-NAME>Manufactory mode</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Manufactory_mode</SHORT-NAME>
                  <LONG-NAME>Manufactory_mode</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_190">
              <SHORT-NAME>Usage_mode</SHORT-NAME>
              <LONG-NAME>Usage mode</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Usage_mode</SHORT-NAME>
                  <LONG-NAME>Usage_mode</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_191">
              <SHORT-NAME>DTC_Setting_control_state</SHORT-NAME>
              <LONG-NAME>DTC Setting control state</LONG-NAME>
              <BYTE-SIZE>12</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DTC_Setting_control</SHORT-NAME>
                  <LONG-NAME>DTC-Setting_control</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_161"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_192">
              <SHORT-NAME>Reprogramming_Counter</SHORT-NAME>
              <LONG-NAME>Reprogramming Counter</LONG-NAME>
              <BYTE-SIZE>2</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Success_Counter</SHORT-NAME>
                  <LONG-NAME>Success_Counter</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_193">
              <SHORT-NAME>Reprogramming_Attempt_Counter</SHORT-NAME>
              <LONG-NAME>Reprogramming Attempt Counter</LONG-NAME>
              <BYTE-SIZE>2</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Attempt_Counter</SHORT-NAME>
                  <LONG-NAME>Attempt_Counter</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_194">
              <SHORT-NAME>GAC_Diagnostic_Parameter_Table_Version</SHORT-NAME>
              <LONG-NAME>GAC Diagnostic Parameter Table Version</LONG-NAME>
              <BYTE-SIZE>4</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Diagnostic_Version</SHORT-NAME>
                  <LONG-NAME>Diagnostic_ Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_11"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_195">
              <SHORT-NAME>GACECUHardwareVersionNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>GACECUHardwareVersionNumberDataIdentifier</LONG-NAME>
              <BYTE-SIZE>17</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Manufacturer_Hardware_Version</SHORT-NAME>
                  <LONG-NAME>Manufacturer_Hardware_Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_165"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_196">
              <SHORT-NAME>bootSoftwareIdentification</SHORT-NAME>
              <LONG-NAME>bootSoftwareIdentification</LONG-NAME>
              <BYTE-SIZE>17</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Bootloader_Version</SHORT-NAME>
                  <LONG-NAME>Bootloader_Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_165"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_197">
              <SHORT-NAME>ApplicationSoftwareFingerprintDataIdentifier</SHORT-NAME>
              <LONG-NAME>ApplicationSoftwareFingerprintDataIdentifier</LONG-NAME>
              <BYTE-SIZE>10</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Tool_Identification</SHORT-NAME>
                  <LONG-NAME>Tool_Identification</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Years</SHORT-NAME>
                  <LONG-NAME>Years</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Months</SHORT-NAME>
                  <LONG-NAME>Months</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Days</SHORT-NAME>
                  <LONG-NAME>Days</LONG-NAME>
                  <BYTE-POSITION>3</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Device_Number</SHORT-NAME>
                  <LONG-NAME>Device_Number</LONG-NAME>
                  <BYTE-POSITION>4</BYTE-POSITION>
                  <DOP-REF ID-REF="_96"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_198">
              <SHORT-NAME>GAC_SparePartNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>GAC SparePartNumberDataIdentifier</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Manufacturer_Part_Number</SHORT-NAME>
                  <LONG-NAME>Manufacturer_Part_Number</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_163"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_199">
              <SHORT-NAME>GACECUSoftwareVersionNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>GACECUSoftwareVersionNumberDataIdentifier</LONG-NAME>
              <BYTE-SIZE>17</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Manufacturer_Software_Version</SHORT-NAME>
                  <LONG-NAME>Manufacturer_Software_Version</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_165"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_200">
              <SHORT-NAME>GAC_KitAssemblyPartNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>GAC KitAssemblyPartNumberDataIdentifier</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Manufacturer_Part_Number</SHORT-NAME>
                  <LONG-NAME>Manufacturer_Part_Number</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_163"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_201">
              <SHORT-NAME>programmingDateDataIdentifier</SHORT-NAME>
              <LONG-NAME>programmingDateDataIdentifier</LONG-NAME>
              <BYTE-SIZE>4</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>year_H</SHORT-NAME>
                  <LONG-NAME>year_H</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Year</SHORT-NAME>
                  <LONG-NAME>Year</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Month</SHORT-NAME>
                  <LONG-NAME>Month</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Day</SHORT-NAME>
                  <LONG-NAME>Day</LONG-NAME>
                  <BYTE-POSITION>3</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_202">
              <SHORT-NAME>Power_Voltage</SHORT-NAME>
              <LONG-NAME>Power Voltage</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_DataObject</SHORT-NAME>
                  <LONG-NAME>DID DataObject</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_203">
              <SHORT-NAME>OTA_mode</SHORT-NAME>
              <LONG-NAME>OTA mode</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>OTA_mode</SHORT-NAME>
                  <LONG-NAME>OTA_mode</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_204">
              <SHORT-NAME>Suction_top_screen_state</SHORT-NAME>
              <LONG-NAME>Suction top screen state</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Neues_Datenobjekt</SHORT-NAME>
                  <LONG-NAME>Neues Datenobjekt</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_163"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_205">
              <SHORT-NAME>Suction_top_screen_calibration_status_query</SHORT-NAME>
              <LONG-NAME>Suction top screen calibration status query</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Neues_Datenobjekt</SHORT-NAME>
                  <LONG-NAME>Neues Datenobjekt</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_206">
              <SHORT-NAME>OTA_Recovery_Status</SHORT-NAME>
              <LONG-NAME>OTA Recovery Status</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Neues_Datenobjekt</SHORT-NAME>
                  <LONG-NAME>Neues Datenobjekt</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_207">
              <SHORT-NAME>OTA_Partition_synchronization_status</SHORT-NAME>
              <LONG-NAME>OTA Partition synchronization status</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Neues_Datenobjekt</SHORT-NAME>
                  <LONG-NAME>Neues Datenobjekt</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_208">
              <SHORT-NAME>Motor_control</SHORT-NAME>
              <LONG-NAME>Motor control</LONG-NAME>
              <BYTE-SIZE>3</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>direction</SHORT-NAME>
                  <LONG-NAME>direction</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_180"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Value_H</SHORT-NAME>
                  <LONG-NAME>Value_H</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Value_L</SHORT-NAME>
                  <LONG-NAME>Value_L</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_209">
              <SHORT-NAME>Snapshot_Record</SHORT-NAME>
              <LONG-NAME>Snapshot Record</LONG-NAME>
              <BYTE-SIZE>14</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Suction_top_screen_motor_speed</SHORT-NAME>
                  <LONG-NAME>Suction top screen motor speed</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Suction_top_screen_motor_current_value</SHORT-NAME>
                  <LONG-NAME>Suction top screen motor current value</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Suction_top_screen_Angle</SHORT-NAME>
                  <LONG-NAME>Suction top screen Angle</LONG-NAME>
                  <BYTE-POSITION>4</BYTE-POSITION>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Usage_mode_status</SHORT-NAME>
                  <LONG-NAME>Usage mode status</LONG-NAME>
                  <BYTE-POSITION>6</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Lock_tongue_condition</SHORT-NAME>
                  <LONG-NAME>Lock tongue condition</LONG-NAME>
                  <BYTE-POSITION>7</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>The_suction_top_screen_is_open_and_closed</SHORT-NAME>
                  <LONG-NAME>The suction top screen is open and closed</LONG-NAME>
                  <BYTE-POSITION>8</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Ambient_temperature</SHORT-NAME>
                  <LONG-NAME>Ambient temperature</LONG-NAME>
                  <BYTE-POSITION>9</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>reserved</SHORT-NAME>
                  <LONG-NAME>reserved</LONG-NAME>
                  <BYTE-POSITION>10</BYTE-POSITION>
                  <DOP-REF ID-REF="_11"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_221">
              <SHORT-NAME>TR_Identification_Write_RQ_GAC_Diagnostic_Parameter_Table_Version</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GAC Diagnostic Parameter Table Version</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF10B</SHORT-NAME>
                  <LONG-NAME>DID 0xF10B</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_194"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_222">
              <SHORT-NAME>TR_Identification_Write_RQ_GACECUHardwareVersionNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GACECUHardwareVersionNumberDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF17F</SHORT-NAME>
                  <LONG-NAME>DID 0xF17F</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_195"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_223">
              <SHORT-NAME>TR_Identification_Write_RQ_bootSoftwareIdentification</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ bootSoftwareIdentification</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF180</SHORT-NAME>
                  <LONG-NAME>DID 0xF180</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_196"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_224">
              <SHORT-NAME>TR_Identification_Write_RQ_Usage_mode</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ Usage mode</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x3400</SHORT-NAME>
                  <LONG-NAME>DID 0x3400</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_190"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_225">
              <SHORT-NAME>TR_Identification_Write_RQ_ApplicationSoftwareFingerprintDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ ApplicationSoftwareFingerprintDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF184</SHORT-NAME>
                  <LONG-NAME>DID 0xF184</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_197"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_226">
              <SHORT-NAME>TR_Identification_Write_RQ_activeDiagnosticSessionDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ activeDiagnosticSessionDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>F186_Active_Diagnostic_Session_123</SHORT-NAME>
                  <LONG-NAME>F186_Active_Diagnostic_Session_123</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_185"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_227">
              <SHORT-NAME>TR_Identification_Write_RQ_GAC_SparePartNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GAC SparePartNumberDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF187</SHORT-NAME>
                  <LONG-NAME>DID 0xF187</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_198"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_228">
              <SHORT-NAME>TR_Identification_Write_RQ_GACECUSoftwareVersionNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GACECUSoftwareVersionNumberDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF189</SHORT-NAME>
                  <LONG-NAME>DID 0xF189</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_199"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_229">
              <SHORT-NAME>TR_Identification_Write_RQ_GAC_KitAssemblyPartNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GAC KitAssemblyPartNumberDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF18E</SHORT-NAME>
                  <LONG-NAME>DID 0xF18E</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_200"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_230">
              <SHORT-NAME>TR_Identification_Write_RQ_systemSupplier_ECU_Hardware_Version</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ systemSupplier ECU Hardware Version</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>F193_Supplier_Hardware_Version_123</SHORT-NAME>
                  <LONG-NAME>F193_Supplier_Hardware_Version_123</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_187"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_231">
              <SHORT-NAME>TR_Identification_Write_RQ_systemSupplierECUSoftwareVersion</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ systemSupplierECUSoftwareVersion</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>F195_Supplier_Software_Version_13</SHORT-NAME>
                  <LONG-NAME>F195_Supplier_Software_Version_13</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_188"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_232">
              <SHORT-NAME>TR_Identification_Write_RQ_programmingDateDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ programmingDateDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF199</SHORT-NAME>
                  <LONG-NAME>DID 0xF199</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_201"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_233">
              <SHORT-NAME>TR_Identification_Write_RQ_Suction_top_screen_state</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ Suction top screen state</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xF19F</SHORT-NAME>
                  <LONG-NAME>DID 0xF19F</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_204"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_234">
              <SHORT-NAME>TR_Identification_Write_RQ_Suction_Top_Screen_Calibration_Status_Query</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ Suction top screen calibration status query</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0xAE3C</SHORT-NAME>
                  <LONG-NAME>DID 0xAE3C</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_205"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_235">
              <SHORT-NAME>TR_Identification_Write_RQ_OTA_Recovery_Status</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ OTA Recovery Status</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x2110</SHORT-NAME>
                  <LONG-NAME>DID 0x2110</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_206"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_236">
              <SHORT-NAME>TR_Identification_Write_RQ_OTA_Partition_synchronization_status</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ OTA Partition synchronization status</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x2111</SHORT-NAME>
                  <LONG-NAME>DID 0x2111</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_207"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_237">
              <SHORT-NAME>TR_Identification_Write_RQ_GAC_ECUSerialNumberDataIdentifier</SHORT-NAME>
              <LONG-NAME>TR Identification Write RQ GAC ECUSerialNumberDataIdentifier</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>F18C_ECU_Serial_Number_13</SHORT-NAME>
                  <LONG-NAME>F18C_ECU_Serial_Number_13</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_186"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_243">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_Manufactory_mode</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ Manufactory mode</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x0110</SHORT-NAME>
                  <LONG-NAME>DID 0x0110</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_189"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_244">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_Reprogramming_Counter</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ Reprogramming Counter</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x0200</SHORT-NAME>
                  <LONG-NAME>DID 0x0200</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_192"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_245">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_Reprogramming_Attempt_Counter</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ Reprogramming Attempt Counter</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x0201</SHORT-NAME>
                  <LONG-NAME>DID 0x0201</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_193"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_246">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_Power_Voltage</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ Power Voltage</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x1000</SHORT-NAME>
                  <LONG-NAME>DID 0x1000</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_202"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_247">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_OTA_mode</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ OTA mode</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x5005</SHORT-NAME>
                  <LONG-NAME>DID 0x5005</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_203"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_248">
              <SHORT-NAME>TR_Stored_Data_Write_RQ_DTC_Setting_control_state</SHORT-NAME>
              <LONG-NAME>TR Stored Data Write RQ DTC Setting control state</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DID_0x0120</SHORT-NAME>
                  <LONG-NAME>DID 0x0120</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_191"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_254">
              <SHORT-NAME>TR_ControlStatusRecord_Motor_control</SHORT-NAME>
              <LONG-NAME>TR ControlStatusRecord Motor control</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Motor_control</SHORT-NAME>
                  <LONG-NAME>Motor control</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_208"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_256">
              <SHORT-NAME>TR_IO_Control_Control_RQ_Motor_control</SHORT-NAME>
              <LONG-NAME>TR IOControl_Control_RQ Motor control</LONG-NAME>
              <PARAMS>
                <PARAM xsi:type="VALUE">
                  <SHORT-NAME>ControlOptionRecord</SHORT-NAME>
                  <LONG-NAME>ControlOptionRecord</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_254"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_262">
              <SHORT-NAME>TR_Routine_Control_Start_RQ_Start_Check_Routine</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start RQ Start Check Routine</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>CheckSum</SHORT-NAME>
                  <LONG-NAME>CheckSum</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_11"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_263">
              <SHORT-NAME>TR_Routine_Control_Start_RQ_Start_Erase_Memory</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start RQ Start Erase Memory</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>AddressAndLengthFormatIdentifier</SHORT-NAME>
                  <LONG-NAME>AddressAndLengthFormatIdentifier</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>MemoryAddress</SHORT-NAME>
                  <LONG-NAME>MemoryAddress</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_11"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>MemorySize</SHORT-NAME>
                  <LONG-NAME>MemorySize</LONG-NAME>
                  <BYTE-POSITION>5</BYTE-POSITION>
                  <DOP-REF ID-REF="_11"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_265">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Suction_top_screen_self_calibration_routine_for_supplier</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Suction top screen self-calibration routine (for supplier)</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Execution_failed</SHORT-NAME>
                  <LONG-NAME>Execution failed</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_266">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Start_Check_Routine</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Start Check Routine</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_267">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Start_Erase_Memory</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Start Erase Memory</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_268">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Check_Programming_Dependencies</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Check Programming Dependencies</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_269">
              <SHORT-NAME>TR_Routine_Control_Start_PR_GAC_suction_top_screen_self_calibration_routine</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR GAC suction top screen self-calibration routine</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>routineStatus</SHORT-NAME>
                  <LONG-NAME>routineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Execution_failed</SHORT-NAME>
                  <LONG-NAME>Execution failed</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_270">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Check_Lock_Condition_RunSet</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Check_Lock_Condition_RunSet</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>routineStatus</SHORT-NAME>
                  <LONG-NAME>routineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Execution_failed</SHORT-NAME>
                  <LONG-NAME>Execution failed</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_271">
              <SHORT-NAME>TR_Routine_Control_Start_PR_Check_Unlock_Condition_RunSet</SHORT-NAME>
              <LONG-NAME>TR Routine Control Start PR Check_Unlock_Condition_RunSet</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>routineStatus</SHORT-NAME>
                  <LONG-NAME>routineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Execution_failed</SHORT-NAME>
                  <LONG-NAME>Execution failed</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_273">
              <SHORT-NAME>TR_Routine_Control_RequestResults_RQ_Start_Software_Recovery</SHORT-NAME>
              <LONG-NAME>TR Routine Control Request Results RQ Start Software Recovery</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>requestRoutineStatus</SHORT-NAME>
                  <LONG-NAME>requestRoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_274">
              <SHORT-NAME>TR_Routine_Control_RequestResults_RQ_Start_A_B_Partition_synchronization</SHORT-NAME>
              <LONG-NAME>TR Routine Control Request Results RQ Start A/B Partition synchronization</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>requestRoutineStatus1</SHORT-NAME>
                  <LONG-NAME>requestRoutineStatus1</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_276">
              <SHORT-NAME>TR_Routine_Control_RequestResults_PR_Start_Software_Recovery</SHORT-NAME>
              <LONG-NAME>TR Routine Control Request Results PR Start Software Recovery</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_277">
              <SHORT-NAME>TR_Routine_Control_RequestResults_PR_Start_A_B_Partition_synchronization</SHORT-NAME>
              <LONG-NAME>TR Routine Control Request Results PR Start A/B Partition synchronization</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>RoutineStatus</SHORT-NAME>
                  <LONG-NAME>RoutineStatus</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_280">
              <SHORT-NAME>DtcStatusbyte_1</SHORT-NAME>
              <LONG-NAME>DTC Statusbyte</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailed</SHORT-NAME>
                  <LONG-NAME>Test failed</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_13"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test failed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>1</BIT-POSITION>
                  <DOP-REF ID-REF="_14"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>PendingDtc</SHORT-NAME>
                  <LONG-NAME>Pending DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>2</BIT-POSITION>
                  <DOP-REF ID-REF="_15"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>ConfirmedDtc</SHORT-NAME>
                  <LONG-NAME>Confirmed DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>3</BIT-POSITION>
                  <DOP-REF ID-REF="_16"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestNotCompletedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test not completed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>4</BIT-POSITION>
                  <DOP-REF ID-REF="_17"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test failed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>5</BIT-POSITION>
                  <DOP-REF ID-REF="_18"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestNotCompletedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test not completed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>6</BIT-POSITION>
                  <DOP-REF ID-REF="_19"/>
                </PARAM>
                <PARAM xsi:type="RESERVED">
                  <SHORT-NAME>WarningIndicatorRequested</SHORT-NAME>
                  <LONG-NAME>Warning indicator requested</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>7</BIT-POSITION>
                  <BIT-LENGTH>1</BIT-LENGTH>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_281">
              <SHORT-NAME>DtcStatusbyte_2</SHORT-NAME>
              <LONG-NAME>DTC Statusbyte</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailed</SHORT-NAME>
                  <LONG-NAME>Test failed</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_13"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test failed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>1</BIT-POSITION>
                  <DOP-REF ID-REF="_14"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>PendingDtc</SHORT-NAME>
                  <LONG-NAME>Pending DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>2</BIT-POSITION>
                  <DOP-REF ID-REF="_15"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>ConfirmedDtc</SHORT-NAME>
                  <LONG-NAME>Confirmed DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>3</BIT-POSITION>
                  <DOP-REF ID-REF="_16"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestNotCompletedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test not completed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>4</BIT-POSITION>
                  <DOP-REF ID-REF="_17"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestFailedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test failed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>5</BIT-POSITION>
                  <DOP-REF ID-REF="_18"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>TestNotCompletedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test not completed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>6</BIT-POSITION>
                  <DOP-REF ID-REF="_19"/>
                </PARAM>
                <PARAM xsi:type="RESERVED">
                  <SHORT-NAME>WarningIndicatorRequested</SHORT-NAME>
                  <LONG-NAME>Warning indicator requested</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>7</BIT-POSITION>
                  <BIT-LENGTH>1</BIT-LENGTH>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_283">
              <SHORT-NAME>DtcStatusbyte_3</SHORT-NAME>
              <LONG-NAME>DTC Statusbyte</LONG-NAME>
              <BYTE-SIZE>1</BYTE-SIZE>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_TestFailed</SHORT-NAME>
                  <LONG-NAME>Test failed</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_13"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_TestFailedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test failed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>1</BIT-POSITION>
                  <DOP-REF ID-REF="_14"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_PendingDtc</SHORT-NAME>
                  <LONG-NAME>Pending DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>2</BIT-POSITION>
                  <DOP-REF ID-REF="_15"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_ConfirmedDtc</SHORT-NAME>
                  <LONG-NAME>Confirmed DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>3</BIT-POSITION>
                  <DOP-REF ID-REF="_16"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_TestNotCompletedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test not completed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>4</BIT-POSITION>
                  <DOP-REF ID-REF="_17"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_TestFailedSinceLastClear</SHORT-NAME>
                  <LONG-NAME>Test failed since last clear</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>5</BIT-POSITION>
                  <DOP-REF ID-REF="_18"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_TestNotCompletedThisMonitoringCycle</SHORT-NAME>
                  <LONG-NAME>Test not completed this monitoring cycle</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>6</BIT-POSITION>
                  <DOP-REF ID-REF="_19"/>
                </PARAM>
                <PARAM xsi:type="RESERVED">
                  <SHORT-NAME>StatusOfDTC_WarningIndicatorRequested</SHORT-NAME>
                  <LONG-NAME>Warning indicator requested</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <BIT-POSITION>7</BIT-POSITION>
                  <BIT-LENGTH>1</BIT-LENGTH>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_284">
              <SHORT-NAME>ListOfDTC</SHORT-NAME>
              <LONG-NAME>ListOfDTC</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DTC</SHORT-NAME>
                  <LONG-NAME>DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_282"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_DtcStatusbyte_STRUCTURE</SHORT-NAME>
                  <LONG-NAME>DTC Statusbyte</LONG-NAME>
                  <BYTE-POSITION>3</BYTE-POSITION>
                  <DOP-REF ID-REF="_283"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_286">
              <SHORT-NAME>DTCExtendedData</SHORT-NAME>
              <LONG-NAME>DTCExtendedData</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Failure_Counter</SHORT-NAME>
                  <LONG-NAME>Failure_Counter</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Ageing_Counter</SHORT-NAME>
                  <LONG-NAME>Ageing_Counter</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_288">
              <SHORT-NAME>DTCSnapshotData</SHORT-NAME>
              <LONG-NAME>DTCSnapshotData</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>New_Data_Object</SHORT-NAME>
                  <LONG-NAME>New Data Object</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DTCSnapshotRecordNumberOfIdentifiers</SHORT-NAME>
                  <LONG-NAME>DTCSnapshotRecordNumberOfIdentifiers</LONG-NAME>
                  <BYTE-POSITION>1</BYTE-POSITION>
                  <DOP-REF ID-REF="_9"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="PHYS-CONST">
                  <SHORT-NAME>Snapshot_Record_ID</SHORT-NAME>
                  <LONG-NAME>Snapshot Record ID</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <PHYS-CONSTANT-VALUE>1282</PHYS-CONSTANT-VALUE>
                  <DOP-REF ID-REF="_10"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>Snapshot_Record</SHORT-NAME>
                  <LONG-NAME>Snapshot Record</LONG-NAME>
                  <BYTE-POSITION>4</BYTE-POSITION>
                  <DOP-REF ID-REF="_209"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_290">
              <SHORT-NAME>ListOfDTCAndStatus</SHORT-NAME>
              <LONG-NAME>ListOfDTCAndStatus</LONG-NAME>
              <PARAMS>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>DTC</SHORT-NAME>
                  <LONG-NAME>DTC</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <DOP-REF ID-REF="_282"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                  <SHORT-NAME>StatusOfDTC_DtcStatusbyte_STRUCTURE</SHORT-NAME>
                  <LONG-NAME>DTC Statusbyte</LONG-NAME>
                  <BYTE-POSITION>3</BYTE-POSITION>
                  <DOP-REF ID-REF="_283"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_545">
              <SHORT-NAME>STR_EOP_DIDs_Identification_ReadRDBI_RQ</SHORT-NAME>
              <PARAMS>
                <PARAM SEMANTIC="ID" ID="_547" xsi:type="TABLE-KEY">
                  <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                  <LONG-NAME>Identifier</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <TABLE-REF ID-REF="_219"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_549">
              <SHORT-NAME>STR_EOP_DIDs_Identification_ReadRDBI_PR</SHORT-NAME>
              <PARAMS>
                <PARAM SEMANTIC="ID" ID="_551" xsi:type="TABLE-KEY">
                  <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                  <LONG-NAME>Identifier</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <TABLE-REF ID-REF="_219"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="TABLE-STRUCT">
                  <SHORT-NAME>DataRecord</SHORT-NAME>
                  <LONG-NAME>DataRecord</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <TABLE-KEY-REF ID-REF="_551"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_569">
              <SHORT-NAME>STR_EOP_DIDs_Stored_Data_ReadRDBI_RQ</SHORT-NAME>
              <PARAMS>
                <PARAM SEMANTIC="ID" ID="_571" xsi:type="TABLE-KEY">
                  <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                  <LONG-NAME>Identifier</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <TABLE-REF ID-REF="_242"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
            <STRUCTURE ID="_573">
              <SHORT-NAME>STR_EOP_DIDs_Stored_Data_ReadRDBI_PR</SHORT-NAME>
              <PARAMS>
                <PARAM SEMANTIC="ID" ID="_575" xsi:type="TABLE-KEY">
                  <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                  <LONG-NAME>Identifier</LONG-NAME>
                  <BYTE-POSITION>0</BYTE-POSITION>
                  <TABLE-REF ID-REF="_242"/>
                </PARAM>
                <PARAM SEMANTIC="DATA" xsi:type="TABLE-STRUCT">
                  <SHORT-NAME>DataRecord</SHORT-NAME>
                  <LONG-NAME>DataRecord</LONG-NAME>
                  <BYTE-POSITION>2</BYTE-POSITION>
                  <TABLE-KEY-REF ID-REF="_575"/>
                </PARAM>
              </PARAMS>
            </STRUCTURE>
          </STRUCTURES>
          <END-OF-PDU-FIELDS>
            <END-OF-PDU-FIELD ID="_285">
              <SHORT-NAME>ListOfDTC_1</SHORT-NAME>
              <LONG-NAME>ListOfDTC</LONG-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_284"/>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_287">
              <SHORT-NAME>DTCExtendedData_1</SHORT-NAME>
              <LONG-NAME>DTCExtendedData</LONG-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_286"/>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_289">
              <SHORT-NAME>DTCSnapshotData_1</SHORT-NAME>
              <LONG-NAME>DTCSnapshotData</LONG-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_288"/>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_291">
              <SHORT-NAME>ListOfDTCAndStatus_1</SHORT-NAME>
              <LONG-NAME>ListOfDTCAndStatus</LONG-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_290"/>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_546">
              <SHORT-NAME>EOP_DIDs_Identification_ReadRDBI_RQ</SHORT-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_545"/>
              <MAX-NUMBER-OF-ITEMS>5</MAX-NUMBER-OF-ITEMS>
              <MIN-NUMBER-OF-ITEMS>1</MIN-NUMBER-OF-ITEMS>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_550">
              <SHORT-NAME>EOP_DIDs_Identification_ReadRDBI_PR</SHORT-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_549"/>
              <MAX-NUMBER-OF-ITEMS>5</MAX-NUMBER-OF-ITEMS>
              <MIN-NUMBER-OF-ITEMS>1</MIN-NUMBER-OF-ITEMS>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_570">
              <SHORT-NAME>EOP_DIDs_Stored_Data_ReadRDBI_RQ</SHORT-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_569"/>
              <MAX-NUMBER-OF-ITEMS>5</MAX-NUMBER-OF-ITEMS>
              <MIN-NUMBER-OF-ITEMS>1</MIN-NUMBER-OF-ITEMS>
            </END-OF-PDU-FIELD>
            <END-OF-PDU-FIELD ID="_574">
              <SHORT-NAME>EOP_DIDs_Stored_Data_ReadRDBI_PR</SHORT-NAME>
              <BASIC-STRUCTURE-REF ID-REF="_573"/>
              <MAX-NUMBER-OF-ITEMS>5</MAX-NUMBER-OF-ITEMS>
              <MIN-NUMBER-OF-ITEMS>1</MIN-NUMBER-OF-ITEMS>
            </END-OF-PDU-FIELD>
          </END-OF-PDU-FIELDS>
          <TABLES>
            <TABLE ID="_219">
              <SHORT-NAME>Identification_Read_PR</SHORT-NAME>
              <LONG-NAME>Identification Read PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_363">
                <SHORT-NAME>GAC_Diagnostic_Parameter_Table_Version</SHORT-NAME>
                <LONG-NAME>GAC Diagnostic Parameter Table Version</LONG-NAME>
                <KEY>61707</KEY>
                <STRUCTURE-REF ID-REF="_221"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_364">
                <SHORT-NAME>GACECUHardwareVersionNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GACECUHardwareVersionNumberDataIdentifier</LONG-NAME>
                <KEY>61823</KEY>
                <STRUCTURE-REF ID-REF="_222"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_365">
                <SHORT-NAME>bootSoftwareIdentification</SHORT-NAME>
                <LONG-NAME>bootSoftwareIdentification</LONG-NAME>
                <KEY>61824</KEY>
                <STRUCTURE-REF ID-REF="_223"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_366">
                <SHORT-NAME>Usage_mode</SHORT-NAME>
                <LONG-NAME>Usage mode</LONG-NAME>
                <KEY>13312</KEY>
                <STRUCTURE-REF ID-REF="_224"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_367">
                <SHORT-NAME>ApplicationSoftwareFingerprintDataIdentifier</SHORT-NAME>
                <LONG-NAME>ApplicationSoftwareFingerprintDataIdentifier</LONG-NAME>
                <KEY>61828</KEY>
                <STRUCTURE-REF ID-REF="_225"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_368">
                <SHORT-NAME>activeDiagnosticSessionDataIdentifier</SHORT-NAME>
                <LONG-NAME>activeDiagnosticSessionDataIdentifier</LONG-NAME>
                <KEY>61830</KEY>
                <STRUCTURE-REF ID-REF="_226"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_369">
                <SHORT-NAME>GAC_SparePartNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GAC SparePartNumberDataIdentifier</LONG-NAME>
                <KEY>61831</KEY>
                <STRUCTURE-REF ID-REF="_227"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_370">
                <SHORT-NAME>GACECUSoftwareVersionNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GACECUSoftwareVersionNumberDataIdentifier</LONG-NAME>
                <KEY>61833</KEY>
                <STRUCTURE-REF ID-REF="_228"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_371">
                <SHORT-NAME>GAC_KitAssemblyPartNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GAC KitAssemblyPartNumberDataIdentifier</LONG-NAME>
                <KEY>61838</KEY>
                <STRUCTURE-REF ID-REF="_229"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_372">
                <SHORT-NAME>systemSupplier_ECU_Hardware_Version</SHORT-NAME>
                <LONG-NAME>systemSupplier ECU Hardware Version</LONG-NAME>
                <KEY>61843</KEY>
                <STRUCTURE-REF ID-REF="_230"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_373">
                <SHORT-NAME>systemSupplierECUSoftwareVersion</SHORT-NAME>
                <LONG-NAME>systemSupplierECUSoftwareVersion</LONG-NAME>
                <KEY>61845</KEY>
                <STRUCTURE-REF ID-REF="_231"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_374">
                <SHORT-NAME>programmingDateDataIdentifier</SHORT-NAME>
                <LONG-NAME>programmingDateDataIdentifier</LONG-NAME>
                <KEY>61849</KEY>
                <STRUCTURE-REF ID-REF="_232"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_375">
                <SHORT-NAME>Suction_top_screen_state</SHORT-NAME>
                <LONG-NAME>Suction top screen state</LONG-NAME>
                <KEY>44603</KEY>
                <STRUCTURE-REF ID-REF="_233"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_376">
                <SHORT-NAME>Suction_Top_Screen_Calibration_Status_Query</SHORT-NAME>
                <LONG-NAME>Suction top screen calibration status query</LONG-NAME>
                <KEY>44604</KEY>
                <STRUCTURE-REF ID-REF="_234"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_377">
                <SHORT-NAME>OTA_Recovery_Status</SHORT-NAME>
                <LONG-NAME>OTA Recovery Status</LONG-NAME>
                <KEY>8464</KEY>
                <STRUCTURE-REF ID-REF="_235"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_378">
                <SHORT-NAME>OTA_Partition_synchronization_status</SHORT-NAME>
                <LONG-NAME>OTA Partition synchronization status</LONG-NAME>
                <KEY>8465</KEY>
                <STRUCTURE-REF ID-REF="_236"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_379">
                <SHORT-NAME>GAC_ECUSerialNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GAC ECUSerialNumberDataIdentifier</LONG-NAME>
                <KEY>61836</KEY>
                <STRUCTURE-REF ID-REF="_237"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_238">
              <SHORT-NAME>Identification_Write_RQ</SHORT-NAME>
              <LONG-NAME>Identification Write RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_380">
                <SHORT-NAME>ApplicationSoftwareFingerprintDataIdentifier</SHORT-NAME>
                <LONG-NAME>ApplicationSoftwareFingerprintDataIdentifier</LONG-NAME>
                <KEY>61828</KEY>
                <STRUCTURE-REF ID-REF="_225"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_381">
                <SHORT-NAME>programmingDateDataIdentifier</SHORT-NAME>
                <LONG-NAME>programmingDateDataIdentifier</LONG-NAME>
                <KEY>61849</KEY>
                <STRUCTURE-REF ID-REF="_232"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_382">
                <SHORT-NAME>GAC_ECUSerialNumberDataIdentifier</SHORT-NAME>
                <LONG-NAME>GAC ECUSerialNumberDataIdentifier</LONG-NAME>
                <KEY>61836</KEY>
                <STRUCTURE-REF ID-REF="_237"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_242">
              <SHORT-NAME>Stored_Data_Read_PR</SHORT-NAME>
              <LONG-NAME>Stored Data Read PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_383">
                <SHORT-NAME>Manufactory_mode</SHORT-NAME>
                <LONG-NAME>Manufactory mode</LONG-NAME>
                <KEY>272</KEY>
                <STRUCTURE-REF ID-REF="_243"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_384">
                <SHORT-NAME>Reprogramming_Counter</SHORT-NAME>
                <LONG-NAME>Reprogramming Counter</LONG-NAME>
                <KEY>512</KEY>
                <STRUCTURE-REF ID-REF="_244"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_385">
                <SHORT-NAME>Reprogramming_Attempt_Counter</SHORT-NAME>
                <LONG-NAME>Reprogramming Attempt Counter</LONG-NAME>
                <KEY>513</KEY>
                <STRUCTURE-REF ID-REF="_245"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_386">
                <SHORT-NAME>Power_Voltage</SHORT-NAME>
                <LONG-NAME>Power Voltage</LONG-NAME>
                <KEY>4096</KEY>
                <STRUCTURE-REF ID-REF="_246"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_387">
                <SHORT-NAME>OTA_mode</SHORT-NAME>
                <LONG-NAME>OTA mode</LONG-NAME>
                <KEY>20485</KEY>
                <STRUCTURE-REF ID-REF="_247"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_388">
                <SHORT-NAME>DTC_Setting_control_state</SHORT-NAME>
                <LONG-NAME>DTC Setting control state</LONG-NAME>
                <KEY>288</KEY>
                <STRUCTURE-REF ID-REF="_248"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_249">
              <SHORT-NAME>Stored_Data_Write_RQ</SHORT-NAME>
              <LONG-NAME>Stored Data Write RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_389">
                <SHORT-NAME>Manufactory_mode</SHORT-NAME>
                <LONG-NAME>Manufactory mode</LONG-NAME>
                <KEY>272</KEY>
                <STRUCTURE-REF ID-REF="_243"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_390">
                <SHORT-NAME>DTC_Setting_control_state</SHORT-NAME>
                <LONG-NAME>DTC Setting control state</LONG-NAME>
                <KEY>288</KEY>
                <STRUCTURE-REF ID-REF="_248"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_252">
              <SHORT-NAME>IOControl_ReturnControl_RQ</SHORT-NAME>
              <LONG-NAME>IO Control ReturnControl RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_391">
                <SHORT-NAME>Motor_control</SHORT-NAME>
                <LONG-NAME>Motor control</LONG-NAME>
                <KEY>44704</KEY>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_253">
              <SHORT-NAME>IOControl_ReturnControl_PR</SHORT-NAME>
              <LONG-NAME>IO Control ReturnControl PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_392">
                <SHORT-NAME>Motor_control</SHORT-NAME>
                <LONG-NAME>Motor control</LONG-NAME>
                <KEY>44704</KEY>
                <STRUCTURE-REF ID-REF="_254"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_255">
              <SHORT-NAME>IOControl_Control_RQ</SHORT-NAME>
              <LONG-NAME>IO Control Control RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_393">
                <SHORT-NAME>Motor_control</SHORT-NAME>
                <LONG-NAME>Motor control</LONG-NAME>
                <KEY>44704</KEY>
                <STRUCTURE-REF ID-REF="_256"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_257">
              <SHORT-NAME>IOControl_Control_PR</SHORT-NAME>
              <LONG-NAME>IO Control Control PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_220"/>
              <TABLE-ROW ID="_394">
                <SHORT-NAME>Motor_control</SHORT-NAME>
                <LONG-NAME>Motor control</LONG-NAME>
                <KEY>44704</KEY>
                <STRUCTURE-REF ID-REF="_254"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_260">
              <SHORT-NAME>Routine_Control_Start_RQ</SHORT-NAME>
              <LONG-NAME>Routine Control Start RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_261"/>
              <TABLE-ROW ID="_395">
                <SHORT-NAME>Suction_top_screen_self_calibration_routine_for_supplier</SHORT-NAME>
                <LONG-NAME>Suction top screen self-calibration routine (for supplier)</LONG-NAME>
                <KEY>44605</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_396">
                <SHORT-NAME>Start_Check_Routine</SHORT-NAME>
                <LONG-NAME>Start Check Routine</LONG-NAME>
                <KEY>514</KEY>
                <STRUCTURE-REF ID-REF="_262"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_397">
                <SHORT-NAME>Start_Erase_Memory</SHORT-NAME>
                <LONG-NAME>Start Erase Memory</LONG-NAME>
                <KEY>65280</KEY>
                <STRUCTURE-REF ID-REF="_263"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_398">
                <SHORT-NAME>Check_Programming_Dependencies</SHORT-NAME>
                <LONG-NAME>Check Programming Dependencies</LONG-NAME>
                <KEY>65281</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_399">
                <SHORT-NAME>Check_Programming_Precondition</SHORT-NAME>
                <LONG-NAME>Check_Programming_Precondition</LONG-NAME>
                <KEY>515</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_400">
                <SHORT-NAME>GAC_suction_top_screen_self_calibration_routine</SHORT-NAME>
                <LONG-NAME>GAC suction top screen self-calibration routine</LONG-NAME>
                <KEY>44604</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_401">
                <SHORT-NAME>Check_Lock_Condition_RunSet</SHORT-NAME>
                <LONG-NAME>Check_Lock_Condition_RunSet</LONG-NAME>
                <KEY>44705</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_402">
                <SHORT-NAME>Check_Unlock_Condition_RunSet</SHORT-NAME>
                <LONG-NAME>Check_Unlock_Condition_RunSet</LONG-NAME>
                <KEY>44706</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_403">
                <SHORT-NAME>Start_Software_Recovery</SHORT-NAME>
                <LONG-NAME>Start Software Recovery</LONG-NAME>
                <KEY>8464</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_404">
                <SHORT-NAME>Start_A_B_Partition_synchronization</SHORT-NAME>
                <LONG-NAME>Start A/B Partition synchronization</LONG-NAME>
                <KEY>8465</KEY>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_264">
              <SHORT-NAME>Routine_Control_Start_PR</SHORT-NAME>
              <LONG-NAME>Routine Control Start PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_261"/>
              <TABLE-ROW ID="_405">
                <SHORT-NAME>Suction_top_screen_self_calibration_routine_for_supplier</SHORT-NAME>
                <LONG-NAME>Suction top screen self-calibration routine (for supplier)</LONG-NAME>
                <KEY>44605</KEY>
                <STRUCTURE-REF ID-REF="_265"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_406">
                <SHORT-NAME>Start_Check_Routine</SHORT-NAME>
                <LONG-NAME>Start Check Routine</LONG-NAME>
                <KEY>514</KEY>
                <STRUCTURE-REF ID-REF="_266"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_407">
                <SHORT-NAME>Start_Erase_Memory</SHORT-NAME>
                <LONG-NAME>Start Erase Memory</LONG-NAME>
                <KEY>65280</KEY>
                <STRUCTURE-REF ID-REF="_267"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_408">
                <SHORT-NAME>Check_Programming_Dependencies</SHORT-NAME>
                <LONG-NAME>Check Programming Dependencies</LONG-NAME>
                <KEY>65281</KEY>
                <STRUCTURE-REF ID-REF="_268"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_409">
                <SHORT-NAME>Check_Programming_Precondition</SHORT-NAME>
                <LONG-NAME>Check_Programming_Precondition</LONG-NAME>
                <KEY>515</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_410">
                <SHORT-NAME>GAC_suction_top_screen_self_calibration_routine</SHORT-NAME>
                <LONG-NAME>GAC suction top screen self-calibration routine</LONG-NAME>
                <KEY>44604</KEY>
                <STRUCTURE-REF ID-REF="_269"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_411">
                <SHORT-NAME>Check_Lock_Condition_RunSet</SHORT-NAME>
                <LONG-NAME>Check_Lock_Condition_RunSet</LONG-NAME>
                <KEY>44705</KEY>
                <STRUCTURE-REF ID-REF="_270"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_412">
                <SHORT-NAME>Check_Unlock_Condition_RunSet</SHORT-NAME>
                <LONG-NAME>Check_Unlock_Condition_RunSet</LONG-NAME>
                <KEY>44706</KEY>
                <STRUCTURE-REF ID-REF="_271"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_413">
                <SHORT-NAME>Start_Software_Recovery</SHORT-NAME>
                <LONG-NAME>Start Software Recovery</LONG-NAME>
                <KEY>8464</KEY>
              </TABLE-ROW>
              <TABLE-ROW ID="_414">
                <SHORT-NAME>Start_A_B_Partition_synchronization</SHORT-NAME>
                <LONG-NAME>Start A/B Partition synchronization</LONG-NAME>
                <KEY>8465</KEY>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_272">
              <SHORT-NAME>Routine_Control_RequestResults_RQ</SHORT-NAME>
              <LONG-NAME>Routine Control Request Results RQ</LONG-NAME>
              <KEY-DOP-REF ID-REF="_261"/>
              <TABLE-ROW ID="_415">
                <SHORT-NAME>Start_Software_Recovery</SHORT-NAME>
                <LONG-NAME>Start Software Recovery</LONG-NAME>
                <KEY>8464</KEY>
                <STRUCTURE-REF ID-REF="_273"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_416">
                <SHORT-NAME>Start_A_B_Partition_synchronization</SHORT-NAME>
                <LONG-NAME>Start A/B Partition synchronization</LONG-NAME>
                <KEY>8465</KEY>
                <STRUCTURE-REF ID-REF="_274"/>
              </TABLE-ROW>
            </TABLE>
            <TABLE ID="_275">
              <SHORT-NAME>Routine_Control_RequestResults_PR</SHORT-NAME>
              <LONG-NAME>Routine Control Request Results PR</LONG-NAME>
              <KEY-DOP-REF ID-REF="_261"/>
              <TABLE-ROW ID="_417">
                <SHORT-NAME>Start_Software_Recovery</SHORT-NAME>
                <LONG-NAME>Start Software Recovery</LONG-NAME>
                <KEY>8464</KEY>
                <STRUCTURE-REF ID-REF="_276"/>
              </TABLE-ROW>
              <TABLE-ROW ID="_418">
                <SHORT-NAME>Start_A_B_Partition_synchronization</SHORT-NAME>
                <LONG-NAME>Start A/B Partition synchronization</LONG-NAME>
                <KEY>8465</KEY>
                <STRUCTURE-REF ID-REF="_277"/>
              </TABLE-ROW>
            </TABLE>
          </TABLES>
        </DIAG-DATA-DICTIONARY-SPEC>
        <DIAG-COMMS>
          <DIAG-SERVICE ID="_457" SEMANTIC="FUNCTION" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Hard_Reset_Reset</SHORT-NAME>
            <LONG-NAME>Hard_Reset Reset</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_458">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Hard_Reset</SD>
                <SD SI="DiagInstanceName">Hard_Reset</SD>
                <SD SI="ServiceQualifier">Reset</SD>
                <SD SI="ServiceName">Reset</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Reset_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <STATE-TRANSITION-REFS>
              <STATE-TRANSITION-REF ID-REF="Default_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Programming_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Extended_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Locked_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="UnlockedL1_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Unlocked_L2_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </STATE-TRANSITION-REFS>
            <REQUEST-REF ID-REF="_453"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_455"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_456"/>
            </NEG-RESPONSE-REFS>
            <POS-RESPONSE-SUPPRESSABLE>
              <BIT-MASK>80</BIT-MASK>
              <CODED-CONST-SNREF SHORT-NAME="ResetType"/>
            </POS-RESPONSE-SUPPRESSABLE>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_463" SEMANTIC="FUNCTION" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Key_Off_On_Reset</SHORT-NAME>
            <LONG-NAME>Key_Off_On Reset</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_464">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Key_Off_On</SD>
                <SD SI="DiagInstanceName">Key_Off_On</SD>
                <SD SI="ServiceQualifier">Reset</SD>
                <SD SI="ServiceName">Reset</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Reset_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <STATE-TRANSITION-REFS>
              <STATE-TRANSITION-REF ID-REF="Default_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Programming_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Extended_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Locked_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="UnlockedL1_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Unlocked_L2_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </STATE-TRANSITION-REFS>
            <REQUEST-REF ID-REF="_459"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_461"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_462"/>
            </NEG-RESPONSE-REFS>
            <POS-RESPONSE-SUPPRESSABLE>
              <BIT-MASK>80</BIT-MASK>
              <CODED-CONST-SNREF SHORT-NAME="ResetType"/>
            </POS-RESPONSE-SUPPRESSABLE>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_469" SEMANTIC="FUNCTION" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Soft_Reset_Reset</SHORT-NAME>
            <LONG-NAME>Soft_Reset Reset</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_470">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Soft_Reset</SD>
                <SD SI="DiagInstanceName">Soft_Reset</SD>
                <SD SI="ServiceQualifier">Reset</SD>
                <SD SI="ServiceName">Reset</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Reset_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <STATE-TRANSITION-REFS>
              <STATE-TRANSITION-REF ID-REF="Default_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Programming_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Extended_Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Locked_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="UnlockedL1_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <STATE-TRANSITION-REF ID-REF="Unlocked_L2_Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </STATE-TRANSITION-REFS>
            <REQUEST-REF ID-REF="_465"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_467"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_468"/>
            </NEG-RESPONSE-REFS>
            <POS-RESPONSE-SUPPRESSABLE>
              <BIT-MASK>80</BIT-MASK>
              <CODED-CONST-SNREF SHORT-NAME="ResetType"/>
            </POS-RESPONSE-SUPPRESSABLE>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_474" SEMANTIC="SECURITY">
            <SHORT-NAME>Security_Access_Level_01_Seed_Request</SHORT-NAME>
            <LONG-NAME>Security_Access_Level_01_Seed Request</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_475">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Security_Access_Level_01_Seed</SD>
                <SD SI="DiagInstanceName">Security_Access_Level_01_Seed</SD>
                <SD SI="ServiceQualifier">Request</SD>
                <SD SI="ServiceName">Request</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_471"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_472"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_473"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_479" SEMANTIC="SECURITY">
            <SHORT-NAME>Security_Access_Level_01_Key_Send</SHORT-NAME>
            <LONG-NAME>Security_Access_Level_01_Key Send</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_480">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Security_Access_Level_01_Key</SD>
                <SD SI="DiagInstanceName">Security_Access_Level_01_Key</SD>
                <SD SI="ServiceQualifier">Send</SD>
                <SD SI="ServiceName">Send</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <STATE-TRANSITION-REFS>
              <STATE-TRANSITION-REF ID-REF="Locked_UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </STATE-TRANSITION-REFS>
            <REQUEST-REF ID-REF="_476"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_477"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_478"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_485" SEMANTIC="SECURITY">
            <SHORT-NAME>Security_Access_Level_02_Seed_Request</SHORT-NAME>
            <LONG-NAME>Security_Access_Level_02_Seed Request</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_486">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Security_Access_Level_02_Seed</SD>
                <SD SI="DiagInstanceName">Security_Access_Level_02_Seed</SD>
                <SD SI="ServiceQualifier">Request</SD>
                <SD SI="ServiceName">Request</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_482"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_483"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_484"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_490" SEMANTIC="SECURITY">
            <SHORT-NAME>Security_Access_Level_02_Key_Send</SHORT-NAME>
            <LONG-NAME>Security_Access_Level_02_Key Send</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_491">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Security_Access_Level_02_Key</SD>
                <SD SI="DiagInstanceName">Security_Access_Level_02_Key</SD>
                <SD SI="ServiceQualifier">Send</SD>
                <SD SI="ServiceName">Send</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <STATE-TRANSITION-REFS>
              <STATE-TRANSITION-REF ID-REF="Locked_Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </STATE-TRANSITION-REFS>
            <REQUEST-REF ID-REF="_487"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_488"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_489"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_529" SEMANTIC="IDENTIFICATION" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Identification_Read</SHORT-NAME>
            <LONG-NAME>Identification Read</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_535">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Read</SD>
                <SD SI="ServiceName">Read</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61707</SD>
                  <SD SI="DiagInstanceQualifier">GAC_Diagnostic_Parameter_Table_Version</SD>
                  <SD SI="DiagInstanceName">GAC Diagnostic Parameter Table Version</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61823</SD>
                  <SD SI="DiagInstanceQualifier">GACECUHardwareVersionNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GACECUHardwareVersionNumberDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61824</SD>
                  <SD SI="DiagInstanceQualifier">bootSoftwareIdentification</SD>
                  <SD SI="DiagInstanceName">bootSoftwareIdentification</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">13312</SD>
                  <SD SI="DiagInstanceQualifier">Usage_mode</SD>
                  <SD SI="DiagInstanceName">Usage mode</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61828</SD>
                  <SD SI="DiagInstanceQualifier">ApplicationSoftwareFingerprintDataIdentifier</SD>
                  <SD SI="DiagInstanceName">ApplicationSoftwareFingerprintDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61830</SD>
                  <SD SI="DiagInstanceQualifier">activeDiagnosticSessionDataIdentifier</SD>
                  <SD SI="DiagInstanceName">activeDiagnosticSessionDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61831</SD>
                  <SD SI="DiagInstanceQualifier">GAC_SparePartNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GAC SparePartNumberDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61833</SD>
                  <SD SI="DiagInstanceQualifier">GACECUSoftwareVersionNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GACECUSoftwareVersionNumberDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61838</SD>
                  <SD SI="DiagInstanceQualifier">GAC_KitAssemblyPartNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GAC KitAssemblyPartNumberDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61843</SD>
                  <SD SI="DiagInstanceQualifier">systemSupplier_ECU_Hardware_Version</SD>
                  <SD SI="DiagInstanceName">systemSupplier ECU Hardware Version</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61845</SD>
                  <SD SI="DiagInstanceQualifier">systemSupplierECUSoftwareVersion</SD>
                  <SD SI="DiagInstanceName">systemSupplierECUSoftwareVersion</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61849</SD>
                  <SD SI="DiagInstanceQualifier">programmingDateDataIdentifier</SD>
                  <SD SI="DiagInstanceName">programmingDateDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44603</SD>
                  <SD SI="DiagInstanceQualifier">Suction_top_screen_state</SD>
                  <SD SI="DiagInstanceName">Suction top screen state</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44604</SD>
                  <SD SI="DiagInstanceQualifier">Suction_Top_Screen_Calibration_Status_Query</SD>
                  <SD SI="DiagInstanceName">Suction top screen calibration status query</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8464</SD>
                  <SD SI="DiagInstanceQualifier">OTA_Recovery_Status</SD>
                  <SD SI="DiagInstanceName">OTA Recovery Status</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8465</SD>
                  <SD SI="DiagInstanceQualifier">OTA_Partition_synchronization_status</SD>
                  <SD SI="DiagInstanceName">OTA Partition synchronization status</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61836</SD>
                  <SD SI="DiagInstanceQualifier">GAC_ECUSerialNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GAC ECUSerialNumberDataIdentifier</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Identification_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61707</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61707</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61707</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61707</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61823</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61824</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>13312</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>13312</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>13312</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>13312</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61830</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61831</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61833</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61833</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61833</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61833</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61838</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61843</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61843</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61843</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61843</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61845</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61845</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61845</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61845</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44603</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44603</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44603</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44603</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44603</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_530"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_532"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_534"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_536" SEMANTIC="IDENTIFICATION">
            <SHORT-NAME>Identification_Write</SHORT-NAME>
            <LONG-NAME>Identification Write</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_542">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Write</SD>
                <SD SI="ServiceName">Write</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61828</SD>
                  <SD SI="DiagInstanceQualifier">ApplicationSoftwareFingerprintDataIdentifier</SD>
                  <SD SI="DiagInstanceName">ApplicationSoftwareFingerprintDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61849</SD>
                  <SD SI="DiagInstanceQualifier">programmingDateDataIdentifier</SD>
                  <SD SI="DiagInstanceName">programmingDateDataIdentifier</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">61836</SD>
                  <SD SI="DiagInstanceQualifier">GAC_ECUSerialNumberDataIdentifier</SD>
                  <SD SI="DiagInstanceName">GAC ECUSerialNumberDataIdentifier</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Identification_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61828</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61849</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>61836</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_537"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_539"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_541"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_543" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Combined_Identification_Read</SHORT-NAME>
            <LONG-NAME>Combined Identification Read</LONG-NAME>
            <REQUEST-REF ID-REF="_544"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_548"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_552"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_553" SEMANTIC="STOREDDATA" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>Stored Data Read</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_559">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Read</SD>
                <SD SI="ServiceName">Read</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">272</SD>
                  <SD SI="DiagInstanceQualifier">Manufactory_mode</SD>
                  <SD SI="DiagInstanceName">Manufactory mode</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">512</SD>
                  <SD SI="DiagInstanceQualifier">Reprogramming_Counter</SD>
                  <SD SI="DiagInstanceName">Reprogramming Counter</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">513</SD>
                  <SD SI="DiagInstanceQualifier">Reprogramming_Attempt_Counter</SD>
                  <SD SI="DiagInstanceName">Reprogramming Attempt Counter</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">4096</SD>
                  <SD SI="DiagInstanceQualifier">Power_Voltage</SD>
                  <SD SI="DiagInstanceName">Power Voltage</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">20485</SD>
                  <SD SI="DiagInstanceQualifier">OTA_mode</SD>
                  <SD SI="DiagInstanceName">OTA mode</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">288</SD>
                  <SD SI="DiagInstanceQualifier">DTC_Setting_control_state</SD>
                  <SD SI="DiagInstanceName">DTC Setting control state</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Stored_Data_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>512</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>513</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>4096</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>4096</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>4096</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>4096</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>20485</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>20485</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>20485</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>20485</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_554"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_556"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_558"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_560" SEMANTIC="STOREDDATA">
            <SHORT-NAME>Stored_Data_Write</SHORT-NAME>
            <LONG-NAME>Stored Data Write</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_566">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Write</SD>
                <SD SI="ServiceName">Write</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">272</SD>
                  <SD SI="DiagInstanceQualifier">Manufactory_mode</SD>
                  <SD SI="DiagInstanceName">Manufactory mode</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">288</SD>
                  <SD SI="DiagInstanceQualifier">DTC_Setting_control_state</SD>
                  <SD SI="DiagInstanceName">DTC Setting control state</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Stored_Data_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>272</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>288</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RecordDataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_561"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_563"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_565"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_567" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Combined_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>Combined Stored Data Read</LONG-NAME>
            <REQUEST-REF ID-REF="_568"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_572"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_576"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_577" SEMANTIC="CONTROL">
            <SHORT-NAME>IOControl_ReturnControl</SHORT-NAME>
            <LONG-NAME>IO Control ReturnControl</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_583">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">ReturnControl</SD>
                <SD SI="ServiceName">ReturnControl</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44704</SD>
                  <SD SI="DiagInstanceQualifier">Motor_control</SD>
                  <SD SI="DiagInstanceName">Motor control</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_IOControl_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_578"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_580"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_582"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_584" SEMANTIC="CONTROL">
            <SHORT-NAME>IOControl_Control</SHORT-NAME>
            <LONG-NAME>IO Control Control</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_590">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Control</SD>
                <SD SI="ServiceName">Control</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44704</SD>
                  <SD SI="DiagInstanceQualifier">Motor_control</SD>
                  <SD SI="DiagInstanceName">Motor control</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_IOControl_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44704</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="DataIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_585"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_587"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_589"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_591" SEMANTIC="ROUTINE">
            <SHORT-NAME>Routine_Control_Start</SHORT-NAME>
            <LONG-NAME>Routine Control Start</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_597">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">Start</SD>
                <SD SI="ServiceName">Start</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44605</SD>
                  <SD SI="DiagInstanceQualifier">Suction_top_screen_self_calibration_routine_for_supplier</SD>
                  <SD SI="DiagInstanceName">Suction top screen self-calibration routine (for supplier)</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">514</SD>
                  <SD SI="DiagInstanceQualifier">Start_Check_Routine</SD>
                  <SD SI="DiagInstanceName">Start Check Routine</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">65280</SD>
                  <SD SI="DiagInstanceQualifier">Start_Erase_Memory</SD>
                  <SD SI="DiagInstanceName">Start Erase Memory</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">65281</SD>
                  <SD SI="DiagInstanceQualifier">Check_Programming_Dependencies</SD>
                  <SD SI="DiagInstanceName">Check Programming Dependencies</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">515</SD>
                  <SD SI="DiagInstanceQualifier">Check_Programming_Precondition</SD>
                  <SD SI="DiagInstanceName">Check_Programming_Precondition</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44604</SD>
                  <SD SI="DiagInstanceQualifier">GAC_suction_top_screen_self_calibration_routine</SD>
                  <SD SI="DiagInstanceName">GAC suction top screen self-calibration routine</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44705</SD>
                  <SD SI="DiagInstanceQualifier">Check_Lock_Condition_RunSet</SD>
                  <SD SI="DiagInstanceName">Check_Lock_Condition_RunSet</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">44706</SD>
                  <SD SI="DiagInstanceQualifier">Check_Unlock_Condition_RunSet</SD>
                  <SD SI="DiagInstanceName">Check_Unlock_Condition_RunSet</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8464</SD>
                  <SD SI="DiagInstanceQualifier">Start_Software_Recovery</SD>
                  <SD SI="DiagInstanceName">Start Software Recovery</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8465</SD>
                  <SD SI="DiagInstanceQualifier">Start_A_B_Partition_synchronization</SD>
                  <SD SI="DiagInstanceName">Start A/B Partition synchronization</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Routine_Control_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44605</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44605</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>514</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>514</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>65280</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>65280</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>65281</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>65281</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>515</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>515</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>515</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44604</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44705</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44705</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44706</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>44706</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_592"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_594"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_596"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_598" SEMANTIC="ROUTINE">
            <SHORT-NAME>Routine_Control_RequestResults</SHORT-NAME>
            <LONG-NAME>Routine Control Request Results</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_604">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="ServiceQualifier">RequestResults</SD>
                <SD SI="ServiceName">Request Results</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8464</SD>
                  <SD SI="DiagInstanceQualifier">Start_Software_Recovery</SD>
                  <SD SI="DiagInstanceName">Start Software Recovery</SD>
                </SDG>
                <SDG>
                  <SD SI="DiagInstanceStaticValue">8465</SD>
                  <SD SI="DiagInstanceQualifier">Start_A_B_Partition_synchronization</SD>
                  <SD SI="DiagInstanceName">Start A/B Partition synchronization</SD>
                </SDG>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Routine_Control_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false"/>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8464</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER">
                <VALUE>8465</VALUE>
                <IN-PARAM-IF-SNREF SHORT-NAME="RoutineIdentifier"/>
              </PRE-CONDITION-STATE-REF>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_599"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_601"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_603"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_608" SEMANTIC="FAULTMEMORY" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Read_Number</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Read_Number</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_609">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Read_Number_Of_DTCs</SD>
                <SD SI="ServiceName">Read_Number_Of_DTCs</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_605"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_606"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_607"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_613" SEMANTIC="FAULTMEMORY" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Read_All_Identified</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Read_All_Identified</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_614">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Read_DTCs_By_Status_Mask</SD>
                <SD SI="ServiceName">Read_DTCs_By_Status_Mask</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_610"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_611"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_612"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_618" SEMANTIC="FAULTMEMORY" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Read_Extended_Data</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Read_Extended_Data</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_619">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Read_Extended_Data_Record</SD>
                <SD SI="ServiceName">Read_Extended_Data_Record</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_615"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_616"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_617"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_623" SEMANTIC="FAULTREAD" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Read_Snapshot_Data</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Read_Snapshot_Data</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_624">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Read_Snapshot_Record</SD>
                <SD SI="ServiceName">Read_Snapshot_Record</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_620"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_621"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_622"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_628" SEMANTIC="FAULTMEMORY" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Read_Supported</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Read_Supported</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_629">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Read_Supported_DTCs</SD>
                <SD SI="ServiceName">Read_Supported_DTCs</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_625"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_626"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_627"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_633" SEMANTIC="FAULTMEMORY" ADDRESSING="FUNCTIONAL-OR-PHYSICAL">
            <SHORT-NAME>Fault_Memory_Clear_All</SHORT-NAME>
            <LONG-NAME>Fault_Memory_Clear_All</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_634">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">PrimaryFaultMemory</SD>
                <SD SI="DiagInstanceName">Primary Fault Memory</SD>
                <SD SI="ServiceQualifier">Clear_DTC</SD>
                <SD SI="ServiceName">Clear_DTC</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_PrimaryFaultMemory_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Default_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Locked_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="UnlockedL1_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_630"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_631"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_632"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_638" SEMANTIC="UP-DOWNLOAD">
            <SHORT-NAME>Upload_Download_Request</SHORT-NAME>
            <LONG-NAME>Upload_Download Request</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_639">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Software_Update</SD>
                <SD SI="DiagInstanceName">Software Update</SD>
                <SD SI="ServiceQualifier">RequestDownload</SD>
                <SD SI="ServiceName">RequestDownload</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Software_Update_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_635"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_636"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_637"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_643" SEMANTIC="UP-DOWNLOAD">
            <SHORT-NAME>Upload_Download_Transmit</SHORT-NAME>
            <LONG-NAME>Upload_Download Transmit</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_644">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Software_Update</SD>
                <SD SI="DiagInstanceName">Software Update</SD>
                <SD SI="ServiceQualifier">Transmit</SD>
                <SD SI="ServiceName">Transmit</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Software_Update_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_640"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_641"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_642"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
          <DIAG-SERVICE ID="_648" SEMANTIC="UP-DOWNLOAD">
            <SHORT-NAME>Upload_Download_Stop</SHORT-NAME>
            <LONG-NAME>Upload_Download Stop</LONG-NAME>
            <SDGS>
              <SDG>
                <SDG-CAPTION ID="_649">
                  <SHORT-NAME>CANdelaServiceInformation</SHORT-NAME>
                </SDG-CAPTION>
                <SD SI="DiagInstanceQualifier">Software_Update</SD>
                <SD SI="DiagInstanceName">Software Update</SD>
                <SD SI="ServiceQualifier">Stop</SD>
                <SD SI="ServiceName">Stop</SD>
                <SD SI="PositiveResponseSuppressed">no</SD>
              </SDG>
            </SDGS>
            <FUNCT-CLASS-REFS>
              <FUNCT-CLASS-REF ID-REF="FCL_Software_Update_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </FUNCT-CLASS-REFS>
            <AUDIENCE IS-SUPPLIER="false" IS-AFTERMARKET="false">
              <ENABLED-AUDIENCE-REFS>
                <ENABLED-AUDIENCE-REF ID-REF="Legislated_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </ENABLED-AUDIENCE-REFS>
            </AUDIENCE>
            <PRE-CONDITION-STATE-REFS>
              <PRE-CONDITION-STATE-REF ID-REF="Programming_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Extended_Session_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              <PRE-CONDITION-STATE-REF ID-REF="Unlocked_L2_SecurityAccess_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
            </PRE-CONDITION-STATE-REFS>
            <REQUEST-REF ID-REF="_645"/>
            <POS-RESPONSE-REFS>
              <POS-RESPONSE-REF ID-REF="_646"/>
            </POS-RESPONSE-REFS>
            <NEG-RESPONSE-REFS>
              <NEG-RESPONSE-REF ID-REF="_647"/>
            </NEG-RESPONSE-REFS>
          </DIAG-SERVICE>
        </DIAG-COMMS>
        <REQUESTS>
          <REQUEST ID="_453">
            <SHORT-NAME>RQ_Hard_Reset_Reset</SHORT-NAME>
            <LONG-NAME>RQ Hard_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_454">
            <SHORT-NAME>RQ_Hard_Reset_Reset_NoResponse</SHORT-NAME>
            <LONG-NAME>RQ Hard_Reset Reset_NoResponse</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>129</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_459">
            <SHORT-NAME>RQ_Key_Off_On_Reset</SHORT-NAME>
            <LONG-NAME>RQ Key_Off_On Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>2</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_460">
            <SHORT-NAME>RQ_Key_Off_On_Reset_NoResponse</SHORT-NAME>
            <LONG-NAME>RQ Key_Off_On Reset_NoResponse</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>130</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_465">
            <SHORT-NAME>RQ_Soft_Reset_Reset</SHORT-NAME>
            <LONG-NAME>RQ Soft_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>3</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_466">
            <SHORT-NAME>RQ_Soft_Reset_Reset_NoResponse</SHORT-NAME>
            <LONG-NAME>RQ Soft_Reset Reset_NoResponse</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>131</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_471">
            <SHORT-NAME>RQ_Security_Access_Level_01_Seed_Request</SHORT-NAME>
            <LONG-NAME>RQ Security_Access_Level_01_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ACCESSMODE" xsi:type="CODED-CONST">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_476">
            <SHORT-NAME>RQ_Security_Access_Level_01_Key_Send</SHORT-NAME>
            <LONG-NAME>RQ Security_Access_Level_01_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>2</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>SecurityKey1</SHORT-NAME>
                <LONG-NAME>SecurityKey1</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_11"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_482">
            <SHORT-NAME>RQ_Security_Access_Level_02_Seed_Request</SHORT-NAME>
            <LONG-NAME>RQ Security_Access_Level_02_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ACCESSMODE" xsi:type="CODED-CONST">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_487">
            <SHORT-NAME>RQ_Security_Access_Level_02_Key_Send</SHORT-NAME>
            <LONG-NAME>RQ Security_Access_Level_02_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>18</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>SecurityKey2</SHORT-NAME>
                <LONG-NAME>SecurityKey2</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_11"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_530">
            <SHORT-NAME>RQ_Identification_Read</SHORT-NAME>
            <LONG-NAME>RQ Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_531" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_219"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_537">
            <SHORT-NAME>RQ_Identification_Write</SHORT-NAME>
            <LONG-NAME>RQ Identification Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>46</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_538" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_238"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>DataRecord</SHORT-NAME>
                <LONG-NAME>DataRecord</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_538"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_544">
            <SHORT-NAME>RQ_Combined_Identification_Read</SHORT-NAME>
            <LONG-NAME>RQ Combined Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>EOP_DIDs_Identification_ReadRDBI_RQ</SHORT-NAME>
                <LONG-NAME>EOP DIDs IdentificationReadRDBI-RQ</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_546"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_554">
            <SHORT-NAME>RQ_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>RQ Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_555" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_242"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_561">
            <SHORT-NAME>RQ_Stored_Data_Write</SHORT-NAME>
            <LONG-NAME>RQ Stored Data Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>46</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_562" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_249"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>DataRecord</SHORT-NAME>
                <LONG-NAME>DataRecord</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_562"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_568">
            <SHORT-NAME>RQ_Combined_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>RQ Combined Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>EOP_DIDs_Stored_Data_ReadRDBI_RQ</SHORT-NAME>
                <LONG-NAME>EOP DIDs Stored DataReadRDBI-RQ</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_570"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_578">
            <SHORT-NAME>RQ_IOControl_ReturnControl</SHORT-NAME>
            <LONG-NAME>RQ IO Control ReturnControl</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>47</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_579" xsi:type="TABLE-KEY">
                <SHORT-NAME>DataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_252"/>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="CODED-CONST">
                <SHORT-NAME>ControlOptionRecord_InputOutputControlParameter</SHORT-NAME>
                <LONG-NAME>ControlOptionRecord_InputOutputControlParameter</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <CODED-VALUE>0</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>ControlEnableMaskRecord</SHORT-NAME>
                <LONG-NAME>ControlEnableMaskRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_579"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_585">
            <SHORT-NAME>RQ_IOControl_Control</SHORT-NAME>
            <LONG-NAME>RQ IO Control Control</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>47</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_586" xsi:type="TABLE-KEY">
                <SHORT-NAME>DataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_255"/>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="CODED-CONST">
                <SHORT-NAME>ControlOptionRecord_InputOutputControlParameter</SHORT-NAME>
                <LONG-NAME>ControlOptionRecord_InputOutputControlParameter</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <CODED-VALUE>3</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>ControlOptionRecord</SHORT-NAME>
                <LONG-NAME>ControlOptionRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_586"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_592">
            <SHORT-NAME>RQ_Routine_Control_Start</SHORT-NAME>
            <LONG-NAME>RQ Routine Control Start</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>49</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>RoutineControlType</SHORT-NAME>
                <LONG-NAME>RoutineControlType</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_593" xsi:type="TABLE-KEY">
                <SHORT-NAME>RoutineIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <TABLE-REF ID-REF="_260"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>RoutineStatus</SHORT-NAME>
                <LONG-NAME>RoutineStatus</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_593"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_599">
            <SHORT-NAME>RQ_Routine_Control_RequestResults</SHORT-NAME>
            <LONG-NAME>RQ Routine Control Request Results</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>49</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>RoutineControlType</SHORT-NAME>
                <LONG-NAME>RoutineControlType</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>3</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_600" xsi:type="TABLE-KEY">
                <SHORT-NAME>RoutineIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <TABLE-REF ID-REF="_272"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>RoutineControlOptionRecord</SHORT-NAME>
                <LONG-NAME>RoutineControlOptionRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_600"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_605">
            <SHORT-NAME>RQ_Fault_Memory_Read_Number</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Read_Number</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportNumberOfDtcByStatusMask</SHORT-NAME>
                <LONG-NAME>ReportNumberOfDtcByStatusMask</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_280"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_610">
            <SHORT-NAME>RQ_Fault_Memory_Read_All_Identified</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Read_All_Identified</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDtcByStatusMask</SHORT-NAME>
                <LONG-NAME>ReportDtcByStatusMask</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>2</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_280"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_615">
            <SHORT-NAME>RQ_Fault_Memory_Read_Extended_Data</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Read_Extended_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDTCExtendedDataRecordByDtcNumber</SHORT-NAME>
                <LONG-NAME>ReportDTCExtendedDataRecordByDtcNumber</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>6</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTC</SHORT-NAME>
                <LONG-NAME>DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_282"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCExtendedDataRecordNumber</SHORT-NAME>
                <LONG-NAME>DTCExtendedDataRecordNumber</LONG-NAME>
                <BYTE-POSITION>5</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_620">
            <SHORT-NAME>RQ_Fault_Memory_Read_Snapshot_Data</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Read_Snapshot_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDTCSnapshotRecordByDTCNumber</SHORT-NAME>
                <LONG-NAME>ReportDTCSnapshotRecordByDTCNumber</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>4</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTC</SHORT-NAME>
                <LONG-NAME>DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_282"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCSnapshotRecordNumber</SHORT-NAME>
                <LONG-NAME>DTCSnapshotRecordNumber</LONG-NAME>
                <BYTE-POSITION>5</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_625">
            <SHORT-NAME>RQ_Fault_Memory_Read_Supported</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Read_Supported</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportSupportedDTC</SHORT-NAME>
                <LONG-NAME>ReportSupportedDTC</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>10</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_630">
            <SHORT-NAME>RQ_Fault_Memory_Clear_All</SHORT-NAME>
            <LONG-NAME>RQ Fault_Memory_Clear_All</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>20</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>GroupOfDtc</SHORT-NAME>
                <LONG-NAME>GroupOfDtc</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_292"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_635">
            <SHORT-NAME>RQ_Upload_Download_Request</SHORT-NAME>
            <LONG-NAME>RQ Upload_Download Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>52</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>FormatIdentifier</SHORT-NAME>
                <LONG-NAME>FormatIdentifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>AddressAndLengthFormatIdentifier</SHORT-NAME>
                <LONG-NAME>AddressAndLengthFormatIdentifier</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_37"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Address</SHORT-NAME>
                <LONG-NAME>Address</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Size</SHORT-NAME>
                <LONG-NAME>Size</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_640">
            <SHORT-NAME>RQ_Upload_Download_Transmit</SHORT-NAME>
            <LONG-NAME>RQ Upload_Download Transmit</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>54</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>BlockSequenceCounter_Req</SHORT-NAME>
                <LONG-NAME>BlockSequenceCounter_Req</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>TransferRequestParameterRecord</SHORT-NAME>
                <LONG-NAME>TransferRequestParameterRecord</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_296"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
          <REQUEST ID="_645">
            <SHORT-NAME>RQ_Upload_Download_Stop</SHORT-NAME>
            <LONG-NAME>RQ Upload_Download Stop</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ</SHORT-NAME>
                <LONG-NAME>SID-RQ</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>55</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>CRC32</SHORT-NAME>
                <LONG-NAME>CRC32</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </REQUEST>
        </REQUESTS>
        <POS-RESPONSES>
          <POS-RESPONSE ID="_455">
            <SHORT-NAME>PR_Hard_Reset_Reset</SHORT-NAME>
            <LONG-NAME>PR Hard_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>81</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_461">
            <SHORT-NAME>PR_Key_Off_On_Reset</SHORT-NAME>
            <LONG-NAME>PR Key_Off_On Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>81</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_467">
            <SHORT-NAME>PR_Soft_Reset_Reset</SHORT-NAME>
            <LONG-NAME>PR Soft_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>81</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>ResetType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_472">
            <SHORT-NAME>PR_Security_Access_Level_01_Seed_Request</SHORT-NAME>
            <LONG-NAME>PR Security_Access_Level_01_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>103</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ACCESSMODE" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>SecuritySeed1</SHORT-NAME>
                <LONG-NAME>SecuritySeed1</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_11"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_477">
            <SHORT-NAME>PR_Security_Access_Level_01_Key_Send</SHORT-NAME>
            <LONG-NAME>PR Security_Access_Level_01_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>103</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_483">
            <SHORT-NAME>PR_Security_Access_Level_02_Seed_Request</SHORT-NAME>
            <LONG-NAME>PR Security_Access_Level_02_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>103</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ACCESSMODE" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>SecuritySeed2</SHORT-NAME>
                <LONG-NAME>SecuritySeed2</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_11"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_488">
            <SHORT-NAME>PR_Security_Access_Level_02_Key_Send</SHORT-NAME>
            <LONG-NAME>PR Security_Access_Level_02_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>103</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>SecurityAccessType</SHORT-NAME>
                <LONG-NAME>Type</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_532">
            <SHORT-NAME>PR_Identification_Read</SHORT-NAME>
            <LONG-NAME>PR Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>98</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>RDBI_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>RDBI_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_533" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_219"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>DataRecord</SHORT-NAME>
                <LONG-NAME>DataRecord</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_533"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_539">
            <SHORT-NAME>PR_Identification_Write</SHORT-NAME>
            <LONG-NAME>PR Identification Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>110</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>WDBI_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>WDBI_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_540" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_238"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_548">
            <SHORT-NAME>PR_Combined_Identification_Read</SHORT-NAME>
            <LONG-NAME>PR Combined Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>98</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>EOP_DIDs_Identification_ReadRDBI_PR</SHORT-NAME>
                <LONG-NAME>EOP DIDs IdentificationReadRDBI-PR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_550"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_556">
            <SHORT-NAME>PR_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>PR Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>98</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>RDBI_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>RDBI_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_557" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_242"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>DataRecord</SHORT-NAME>
                <LONG-NAME>DataRecord</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_557"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_563">
            <SHORT-NAME>PR_Stored_Data_Write</SHORT-NAME>
            <LONG-NAME>PR Stored Data Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>110</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>WDBI_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>WDBI_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_564" xsi:type="TABLE-KEY">
                <SHORT-NAME>RecordDataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_249"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_572">
            <SHORT-NAME>PR_Combined_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>PR Combined Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>98</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>EOP_DIDs_Stored_Data_ReadRDBI_PR</SHORT-NAME>
                <LONG-NAME>EOP DIDs Stored DataReadRDBI-PR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_574"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_580">
            <SHORT-NAME>PR_IOControl_ReturnControl</SHORT-NAME>
            <LONG-NAME>PR IO Control ReturnControl</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>111</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>IOCBI_RCTECU_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>IOCBI_RCTECU_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_581" xsi:type="TABLE-KEY">
                <SHORT-NAME>DataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_253"/>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="CODED-CONST">
                <SHORT-NAME>ControlStatusRecord_InputOutputControlParameter</SHORT-NAME>
                <LONG-NAME>ControlStatusRecord_InputOutputControlParameter</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <CODED-VALUE>0</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>ControlStatusRecord</SHORT-NAME>
                <LONG-NAME>ControlStatusRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_581"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_587">
            <SHORT-NAME>PR_IOControl_Control</SHORT-NAME>
            <LONG-NAME>PR IO Control Control</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>111</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>IOCBI_STA_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>IOCBI_STA_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>1</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_588" xsi:type="TABLE-KEY">
                <SHORT-NAME>DataIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <TABLE-REF ID-REF="_257"/>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="CODED-CONST">
                <SHORT-NAME>ControlStatusRecord_InputOutputControlParameter</SHORT-NAME>
                <LONG-NAME>ControlStatusRecord_InputOutputControlParameter</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <CODED-VALUE>3</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>ControlStatusRecord</SHORT-NAME>
                <LONG-NAME>ControlStatusRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_588"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_594">
            <SHORT-NAME>PR_Routine_Control_Start</SHORT-NAME>
            <LONG-NAME>PR Routine Control Start</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>113</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>RoutineControlType</SHORT-NAME>
                <LONG-NAME>RoutineControlType</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>RC_STR_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>RC_STR_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <REQUEST-BYTE-POS>2</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_595" xsi:type="TABLE-KEY">
                <SHORT-NAME>RoutineIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <TABLE-REF ID-REF="_264"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>RoutineStatusRecord</SHORT-NAME>
                <LONG-NAME>RoutineStatusRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_595"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_601">
            <SHORT-NAME>PR_Routine_Control_RequestResults</SHORT-NAME>
            <LONG-NAME>PR Routine Control Request Results</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>113</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>RoutineControlType</SHORT-NAME>
                <LONG-NAME>RoutineControlType</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>3</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="ID" xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>RC_RRR_RQ_MatchingRequestParam</SHORT-NAME>
                <LONG-NAME>RC_RRR_RQ_MatchingRequestParam</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <REQUEST-BYTE-POS>2</REQUEST-BYTE-POS>
                <BYTE-LENGTH>2</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="ID" ID="_602" xsi:type="TABLE-KEY">
                <SHORT-NAME>RoutineIdentifier</SHORT-NAME>
                <LONG-NAME>Identifier</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <TABLE-REF ID-REF="_275"/>
              </PARAM>
              <PARAM xsi:type="TABLE-STRUCT">
                <SHORT-NAME>RoutineStatusRecord</SHORT-NAME>
                <LONG-NAME>RoutineStatusRecord</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <TABLE-KEY-REF ID-REF="_602"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_606">
            <SHORT-NAME>PR_Fault_Memory_Read_Number</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Read_Number</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>89</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportNumberOfDtcByStatusMask</SHORT-NAME>
                <LONG-NAME>ReportNumberOfDtcByStatusMask</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>1</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_281"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCFormatIdentifier</SHORT-NAME>
                <LONG-NAME>DTCFormatIdentifier</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCCount</SHORT-NAME>
                <LONG-NAME>DTCCount</LONG-NAME>
                <BYTE-POSITION>4</BYTE-POSITION>
                <DOP-REF ID-REF="_10"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_611">
            <SHORT-NAME>PR_Fault_Memory_Read_All_Identified</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Read_All_Identified</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>89</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDtcByStatusMask</SHORT-NAME>
                <LONG-NAME>ReportDtcByStatusMask</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>2</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_281"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>ListOfDTC</SHORT-NAME>
                <LONG-NAME>ListOfDTC</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <DOP-REF ID-REF="_285"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_616">
            <SHORT-NAME>PR_Fault_Memory_Read_Extended_Data</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Read_Extended_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>89</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDTCExtendedDataRecordByDtcNumber</SHORT-NAME>
                <LONG-NAME>ReportDTCExtendedDataRecordByDtcNumber</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>6</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTC</SHORT-NAME>
                <LONG-NAME>DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_282"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>StatusOfDTC_DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>5</BYTE-POSITION>
                <DOP-REF ID-REF="_283"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCExtendedDataRecordNumber</SHORT-NAME>
                <LONG-NAME>DTCExtendedDataRecordNumber</LONG-NAME>
                <BYTE-POSITION>6</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCExtendedData</SHORT-NAME>
                <LONG-NAME>DTCExtendedData</LONG-NAME>
                <BYTE-POSITION>7</BYTE-POSITION>
                <DOP-REF ID-REF="_287"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_621">
            <SHORT-NAME>PR_Fault_Memory_Read_Snapshot_Data</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Read_Snapshot_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>89</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportDTCSnapshotRecordByDTCNumber</SHORT-NAME>
                <LONG-NAME>ReportDTCSnapshotRecordByDTCNumber</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>4</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTC</SHORT-NAME>
                <LONG-NAME>DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_282"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>StatusOfDTC_DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>5</BYTE-POSITION>
                <DOP-REF ID-REF="_283"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DTCSnapshotData</SHORT-NAME>
                <LONG-NAME>DTCSnapshotData</LONG-NAME>
                <BYTE-POSITION>6</BYTE-POSITION>
                <DOP-REF ID-REF="_289"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_626">
            <SHORT-NAME>PR_Fault_Memory_Read_Supported</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Read_Supported</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>89</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SUBFUNCTION" xsi:type="CODED-CONST">
                <SHORT-NAME>ReportSupportedDTC</SHORT-NAME>
                <LONG-NAME>ReportSupportedDTC</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>10</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>DtcStatusbyte_STRUCTURE</SHORT-NAME>
                <LONG-NAME>DTC Statusbyte</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_281"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>ListOfDTCAndStatus</SHORT-NAME>
                <LONG-NAME>ListOfDTCAndStatus</LONG-NAME>
                <BYTE-POSITION>3</BYTE-POSITION>
                <DOP-REF ID-REF="_291"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_631">
            <SHORT-NAME>PR_Fault_Memory_Clear_All</SHORT-NAME>
            <LONG-NAME>PR Fault_Memory_Clear_All</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>84</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_636">
            <SHORT-NAME>PR_Upload_Download_Request</SHORT-NAME>
            <LONG-NAME>PR Upload_Download Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>116</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>LFID</SHORT-NAME>
                <LONG-NAME>LengthFormatIdentifier</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>MNROB</SHORT-NAME>
                <LONG-NAME>MaxNumberOfBlockLength</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_641">
            <SHORT-NAME>PR_Upload_Download_Transmit</SHORT-NAME>
            <LONG-NAME>PR Upload_Download Transmit</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>118</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>BlockSequenceCounter_Resp</SHORT-NAME>
                <LONG-NAME>BlockSequenceCounter_Resp</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
          <POS-RESPONSE ID="_646">
            <SHORT-NAME>PR_Upload_Download_Stop</SHORT-NAME>
            <LONG-NAME>PR Upload_Download Stop</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_PR</SHORT-NAME>
                <LONG-NAME>SID-PR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>119</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>CRC32</SHORT-NAME>
                <LONG-NAME>CRC32 </LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <DOP-REF ID-REF="_9"/>
              </PARAM>
            </PARAMS>
          </POS-RESPONSE>
        </POS-RESPONSES>
        <NEG-RESPONSES>
          <NEG-RESPONSE ID="_456">
            <SHORT-NAME>NR_Hard_Reset_Reset</SHORT-NAME>
            <LONG-NAME>NR Hard_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="Start_NR_DOP_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_462">
            <SHORT-NAME>NR_Key_Off_On_Reset</SHORT-NAME>
            <LONG-NAME>NR Key_Off_On Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_212"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_468">
            <SHORT-NAME>NR_Soft_Reset_Reset</SHORT-NAME>
            <LONG-NAME>NR Soft_Reset Reset</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>17</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="Start_NR_DOP_FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Reset</SHORT-NAME>
                <LONG-NAME>Reset</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_473">
            <SHORT-NAME>NR_Security_Access_Level_01_Seed_Request</SHORT-NAME>
            <LONG-NAME>NR Security_Access_Level_01_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Request</SHORT-NAME>
                <LONG-NAME>Request</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_213"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Request</SHORT-NAME>
                <LONG-NAME>Request</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_478">
            <SHORT-NAME>NR_Security_Access_Level_01_Key_Send</SHORT-NAME>
            <LONG-NAME>NR Security_Access_Level_01_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Send</SHORT-NAME>
                <LONG-NAME>Send</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_214"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Send</SHORT-NAME>
                <LONG-NAME>Send</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>53</CODED-VALUE>
                  <CODED-VALUE>54</CODED-VALUE>
                  <CODED-VALUE>55</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_484">
            <SHORT-NAME>NR_Security_Access_Level_02_Seed_Request</SHORT-NAME>
            <LONG-NAME>NR Security_Access_Level_02_Seed Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Request</SHORT-NAME>
                <LONG-NAME>Anfordern</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_215"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Request</SHORT-NAME>
                <LONG-NAME>Anfordern</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>55</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_489">
            <SHORT-NAME>NR_Security_Access_Level_02_Key_Send</SHORT-NAME>
            <LONG-NAME>NR Security_Access_Level_02_Key Send</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>39</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Send</SHORT-NAME>
                <LONG-NAME>Senden</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_216"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Send</SHORT-NAME>
                <LONG-NAME>Senden</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>53</CODED-VALUE>
                  <CODED-VALUE>54</CODED-VALUE>
                  <CODED-VALUE>55</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_534">
            <SHORT-NAME>NR_Identification_Read</SHORT-NAME>
            <LONG-NAME>NR Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_239"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>20</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_541">
            <SHORT-NAME>NR_Identification_Write</SHORT-NAME>
            <LONG-NAME>NR Identification Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>46</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_241"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_552">
            <SHORT-NAME>NR_Combined_Identification_Read</SHORT-NAME>
            <LONG-NAME>NR Combined Identification Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_240"/>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_558">
            <SHORT-NAME>NR_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>NR Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_250"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>20</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_565">
            <SHORT-NAME>NR_Stored_Data_Write</SHORT-NAME>
            <LONG-NAME>NR Stored Data Write</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>46</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_251"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_576">
            <SHORT-NAME>NR_Combined_Stored_Data_Read</SHORT-NAME>
            <LONG-NAME>NR Combined Stored Data Read</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>34</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_240"/>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_582">
            <SHORT-NAME>NR_IOControl_ReturnControl</SHORT-NAME>
            <LONG-NAME>NR IO Control ReturnControl</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>47</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_258"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_589">
            <SHORT-NAME>NR_IOControl_Control</SHORT-NAME>
            <LONG-NAME>NR IO Control Control</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>47</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_259"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_596">
            <SHORT-NAME>NR_Routine_Control_Start</SHORT-NAME>
            <LONG-NAME>NR Routine Control Start</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>49</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_278"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_603">
            <SHORT-NAME>NR_Routine_Control_RequestResults</SHORT-NAME>
            <LONG-NAME>NR Routine Control Request Results</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>49</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="VALUE">
                <SHORT-NAME>RC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_279"/>
              </PARAM>
              <PARAM xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConstRC</SHORT-NAME>
                <LONG-NAME>RESPONSE CODE</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>36</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_607">
            <SHORT-NAME>NR_Fault_Memory_Read_Number</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Read_Number</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>ReadNumber</SHORT-NAME>
                <LONG-NAME>Read (number)</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_293"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_ReadNumber</SHORT-NAME>
                <LONG-NAME>Read (number)</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_612">
            <SHORT-NAME>NR_Fault_Memory_Read_All_Identified</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Read_All_Identified</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>ReadAllIdentified</SHORT-NAME>
                <LONG-NAME>Read - DTCs by status mask</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_294"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_ReadAllIdentified</SHORT-NAME>
                <LONG-NAME>Read - DTCs by status mask</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_617">
            <SHORT-NAME>NR_Fault_Memory_Read_Extended_Data</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Read_Extended_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ_NR</SHORT-NAME>
                <LONG-NAME>SID-RQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="RESERVED">
                <SHORT-NAME>Read_extended_data_record</SHORT-NAME>
                <LONG-NAME>Read - extended data record</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <BIT-LENGTH>8</BIT-LENGTH>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_622">
            <SHORT-NAME>NR_Fault_Memory_Read_Snapshot_Data</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Read_Snapshot_Data</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ_NR</SHORT-NAME>
                <LONG-NAME>SID-RQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="RESERVED">
                <SHORT-NAME>Read_snapshot_record</SHORT-NAME>
                <LONG-NAME>Read - snapshot record</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <BIT-LENGTH>8</BIT-LENGTH>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_627">
            <SHORT-NAME>NR_Fault_Memory_Read_Supported</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Read_Supported</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ_NR</SHORT-NAME>
                <LONG-NAME>SID-RQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>25</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>ReadAllSupported</SHORT-NAME>
                <LONG-NAME>Read - supported DTCs</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_294"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_ReadAllSupported</SHORT-NAME>
                <LONG-NAME>Read - supported DTCs</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_632">
            <SHORT-NAME>NR_Fault_Memory_Clear_All</SHORT-NAME>
            <LONG-NAME>NR Fault_Memory_Clear_All</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>20</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Clear</SHORT-NAME>
                <LONG-NAME>Clear DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_295"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Clear</SHORT-NAME>
                <LONG-NAME>Clear DTC</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_637">
            <SHORT-NAME>NR_Upload_Download_Request</SHORT-NAME>
            <LONG-NAME>NR Upload_Download Request</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>52</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Request</SHORT-NAME>
                <LONG-NAME>Request</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_297"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Request</SHORT-NAME>
                <LONG-NAME>Request</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>112</CODED-VALUE>
                  <CODED-VALUE>113</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                  <CODED-VALUE>115</CODED-VALUE>
                  <CODED-VALUE>120</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_642">
            <SHORT-NAME>NR_Upload_Download_Transmit</SHORT-NAME>
            <LONG-NAME>NR Upload_Download Transmit</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_RQ_NR</SHORT-NAME>
                <LONG-NAME>SID-RQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>54</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Transmit</SHORT-NAME>
                <LONG-NAME>Transmit</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_297"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Transmit</SHORT-NAME>
                <LONG-NAME>Transmit</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>112</CODED-VALUE>
                  <CODED-VALUE>113</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                  <CODED-VALUE>115</CODED-VALUE>
                  <CODED-VALUE>120</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
          <NEG-RESPONSE ID="_647">
            <SHORT-NAME>NR_Upload_Download_Stop</SHORT-NAME>
            <LONG-NAME>NR Upload_Download Stop</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="SERVICEIDRQ" xsi:type="CODED-CONST">
                <SHORT-NAME>SIDRQ_NR</SHORT-NAME>
                <LONG-NAME>SIDRQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <CODED-VALUE>55</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>Stop</SHORT-NAME>
                <LONG-NAME>Stop</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_297"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_Stop</SHORT-NAME>
                <LONG-NAME>Stop</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>19</CODED-VALUE>
                  <CODED-VALUE>34</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                  <CODED-VALUE>51</CODED-VALUE>
                  <CODED-VALUE>112</CODED-VALUE>
                  <CODED-VALUE>113</CODED-VALUE>
                  <CODED-VALUE>114</CODED-VALUE>
                  <CODED-VALUE>115</CODED-VALUE>
                  <CODED-VALUE>120</CODED-VALUE>
                  <CODED-VALUE>126</CODED-VALUE>
                  <CODED-VALUE>127</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </NEG-RESPONSE>
        </NEG-RESPONSES>
        <GLOBAL-NEG-RESPONSES>
          <GLOBAL-NEG-RESPONSE ID="_362">
            <SHORT-NAME>UNSUPPORTED_SERVICE_NR</SHORT-NAME>
            <LONG-NAME>Negative Response Codes for unsupported services</LONG-NAME>
            <PARAMS>
              <PARAM SEMANTIC="SERVICE-ID" xsi:type="CODED-CONST">
                <SHORT-NAME>SID_NR</SHORT-NAME>
                <LONG-NAME>SID-NR</LONG-NAME>
                <BYTE-POSITION>0</BYTE-POSITION>
                <CODED-VALUE>127</CODED-VALUE>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
              <PARAM xsi:type="MATCHING-REQUEST-PARAM">
                <SHORT-NAME>SID_RQ_NR</SHORT-NAME>
                <LONG-NAME>SID-RQ-NR</LONG-NAME>
                <BYTE-POSITION>1</BYTE-POSITION>
                <REQUEST-BYTE-POS>0</REQUEST-BYTE-POS>
                <BYTE-LENGTH>1</BYTE-LENGTH>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="VALUE">
                <SHORT-NAME>NRC</SHORT-NAME>
                <LONG-NAME>NEGATIVE RESPONSE CODES</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <DOP-REF ID-REF="_298"/>
              </PARAM>
              <PARAM SEMANTIC="DATA" xsi:type="NRC-CONST">
                <SHORT-NAME>NRCConst_NRC</SHORT-NAME>
                <LONG-NAME>NEGATIVE RESPONSE CODES</LONG-NAME>
                <BYTE-POSITION>2</BYTE-POSITION>
                <CODED-VALUES>
                  <CODED-VALUE>17</CODED-VALUE>
                  <CODED-VALUE>18</CODED-VALUE>
                  <CODED-VALUE>49</CODED-VALUE>
                </CODED-VALUES>
                <DIAG-CODED-TYPE BASE-DATA-TYPE="A_UINT32" xsi:type="STANDARD-LENGTH-TYPE">
                  <BIT-LENGTH>8</BIT-LENGTH>
                </DIAG-CODED-TYPE>
              </PARAM>
            </PARAMS>
          </GLOBAL-NEG-RESPONSE>
        </GLOBAL-NEG-RESPONSES>
        <IMPORT-REFS>
          <IMPORT-REF ID-REF="_303"/>
        </IMPORT-REFS>
        <STATE-CHARTS>
          <STATE-CHART ID="_1">
            <SHORT-NAME>Session</SHORT-NAME>
            <LONG-NAME>Session</LONG-NAME>
            <SEMANTIC>SESSION</SEMANTIC>
            <START-STATE-SNREF SHORT-NAME="Default"/>
            <STATES>
              <STATE ID="_2">
                <SHORT-NAME>Default</SHORT-NAME>
                <LONG-NAME>Default</LONG-NAME>
                <DESC>
                  <p>{P2=50, P2Ex=5000}</p>
                </DESC>
              </STATE>
              <STATE ID="_3">
                <SHORT-NAME>Programming</SHORT-NAME>
                <LONG-NAME>Programming</LONG-NAME>
                <DESC>
                  <p>{P2=50, P2Ex=5000}</p>
                </DESC>
              </STATE>
              <STATE ID="_4">
                <SHORT-NAME>Extended</SHORT-NAME>
                <LONG-NAME>Extended</LONG-NAME>
                <DESC>
                  <p>{P2=50, P2Ex=5000}</p>
                </DESC>
              </STATE>
            </STATES>
          </STATE-CHART>
          <STATE-CHART ID="_5">
            <SHORT-NAME>SecurityAccess</SHORT-NAME>
            <LONG-NAME>SecurityAccess</LONG-NAME>
            <SEMANTIC>SECURITY</SEMANTIC>
            <START-STATE-SNREF SHORT-NAME="Locked"/>
            <STATES>
              <STATE ID="_6">
                <SHORT-NAME>Locked</SHORT-NAME>
                <LONG-NAME>Locked</LONG-NAME>
              </STATE>
              <STATE ID="_7">
                <SHORT-NAME>UnlockedL1</SHORT-NAME>
                <LONG-NAME>Unlocked L1</LONG-NAME>
              </STATE>
              <STATE ID="_8">
                <SHORT-NAME>Unlocked_L2</SHORT-NAME>
                <LONG-NAME>Unlocked L2</LONG-NAME>
              </STATE>
            </STATES>
          </STATE-CHART>
        </STATE-CHARTS>
        <COMPARAM-REFS>
          <COMPARAM-REF ID-REF="ISO_15765_2.CP_UniqueRespIdTable" DOCREF="ISO_15765_2" DOCTYPE="COMPARAM-SUBSET">
            <COMPLEX-VALUE>
              <SIMPLE-VALUE>0</SIMPLE-VALUE>
              <SIMPLE-VALUE>normal segmented 11-bit transmit with FC</SIMPLE-VALUE>
              <SIMPLE-VALUE>1549</SIMPLE-VALUE>
              <SIMPLE-VALUE>0</SIMPLE-VALUE>
              <SIMPLE-VALUE>normal segmented 11-bit receive with FC</SIMPLE-VALUE>
              <SIMPLE-VALUE>1677</SIMPLE-VALUE>
              <SIMPLE-VALUE>0</SIMPLE-VALUE>
              <SIMPLE-VALUE>normal unsegmented 11-bit receive</SIMPLE-VALUE>
              <SIMPLE-VALUE>4294967295</SIMPLE-VALUE>
              <SIMPLE-VALUE>PDB131113A</SIMPLE-VALUE>
            </COMPLEX-VALUE>
            <PROTOCOL-SNREF SHORT-NAME="CAN_FD"/>
          </COMPARAM-REF>
        </COMPARAM-REFS>
        <PARENT-REFS>
          <PARENT-REF ID-REF="FGL_UDS" DOCREF="DLC_FGL_UDS" DOCTYPE="CONTAINER" xsi:type="FUNCTIONAL-GROUP-REF">
            <NOT-INHERITED-DIAG-COMMS>
              <NOT-INHERITED-DIAG-COMM>
                <DIAG-COMM-SNREF SHORT-NAME="DisableRxAndEnableTx_Control"/>
              </NOT-INHERITED-DIAG-COMM>
              <NOT-INHERITED-DIAG-COMM>
                <DIAG-COMM-SNREF SHORT-NAME="EnableRxAndDisableTx_Control"/>
              </NOT-INHERITED-DIAG-COMM>
            </NOT-INHERITED-DIAG-COMMS>
          </PARENT-REF>
        </PARENT-REFS>
      </BASE-VARIANT>
    </BASE-VARIANTS>
  </DIAG-LAYER-CONTAINER>
</ODX>
