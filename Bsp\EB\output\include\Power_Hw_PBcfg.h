/**
 * *************************************************************************
 * @file Power_Hw_PBcfg.h
 * @brief This file is used for mcu power dynamic code generation
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

#ifndef POWER_HW_PBCFG_H
#define POWER_HW_PBCFG_H

/**
 * *************************************************************************
 * @file   Power_Hw_PBcfg.h
 * @brief This file is used for Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Power_Hw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define POWER_HW_PBCFG_VENDOR_ID                      (110U)
#define POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION       (4U)
#define POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION       (4U)
#define POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION    (0U)
#define POWER_HW_PBCFG_SW_MAJOR_VERSION               (1U)
#define POWER_HW_PBCFG_SW_MINOR_VERSION               (0U)
#define POWER_HW_PBCFG_SW_PATCH_VERSION               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Power_Hw_Types.h file are of the same vendor */
#if (POWER_HW_PBCFG_VENDOR_ID != POWER_HW_TYPES_VENDOR_ID)
    #error "Power_Hw_PBcfg.h and Power_Hw_Types.h have different vendor ids"
#endif

/* Check if header file and Power_Hw_Types.h file are of the same Autosar version */
#if ((POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION != POWER_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION != POWER_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION != POWER_HW_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_PBcfg.h and Power_Hw_Types.h are different"
#endif

/* Check if header file and Power_Hw_Types.h file are of the same Software version */
#if ((POWER_HW_PBCFG_SW_MAJOR_VERSION != POWER_HW_TYPES_SW_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_SW_MINOR_VERSION != POWER_HW_TYPES_SW_MINOR_VERSION) || \
     (POWER_HW_PBCFG_SW_PATCH_VERSION != POWER_HW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Power_Hw_PBcfg.h and Power_Hw_Types.h are different"
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/


/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/



/*==================================================================================================
*                                             ENUMS
==================================================================================================*/


/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/


/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"


extern const Power_Hw_ModeConfigType_t Power_Hw_aModeConfigPB[];
extern const Power_Hw_HwIPsConfigType_t Power_Hw_HwIPsConfigPB;

#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"


#ifdef __cplusplus
}
#endif

/** @} */
#endif /* POWER_HW_PBCFG_H */


