/**
* 
* @file    Dio_Cfg.h
* @brief   Digital Input/Output for LSE14XX
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 

#ifndef DIO_CFG_H
#define DIO_CFG_H

#include "Std_Types.h"
#include "Dio_Hw_Cfg.h"


#ifdef __cplusplus
extern "C" {
#endif

/*=================================================================================================
*                              SOURCE FILE VERSION INFORMATION
=================================================================================================*/

#define DIO_VENDOR_ID_CFG_H                   (110U)
#define DIO_AR_RELEASE_MAJOR_VERSION_CFG_H    (4U)
#define DIO_AR_RELEASE_MINOR_VERSION_CFG_H    (4U)
#define DIO_AR_RELEASE_REVISION_VERSION_CFG_H (0U)
#define DIO_SW_MAJOR_VERSION_CFG_H            (1U)
#define DIO_SW_MINOR_VERSION_CFG_H            (0U)
#define DIO_SW_PATCH_VERSION_CFG_H            (0U)


/*=================================================================================================
*                                     FILE VERSION CHECKS
=================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
	/* [cover SWSID=SWS_Dio_00131]
	Module:Std_Types
	Header File:Std_Types.h;Std_Types.h
	Imported Type:Std_ReturnType;Std_VersionInfoType*/

    /* Std_Types.h version check start */
    #if ((DIO_AR_RELEASE_MAJOR_VERSION_CFG_H != STD_AR_RELEASE_MAJOR_VERSION) ||   \
        (DIO_AR_RELEASE_MINOR_VERSION_CFG_H != STD_AR_RELEASE_MINOR_VERSION)       \
        )
        #error "AUTOSAR Version Numbers of Dio_Cfg.h and Std_Types.h are different"
    #endif
    /* Std_Types.h version check end */
#endif

/* Check if Dio_Cfg.h and Dio_Hw_Cfg.h files are of the same vendor.*/
#if (DIO_VENDOR_ID_CFG_H != DIO_HW_VENDOR_ID_CFG_H)
    #error "Dio_Cfg.h and Dio_Hw_Cfg.h have different vendor ids"
#endif
/* Check if Dio_Cfg.h and Dio_Hw_Cfg.h files are of the same Autosar version.*/
#if ((DIO_AR_RELEASE_MAJOR_VERSION_CFG_H    != DIO_HW_AR_RELEASE_MAJOR_VERSION_CFG_H) || \
     (DIO_AR_RELEASE_MINOR_VERSION_CFG_H    != DIO_HW_AR_RELEASE_MINOR_VERSION_CFG_H) || \
     (DIO_AR_RELEASE_REVISION_VERSION_CFG_H != DIO_HW_AR_RELEASE_REVISION_VERSION_CFG_H) \
    )
    #error "AutoSar Version Numbers of Dio_Cfg.h and Dio_Hw_Cfg.h are different"
#endif
/* Check if Dio_Cfg.h and Dio_Hw_Cfg.h files are of the same Software version.*/
#if ((DIO_SW_MAJOR_VERSION_CFG_H != DIO_HW_SW_MAJOR_VERSION_CFG_H) || \
     (DIO_SW_MINOR_VERSION_CFG_H != DIO_HW_SW_MINOR_VERSION_CFG_H) || \
     (DIO_SW_PATCH_VERSION_CFG_H != DIO_HW_SW_PATCH_VERSION_CFG_H)    \
    )
    #error "Software Version Numbers of Dio_Cfg.h and Dio_Hw_Cfg.h are different"
#endif

/*=================================================================================================
*                                          CONSTANTS
=================================================================================================*/


/**
* @brief          The number of partition on the platform.
*
* @note           Used for channel, port and channel group validation.
*/

#define DIO_MULTICORE_ENABLED           STD_OFF
    #define DIO_MAX_PARTITION              (1U)


/**
* @brief          Enable or Disable Development Error Detection.
*
* @implements     DIO_DEV_ERROR_DETECT_define
*/
/* [cover SWSID = SWS_Dio_00140]
API function  Header File  Description Det_ReportError  Det.h  Service to report development errors.*/

#define DIO_DEV_ERROR_DETECT    (STD_OFF)

/**
* @brief          Function @p Dio_GetVersionInfo() enable switch.
*
* @implements DIO_VERSION_INFO_API_define
*/
#define DIO_VERSION_INFO_API    (STD_OFF)

/**
* @brief          Function @p Dio_FlipChannel() enable switch.
*/
#define DIO_FLIP_CHANNEL_API    (STD_OFF)

/**
* @brief          Function @p Dio_MaskedWritePort() enable switch.
*/
#define DIO_MASKEDWRITEPORT_API (STD_OFF)

/**
* @brief          Reversed port functionality enable switch.
*
* @implements DIO_REVERSEPORTBITS_define
*/
#define DIO_REVERSEPORTBITS     (STD_OFF)

/**
* @brief          Undefined pins masking enable switch.
*/
#define DIO_READZERO_UNDEFINEDPORTS (STD_OFF)



/**
* @brief Enable/Disable multiocre function from the driver
*/


/**
* @brief          Number of implemented ports.
*
* @note           Used for channel, port and channel group validation.
*/
#define DIO_NUM_PORTS_U16               ((uint16)0x5U)

/**
* @brief          The number of partition on the port
*
* @note           Used for port validation.
*/
#define DIO_PORT_PARTITION_U16          ((uint16)5U)

/**
* @brief          Number of channels available on the implemented ports.
*
* @note           Used for channel validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_NUM_CHANNELS_U16            ((uint16)153U)
#endif

/**
* @brief The number of partition on the channel.
*
* @note           Used for channel validation.
*/
/* [cover SWSID = SWS_Dio_00103] The port width within the types defined for the DIO Driver shall be the 
							   size of the largest port on the MCU which may be accessed by the DIO Driver. */
#define DIO_CHANNEL_PARTITION_U16            ((uint16)154U)

/**
* @brief          Mask representing no available channels on a port.
*
* @note           Used for channel validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_NO_AVAILABLE_CHANNELS_U16   ((Dio_PortLevelType)0x0U)
#endif

/**
* @brief          Mask representing the maximum valid offset for a channel group.
*
* @note           Used for channel group validation.
*/
#if (STD_ON == DIO_DEV_ERROR_DETECT)
    #define DIO_MAX_VALID_OFFSET_U8         ((uint8)0x1FU)
#endif

/**
*   @brief   Enables or disables the access to a hardware register from user mode
*            USER_MODE_SOFT_LOCKING:        All reads to hw registers will be done via REG_PROT, user mode access
*            SUPERVISOR_MODE_SOFT_LOCKING:  Locks the access to the registers only for supervisor mode
*
*   @note    Currently, no register protection mechanism is used for Dio driver.
*/
#define DIO_USER_MODE_SOFT_LOCKING      (STD_OFF)

/**
* @brief          Dio driver Link-time configuration switch.
*/
#define DIO_LINKTIME_SUPPORT


/*=================================================================================================
*                                      DEFINES AND MACROS
=================================================================================================*/

/**
* @brief          Symbolic name for the configuration Dio_ConfigPC.
*
*/
/* [cover SWSID = SWS_Dio_00026] The configuration process for Dio module shall provide 
							   symbolic names for each configured DIO channel, port and group.*/

#define Dio_ConfigPC    (Dio_Config)

/* ========== DioConfig ========== */

/* ---------- DioPortA ---------- */

/**
* @brief          Symbolic name for the port DioPortA.
*
*/
/* [cover SWSID = SWS_Dio_00181] The mapping of ID is implementation specific but not configurable.*/
#define DioConf_DioPort_DioPortA  ((uint8)0x00U)



/**
* @brief          Symbolic name for the channel PTA0_KEY3.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA0_KEY3 ((uint16)0x0000U)



/**
* @brief          Symbolic name for the channel PTA8_VB_12V_EN.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA8_VB_12V_EN ((uint16)0x0008U)



/**
* @brief          Symbolic name for the channel PTA9_AIR_12V_EN.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA9_AIR_12V_EN ((uint16)0x0009U)



/**
* @brief          Symbolic name for the channel PTA12_MCU_HALL_4.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA12_MCU_HALL_4 ((uint16)0x000cU)



/**
* @brief          Symbolic name for the channel PTA13_MCU_HALL_3.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA13_MCU_HALL_3 ((uint16)0x000dU)



/**
* @brief          Symbolic name for the channel PTA17_DRV8145_DRVOFF_3.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA17_DRV8145_DRVOFF_3 ((uint16)0x0011U)



/**
* @brief          Symbolic name for the channel PTA1_DRV8145_DRVOFF_5.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA1_DRV8145_DRVOFF_5 ((uint16)0x0001U)



/**
* @brief          Symbolic name for the channel PTA14_DRV8145_NFAULT_6.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTA14_DRV8145_NFAULT_6 ((uint16)0x000eU)

/* ---------- DioPortB ---------- */

/**
* @brief          Symbolic name for the port DioPortB.
*
*/
/* [cover SWSID = SWS_Dio_00181] The mapping of ID is implementation specific but not configurable.*/
#define DioConf_DioPort_DioPortB  ((uint8)0x01U)



/**
* @brief          Symbolic name for the channel PTB11_KEY1.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTB11_KEY1 ((uint16)0x002bU)



/**
* @brief          Symbolic name for the channel PTB10_KEY2.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTB10_KEY2 ((uint16)0x002aU)



/**
* @brief          Symbolic name for the channel PB12_DRV8145_NSLEEP_4.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PB12_DRV8145_NSLEEP_4 ((uint16)0x002cU)



/**
* @brief          Symbolic name for the channel PB8_DRV8145_NSLEEP_5.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PB8_DRV8145_NSLEEP_5 ((uint16)0x0028U)



/**
* @brief          Symbolic name for the channel PTB17_DRV8145_NFAULT_3.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTB17_DRV8145_NFAULT_3 ((uint16)0x0031U)



/**
* @brief          Symbolic name for the channel PTB9_DRV8145_NFAULT_4.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTB9_DRV8145_NFAULT_4 ((uint16)0x0029U)

/* ---------- DioPortC ---------- */

/**
* @brief          Symbolic name for the port DioPortC.
*
*/
/* [cover SWSID = SWS_Dio_00181] The mapping of ID is implementation specific but not configurable.*/
#define DioConf_DioPort_DioPortC  ((uint8)0x02U)



/**
* @brief          Symbolic name for the channel PTC6_KEY4.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC6_KEY4 ((uint16)0x0046U)



/**
* @brief          Symbolic name for the channel PTC13_MCU_HALL_1.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC13_MCU_HALL_1 ((uint16)0x004dU)



/**
* @brief          Symbolic name for the channel PTC12_MCU_HALL_2.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC12_MCU_HALL_2 ((uint16)0x004cU)



/**
* @brief          Symbolic name for the channel PTC9_MCU_HALL_5.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC9_MCU_HALL_5 ((uint16)0x0049U)



/**
* @brief          Symbolic name for the channel PTC8_MCU_HALL_6.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC8_MCU_HALL_6 ((uint16)0x0048U)



/**
* @brief          Symbolic name for the channel PTC3_DRV8145_NFAULT_1.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC3_DRV8145_NFAULT_1 ((uint16)0x0043U)



/**
* @brief          Symbolic name for the channel PTC7_DRV8145_NFAULT_5.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC7_DRV8145_NFAULT_5 ((uint16)0x0047U)



/**
* @brief          Symbolic name for the channel PTC2_DRV8145_NSLEEP_2.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC2_DRV8145_NSLEEP_2 ((uint16)0x0042U)



/**
* @brief          Symbolic name for the channel PTC10_DRV8145_NSLEEP_6.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC10_DRV8145_NSLEEP_6 ((uint16)0x004aU)



/**
* @brief          Symbolic name for the channel PTC11_DRV8145_DRVOFF_6.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTC11_DRV8145_DRVOFF_6 ((uint16)0x004bU)

/* ---------- DioPortD ---------- */

/**
* @brief          Symbolic name for the port DioPortD.
*
*/
/* [cover SWSID = SWS_Dio_00181] The mapping of ID is implementation specific but not configurable.*/
#define DioConf_DioPort_DioPortD  ((uint8)0x03U)



/**
* @brief          Symbolic name for the channel PTD9_LIN_SLP.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD9_LIN_SLP ((uint16)0x0069U)



/**
* @brief          Symbolic name for the channel PTD0_CAN_STB.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD0_CAN_STB ((uint16)0x0060U)



/**
* @brief          Symbolic name for the channel PTD1_CAN_ERR.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD1_CAN_ERR ((uint16)0x0061U)



/**
* @brief          Symbolic name for the channel PTD8_DRV8145_NSLEEP_3.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD8_DRV8145_NSLEEP_3 ((uint16)0x0068U)



/**
* @brief          Symbolic name for the channel PTD4_DRV8145_DRVOFF_4.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD4_DRV8145_DRVOFF_4 ((uint16)0x0064U)



/**
* @brief          Symbolic name for the channel PTD7_DRV8145_DRVOFF_2.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD7_DRV8145_DRVOFF_2 ((uint16)0x0067U)



/**
* @brief          Symbolic name for the channel PTD6_DRV8145_NFAULT_2.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTD6_DRV8145_NFAULT_2 ((uint16)0x0066U)

/* ---------- DioPortE ---------- */

/**
* @brief          Symbolic name for the port DioPortE.
*
*/
/* [cover SWSID = SWS_Dio_00181] The mapping of ID is implementation specific but not configurable.*/
#define DioConf_DioPort_DioPortE  ((uint8)0x04U)



/**
* @brief          Symbolic name for the channel PTE13_CAN_EN.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE13_CAN_EN ((uint16)0x008dU)



/**
* @brief          Symbolic name for the channel PTE16_MCU_HOLD.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE16_MCU_HOLD ((uint16)0x0090U)



/**
* @brief          Symbolic name for the channel PTE15_Motor_PWR_EN.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE15_Motor_PWR_EN ((uint16)0x008fU)



/**
* @brief          Symbolic name for the channel PTE3_DRV8145_DRVOFF_1.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE3_DRV8145_DRVOFF_1 ((uint16)0x0083U)



/**
* @brief          Symbolic name for the channel PTE14_DRV8145_NSLEEP_1.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE14_DRV8145_NSLEEP_1 ((uint16)0x008eU)



/**
* @brief          Symbolic name for the channel PTE1_UART0_EN.
*
*/
/* [cover SWSID = SWS_Dio_00180] The mapping of the ID is implementation specific but not configurable.*/
#define  DioConf_DioChannel_PTE1_UART0_EN ((uint16)0x0081U)


/*=================================================================================================
*                                             ENUMS
=================================================================================================*/

/*=================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
=================================================================================================*/

/**
* @brief          Type of a DIO port representation.
*
* @implements     Dio_PortType_typedef
*/
/* [cover SWSID = SWS_Dio_00018] Parameters of type Dio_PortType contain the numeric ID of a DIO port. */
/* [cover SWSID = SWS_Dio_00020] For parameter values of type Dio_PortType, the user shall use the symbolic names 
							   provided by the configuration description.  Furthermore, SWS_Dio_00103 applies to 
							   the type Dio_PortType.*/
/* [cover SWSID = SWS_Dio_00183] Name:  Dio_PortType Type:  uint  Range:  0..<number of  --  Shall cover all available 
							   DIO Ports. ports> Description:  Numeric ID of a DIO port. Available via:  Dio.h */
typedef uint8 Dio_PortType;


/**
* @brief          Type of a DIO channel representation.
*
* @implements     Dio_ChannelType_typedef
*/
/* [cover SWSID = SWS_Dio_00128] A general-purpose digital IO pin represents a DIO channel. */
/* [cover SWSID = SWS_Dio_00015] Parameters of type Dio_ChannelType contain the numeric ID of a DIO channel. */
/* [cover SWSID = SWS_Dio_00017] For parameter values of type Dio_ChannelType, the Dio’s user shall use the symbolic 
							   names provided by the configuration description.  Furthermore, SWS_Dio_00103 applies 
							   to the type Dio_ChannelType. */
/* [cover SWSID = SWS_Dio_00182] Name:  Dio_ChannelType Type:  uint  Range:  This is  -- Shall cover all available 
							   DIO channels implementation specific but not all values may be valid within the type. 
							   Description:  Numeric ID of a DIO channel. Available via:  Dio.h */						   
typedef uint16 Dio_ChannelType;


/**
* @brief          Type of a DIO port levels representation.
*
* @implements     Dio_PortLevelType_typedef
*/
/* [cover SWSID = SWS_Dio_00186] Name:  Dio_PortLevelType Type:  uint  Range:  0...xxx  -- If the µC owns ports of 
							   different port widths (e.g. 4, 8,16...Bit) Dio_PortLevelType inherits the size of 
							   the largest port Description:  If the µC owns ports of different port widths 
							   (e.g. 4, 8,16...Bit) Dio_PortLevelType inherits the size of the largest port. 
							   Available via:  Dio.h*/
/* [cover SWSID = SWS_Dio_00007] The Dio_WritePort function shall simultaneously set the levels of all 
							   output channels. A bit value '0' sets the corresponding channel to physical 
							   STD_LOW, a bit value '1' sets the corresponding channel to physical STD_HIGH.*/
/* [cover SWSID = SWS_Dio_00024] Dio_PortLevelType is the type for the value of a DIO port.  Furthermore, SWS_Dio_00103 
							   applies to the type Dio_PortLevelType.*/
typedef uint32 Dio_PortLevelType;


/**
* @brief          Type of a DIO channel levels representation.
*
* @implements     Dio_LevelType_typedef
*/
/* [cover SWSID = SWS_Dio_00089] Values used by the DIO Driver for the software 
   							   level of Channels are either STD_HIGH or STD_LOW. */
/* [cover SWSID = SWS_Dio_00023] Dio_LevelType is the type for the possible levels that a DIO channel can have 
							   (input or output). */
/* [cover SWSID = SWS_Dio_00185] Name:  Dio_LevelType Type:  uint8  Range:  STD_LOW  0x00  Physical state 0V STD_HIGH  
							   0x01  Physical state 5V or 3.3V Description:  These are the possible levels a DIO 
							   channel can have (input or output) Available via:  Dio.h */
/* [cover SWSID = SWS_Dio_00006] The Dio_WriteChannel function shall set the level of a single 
							   DIO channel to STD_HIGH or STD_LOW. */
typedef uint8 Dio_LevelType;


/**
* @brief          Type of a DIO channel group representation.
*
* @implements     Dio_ChannelGroupType_struct
*/
/* [cover SWSID = SWS_Dio_00053] In the DIO Driver, it shall be possible to group several DIO
							   channels by hardware (typically controlled by one hardware register) 
							   to represent aDIO port. */
/* [cover SWSID = SWS_Dio_00056] A channel group is a formal logical combination of several 
							   adjoining DIO channels within a DIO port. */
/* [cover SWSID = SWS_Dio_00022] For parameter values of type Dio_ChannelGroupType, the user shall use the symbolic 
							   names provided by the configuration description.  Furthermore, SWS_Dio_00056 applies 
							   to the type Dio_ChannelGroupType. */							   
/* [cover SWSID = SWS_Dio_00008] The Dio_WriteChannelGroup function shall simultaneously set an adjoining subset 
							   of DIO channels (channel group). A bit value '0' sets the corresponding channel to 
							   physical STD_LOW, a bit value '1' sets the corresponding channel to physical STD_HIGH. */
/* [cover SWSID = SWS_Dio_00184] Name:  Dio_ChannelGroupType Type:  Structure Element:  uint8/16/32  mask . This element 
							   mask which defines the positions of the channel group. uint8  offset  This element shall 
							   be the position of the Channel Group on the port, counted from the LSB. Dio_PortType  port.  
							   This shall be the port on which the Channel group is defined. Description:  Type for the 
							   definition of a channel group, which consists of several adjoining channels within a port. 
							   Available via:  Dio.h*/
typedef struct
{
    Dio_PortType      port;      /**< @brief Port identifier.  */
    uint8             u8offset;    /**< @brief Bit offset within the port. */
    Dio_PortLevelType mask;      /**< @brief Group mask. */
} Dio_ChannelGroupType;


/**
* @brief          Type of a DIO configuration structure.
*
* @note           In this implementation there is no need for a configuration
*                 structure there is only a dummy field, it is recommended
*                 to initialize this field to zero.
*
* @implements     Dio_ConfigType_struct
*/
typedef struct
{
    uint8 u8NumChannelGroups; /**< @brief Number of channel groups in configuration */
    const Dio_ChannelGroupType * pChannelGroupList;  /**< @brief
                                               Pointer to list of channel groups in configuration */
    const uint32 * pau32Dio_ChannelToPartitionMap;      /**< @brief Pointer to channel to partition mapping */
    const uint32 * pau32Dio_PortToPartitionMap;         /**< @brief Pointer to port to partition mapping */
} Dio_ConfigType;

/*=================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
=================================================================================================*/
#define DIO_START_SEC_CONST_32
#include "Dio_MemMap.h"
/**
* @brief Array containing list of mapping channel for partition
*/
extern const uint32 Dio_au32ChannelToPartitionMap[DIO_CHANNEL_PARTITION_U16];

/**
* @brief Array containing list of mapping port for partition
*/
extern const uint32 Dio_au32PortToPartitionMap[DIO_PORT_PARTITION_U16];

/**
* @brief Array of bitmaps of output pins available per port
*/
extern const Dio_PortLevelType Dio_aAvailablePinsForWrite[DIO_NUM_PORTS_U16];

/**
* @brief Array of bitmaps of input pins available per port
*/
extern const Dio_PortLevelType Dio_aAvailablePinsForRead[DIO_NUM_PORTS_U16];

#define DIO_STOP_SEC_CONST_32
#include "Dio_MemMap.h"

/*=================================================================================================
*                                    FUNCTION PROTOTYPES
=================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif  /* DIO_CFG_H */

/** @} */

