/**
 * file    Flexio_Pwm_Hw_Cfg.h
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef FLEXIO_PWM_HW_CFG_H
#define FLEXIO_PWM_HW_CFG_H

/*==================================================================================================
*                                          INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Flexio_Pwm_Hw_CfgDefines.h"
/* Include all variants header files. */

#include "Flexio_Pwm_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_PWM_HW_CFG_VENDOR_ID                    (110U)
#define FLEXIO_PWM_HW_CFG_MODULE_ID                    (121U)
#define FLEXIO_PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION     (4U)
#define FLEXIO_PWM_HW_CFG_AR_RELEASE_MINOR_VERSION     (4U)
#define FLEXIO_PWM_HW_CFG_AR_RELEASE_REVISION_VERSION  (0U)
#define FLEXIO_PWM_HW_CFG_SW_MAJOR_VERSION             (1U)
#define FLEXIO_PWM_HW_CFG_SW_MINOR_VERSION             (0U)
#define FLEXIO_PWM_HW_CFG_SW_PATCH_VERSION             (0U)

/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((FLEXIO_PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION != STD_AR_RELEASE_MAJOR_VERSION) || \
            (FLEXIO_PWM_HW_CFG_AR_RELEASE_MINOR_VERSION != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Flexio_Pwm_Hw_Cfg.c and Std_Types.h are different"
    #endif
#endif
/* Check if header file and Flexio_Pwm_Hw_CfgDefines.h header file are of the same vendor */
#if (FLEXIO_PWM_HW_CFG_VENDOR_ID != FLEXIO_PWM_HW_CFGDEFINES_VENDOR_ID)
    #error "Vendor IDs of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_CfgDefines.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_CfgDefines.h header file are of the same AUTOSAR version */
#if ((FLEXIO_PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION    != FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_AR_RELEASE_MINOR_VERSION    != FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_AR_RELEASE_REVISION_VERSION != FLEXIO_PWM_HW_CFGDEFINES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR version numbers of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_CfgDefines.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_CfgDefines.h header file are of the same software version */
#if ((FLEXIO_PWM_HW_CFG_SW_MAJOR_VERSION != FLEXIO_PWM_HW_CFGDEFINES_SW_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_SW_MINOR_VERSION != FLEXIO_PWM_HW_CFGDEFINES_SW_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_SW_PATCH_VERSION != FLEXIO_PWM_HW_CFGDEFINES_SW_PATCH_VERSION))
    #error "Software version numbers of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_CfgDefines.h are different."
#endif


/* Check if header file and Flexio_Pwm_Hw_PBcfg header file are of the same vendor */
#if (FLEXIO_PWM_HW_CFG_VENDOR_ID != FLEXIO_PWM_HW_PBCFG_VENDOR_ID)
    #error "Vendor IDs of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_PBcfg.h are different."
#endif

/* Check if header file and Pwm_EnvCfg header file are of the same AUTOSAR version */
#if ((FLEXIO_PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION    != FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_AR_RELEASE_MINOR_VERSION    != FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_AR_RELEASE_REVISION_VERSION != FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR version numbers of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_PBcfg.h are different."
#endif

/* Check if header file and Pwm_EnvCfg header file are of the same software version */
#if ((FLEXIO_PWM_HW_CFG_SW_MAJOR_VERSION != FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_SW_MINOR_VERSION != FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_CFG_SW_PATCH_VERSION != FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software version numbers of Flexio_Pwm_Hw_Cfg.h and Flexio_Pwm_Hw_PBcfg.h are different."
#endif

/*==================================================================================================
*                                            CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                       DEFINES AND MACROS
==================================================================================================*/
/** @brief      Check to use the Flexio Hw driver */
#define FLEXIO_PWM_HW_USED                      (STD_OFF)
/** @brief      Switch to enable the development error detection. */
#define FLEXIO_PWM_HW_DEV_ERROR_DETECT          (STD_ON)
/** @brief      Number of instances for Flexio */
#define FLEXIO_PWM_HW_INSTANCE_COUNT            (1u)
/** @brief      Number of channels available for PWM on Flexio */
#define FLEXIO_PWM_HW_CHANNEL_COUNT             (4u)
/** @brief      Number of pins available for PWM on Flexio */
#define FLEXIO_PWM_HW_PIN_COUNT                 (8U)

/** @brief      Value of Timer Ouput bitfield for 8bit PWM Low and High modes */
#define FLEXIO_PWM_HW_TIMER_START_HIGH_MODE     (0U)
#define FLEXIO_PWM_HW_TIMER_START_LOW_MODE      (1U)
/** @brief      Value of Timer Ouput bitfield for 16bit PWM Low and High modes */
#define FLEXIO_PWM_HW_TIMER_CMP_MAX_VALUE       (0xD055U)
/** @brief      Timer comparator upper 8 bit mask*/
#define FLEXIO_PWM_HW_TIMCMP_CMP_UPPER_MASK     (0xFFFF0000UL)
#define FLEXIO_PWM_HW_TIMCMP_CMP_UPPER_SHIFT    (16U)
#define FLEXIO_PWM_HW_TIMCMP_CMP_UPPER(x)       (((uint32)(((uint32)(x)) << FLEXIO_PWM_HW_TIMCMP_CMP_UPPER_SHIFT)) & FLEXIO_PWM_HW_TIMCMP_CMP_UPPER_MASK)
/** @brief      Timer comparator lower 8 bit mask*/
#define FLEXIO_PWM_HW_TIMCMP_CMP_LOWER_MASK     (0x0000FFFFUL)
#define FLEXIO_PWM_HW_TIMCMP_CMP_LOWER_SHIFT    (0x0U)
#define FLEXIO_PWM_HW_TIMCMP_CMP_LOWER(x)       (((uint32)(((uint32)(x)) << FLEXIO_PWM_HW_TIMCMP_CMP_LOWER_SHIFT)) & FLEXIO_PWM_HW_TIMCMP_CMP_LOWER_MASK)

/*==================================================================================================
*                                              ENUMS
==================================================================================================*/

/*==================================================================================================
*                                  STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                  GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                       FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FLEXIO_PWM_HW_CFG_H */

