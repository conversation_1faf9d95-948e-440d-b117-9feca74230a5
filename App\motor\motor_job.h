#ifndef MOTOR_JOB_H
#define MOTOR_JOB_H

#include "types.h"
#include "motor.h"

#define MOTOR_JOB_QUEUE_SIZE 20

typedef enum {
    JOB_NONE,
    JOB_MOVE, // go to target position
    JOB_FORCE, // go to a direction forcefully
    JOB_CLEAR, // go to min position and clear memory to zero
    JOB_RANGE, // go to target position and record range
} tMotorJobKind;

typedef struct {
    tMotorJobKind kind;
    tMotor *motor;
    i32 position;
    bool go_on;
    u8 wait;
} tMotorJob;

typedef struct {
    tMotorJob jobs[MOTOR_JOB_QUEUE_SIZE];
    i8 back;
    i8 front;
} tMotorJobQueue;

bool motor_job_queue_is_empty(void);
bool motor_job_queue_is_full(void);
usize motor_job_queue_size(void);
void motor_job_init(void);
void motor_job_push(tMotorJob* job);
tMotorJob* motor_job_pop(void);

void do_motor_job_queue(void);

#endif