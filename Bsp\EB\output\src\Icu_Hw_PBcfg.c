/**
 * 
 * @file Icu_Hw_PBcfg.c
 * @brief AUTOSAR Icu - contains the data exported by the Icu module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, W<PERSON><PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 
#ifdef __cplusplus
extern "C"{
#endif

#include "Std_Types.h"
#include "Icu_Hw_Types.h"
#include "Icu_Hw_Port.h"


/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define ICU_HW_PBCFG_VENDOR_ID_C                   (110u)
#define ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    (4u)
#define ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    (4u)
#define ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C (0u)
#define ICU_HW_PBCFG_SW_MAJOR_VERSION_C            (1u)
#define ICU_HW_PBCFG_SW_MINOR_VERSION_C            (0u)
#define ICU_HW_PBCFG_SW_PATCH_VERSION_C            (0u)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types.h file are of the same Autosar version */
    #if ((ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Icu_Hw_PBcfg.c and Std_Types.h are different"
    #endif
#endif

/* Check if source file and ICU header file are of the same vendor */
#if (ICU_HW_PBCFG_VENDOR_ID_C != ICU_HW_TYPES_VENDOR_ID)
    #error "Icu_Hw_PBcfg.c and Icu_Hw_Types.h have different vendor IDs"
#endif
/* Check if source file and ICU header file are of the same AutoSar version */
#if ((ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != ICU_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != ICU_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != ICU_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Types.h are different"
#endif
/* Check if source file and ICU header file are of the same Software version */
#if ((ICU_HW_PBCFG_SW_MAJOR_VERSION_C != ICU_HW_TYPES_SW_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_SW_MINOR_VERSION_C != ICU_HW_TYPES_SW_MINOR_VERSION) || \
     (ICU_HW_PBCFG_SW_PATCH_VERSION_C != ICU_HW_TYPES_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Types.h are different"
#endif

/* Check if source file and Icu_Hw_Port.h header file are of the same vendor */
#if (ICU_HW_PBCFG_VENDOR_ID_C != ICU_HW_PORT_VENDOR_ID)
    #error "Icu_Hw_PBcfg.c and Icu_Hw_Port.h have different vendor ids"
#endif
/* Check if  source file and Icu_Hw_Port.h file are of the same Autosar version */
#if ((ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != ICU_HW_PORT_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != ICU_HW_PORT_AR_RELEASE_MINOR_VERSION) || \
     (ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != ICU_HW_PORT_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Port.h are different"
#endif
/* Check if source file and Icu_Hw_Port.h file are of the same Software version */
#if ((ICU_HW_PBCFG_SW_MAJOR_VERSION_C != ICU_HW_PORT_SW_MAJOR_VERSION) || \
     (ICU_HW_PBCFG_SW_MINOR_VERSION_C != ICU_HW_PORT_SW_MINOR_VERSION) || \
     (ICU_HW_PBCFG_SW_PATCH_VERSION_C != ICU_HW_PORT_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Hw_PBcfg.c and Icu_Hw_Port.h are different"
#endif

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/
#define ICU_START_SEC_CODE
#include "Icu_MemMap.h"

/** @brief Signature of report events interrupt function. */
extern void Icu_ReportEvents(uint16 Channel, uint8 bOverflow);

#define ICU_STOP_SEC_CODE
#include "Icu_MemMap.h"

/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/
#define ICU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"


/*
 *  @brief    PB IcuPwmChannels_Hall1 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_14_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    2,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    0u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM0_3_CH7,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH4,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};

/*
 *  @brief    PB IcuPwmChannels_Hall2 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_15_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    3,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    9u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM0_3_CH6,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH2,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};

/*
 *  @brief    PB IcuPwmChannels_Hall3 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_3_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    3,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    0u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH7,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH1,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};

/*
 *  @brief    PB IcuPwmChannels_Hall4 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_4_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    4,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    0u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH6,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM1_4_CH5,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};

/*
 *  @brief    PB IcuPwmChannels_Hall5 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_22_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    10,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    0u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM2_5_CH0,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM0_3_CH3,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};

/*
 *  @brief    PB IcuPwmChannels_Hall6 Channels Configuration
 */
static const Icu_Hw_Pwm_IcChannelConfig_t Icu_Hw_Pwm_23_ChannelConfig_PB =
{
    /* GPCM Channel ID */
    11,
    /* INIT0 mask enable */
    FALSE,
    /* INIT1 mask enable */
    FALSE,
    /* INIT2 mask enable */
    FALSE,
    /* GPCM INIT0 mask event */
    0x1FFFFFu,
    /* GPCM INIT1 mask event */
    0x1FFFFFu,
    /* GPCM INIT2 mask event */
    0x1FFFFFu,
    /* Count mode */
    ICU_HW_PWM_CNTMODE_UP_CONTINUOUS,
    /* Period load mode */
    ICU_HW_PWM_PRD_LOAD_SHADOW,
    /* Init value load enable */
    TRUE,
    /* GPCM synchronization trigger source */
    ICU_HW_PWM_NULL_SYNCTRIG,
    /* Enable synchronous startup */
    ICU_HW_PWM_SYNC_START_DISABLE,
    /* Period value */
    4294967294u,
    /* Initial value */
    0u,
    /* Clock source */
    ICU_HW_PWM_SYS_CLK,
    /* Clock division */
    0u,
    /* External clock source */
    ICU_HW_PWM_EXT_CLK_0,
    /* Channel A capture source */
    ICU_HW_PWM_CAP_PAD_0,
    /* Channel A filtering width */
    400u,
    /* Channel A filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Channel B capture source */
    ICU_HW_PWM_CAP_PAD_1,
    /* Channel B filtering width */
    0u,
    /* Channel B filtering polarity */
    ICU_HW_PWM_CAP_POSITIVE,
    /* Capture period upper limit alarm value */
    0xFFFFFFFEu,
    /* Capture period lower limit alarm value */
    0u,
    /* Event output to TRGMUX */
    0u,
    /* DMA request source 0 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 1 event */
    ICU_HW_PWM_GPCM_DMA_EVENT_NULL,
    /* DMA request source 0 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* DMA request source 1 number within the group */
    ICU_HW_PWM_COMMON_DMA_REQ_NUM_0,
    /* Channel A is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM2_5_CH1,
    /* Channel B is connected to the output PAD channel */
    ICU_HW_PWM_XMUX_CAP_PWM0_3_CH2,
    /* Synchronization Source */
    ICU_HW_PWM_XMUX_SYNC_NULL,
};




/** 
 * @brief   Icu channels IP related configuration array
 */
const Icu_Hw_ChannelConfig_t Icu_Hw_IpChannelConfig_PB[12U] =
{
    /** @brief IcuChannel_HALL1 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_14_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_HALL2 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_15_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_HALL3 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_3_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        0u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_HALL4 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_4_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        0u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_HALL5 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_22_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_HALL6 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_23_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        0u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_0 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_14_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_1 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_15_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_2 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_3_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        0u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_3 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_4_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        0u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_4 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_22_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    },
    /** @brief IcuChannel_5 */
    {
        /** @brief IP type of this channel */
        ICU_HW_PWM_MODULE,
        /** @brief Port Ci IP channel pointer */
        NULL_PTR,
        /** @brief PWM IP channel pointer */
        &Icu_Hw_Pwm_23_ChannelConfig_PB,
        /** @brief Pwm Gpcm AB channel.(0: A, 1: B) */
        1u,
        /** @brief Instance number */
        1u,
        /** @brief PWM interrupt ID */
        0xFFu,
    }
};

#define ICU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Icu_MemMap.h"

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

#ifdef __cplusplus
}
#endif


