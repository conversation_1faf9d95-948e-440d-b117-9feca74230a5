/**
 * *************************************************************************
 * @file   Power_Hw_PBcfg.c
 * @brief  This file is used for Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Power_Hw_PBcfg.h"
#include "Power_Hw.h"
#include "Std_Types.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define POWER_HW_PBCFG_VENDOR_ID_C                      (110U)
#define POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C       (4U)
#define POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C       (4U)
#define POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C    (0U)
#define POWER_HW_PBCFG_SW_MAJOR_VERSION_C               (1U)
#define POWER_HW_PBCFG_SW_MINOR_VERSION_C               (0U)
#define POWER_HW_PBCFG_SW_PATCH_VERSION_C               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Power_Hw_PBcfg.c file and Power_Hw_PBcfg.h file are of the same vendor */
#if (POWER_HW_PBCFG_VENDOR_ID_C != POWER_HW_PBCFG_VENDOR_ID)
    #error "Power_Hw_PBcfg.c and Power_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Power_Hw_Type.c file and Power_Hw_PBcfg.h file are of the same Autosar version */
#if ((POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_PBcfg.c and Power_Hw_PBcfg.h are different"
#endif

/* Check if Power_Hw_PBcfg.c file and Power_Hw_PBcfg.h file are of the same Software version */
#if ((POWER_HW_PBCFG_SW_MAJOR_VERSION_C != POWER_HW_PBCFG_SW_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_SW_MINOR_VERSION_C != POWER_HW_PBCFG_SW_MINOR_VERSION) || \
     (POWER_HW_PBCFG_SW_PATCH_VERSION_C != POWER_HW_PBCFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Power_Hw_PBcfg.c and Power_Hw_PBcfg.h are different"
#endif

/* Check if Power_Hw_PBcfg.c file and Power_Hw.h file are of the same vendor */
#if (POWER_HW_PBCFG_VENDOR_ID_C != POWER_HW_VENDOR_ID)
    #error "Power_Hw_PBcfg.c and Power_Hw.h have different vendor ids"
#endif

/* Check if Power_Hw_PBcfg.c file and Power_Hw.h file are of the same Autosar version */
#if ((POWER_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != POWER_HW_AR_RELEASE_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != POWER_HW_AR_RELEASE_MINOR_VERSION) || \
     (POWER_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != POWER_HW_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Power_Hw_PBcfg.c and Power_Hw.h are different"
#endif

/* Check if Power_Hw_PBcfg.c file and Power_Hw.h file are of the same Software version */
#if ((POWER_HW_PBCFG_SW_MAJOR_VERSION_C != POWER_HW_SW_MAJOR_VERSION) || \
     (POWER_HW_PBCFG_SW_MINOR_VERSION_C != POWER_HW_SW_MINOR_VERSION) || \
     (POWER_HW_PBCFG_SW_PATCH_VERSION_C != POWER_HW_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Power_Hw_PBcfg.c and Power_Hw.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/
#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"








/**
* @brief          Initialization data for PMC hw.
* @details        Static configuration realized at startup by calling Mcu_Init() API.
*
*/
static const Power_Hw_PmcConfigType Power_Hw_PMC_ConfigPB =
{
        /* Low Voltage Detect Status and Control 1 Register (PMC_PWER). This register only exist on 1xx series of devices */
    (
        PMC_PWER_WK_NMI_ENABLE_U32 |
        PMC_PWER_WK_RTCOVF_DISABLE_U32 |
        PMC_PWER_WK_RTCALR_ENABLE_U32 |
        PMC_PWER_WK_RTCSEC_ENABLE_U32 |
        PMC_PWER_WK_WKTM_ENABLE_U32 |
        PMC_PWER_WK_PORTA_ENABLE_U32 |
        PMC_PWER_WK_PORTB_ENABLE_U32 |
        PMC_PWER_WK_PORTC_ENABLE_U32 |
        PMC_PWER_WK_PORTD_ENABLE_U32 |
        PMC_PWER_WK_PORTE_ENABLE_U32
    ),
        /* Low Voltage Detect Status and Control 2 Register (PMC_PORDWESR) */
    (
        PMC_PORDWESR_WK_PA_RISING_EDGE_U32 |
        PMC_PORDWESR_WK_PB_RISING_EDGE_U32 |
        PMC_PORDWESR_WK_PC_RISING_EDGE_U32 |
        PMC_PORDWESR_WK_PD_RISING_EDGE_U32 |
        PMC_PORDWESR_WK_PE_RISING_EDGE_U32 |
        PMC_PORDWESR_WK_NMI_FALLING_EDGE_U32
    ),
    /* Regulator Status and Control Register (PMC_PVSCR) */
    (
        PMC_PVSCR_LVD1_DET_ENABLE_U32 |
        PMC_PVSCR_LVD2_DET_ENABLE_U32 |
        PMC_PVSCR_LVD0_OE_ENABLE_U32 |
        PMC_PVSCR_LVD1_OE_ENABLE_U32 |
        PMC_PVSCR_LVD2_OE_ENABLE_U32 |
        PMC_PVSCR_LVD1_NFEN_ENABLE_U32 |
        PMC_PVSCR_LVD1_NFSEL_4RC_U32 |
        PMC_PVSCR_LVD2_NFEN_DISABLE_U32 |
        PMC_PVSCR_LVD2_NFSEL_8RC_U32
        
    ),
     /* Regulator Status and Control Register (PVSLCR) */
    (
        PMC_PVSLCR_LVD1_VL_4V2_U32
         |
        PMC_PVSLCR_LVD2_VL_4V4_U32
        
    ),
     /* Regulator Status and Control Register (PVSICR) */
    (
        PMC_PVSICR_LVD1_ITRST_DISABLE_U32 |
        PMC_PVSICR_LVD1_GEN_RST_U32 |
        PMC_PVSICR_LVD1_EDG_RISING_U32 |
        PMC_PVSICR_LVD1_ITTYPE_NMASK_U32 |
        PMC_PVSICR_LVD2_ITRST_DISABLE_U32 |
        PMC_PVSICR_LVD2_GEN_RST_U32 |
        PMC_PVSICR_LVD2_EDG_RISING_U32 |
        PMC_PVSICR_LVD2_ITTYPE_MASK_U32
    ),

     /* Regulator Status and Control Register (WTR) */
    (
        0U |
        PMC_WTR_WKTIMER_CLK_32K_U32
         |
        PMC_WTR_WKTTIMER_DISABLE_U32
    ),

	 /* Low Voltage Detect Status and Control 1 Register (DMWER). This register only exist on 1xx series of devices */
    (
        PMC_DMWER_WK_NMI_ENABLE_U32 |
        PMC_DMWER_WK_RTCPOVF_DISABLE_U32 |
        PMC_DMWER_WK_RTCPSEC_ENABLE_U32 |
        PMC_DMWER_WK_RTCALR_ENABLE_U32 |
        PMC_DMWER_WK_TIME_DISABLE_U32 |
        PMC_DMWER_WK_PORTA_ENABLE_U32 |
        PMC_DMWER_WK_PORTB_ENABLE_U32 |
        PMC_DMWER_WK_PORTC_ENABLE_U32 |
        PMC_DMWER_WK_PORTD_ENABLE_U32 |
        PMC_DMWER_WK_PORTE_ENABLE_U32
    )
};



/**
* @brief          Initialization mode data.
* @details        Static configuration realized by calling Mcu_SetMode() API.
*
*/
const Power_Hw_ModeConfigType_t Power_Hw_aModeConfigPB[2U] =
{
    /* Start of Mcu_aModeConfig[0] */
    {
        /* Mode Configuration ID. */
        (Power_Hw_ModeType)0U,

        /* The Power Mode name (code). */
        POWER_HW_RUN_MODE,

		/* The wdg config name (code). */
		POWER_HW_SLEEP_WDG_COUNT_KEEP,

        /* Clock switches to MRC selection (code). */
		POWER_HW_DSLEEP_WAK_CLK_SW_MRC,

        /* RC power switch signal (code) */
        FALSE,

        /* The Sleep On Exit configuration */
        FALSE,
    }, /* End of Mcu_aModeConfig[0] */
    /* Start of Mcu_aModeConfig[1] */
    {
        /* Mode Configuration ID. */
        (Power_Hw_ModeType)1U,

        /* The Power Mode name (code). */
        POWER_HW_SLEEP_MODE,

		/* The wdg config name (code). */
		POWER_HW_SLEEP_WDG_COUNT_KEEP,

        /* Clock switches to MRC selection (code). */
		POWER_HW_DSLEEP_WAK_CLK_SW_MRC,

        /* RC power switch signal (code) */
        FALSE,

        /* The Sleep On Exit configuration */
        FALSE,
    } /* End of Mcu_aModeConfig[1] */
};



/**
* @brief          Initialization data for different hw.
* @details        Static configuration realized at startup by calling Mcu_Init() API.
*
*/
const Power_Hw_HwIPsConfigType_t Power_Hw_HwIPsConfigPB =
{
    /* Configuration for PMC (Power Management Controller) hardware */
    &Power_Hw_PMC_ConfigPB,
};



#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED

#include "Mcu_MemMap.h"

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

