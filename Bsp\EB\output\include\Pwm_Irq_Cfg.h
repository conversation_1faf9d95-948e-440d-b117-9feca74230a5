/**
 * 
 * @file Pwm_Irq_Cfg.h
 * @brief AUTOSAR Pwm - contains the data exported by the Pwm module
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * <PERSON><PERSON><PERSON><PERSON><PERSON> CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON><PERSON><PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef PWM_IRQ_CFG_H
#define PWM_IRQ_CFG_H


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define PWM_IRQ_CFG_VENDOR_ID                       (110U)
#define PWM_IRQ_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define PWM_IRQ_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define PWM_IRQ_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define PWM_IRQ_CFG_SW_MAJOR_VERSION                (1U)
#define PWM_IRQ_CFG_SW_MINOR_VERSION                (0U)
#define PWM_IRQ_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/

/**************************************** PORT IRQ DEFINES ****************************************/

/**************************************** PORT IRQ DEFINES ****************************************/

#ifdef __cplusplus
}
#endif


#endif  /* PWM_IRQ_CFG_H */
