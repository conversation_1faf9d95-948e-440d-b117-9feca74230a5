/**
 * @file    Rtc_Gpt_Hw_Cfg_Defines.h
 * @brief   Gpt Cfg file.
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef RTC_GPT_HW_CFG_DEFINES_H
#define RTC_GPT_HW_CFG_DEFINES_H

#include "Std_Types.h"
#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RTC_HW_DEFINES_VENDOR_ID_CFG                       (110U)
#define RTC_HW_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG        (4U)
#define RTC_HW_DEFINES_AR_RELEASE_MINOR_VERSION_CFG        (4U)
#define RTC_HW_DEFINES_AR_RELEASE_REVISION_VERSION_CFG     (0U)
#define RTC_HW_DEFINES_SW_MAJOR_VERSION_CFG                (1U)
#define RTC_HW_DEFINES_SW_MINOR_VERSION_CFG                (0U)
#define RTC_HW_DEFINES_SW_PATCH_VERSION_CFG                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Std_Types.h file are of the same Autosar version */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
#if ((RTC_HW_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG != STD_AR_RELEASE_MAJOR_VERSION) || \
(RTC_HW_DEFINES_AR_RELEASE_MINOR_VERSION_CFG != STD_AR_RELEASE_MINOR_VERSION))
#error "AutoSar Version Numbers of Rtc_Gpt_Hw_Cfg_Defines.h and Std_Types.h are different"
#endif
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/


/**
 * @brief Compensation enable/disable for each channel used
 *
 */


/**
 * @brief These defines indicate that at least one channel from each module is used in all configurations.
 *
 */
#define RTC_GPT_HW_USED (STD_OFF)

/**
 * @brief IRQ Defines for each channel used
 */

/**
 * @brief Interrupt enable/disable for channel used
 */



/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** } */
#endif  /* RTC_GPT_HW_CFG_DEFINES_H */
