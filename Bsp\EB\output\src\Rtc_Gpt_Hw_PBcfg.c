/**
 * @file    Rtc_Gpt_Hw_PBcfg.c
 * @brief   Rtc Hw PBcfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Advisory Rule 20.1, #include directives should only be preceded by preprocessor directives or comments.
 * REASON: Variables and text need to be placed in the specified location
 */
#include "Rtc_Gpt_Hw_PBcfg.h"
#include "Gpt_Irq.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
 *                              SOURCE FILE VERSION INFORMATION
 *================================================================================================*/
#define RTC_GPT_HW_VENDOR_ID_PBCFG_C                    (110U)
#define RTC_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C     (4U)
#define RTC_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C     (4U)
#define RTC_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C  (0U)
#define RTC_GPT_HW_SW_MAJOR_VERSION_PBCFG_C             (1U)
#define RTC_GPT_HW_SW_MINOR_VERSION_PBCFG_C             (0U)
#define RTC_GPT_HW_SW_PATCH_VERSION_PBCFG_C             (0U)
/*==================================================================================================
 *                                      FILE VERSION CHECKS
 *================================================================================================*/
#if (RTC_GPT_HW_VENDOR_ID_PBCFG_C != RTC_HW_VENDOR_ID_PBCFG_H)
    #error "Rtc_Gpt_Hw_PBcfg.c and Rtc_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((RTC_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (RTC_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (RTC_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != RTC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Rtc_Gpt_Hw_PBcfg.c and Rtc_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((RTC_GPT_HW_SW_MAJOR_VERSION_PBCFG_C != RTC_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (RTC_GPT_HW_SW_MAJOR_VERSION_PBCFG_C != RTC_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (RTC_GPT_HW_SW_PATCH_VERSION_PBCFG_C != RTC_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Rtc_Gpt_Hw_PBcfg.c and Rtc_Gpt_Hw_PBcfg.h are different"
#endif

#if (RTC_GPT_HW_VENDOR_ID_PBCFG_C != GPT_IRQ_VENDOR_ID)
    #error "Rtc_Gpt_Hw_PBcfg.c and Gpt_Irq.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((RTC_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MAJOR_VERSION) || \
     (RTC_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_MINOR_VERSION) || \
     (RTC_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != GPT_IRQ_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Rtc_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((RTC_GPT_HW_SW_MAJOR_VERSION_PBCFG_C != GPT_IRQ_SW_MAJOR_VERSION) || \
     (RTC_GPT_HW_SW_MINOR_VERSION_PBCFG_C != GPT_IRQ_SW_MINOR_VERSION) || \
     (RTC_GPT_HW_SW_PATCH_VERSION_PBCFG_C != GPT_IRQ_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Rtc_Gpt_Hw_PBcfg.c and Gpt_Irq.h are different"
#endif

/*================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                                       GLOBAL CONSTANTS
 *================================================================================================*/
#define GPT_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
#define GPT_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Gpt_MemMap.h"
/*==================================================================================================
 *                                       GLOBAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL VARIABLES
 *================================================================================================*/

/*==================================================================================================
 *                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL MACROS
 *================================================================================================*/

/*==================================================================================================
 *                                      LOCAL CONSTANTS
 *================================================================================================*/

/*==================================================================================================
 *                                       LOCAL FUNCTIONS
 *================================================================================================*/

/*==================================================================================================
 *                                   LOCAL FUNCTION PROTOTYPES
 *================================================================================================*/

#ifdef __cplusplus
}
#endif/* RTC_GPT_HW_PBCFG_C*/

/** } */
