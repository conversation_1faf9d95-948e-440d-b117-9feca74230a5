/**
 * file    Flexio_Pwm_Hw_PBcfg.c
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#include "Flexio_Pwm_Hw_Types.h"
#include "Flexio_Pwm_Hw_Cfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                                 SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLEXIO_PWM_HW_PBCFG_VENDOR_ID_C                      (110U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C       (4U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C       (4U)
#define FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C    (0U)
#define FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION_C               (1U)
#define FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION_C               (0U)
#define FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION_C               (0U)

/*==================================================================================================
*                                       FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same vendor */
#if (FLEXIO_PWM_HW_PBCFG_VENDOR_ID_C != FLEXIO_PWM_HW_TYPES_VENDOR_ID)
    #error "Vendor IDs of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same AUTOSAR version */
#if ((FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != FLEXIO_PWM_HW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != FLEXIO_PWM_HW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != FLEXIO_PWM_HW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR version numbers of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Types.h header file are of the same software version */
#if ((FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION_C != FLEXIO_PWM_HW_TYPES_SW_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION_C != FLEXIO_PWM_HW_TYPES_SW_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION_C != FLEXIO_PWM_HW_TYPES_SW_PATCH_VERSION))
    #error "Software version numbers of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Types.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Cfg.h header file are of the same vendor */
#if (FLEXIO_PWM_HW_PBCFG_VENDOR_ID_C != FLEXIO_PWM_HW_CFG_VENDOR_ID)
    #error "Vendor IDs of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Cfg.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Cfg.h header file are of the same AUTOSAR version */
#if ((FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != FLEXIO_PWM_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != FLEXIO_PWM_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != FLEXIO_PWM_HW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR version numbers of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Cfg.h are different."
#endif

/* Check if header file and Flexio_Pwm_Hw_Cfg.h header file are of the same software version */
#if ((FLEXIO_PWM_HW_PBCFG_SW_MAJOR_VERSION_C != FLEXIO_PWM_HW_CFG_SW_MAJOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_MINOR_VERSION_C != FLEXIO_PWM_HW_CFG_SW_MINOR_VERSION) || \
     (FLEXIO_PWM_HW_PBCFG_SW_PATCH_VERSION_C != FLEXIO_PWM_HW_CFG_SW_PATCH_VERSION))
    #error "Software version numbers of Flexio_Pwm_HW_PBcfg.h and Flexio_Pwm_Hw_Cfg.h are different."
#endif
/*==================================================================================================
*                           LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                          LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL VARIABLES
==================================================================================================*/
/*==================================================================================================
*                                    LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                         LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                        GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

