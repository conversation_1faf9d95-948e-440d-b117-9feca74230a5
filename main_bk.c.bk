#ifdef __cplusplus
extern "C" {
#endif

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
//#include "check_example.h"
// osal includes
#include "FreeRTOS.h"
#include "task.h"
#include "osal.h"
#include "log.h"

#ifdef WIN32
#else
#include "lse14xx.h"
#include "Mcu.h"
#include "Lin.h"
#include "Port.h"
#include "Dio.h"
#include "Can.h"
#include "core_cm4.h"

#include "CanIf_Cfg.h"
#include "CanIf.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Dcm.h"
#include "Dem.h"
#include "Com.h"
#include "CanSM.h"
#include "ComM.h"
#include "ComM_PBCfg.h"
#include "ComM_Gent.h"

#endif

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define LIN_MASTER_SEND LIN_FRAMERESPONSE_TX
#define LIN_MASTER_RECEIVE  LIN_FRAMERESPONSE_RX
#define T_LIN_TEST_FRM_NUM      (2U)
#define LIN_FRAME_LEN           (8U)
#define T_LIN_TIME_OUT          40000

/* volatile  */uint8 T_Lin_Data[LIN_FRAME_LEN] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08};
/* volatile  */uint8 R_Lin_DATA[LIN_FRAME_LEN] = {0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
uint8 g_Lin_MasterSendFrameIdx = 0U;
uint8 g_Lin_MasterReceiveFrameIdx = 1U;
volatile uint8 g_point = 0U;
boolean g_SlaveAddr_0x32 = FALSE;
boolean g_SlaveAddr_0x30 = FALSE;
uint8 g_TestType = 0xFF;
Std_VersionInfoType g_Lin_Version = {0u};

const Lin_PduType Lin_FrameData[T_LIN_TEST_FRM_NUM] =
{
    {0x32U, LIN_CLASSIC_CS, LIN_MASTER_SEND, 8U, T_Lin_Data},
    {0x30U, LIN_CLASSIC_CS, LIN_MASTER_RECEIVE, 8U, R_Lin_DATA}
};
/*==================================================================================================
*                                      EXTERN DECLARATIONS
==================================================================================================*/

void SlaveCallBack(Lin_PduType * PduPtr)
{
    println(LogInfo, "Hello Demo!\n");
    os_timers_create();
    os_queues_create();
    os_pool_init();
	os_buffer_init();
    os_create_tasks();
    vTaskStartScheduler();
    for (;;);
}


#ifdef WIN32

int main(void)
{
    println(LogInfo, "Hello WIN32 Demo!\n");
    main_osal();
    return 0;
}

#else
Std_ReturnType Can_SetControllerMode_Std_ReturnType0;
Std_ReturnType Can_SetControllerMode_Std_ReturnType1;
Std_ReturnType Can_SetControllerMode_Std_ReturnType2;
Std_ReturnType Can_SetControllerMode_Std_ReturnType3;

// FUNC(void, CANIF_CODE) CanIf_RxIndication
// (
//     const Can_HwType* Mailbox,
//     const PduInfoType* PduInfoPtr
// )
// {
//     (void)Mailbox;
//     (void)PduInfoPtr;
//     Can_PduType pdu;
//     pdu.id = Mailbox->CanId & 0xFFFFFFFE;
//     pdu.length = PduInfoPtr->SduLength;
//     pdu.sdu = PduInfoPtr->SduDataPtr;
//     pdu.swPduHandle = 1;
//     Can_Write(Mailbox->Hoh + 4, &pdu);
// }

FUNC(void, CANIF_CODE) CanIf_CurrentIcomConfiguration
(
    uint8 ControllerId,
    IcomConfigIdType ConfigurationId,
    IcomSwitch_ErrorType Error
)
{
    (void)ControllerId;
    (void)ConfigurationId;
    (void)Error;
}

Std_ReturnType Can_Write_Std_ReturnType4;
Std_ReturnType Can_Write_Std_ReturnType5;
Std_ReturnType Can_Write_Std_ReturnType6;
Std_ReturnType Can_Write_Std_ReturnType7;

uint32 tick_ctr = 0;
uint32 flag = false;
boolean send_flag = true;

uint32 Can_SetControllerMode_Flag = 0;
Can_ControllerStateType Can_ControllerStateType_Can0;

void can_task_handler(void)
{
    tick_ctr++;

    if (flag == true)
    {
        uint8 data[64] = {0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x9};
        Can_PduType pdu;
        pdu.id = 0x123;
        pdu.length = 8;
        pdu.sdu = &data[0];
        pdu.swPduHandle = 1;
        Can_Write_Std_ReturnType4 = Can_Write(CanConf_CanHardwareObject_0_CanHardwareObject_Transmit, &pdu);

        if (Can_SetControllerMode_Flag == 1)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_SLEEP);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 2)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 3)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STOPPED);
            Can_GetControllerMode(CanConf_CanController_0_CanController_0, &Can_ControllerStateType_Can0);
            Can_SetControllerMode_Flag = 0;
        }
        else if (Can_SetControllerMode_Flag == 4)
        {
            Can_SetControllerMode_Std_ReturnType0 =
            Can_SetControllerMode(CanConf_CanController_0_CanController_0, 4);
            Can_SetControllerMode_Flag = 0;
        }
    }
}
void canif_task_handler(void) {
    PduInfoType pduInfor;
    uint8 data[64u] = {0x00u, 0x01u, 0x02u, 0, 0x03u, 0x04u};
    pduInfor.SduLength = 64u;
    // CanTxMsgGetData(PDUR_CANIF_CD_1_0X3BF, data); //FDJ
    pduInfor.SduDataPtr = &data[0];
    /* PduR Id */
    CanIf_Transmit(PDUR_CANIF_CD_1_0X3BF, &pduInfor);
}

void Enable_Lin_ISR(IRQn_Type source)
{
    uint32_t irq_prio = 0xE0 >> (8U - __NVIC_PRIO_BITS);
    NVIC_SetPriority(source, irq_prio);
    NVIC_ClearPendingIRQ(source);
    NVIC_EnableIRQ(source);
}

void Enable_Can_ISR(IRQn_Type source)
{
    uint32_t irq_prio = 0xE0 >> (8U - __NVIC_PRIO_BITS);
    NVIC_SetPriority(source, irq_prio);
    NVIC_ClearPendingIRQ(source);
    NVIC_EnableIRQ(source);
}
/*==================================================================================================
*                                       CALLBACK DEFINE
==================================================================================================*/
/* 仅供从节点使用，在LinIf_HeaderIndication中调用，用于装载数据 */
void SlaveCallBack(Lin_PduType * PduPtr)
{
    //if (g_SlaveAddr_0x30 == TRUE)
    //{
    //    g_SlaveAddr_0x30 = FALSE;
    //    PduPtr->Cs = LIN_CLASSIC_CS;
    //    PduPtr->Dl = 8U;
    //    PduPtr->Drc = LIN_FRAMERESPONSE_TX;
    //    PduPtr->SduPtr = T_Lin_Data;
    //}
    //else if (g_SlaveAddr_0x32 == TRUE)
    //{
    //    g_SlaveAddr_0x32 = FALSE;
    //    PduPtr->Cs = LIN_CLASSIC_CS;
    //    PduPtr->Dl = 8U;
    //    PduPtr->Drc = LIN_FRAMERESPONSE_RX;
    //}
    //else
    //{
    //    PduPtr->Drc = LIN_FRAMERESPONSE_IGNORE;
    //}
}

extern const CanIf_ConfigType CanIf_InitCfgSet;
extern const PduR_PBConfigType PduR_PBConfigData;
extern const Com_ConfigType Com_PBConfigData;
extern const ComM_ConfigType ComM_Config;
extern const CanSM_ConfigType CanSM_Config;
extern const CanTp_ConfigType CanTp_Config;
extern const Dcm_CfgType Dcm_Cfg;
extern const Dem_ConfigType DemPbCfg;

int lin_main(){
    Enable_Lin_ISR(LIN0_IRQn);
    Enable_Lin_ISR(LIN1_IRQn);

    Mcu_Init(&Mcu_Config);
    Mcu_SetMode(McuModeSettingConf_0);
    Mcu_InitClock(McuClockSettingConfig_0);
    while (MCU_PLL_LOCKED != Mcu_GetPllStatus())
    {
        /* Busy wait until the System PLL is locked */
    }
    Mcu_DistributePllClock();

    Port_Init(&Port_Config);

#if (STD_ON != LIN_PRECOMPILE_SUPPORT)
    Lin_Init(&Lin_Config);
#else
    Lin_Init(NULL_PTR);
#endif

    __enable_irq();
    
    (void)Lin_WakeupInternal(LIN_CHANNEL_ID_0);
    (void)Lin_CheckWakeup(LIN_CHANNEL_ID_0);
    
    Lin_PduType linFrame;
    volatile uint32 TimeOut;
    volatile Lin_StatusType T_LinStatus;
    volatile uint8 T_result = 0U;
    static uint8 linSdu[8] = {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0};
    uint8* linSduPtr = linSdu;

    while(true){
        /* Master Tx id 0x26 */
        linFrame = Lin_FrameData[g_Lin_MasterSendFrameIdx];
        (void)Lin_SendFrame(LIN_CHANNEL_ID_0, &linFrame);
        TimeOut = 4 * T_LIN_TIME_OUT;
        do 
        {
            T_LinStatus = Lin_GetStatus(LIN_CHANNEL_ID_0, &linSduPtr);
        } while (LIN_TX_OK != T_LinStatus && 0 != TimeOut--);
        if (LIN_TX_OK == T_LinStatus)
        {
            T_result = T_result + 1U;
        }

        /* Master Rx slave id 0x27 */
        linFrame = Lin_FrameData[g_Lin_MasterReceiveFrameIdx];
        (void)Lin_SendFrame(LIN_CHANNEL_ID_0, &linFrame);
        TimeOut = 4 * T_LIN_TIME_OUT;
        do 
        {
            T_LinStatus = Lin_GetStatus(LIN_CHANNEL_ID_0, &linSduPtr);
        } while (LIN_RX_OK != T_LinStatus && 0 != TimeOut--);
        if (LIN_RX_OK == T_LinStatus)
        {
            T_result = T_result + 1U;
        }
    }
}

int can_main(void)
{
    __enable_irq();

    Mcu_Init(&Mcu_Config);
    Mcu_SetMode(McuModeSettingConf_0);
    Mcu_InitClock(McuClockSettingConfig_0);
    while ( MCU_PLL_LOCKED != Mcu_GetPllStatus() )
    {
        /* Busy wait until the System PLL is locked */
    }
    Mcu_DistributePllClock();

    Port_Init(&Port_Config);

    Dio_WriteChannel(DioConf_DioChannel_CAN0_STB, STD_LOW);

//    Dio_WriteChannel(DioConf_DioChannel_PTC1_CAN2_EN, STD_HIGH);
//    Dio_WriteChannel(DioConf_DioChannel_PTC0_CAN2_STB_N, STD_HIGH);



    Enable_Can_ISR(CAN0_LINE0_IRQn);    /* 39 CAN0_INT0_IRQHandler  CAN0 �ж���0 �ж� */
    Enable_Can_ISR(CAN0_LINE1_IRQn);    /* 40 CAN0_INT1_IRQHandler  CAN0 �ж���1 �ж� */
    Enable_Can_ISR(CAN0_DMU_IRQn);      /* 41 CAN0_DMU_IRQHandler   CAN0 ��DMU �ж� */
//    Enable_Can_ISR(CAN1_LINE0_IRQn);    /* 42 CAN1_INT0_IRQHandler  CAN1 �ж���0 �ж� */
//    Enable_Can_ISR(CAN1_LINE1_IRQn);    /* 43 CAN1_INT1_IRQHandler  CAN1 �ж���1 �ж� */
//    Enable_Can_ISR(CAN1_DMU_IRQn);      /* 44 CAN1_DMU_IRQHandler   CAN1 ��DMU �ж� */
//    Enable_Can_ISR(CAN2_LINE0_IRQn);    /* 45 CAN2_INT0_IRQHandler  CAN2 �ж���0 �ж� */
//    Enable_Can_ISR(CAN2_LINE1_IRQn);    /* 46 CAN2_INT1_IRQHandler  CAN2 �ж���1 �ж� */
//    Enable_Can_ISR(CAN2_DMU_IRQn);      /* 47 CAN2_DMU_IRQHandler   CAN2 ��DMU �ж� */
//    Enable_Can_ISR(CAN3_LINE0_IRQn);    /* 48 CAN3_INT0_IRQHandler  CAN3 �ж���0 �ж� */
//    Enable_Can_ISR(CAN3_LINE1_IRQn);    /* 49 CAN3_INT1_IRQHandler  CAN3 �ж���1 �ж� */
//    Enable_Can_ISR(CAN3_DMU_IRQn);      /* 50 CAN3_DMU_IRQHandler   CAN3 ��DMU �ж� */
//    Enable_Can_ISR(CAN_ERR_IRQn);       /* 51 CAN_MEM_IRQHandler    CAN memory �쳣�ж� */

    Can_Init(&CanConfigSet_0);

    Can_DeInit();

    Can_Init(&CanConfigSet_0);


    flag = true;

    SysTick_Config(160000);    

    CanIf_Init(&CanIf_InitCfgSet);
    Can_SetControllerMode(CanConf_CanController_0_CanController_0, CAN_CS_STARTED);
    // CanIf_SetControllerMode( 0, CANIF_CS_STARTED );
    CanIf_SetPduMode( 0, CANIF_ONLINE );
    // CanSM_Init(&CanSM_Config);
    PduR_Init(&PduR_PBConfigData);
    Com_Init(&Com_PBConfigData);
    // Init Com group
    Com_IpduGroupVector ipduGroupVector={0};
    Com_SetIpduGroup(ipduGroupVector, Com_RxPduGroup_CanController_0, TRUE);
    Com_SetIpduGroup(ipduGroupVector, Com_TxPduGroup_CanController_0, TRUE);
    Com_ReceptionDMControl(ipduGroupVector);
    Com_IpduGroupControl(ipduGroupVector,TRUE);

    // ComM init
    ComM_Init(&ComM_Config);
    ComM_RequestComMode(0, COMM_FULL_COMMUNICATION);
//    ComM_CommunicationAllowed(ComMUser_0, TRUE);

    // RttLogPrintf("BswInit: Com Init end\n");

    CanTp_Init(&CanTp_Config);
    Dcm_Init(&Dcm_Cfg);
    Dem_PreInit();
    Dem_Init(&DemPbCfg);

    main_osal();
    return 0;
}

int main(void)
{
    // can_main();
    lin_main();
    return 0;
}

#endif

#ifdef __cplusplus
}
#endif

/** @} */
