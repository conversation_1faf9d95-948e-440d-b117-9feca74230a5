/**
 * @file     Lin_Cfg.h
 * @brief    the pc configure head files for <PERSON> .
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-04
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/4      1.0     zhangb    Initilization
 **/


#ifndef LIN_CFG_H
#define LIN_CFG_H


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"
#include "Lin_PBcfg.h"


#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LIN_CFG_VENDOR_ID                    (110u)
#define LIN_CFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define LIN_CFG_AR_RELEASE_MINOR_VERSION     (4u)
#define LIN_CFG_AR_RELEASE_REVISION_VERSION  (0u)
#define LIN_CFG_SW_MAJOR_VERSION             (1u)
#define LIN_CFG_SW_MINOR_VERSION             (0u)
#define LIN_CFG_SW_PATCH_VERSION             (0u)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lin_PBcfg.h */
#if (LIN_CFG_VENDOR_ID != LIN_PBCFG_VENDOR_ID)
    #error "Lin_Cfg.h and Lin_PBcfg.h have different vendor ids"
#endif
#if ((LIN_CFG_AR_RELEASE_MAJOR_VERSION    != LIN_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_CFG_AR_RELEASE_MINOR_VERSION    != LIN_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_CFG_AR_RELEASE_REVISION_VERSION != LIN_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Lin_Cfg.h and Lin_PBcfg.h are different"
#endif
#if ((LIN_CFG_SW_MAJOR_VERSION != LIN_PBCFG_SW_MAJOR_VERSION) || \
     (LIN_CFG_SW_MINOR_VERSION != LIN_PBCFG_SW_MINOR_VERSION) || \
     (LIN_CFG_SW_PATCH_VERSION != LIN_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Lin_Cfg.h and Lin_PBcfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((LIN_CFG_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (LIN_CFG_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of Lin_Cfg.h and Mcal.h are different"
    #endif
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define LIN_CONFIG_EXT \
    LIN_CONFIG_PB

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/



/**
* @brief          Configuration for Lin Channel 0 - Configuration:
*                 LinGlobalConfig.
*
* 
*/
extern const LinChannelConfig_t Lin_LinChannel_0_UnAllocatedPar;

/**
* @brief          Configuration for Lin Channel 1 - Configuration:
*                 LinGlobalConfig.
*
* 
*/
extern const LinChannelConfig_t Lin_LinChannel_1_UnAllocatedPar;

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                     EXTERNAL CONSTANTS
==================================================================================================*/
#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

#if (LIN_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF)
extern const Mcal_DemErrorType Lin_E_TimeoutCfg;
#endif /* LIN_DISABLE_DEM_REPORT_ERROR_STATUS == STD_OFF */

#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_CFG_H */
