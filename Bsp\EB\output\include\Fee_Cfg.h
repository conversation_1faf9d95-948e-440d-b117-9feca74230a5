/**
 * file     Fee.h
 * brief    Specification of Flash EEPROM Emulation
 * author   Huhl <<EMAIL>>
 * date     2024.5.25
 * version  1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved
 */

#ifndef FEE_CFG_H
#define FEE_CFG_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fee_Types.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FEE_VENDOR_ID_CFG                    (110U)
#define FEE_AR_RELEASE_MAJOR_VERSION_CFG     (4U)
#define FEE_AR_RELEASE_MINOR_VERSION_CFG     (4U)
#define FEE_AR_RELEASE_REVISION_VERSION_CFG  (0U)
#define FEE_SW_MAJOR_VERSION_CFG             (1U)
#define FEE_SW_MINOR_VERSION_CFG             (0U)
#define FEE_SW_PATCH_VERSION_CFG             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Fee Types header file are of the same vendor */
#if (FEE_VENDOR_ID_CFG != FEE_TYPES_VENDOR_ID)
    #error "Fee_Types.h and Fee_Cfg.h have different vendor ids"
#endif
/* Check if current file and Fee Types header file are of the same Autosar version */
#if ((FEE_AR_RELEASE_MAJOR_VERSION_CFG    != FEE_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (FEE_AR_RELEASE_MINOR_VERSION_CFG    != FEE_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (FEE_AR_RELEASE_REVISION_VERSION_CFG != FEE_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Fee_Types.h and Fee_Cfg.h are different"
#endif
/* Check if this file and Types header header file are of the same software version */
#if ((FEE_SW_MAJOR_VERSION_CFG != FEE_TYPES_SW_MAJOR_VERSION) || \
     (FEE_SW_MINOR_VERSION_CFG != FEE_TYPES_SW_MINOR_VERSION) || \
     (FEE_SW_PATCH_VERSION_CFG != FEE_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Fee_Types.h and Fee_Cfg.h are different"
#endif




/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/

    



/* Pre-processor switch to enable and disable development error detection */

#define FEE_DEV_ERROR_DETECT                      (STD_ON)

/* Pre-processor switch to enable / disable the API to read out the modules
    version information */
#define FEE_VERSION_INFO_API                      (STD_ON)

/* Compiler switch to enable/disable the SetMode functionality of the module */
#define FEE_SETMODE_API_SUPPORTED                 (STD_ON)

/* Pre-processor switch to enable /disable the Fls_Cancel function of the module */
#define FEE_CANCEL_API                            (STD_ON)

/* The size in bytes to which logical blocks shall be aligned */
#define FEE_VIRTUAL_PAGE_SIZE                     (16U)

/* Job end notification routine provided by the upper layer module (declaration) */

/* Job end notification routine provided by the upper layer module */
#define FEE_NVM_JOB_END_NOTIFICATION              
/* Job error notification routine provided by the upper layer module (declaration) */

/* Job error notification routine provided by the upper layer module */
#define FEE_NVM_JOB_ERROR_NOTIFICATION            
/* Job Sector format notification routine provided by the upper layer module (declaration) */

/* Job Sector format notification routine provided by the upper layer module */
#define FEE_NVM_FORMAT_NOTIFICATION       
/* Number of configured Fee Sector groups */
#define FEE_NUM_OF_SECTOR_GROUPS              (1U)

/* Number of configured Fee blocks */
#define FEE_NUM_OF_CFG_BLOCKS                  (1U)

/* Maximum number of Fee blocks in all project versions and configurations */
#define FEE_MAX_NUM_OF_BLOCKS                      (10U)

/* Management overhead per logical block in bytes */
#define FEE_BLOCK_OVERHEAD                        (48U)

/* Management overhead per logical sector in bytes */
#define FEE_SECTOR_OVERHEAD                      (64U)

/* Size of the data buffer in bytes */
#define FEE_DATA_BUFFER_SIZE                      (64U)

/* The contents of an erased flash memory cell */
#define FEE_ERASED_VALUE                          (0xFFU)

/* Value of the block and Sector validation flag */
#define FEE_VALIDATED_VALUE                       (0x81U)

/* Value of the block and Sector invalidation flag */
#define FEE_INVALIDATED_VALUE                     (0x18U)

/* If reset, power loss etc. occurs here, neither newly nor previously written data is available */
#define FEE_BLOCK_ALWAYS_AVAILABLE                (STD_ON)

/* Behavior of Fee_ImmediateBlockErase */
#define FEE_LEGACY_IMM_ERASE_MODE                 (STD_OFF)

/* This configuration defines wheather Fee should swap foreign blocks found in flash at swap or not */
#define FEE_SWAP_FOREIGN_BLOCKS_ENABLED           (STD_ON)

#if (FEE_SWAP_FOREIGN_BLOCKS_ENABLED == STD_ON)
/* This configuration defines for which project the Fee configuration is used  */
#define FEE_BOOTLOADER_CONFIG                     (STD_OFF)
#endif
/* This configuration defines if the status for the never written blocks must be INVALID or INCONSISTENT */
#define FEE_MARK_EMPTY_BLOCKS_INVALID             (STD_ON)

/* Symbolic names of configured Fee blocks */
/* MASKPRQAS 0791 ++ #Misra-C:2012 Rule-5.4 Macro identifiers shall be distinct
 * Reason: These macros are distinct */
#define FeeConf_FeeBlockConfiguration_FeeBlockConfiguration_Motor     (1U)
/* MASKPRQAS 0791 -- */
#define FEE_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"
/* Configuration of Sector group set */
extern const FeeSectorGroup_t Fee_SectorGrps[ FEE_NUM_OF_SECTOR_GROUPS ];

/* Configuration of Fee blocks */
extern const FeeBlockConfig_t Fee_BlockConfig[ FEE_NUM_OF_CFG_BLOCKS ];

#define FEE_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

#define FEE_START_SEC_VAR_INIT_UNSPECIFIED
#include "Fee_MemMap.h"

/* List of pointer for information accross all sector groups */
extern FeeSectorRuntimeInfo_t *Fee_SectorRuntimeInfo[ FEE_NUM_OF_SECTOR_GROUPS ];

#define FEE_STOP_SEC_VAR_INIT_UNSPECIFIED
#include "Fee_MemMap.h"

#define FEE_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

/* List of sectors to be recovered */
extern const FeeSectorToRecover_t Fee_SectorToRecover[FEE_NUMBER_OF_SECTORS_TO_RECOVER];

#define FEE_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fee_MemMap.h"

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* FEE_CFG_H */
