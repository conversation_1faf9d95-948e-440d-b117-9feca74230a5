<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Dma" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Dma" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Dma"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="true">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="DmaGeneral" type="IDENTIFIABLE">
                <d:var name="DmaEnableDemReportErrorStatus" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="DmaEnableDevErrorDetect" type="BOOLEAN" 
                       value="false"/>
                <d:var name="Dma_VersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ctr name="DmaDma" type="IDENTIFIABLE">
                  <d:var name="DmaEnableDma" type="BOOLEAN" value="true">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
              </d:ctr>
              <d:ctr name="DmaConfig" type="IDENTIFIABLE">
                <d:ctr name="DmaDemEventParameterRefs" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:ref name="DMA_E_FAILURE" type="REFERENCE" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:ref>
                </d:ctr>
                <d:lst name="dmaLogicInstance_ConfigType" type="MAP">
                  <d:ctr name="dmaLogicInstance_ConfigType_0" 
                         type="IDENTIFIABLE">
                    <d:var name="dmaLogicInstance_IdName" type="STRING" 
                           value="DMA_LOGIC_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicInstance_hwId" type="STRING" 
                           value="DMA_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="ChannelTimedOut" type="INTEGER" value="1023"/>
                    <d:var name="AbnormalAlarmInterruption" 
                           type="FUNCTION-NAME" value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="FunctionalSafetyInterruption" 
                           type="FUNCTION-NAME" value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                  </d:ctr>
                </d:lst>
                <d:lst name="dmaLogicChannel_Type" type="MAP">
                  <d:ctr name="dmaLogicChannel_Type_0" type="IDENTIFIABLE">
                    <d:var name="dmaLogicChannel_LogicName" type="STRING" 
                           value="DMA_LOGIC_CH_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwInstId" type="STRING" 
                           value="DMA_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwChId" type="STRING" 
                           value="DMA_HW_CH_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_EnableGlobalConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableTransferConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableScatterGather" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="DmaLogicChannelConfigType" type="IDENTIFIABLE">
                      <d:ctr name="DmaLogicChannelGlobalConfigType" 
                             type="IDENTIFIABLE">
                        <d:var name="TimeoutThreshold" type="INTEGER" 
                               value="4095">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ctr name="dmaLogicChannelConfig_GlobalRequestType" 
                               type="IDENTIFIABLE">
                          <d:var name="PeripheralRequestConfiguration" 
                                 type="STRING" value="DMA_ADC0"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxCh" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxChTrig" 
                                 type="BOOLEAN" value="false"/>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalInterruptType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCompleteInterrupt" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalInterrupt_enDmaLrInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCfgErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaTransErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaListRdErrorInterrupt" 
                                 type="BOOLEAN" value="false"/>
                          <d:var name="CompleteInterruptCallback" 
                                 type="FUNCTION-NAME" value="Dma_Adc0_CallBack">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="LinkListInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CfgErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ChTransErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ListRdErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalPriorityType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalPriority_LevelPriority" 
                                 type="STRING" value="DMA_HW_LEVEL_PRIO0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_TransferConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_TransferControlType" 
                               type="IDENTIFIABLE">
                          <d:var name="FlowCtrlTransType" type="STRING" 
                                 value="DMA_FC_M2P_D"/>
                          <d:var name="LinkMode" type="ENUMERATION" 
                                 value="DMA_LINK_INVALID">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="NextChannelId" type="STRING" 
                                 value="DMA_HW_CH_0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DmaUserHeaderFileWithExternDeclarations" 
                                 type="STRING" value="mcal_stub.h">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="SourceAddress" type="STRING" 
                                 value="0x4800012CU"/>
                          <d:var name="SourceIncrementEnalbe" type="BOOLEAN" 
                                 value="true"/>
                          <d:var name="DestinationAddress" type="STRING" 
                                 value="&amp;Adc0_SoftOneShotResultBuff"/>
                          <d:var name="DestinationIncrementEnalbe" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="ACountLength" type="INTEGER" value="40"/>
                          <d:var name="TransBurstlength" type="STRING" 
                                 value="DMA_BURST_SIZE0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransDatawide" type="STRING" 
                                 value="DMA_TRANS_32BIT"/>
                          <d:var name="TransDim" type="ENUMERATION" 
                                 value="ONE_DIM"/>
                          <d:ctr name="dmaLogicChannelConfig_DIM2ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="BCountLength" type="INTEGER" value="8"/>
                            <d:var name="SourceAddrOffset2D" type="INTEGER" 
                                   value="4"/>
                            <d:var name="DestinationAddrOffset2D" 
                                   type="INTEGER" value="2"/>
                          </d:ctr>
                          <d:ctr name="dmaLogicChannelConfig_DIM3ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="CCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset3D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset3D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceIncrementMode" type="STRING" 
                                   value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationIncrementMode" 
                                   type="STRING" value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_ScatterGatherConfigType" 
                             type="IDENTIFIABLE">
                        <d:lst 
                               name="dmaLogicChannelConfig_ScatterGatherArrayType" 
                               type="MAP"/>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="dmaLogicChannel_Type_1" type="IDENTIFIABLE">
                    <d:var name="dmaLogicChannel_LogicName" type="STRING" 
                           value="DMA_LOGIC_CH_1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwInstId" type="STRING" 
                           value="DMA_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwChId" type="STRING" 
                           value="DMA_HW_CH_1">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_EnableGlobalConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableTransferConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableScatterGather" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="DmaLogicChannelConfigType" type="IDENTIFIABLE">
                      <d:ctr name="DmaLogicChannelGlobalConfigType" 
                             type="IDENTIFIABLE">
                        <d:var name="TimeoutThreshold" type="INTEGER" 
                               value="4095">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ctr name="dmaLogicChannelConfig_GlobalRequestType" 
                               type="IDENTIFIABLE">
                          <d:var name="PeripheralRequestConfiguration" 
                                 type="STRING" value="DMA_ADC1"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxCh" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxChTrig" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalInterruptType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCompleteInterrupt" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalInterrupt_enDmaLrInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCfgErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaTransErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaListRdErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CompleteInterruptCallback" 
                                 type="FUNCTION-NAME" value="Dma_Adc1_CallBack">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="LinkListInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CfgErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ChTransErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ListRdErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalPriorityType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalPriority_LevelPriority" 
                                 type="STRING" value="DMA_HW_LEVEL_PRIO0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_TransferConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_TransferControlType" 
                               type="IDENTIFIABLE">
                          <d:var name="FlowCtrlTransType" type="STRING" 
                                 value="DMA_FC_M2P_D"/>
                          <d:var name="LinkMode" type="ENUMERATION" 
                                 value="DMA_LINK_INVALID">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="NextChannelId" type="STRING" 
                                 value="DMA_HW_CH_0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DmaUserHeaderFileWithExternDeclarations" 
                                 type="STRING" value="mcal_stub.h">
                            <a:a name="ENABLE" value="false"/>
                          </d:var>
                          <d:var name="SourceAddress" type="STRING" 
                                 value="0x4800112CU"/>
                          <d:var name="SourceIncrementEnalbe" type="BOOLEAN" 
                                 value="true"/>
                          <d:var name="DestinationAddress" type="STRING" 
                                 value="&amp;Adc1_SoftOneShotResultBuff"/>
                          <d:var name="DestinationIncrementEnalbe" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="ACountLength" type="INTEGER" value="36"/>
                          <d:var name="TransBurstlength" type="STRING" 
                                 value="DMA_BURST_SIZE0"/>
                          <d:var name="TransDatawide" type="STRING" 
                                 value="DMA_TRANS_32BIT"/>
                          <d:var name="TransDim" type="ENUMERATION" 
                                 value="ONE_DIM"/>
                          <d:ctr name="dmaLogicChannelConfig_DIM2ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="BCountLength" type="INTEGER" value="8"/>
                            <d:var name="SourceAddrOffset2D" type="INTEGER" 
                                   value="4"/>
                            <d:var name="DestinationAddrOffset2D" 
                                   type="INTEGER" value="2"/>
                          </d:ctr>
                          <d:ctr name="dmaLogicChannelConfig_DIM3ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="CCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset3D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset3D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceIncrementMode" type="STRING" 
                                   value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationIncrementMode" 
                                   type="STRING" value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_ScatterGatherConfigType" 
                             type="IDENTIFIABLE">
                        <d:lst 
                               name="dmaLogicChannelConfig_ScatterGatherArrayType" 
                               type="MAP"/>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="dmaLogicChannel_uart0_tx" type="IDENTIFIABLE">
                    <d:var name="dmaLogicChannel_LogicName" type="STRING" 
                           value="DMA_LOGIC_CH_2">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwInstId" type="STRING" 
                           value="DMA_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwChId" type="STRING" 
                           value="DMA_HW_CH_2">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_EnableGlobalConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableTransferConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableScatterGather" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="DmaLogicChannelConfigType" type="IDENTIFIABLE">
                      <d:ctr name="DmaLogicChannelGlobalConfigType" 
                             type="IDENTIFIABLE">
                        <d:var name="TimeoutThreshold" type="INTEGER" value="1"/>
                        <d:ctr name="dmaLogicChannelConfig_GlobalRequestType" 
                               type="IDENTIFIABLE">
                          <d:var name="PeripheralRequestConfiguration" 
                                 type="STRING" value="DMA_UART0_TX"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxCh" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxChTrig" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalInterruptType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCompleteInterrupt" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalInterrupt_enDmaLrInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCfgErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaTransErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaListRdErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CompleteInterruptCallback" 
                                 type="FUNCTION-NAME" 
                                 value="Lpuart_0_Uart_Hw_DmaTxCompleteCallback">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="LinkListInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CfgErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ChTransErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ListRdErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalPriorityType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalPriority_LevelPriority" 
                                 type="STRING" value="DMA_HW_LEVEL_PRIO0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_TransferConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_TransferControlType" 
                               type="IDENTIFIABLE">
                          <d:var name="FlowCtrlTransType" type="STRING" 
                                 value="DMA_FC_M2P_D"/>
                          <d:var name="LinkMode" type="ENUMERATION" 
                                 value="DMA_LINK_INVALID">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="NextChannelId" type="STRING" 
                                 value="DMA_HW_CH_0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DmaUserHeaderFileWithExternDeclarations" 
                                 type="STRING" value="">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="SourceAddress" type="STRING" value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="SourceIncrementEnalbe" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DestinationAddress" type="STRING" 
                                 value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DestinationIncrementEnalbe" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ACountLength" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransBurstlength" type="STRING" 
                                 value="DMA_BURST_SIZE0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransDatawide" type="STRING" 
                                 value="DMA_TRANS_8BIT">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransDim" type="ENUMERATION" 
                                 value="ONE_DIM">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ctr name="dmaLogicChannelConfig_DIM2ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="BCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset2D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset2D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                          <d:ctr name="dmaLogicChannelConfig_DIM3ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="CCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset3D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset3D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceIncrementMode" type="STRING" 
                                   value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationIncrementMode" 
                                   type="STRING" value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_ScatterGatherConfigType" 
                             type="IDENTIFIABLE">
                        <d:lst 
                               name="dmaLogicChannelConfig_ScatterGatherArrayType" 
                               type="MAP"/>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                  <d:ctr name="dmaLogicChannel_uart0_rx" type="IDENTIFIABLE">
                    <d:var name="dmaLogicChannel_LogicName" type="STRING" 
                           value="DMA_LOGIC_CH_3">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwInstId" type="STRING" 
                           value="DMA_HW_INST_0">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_HwChId" type="STRING" 
                           value="DMA_HW_CH_3">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:var name="dmaLogicChannel_EnableGlobalConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableTransferConfig" 
                           type="BOOLEAN" value="true"/>
                    <d:var name="dmaLogicChannel_EnableScatterGather" 
                           type="BOOLEAN" value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ctr name="DmaLogicChannelConfigType" type="IDENTIFIABLE">
                      <d:ctr name="DmaLogicChannelGlobalConfigType" 
                             type="IDENTIFIABLE">
                        <d:var name="TimeoutThreshold" type="INTEGER" value="1"/>
                        <d:ctr name="dmaLogicChannelConfig_GlobalRequestType" 
                               type="IDENTIFIABLE">
                          <d:var name="PeripheralRequestConfiguration" 
                                 type="STRING" value="DMA_UART0_RX"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxCh" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalRequest_enDmaMuxChTrig" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalInterruptType" 
                               type="IDENTIFIABLE">
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCompleteInterrupt" 
                                 type="BOOLEAN" value="true"/>
                          <d:var name="dmaGlobalInterrupt_enDmaLrInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaCfgErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaTransErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var 
                                 name="dmaGlobalInterrupt_enDmaListRdErrorInterrupt" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CompleteInterruptCallback" 
                                 type="FUNCTION-NAME" 
                                 value="Lpuart_0_Uart_Hw_DmaRxCompleteCallback">
                            <a:a name="ENABLE" value="true"/>
                          </d:var>
                          <d:var name="LinkListInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="CfgErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ChTransErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ListRdErrorInterruptCallback" 
                                 type="FUNCTION-NAME" value="NULL_PTR">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                        <d:ctr name="dmaLogicChannelConfig_GlobalPriorityType" 
                               type="IDENTIFIABLE">
                          <d:var name="dmaGlobalPriority_LevelPriority" 
                                 type="STRING" value="DMA_HW_LEVEL_PRIO0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_TransferConfigType" 
                             type="IDENTIFIABLE">
                        <d:ctr name="dmaLogicChannelConfig_TransferControlType" 
                               type="IDENTIFIABLE">
                          <d:var name="FlowCtrlTransType" type="STRING" 
                                 value="DMA_FC_M2P_D"/>
                          <d:var name="LinkMode" type="ENUMERATION" 
                                 value="DMA_LINK_INVALID">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="NextChannelId" type="STRING" 
                                 value="DMA_HW_CH_0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DmaUserHeaderFileWithExternDeclarations" 
                                 type="STRING" value="">
                            <a:a name="ENABLE" value="false"/>
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="SourceAddress" type="STRING" value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="SourceIncrementEnalbe" type="BOOLEAN" 
                                 value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DestinationAddress" type="STRING" 
                                 value="0U">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="DestinationIncrementEnalbe" 
                                 type="BOOLEAN" value="false">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="ACountLength" type="INTEGER" value="1">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransBurstlength" type="STRING" 
                                 value="DMA_BURST_SIZE0">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransDatawide" type="STRING" 
                                 value="DMA_TRANS_8BIT">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:var name="TransDim" type="ENUMERATION" 
                                 value="ONE_DIM">
                            <a:a name="IMPORTER_INFO" value="@DEF"/>
                          </d:var>
                          <d:ctr name="dmaLogicChannelConfig_DIM2ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="BCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset2D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset2D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                          <d:ctr name="dmaLogicChannelConfig_DIM3ParaType" 
                                 type="IDENTIFIABLE">
                            <d:var name="CCountLength" type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceAddrOffset3D" type="INTEGER" 
                                   value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationAddrOffset3D" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="SourceIncrementMode" type="STRING" 
                                   value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="DestinationIncrementMode" 
                                   type="STRING" value="A_SYNC">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:ctr>
                      <d:ctr name="dmaLogicChannel_ScatterGatherConfigType" 
                             type="IDENTIFIABLE">
                        <d:lst 
                               name="dmaLogicChannelConfig_ScatterGatherArrayType" 
                               type="MAP"/>
                      </d:ctr>
                    </d:ctr>
                  </d:ctr>
                </d:lst>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="255">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
