//#include "check_example.h"
#include "log.h"
#include "osal.h"
#include "pid.h"
#include "motor.h"


/*pid*/
#define LOC_DEAD_ZONE 0.0001f
#define LOC_INTEGRAL_START_ERR 10000
#define LOC_INTEGRAL_MAX_VAL 800

void pid_init(tMotor* motor)
{
    motor->speed_pid.kp = motor->config.speed_pid.kp;
    motor->speed_pid.ki = motor->config.speed_pid.ki;
    motor->speed_pid.kd = motor->config.speed_pid.kd;

    motor->position_pid.kp = motor->config.position_pid.kp;
    motor->position_pid.ki = motor->config.position_pid.ki;
    motor->position_pid.kd = motor->config.position_pid.kd;    
}

void pid_reset(tPID * pid)
{
    pid->integral  = 0;
    pid->error_last = 0;
}

void pid_set_target(tPID * pid, float temp_val)
{  
    pid->target = temp_val;    // 设置当前的目标值
}

float pid_get_target(tPID * pid)
{
	return pid->target;    // 设置当前的目标值
}


float pid_loop(tPID * pid, float error)
{
    /* 计算误差 */
    pid->error = error;

    /*积分项，积分分离，偏差较大时去掉积分作用*/
	if(IS_IN_RANGE(pid->error, -LOC_INTEGRAL_START_ERR, LOC_INTEGRAL_START_ERR))
	{
		pid->integral += pid->error;  
        /*积分范围限定，防止积分饱和*/
        LIMIT(pid->integral, -LOC_INTEGRAL_MAX_VAL, LOC_INTEGRAL_MAX_VAL);
	}

    float p = pid->kp * pid->error;
    float i = pid->ki * pid->integral;
    float d = pid->kd * (pid->error - pid->error_last);
    pid->output = p + i + d;

    /*误差传递*/
    pid->error_last = pid->error;

    return pid->output;
}



