/**
 * *************************************************************************
 * @file   Ram_Hw_PBcfg.c
 * @brief  This file is used for Post-Build(PB) configuration file code template.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Std_Types.h"
#include "Ram_Hw_Cfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RAM_HW_PBCFG_VENDOR_ID_C                         (110U)
#define RAM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C          (4U)
#define RAM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C          (4U)
#define RAM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C       (0U)
#define RAM_HW_PBCFG_SW_MAJOR_VERSION_C                  (1U)
#define RAM_HW_PBCFG_SW_MINOR_VERSION_C                  (0U)
#define RAM_HW_PBCFG_SW_PATCH_VERSION_C                  (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Ram_Hw_PBcfg.c file and Ram_Hw_Cfg.h file are of the same vendor */
#if (RAM_HW_PBCFG_VENDOR_ID_C != RAM_HW_CFG_VENDOR_ID)
    #error "Ram_Hw_PBcfg.c and Ram_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Ram_Hw_PBcfg.c file and Ram_Hw_Cfg.h file are of the same Autosar version */
#if ((RAM_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != RAM_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (RAM_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != RAM_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (RAM_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != RAM_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Ram_Hw_PBcfg.c and Ram_Hw_Cfg.h are different"
#endif

/* Check if Ram_Hw_PBcfg.c file and Ram_Hw_Cfg.h file are of the same Software version */
#if ((RAM_HW_PBCFG_SW_MAJOR_VERSION_C != RAM_HW_CFG_SW_MAJOR_VERSION) || \
     (RAM_HW_PBCFG_SW_MINOR_VERSION_C != RAM_HW_CFG_SW_MINOR_VERSION) || \
     (RAM_HW_PBCFG_SW_PATCH_VERSION_C != RAM_HW_CFG_SW_PATCH_VERSION) \
    )
  #error "Software Version Numbers of Ram_Hw_PBcfg.c and Ram_Hw_Cfg.h are different"
#endif

/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/


#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

CONST(Ram_Hw_SramEccCallback, AUTOMATIC)  Ram_Hw_BitEccCallback = NULL_PTR;
CONST(Ram_Hw_SramEccCallback, AUTOMATIC)  Ram_Hw_FatalEccCallback = NULL_PTR;



#ifndef MCU_MAX_NORAMCONFIGS

/**
* @brief        Definition of RAM sections within the configuration structure.
* @details      Blocks of memory are initialized with a default data.
*               Configuration set by calling Mcu_InitRamSection() API.
*
*/
const Ram_Hw_RamConfigType_t Mcu_aRamConfigPB[3] =
{

    {
        /* The ID for Ram Sector configuration. */
        (Ram_Hw_RamSectionType)0U,

        /* RAM section base address: Start of Mcu_aRamConfig[0]. */

        (uint8 (*)[1U])0x1ffea000U,

        /* RAM section size: Section base address (must be aligned to 4 bytes). */

        (Ram_Hw_RamSizeType *)0x00000400U,

        /* RAM default value. */
        (uint64) ( ((uint64)0x0aU << 0U) | ((uint64)0x0aU << 8U) | ((uint64)0x0aU << 16U) | ((uint64)0x0aU << 24U) ),

        /* RAM section write size (maximum allowed: 8). */
        (Ram_Hw_RamWriteSizeType)4U

    }, /* End of Mcu_aRamConfigPB[0]. */

    {
        /* The ID for Ram Sector configuration. */
        (Ram_Hw_RamSectionType)1U,

        /* RAM section base address: Start of Mcu_aRamConfig[1]. */

        (uint8 (*)[1U])0x1ffec000U,

        /* RAM section size: Section base address (must be aligned to 4 bytes). */

        (Ram_Hw_RamSizeType *)0x00000100U,

        /* RAM default value. */
        (uint64) ( ((uint64)0x1bU << 0U) | ((uint64)0x1bU << 8U) | ((uint64)0x1bU << 16U) | ((uint64)0x1bU << 24U) ),

        /* RAM section write size (maximum allowed: 8). */
        (Ram_Hw_RamWriteSizeType)4U

    }, /* End of Mcu_aRamConfigPB[1]. */

    {
        /* The ID for Ram Sector configuration. */
        (Ram_Hw_RamSectionType)2U,

        /* RAM section base address: Start of Mcu_aRamConfig[2]. */

        (uint8 (*)[1U])0x2001a000U,

        /* RAM section size: Section base address (must be aligned to 4 bytes). */

        (Ram_Hw_RamSizeType *)0x00000400U,

        /* RAM default value. */
        (uint64) ( ((uint64)0x2cU << 0U) | ((uint64)0x2cU << 8U) | ((uint64)0x2cU << 16U) | ((uint64)0x2cU << 24U) ),

        /* RAM section write size (maximum allowed: 8). */
        (Ram_Hw_RamWriteSizeType)4U

    } /* End of Mcu_aRamConfigPB[2]. */

};
#endif /* ifndef MCU_MAX_NORAMCONFIGS */




/**
* @brief        Definition of RAM sections within the configuration structure.
* @details      Blocks of memory are initialized with a default data.
*               Configuration set by calling Mcu_InitRamSection() API.
*
*/
const Ram_Hw_RamModuleConfigType_t Mcu_aRamModuleConfigPB =
{
     0U,
     0U,
	 {
	     0U,
		 0U,
		 0U,
		 0U,
	 },
     
	 {
	     0U,
		 0U,
		 0U,
		 0U,
	 },
   

};




#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

