/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : LinIf_Cfg.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-17 15:29:41 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef LINIF_CFG_H
#define LINIF_CFG_H

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "Std_Types.h"

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/
#define LINIF_LIN_AUTOSAR_422                       0u
#define LINIF_LIN_AUTOSAR_440                       1u

/* The lin driver version */
#define LINIF_LIN_AUTOSAR_VERSION                   LINIF_LIN_AUTOSAR_440

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

/* Switch to enable/disable the APIs LinIf_CancelTransmit/LinTp_CancelReceive */
#define LINIF_CANCEL_TRANSMIT_SUPPORTED STD_OFF

/* Switches the Development Error Detection ON or OFF */
#define LINIF_DEV_ERROR_DETECT          STD_OFF

/* Switches the multiple drivers ON or OFF */
#define LINIF_MULTIPLE_DRIVER_SUPPORT   STD_OFF

/* Switches the multiple transceiver drivers ON or OFF */
#define LINIF_MULTIPLE_TRCV_DRIVER_SUPPORT STD_OFF

/* States if the node configuration commands Assign NAD and Conditional Change NAD are supported. */
#define LINIF_NC_OPTIONAL_REQUEST_SUPPORTED STD_OFF

/* Switches the TP ON or OFF */
#define LINIF_TP_SUPPORTED              STD_ON

/* States if transceiver driver support is included in the LIN Interface or not. */
#define LINIF_TRCV_DRIVER_SUPPORTED     STD_OFF

/* Switches the LinIf_GetVersionInfo function ON or OFF */
#define LINIF_VERSION_INFO_API          STD_OFF

/* Null-schedule index */
#define LINIF_NULL_SCHEDULE_INDEX       0u

#define LinIfScheduleTable_schedule_1  1

#define LINIF_NUMBER_OF_CHANNELS        1u

#define LINIF_MASTER_CHANNEL_NUMBER     1u
#define LINIF_SLAVE_CHANNEL_NUMBER      0u

#define LINIF_MASTER_FRAME_NUM          7u
#define LINIF_SLAVE_FRAME_NUM           0u

#define LINIF_NUMBER_OF_DRIVERS         1u

/* When processing the go-to-sleep command,After this time MainFunction shall call
   the function Lin_GetStatus to check the bus state */
#define LINIF_SLEEP_MODE_FRAME_DELAY    2000u

#define LINIF_LIN_CHANNEL_WAKEUP_SUPPORT        STD_OFF

#define LINIF_LIN_TRCV_WAKEUP_SUPPORT            STD_OFF

#define LINIF_WAKEUP_SUPPORT            STD_OFF

#define LINIF_SLAVE_SUPPORT      STD_OFF
#define LINIF_MASTER_SUPPORT     STD_ON

/* If set to true, enables the LinTp_ChangeParameterRequest Api for this Module. */
#define LINTP_CHANGE_PARAMETER_API        STD_OFF

/* Switches the LinTp_GetVersionInfo function ON or OFF */
#define LINTP_VERSION_INFO_API            STD_OFF

#define LINTP_PADDING_VALUE             0xff

#define LINTP_NUMBER_OF_CHANNELS             1u

#define LINTP_MASTER_CHANNEL_NUMBER           1u

#define LINTP_SLAVE_CHANNEL_NUMBER            0u

#define LINTP_MASTER_SUPPORT        STD_ON
#define LINTP_SLAVE_SUPPORT         STD_OFF

#define LINTP_SCHEDULE_CHANGE_DIAG_SUPPORT STD_OFF

/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define LINIF_TXPDU_DSM_LIN1      0
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#endif /* LINIF_CFG_H */

