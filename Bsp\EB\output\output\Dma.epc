<?xml version='1.0'?>
<AUTOSAR xmlns="http://autosar.org/schema/r4.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://autosar.org/schema/r4.0 AUTOSAR_00046.xsd">
  <AR-PACKAGES>
    <AR-PACKAGE>
      <SHORT-NAME>Dma</SHORT-NAME>
      <ELEMENTS>
        <ECUC-MODULE-CONFIGURATION-VALUES>
          <SHORT-NAME>Dma</SHORT-NAME>
          <DEFINITION-REF DEST="ECUC-MODULE-DEF">/LS_LSE14M01I0R0/Dma</DEFINITION-REF>
          <IMPLEMENTATION-CONFIG-VARIANT>VARIANT-POST-BUILD</IMPLEMENTATION-CONFIG-VARIANT>
          <POST-BUILD-VARIANT-USED>true</POST-BUILD-VARIANT-USED>
          <CONTAINERS>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>CommonPublishedInformation</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/ArReleaseMajorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/ArReleaseMinorVersion</DEFINITION-REF>
                  <VALUE>4</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/ArReleaseRevisionVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/ModuleId</DEFINITION-REF>
                  <VALUE>255</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/SwMajorVersion</DEFINITION-REF>
                  <VALUE>1</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/SwMinorVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/SwPatchVersion</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/CommonPublishedInformation/VendorId</DEFINITION-REF>
                  <VALUE>110</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>DmaConfig</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig</DEFINITION-REF>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>dmaLogicChannel_Type_0</SHORT-NAME>
                  <INDEX>0</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableGlobalConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableScatterGather</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableTransferConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwChId</DEFINITION-REF>
                      <VALUE>DMA_HW_CH_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwInstId</DEFINITION-REF>
                      <VALUE>DMA_HW_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_LogicName</DEFINITION-REF>
                      <VALUE>DMA_LOGIC_CH_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>DmaLogicChannelConfigType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DmaLogicChannelGlobalConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/TimeoutThreshold</DEFINITION-REF>
                              <VALUE>4095</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalInterruptType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/CompleteInterruptCallback</DEFINITION-REF>
                                  <VALUE>Dma_Adc0_CallBack</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCfgErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCompleteInterrupt</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaListRdErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaLrInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaTransErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalPriorityType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType/dmaGlobalPriority_LevelPriority</DEFINITION-REF>
                                  <VALUE>DMA_HW_LEVEL_PRIO0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalRequestType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/PeripheralRequestConfiguration</DEFINITION-REF>
                                  <VALUE>DMA_ADC0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxCh</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxChTrig</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_ScatterGatherConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_ScatterGatherConfigType</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_TransferConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType</DEFINITION-REF>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_TransferControlType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/ACountLength</DEFINITION-REF>
                                  <VALUE>40</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationAddress</DEFINITION-REF>
                                  <VALUE>&amp;Adc0_SoftOneShotResultBuff</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DmaUserHeaderFileWithExternDeclarations</DEFINITION-REF>
                                  <VALUE>mcal_stub.h</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/FlowCtrlTransType</DEFINITION-REF>
                                  <VALUE>DMA_FC_M2P_D</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/LinkMode</DEFINITION-REF>
                                  <VALUE>DMA_LINK_INVALID</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/NextChannelId</DEFINITION-REF>
                                  <VALUE>DMA_HW_CH_0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceAddress</DEFINITION-REF>
                                  <VALUE>0x4800012CU</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransBurstlength</DEFINITION-REF>
                                  <VALUE>DMA_BURST_SIZE0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDatawide</DEFINITION-REF>
                                  <VALUE>DMA_TRANS_32BIT</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDim</DEFINITION-REF>
                                  <VALUE>ONE_DIM</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM2ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/BCountLength</DEFINITION-REF>
                                      <VALUE>8</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/DestinationAddrOffset2D</DEFINITION-REF>
                                      <VALUE>2</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/SourceAddrOffset2D</DEFINITION-REF>
                                      <VALUE>4</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM3ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/CCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>dmaLogicChannel_Type_1</SHORT-NAME>
                  <INDEX>1</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableGlobalConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableScatterGather</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableTransferConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwChId</DEFINITION-REF>
                      <VALUE>DMA_HW_CH_1</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwInstId</DEFINITION-REF>
                      <VALUE>DMA_HW_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_LogicName</DEFINITION-REF>
                      <VALUE>DMA_LOGIC_CH_1</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>DmaLogicChannelConfigType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DmaLogicChannelGlobalConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/TimeoutThreshold</DEFINITION-REF>
                              <VALUE>4095</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalInterruptType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/CompleteInterruptCallback</DEFINITION-REF>
                                  <VALUE>Dma_Adc1_CallBack</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCfgErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCompleteInterrupt</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaListRdErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaLrInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaTransErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalPriorityType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType/dmaGlobalPriority_LevelPriority</DEFINITION-REF>
                                  <VALUE>DMA_HW_LEVEL_PRIO0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalRequestType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/PeripheralRequestConfiguration</DEFINITION-REF>
                                  <VALUE>DMA_ADC1</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxCh</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxChTrig</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_ScatterGatherConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_ScatterGatherConfigType</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_TransferConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType</DEFINITION-REF>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_TransferControlType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/ACountLength</DEFINITION-REF>
                                  <VALUE>36</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationAddress</DEFINITION-REF>
                                  <VALUE>&amp;Adc1_SoftOneShotResultBuff</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/FlowCtrlTransType</DEFINITION-REF>
                                  <VALUE>DMA_FC_M2P_D</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/LinkMode</DEFINITION-REF>
                                  <VALUE>DMA_LINK_INVALID</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/NextChannelId</DEFINITION-REF>
                                  <VALUE>DMA_HW_CH_0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceAddress</DEFINITION-REF>
                                  <VALUE>0x4800112CU</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransBurstlength</DEFINITION-REF>
                                  <VALUE>DMA_BURST_SIZE0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDatawide</DEFINITION-REF>
                                  <VALUE>DMA_TRANS_32BIT</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDim</DEFINITION-REF>
                                  <VALUE>ONE_DIM</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM2ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/BCountLength</DEFINITION-REF>
                                      <VALUE>8</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/DestinationAddrOffset2D</DEFINITION-REF>
                                      <VALUE>2</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/SourceAddrOffset2D</DEFINITION-REF>
                                      <VALUE>4</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM3ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/CCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>dmaLogicChannel_uart0_tx</SHORT-NAME>
                  <INDEX>2</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableGlobalConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableScatterGather</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableTransferConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwChId</DEFINITION-REF>
                      <VALUE>DMA_HW_CH_2</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwInstId</DEFINITION-REF>
                      <VALUE>DMA_HW_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_LogicName</DEFINITION-REF>
                      <VALUE>DMA_LOGIC_CH_2</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>DmaLogicChannelConfigType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DmaLogicChannelGlobalConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/TimeoutThreshold</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalInterruptType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/CompleteInterruptCallback</DEFINITION-REF>
                                  <VALUE>Lpuart_0_Uart_Hw_DmaTxCompleteCallback</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCfgErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCompleteInterrupt</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaListRdErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaLrInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaTransErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalPriorityType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType/dmaGlobalPriority_LevelPriority</DEFINITION-REF>
                                  <VALUE>DMA_HW_LEVEL_PRIO0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalRequestType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/PeripheralRequestConfiguration</DEFINITION-REF>
                                  <VALUE>DMA_UART0_TX</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxCh</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxChTrig</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_ScatterGatherConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_ScatterGatherConfigType</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_TransferConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType</DEFINITION-REF>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_TransferControlType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/ACountLength</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationAddress</DEFINITION-REF>
                                  <VALUE>0U</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/FlowCtrlTransType</DEFINITION-REF>
                                  <VALUE>DMA_FC_M2P_D</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/LinkMode</DEFINITION-REF>
                                  <VALUE>DMA_LINK_INVALID</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/NextChannelId</DEFINITION-REF>
                                  <VALUE>DMA_HW_CH_0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceAddress</DEFINITION-REF>
                                  <VALUE>0U</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransBurstlength</DEFINITION-REF>
                                  <VALUE>DMA_BURST_SIZE0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDatawide</DEFINITION-REF>
                                  <VALUE>DMA_TRANS_8BIT</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDim</DEFINITION-REF>
                                  <VALUE>ONE_DIM</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM2ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/BCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/DestinationAddrOffset2D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/SourceAddrOffset2D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM3ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/CCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>dmaLogicChannel_uart0_rx</SHORT-NAME>
                  <INDEX>3</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableGlobalConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableScatterGather</DEFINITION-REF>
                      <VALUE>0</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_EnableTransferConfig</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwChId</DEFINITION-REF>
                      <VALUE>DMA_HW_CH_3</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_HwInstId</DEFINITION-REF>
                      <VALUE>DMA_HW_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/dmaLogicChannel_LogicName</DEFINITION-REF>
                      <VALUE>DMA_LOGIC_CH_3</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                  <SUB-CONTAINERS>
                    <ECUC-CONTAINER-VALUE>
                      <SHORT-NAME>DmaLogicChannelConfigType</SHORT-NAME>
                      <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType</DEFINITION-REF>
                      <SUB-CONTAINERS>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>DmaLogicChannelGlobalConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType</DEFINITION-REF>
                          <PARAMETER-VALUES>
                            <ECUC-NUMERICAL-PARAM-VALUE>
                              <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/TimeoutThreshold</DEFINITION-REF>
                              <VALUE>1</VALUE>
                            </ECUC-NUMERICAL-PARAM-VALUE>
                          </PARAMETER-VALUES>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalInterruptType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-FUNCTION-NAME-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/CompleteInterruptCallback</DEFINITION-REF>
                                  <VALUE>Lpuart_0_Uart_Hw_DmaRxCompleteCallback</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCfgErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaCompleteInterrupt</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaListRdErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaLrInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalInterruptType/dmaGlobalInterrupt_enDmaTransErrorInterrupt</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalPriorityType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalPriorityType/dmaGlobalPriority_LevelPriority</DEFINITION-REF>
                                  <VALUE>DMA_HW_LEVEL_PRIO0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_GlobalRequestType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/PeripheralRequestConfiguration</DEFINITION-REF>
                                  <VALUE>DMA_UART0_RX</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxCh</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/DmaLogicChannelGlobalConfigType/dmaLogicChannelConfig_GlobalRequestType/dmaGlobalRequest_enDmaMuxChTrig</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_ScatterGatherConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_ScatterGatherConfigType</DEFINITION-REF>
                        </ECUC-CONTAINER-VALUE>
                        <ECUC-CONTAINER-VALUE>
                          <SHORT-NAME>dmaLogicChannel_TransferConfigType</SHORT-NAME>
                          <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType</DEFINITION-REF>
                          <SUB-CONTAINERS>
                            <ECUC-CONTAINER-VALUE>
                              <SHORT-NAME>dmaLogicChannelConfig_TransferControlType</SHORT-NAME>
                              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType</DEFINITION-REF>
                              <PARAMETER-VALUES>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/ACountLength</DEFINITION-REF>
                                  <VALUE>1</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationAddress</DEFINITION-REF>
                                  <VALUE>0U</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/DestinationIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/FlowCtrlTransType</DEFINITION-REF>
                                  <VALUE>DMA_FC_M2P_D</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/LinkMode</DEFINITION-REF>
                                  <VALUE>DMA_LINK_INVALID</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/NextChannelId</DEFINITION-REF>
                                  <VALUE>DMA_HW_CH_0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceAddress</DEFINITION-REF>
                                  <VALUE>0U</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-NUMERICAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/SourceIncrementEnalbe</DEFINITION-REF>
                                  <VALUE>0</VALUE>
                                </ECUC-NUMERICAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransBurstlength</DEFINITION-REF>
                                  <VALUE>DMA_BURST_SIZE0</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDatawide</DEFINITION-REF>
                                  <VALUE>DMA_TRANS_8BIT</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                                <ECUC-TEXTUAL-PARAM-VALUE>
                                  <DEFINITION-REF DEST="ECUC-ENUMERATION-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/TransDim</DEFINITION-REF>
                                  <VALUE>ONE_DIM</VALUE>
                                </ECUC-TEXTUAL-PARAM-VALUE>
                              </PARAMETER-VALUES>
                              <SUB-CONTAINERS>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM2ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/BCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/DestinationAddrOffset2D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM2ParaType/SourceAddrOffset2D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                                <ECUC-CONTAINER-VALUE>
                                  <SHORT-NAME>dmaLogicChannelConfig_DIM3ParaType</SHORT-NAME>
                                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType</DEFINITION-REF>
                                  <PARAMETER-VALUES>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/CCountLength</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/DestinationIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                    <ECUC-NUMERICAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceAddrOffset3D</DEFINITION-REF>
                                      <VALUE>0</VALUE>
                                    </ECUC-NUMERICAL-PARAM-VALUE>
                                    <ECUC-TEXTUAL-PARAM-VALUE>
                                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicChannel_Type/DmaLogicChannelConfigType/dmaLogicChannel_TransferConfigType/dmaLogicChannelConfig_TransferControlType/dmaLogicChannelConfig_DIM3ParaType/SourceIncrementMode</DEFINITION-REF>
                                      <VALUE>A_SYNC</VALUE>
                                    </ECUC-TEXTUAL-PARAM-VALUE>
                                  </PARAMETER-VALUES>
                                </ECUC-CONTAINER-VALUE>
                              </SUB-CONTAINERS>
                            </ECUC-CONTAINER-VALUE>
                          </SUB-CONTAINERS>
                        </ECUC-CONTAINER-VALUE>
                      </SUB-CONTAINERS>
                    </ECUC-CONTAINER-VALUE>
                  </SUB-CONTAINERS>
                </ECUC-CONTAINER-VALUE>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>dmaLogicInstance_ConfigType_0</SHORT-NAME>
                  <INDEX>0</INDEX>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicInstance_ConfigType</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-INTEGER-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicInstance_ConfigType/ChannelTimedOut</DEFINITION-REF>
                      <VALUE>1023</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicInstance_ConfigType/dmaLogicInstance_IdName</DEFINITION-REF>
                      <VALUE>DMA_LOGIC_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                    <ECUC-TEXTUAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-STRING-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaConfig/dmaLogicInstance_ConfigType/dmaLogicInstance_hwId</DEFINITION-REF>
                      <VALUE>DMA_HW_INST_0</VALUE>
                    </ECUC-TEXTUAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
            <ECUC-CONTAINER-VALUE>
              <SHORT-NAME>DmaGeneral</SHORT-NAME>
              <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral</DEFINITION-REF>
              <PARAMETER-VALUES>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral/DmaEnableDemReportErrorStatus</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral/DmaEnableDevErrorDetect</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
                <ECUC-NUMERICAL-PARAM-VALUE>
                  <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral/Dma_VersionInfoApi</DEFINITION-REF>
                  <VALUE>0</VALUE>
                </ECUC-NUMERICAL-PARAM-VALUE>
              </PARAMETER-VALUES>
              <SUB-CONTAINERS>
                <ECUC-CONTAINER-VALUE>
                  <SHORT-NAME>DmaDma</SHORT-NAME>
                  <DEFINITION-REF DEST="ECUC-PARAM-CONF-CONTAINER-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral/DmaDma</DEFINITION-REF>
                  <PARAMETER-VALUES>
                    <ECUC-NUMERICAL-PARAM-VALUE>
                      <DEFINITION-REF DEST="ECUC-BOOLEAN-PARAM-DEF">/LS_LSE14M01I0R0/Dma/DmaGeneral/DmaDma/DmaEnableDma</DEFINITION-REF>
                      <VALUE>1</VALUE>
                    </ECUC-NUMERICAL-PARAM-VALUE>
                  </PARAMETER-VALUES>
                </ECUC-CONTAINER-VALUE>
              </SUB-CONTAINERS>
            </ECUC-CONTAINER-VALUE>
          </CONTAINERS>
        </ECUC-MODULE-CONFIGURATION-VALUES>
      </ELEMENTS>
    </AR-PACKAGE>
  </AR-PACKAGES>
</AUTOSAR>
