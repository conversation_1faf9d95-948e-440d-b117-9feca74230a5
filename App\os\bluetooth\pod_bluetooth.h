#ifndef POD_BLUETOOTH_H
#define POD_BLUETOOTH_H
/// ---------- begin of guard: <includes> -----------------------------------
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
typedef enum {
   SEAT_BLUETOOTH_COMMAND_SEAT_FORWARD,       //座椅前
   SEAT_BLUETOOTH_COMMAND_SEAT_BACK,            //座椅后
   SEAT_BLUETOOTH_COMMAND_SEAT_LEFT,            //座椅左
   SEAT_BLUETOOTH_COMMAND_SEAT_RIGHT,           //座椅右
   SEAT_BLUETOOTH_COMMAND_SEAT_UP,              //座椅上
   SEAT_BLUETOOTH_COMMAND_SEAT_DOWN,            //座椅下

   SEAT_BLUETOOTH_COMMAND_BACK_UP,              //靠背前
   SEAT_BLUETOOTH_COMMAND_BACK_DOWN,            //靠背后

   SEAT_BLUETOOTH_COMMAND_LEG_REST_UP,          //腿托上
   SEAT_BLUETOOTH_COMMAND_LEG_REST_DOWN,        //腿托下
   SEAT_BLUETOOTH_COMMAND_LEG_REST_FORWARD,     //腿托伸出
   SEAT_BLUETOOTH_COMMAND_LEG_REST_BACK,        //腿托收回

   SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_UP,       //腰托上
   SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_DOWN,     //腰托下
   SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_FORWARD,  //腰托挤出
   SEAT_BLUETOOTH_COMMAND_LUMBAR_REST_BACK,     //腰托收回

   SEAT_BLUETOOTH_COMMAND_MASSAGE_SWITCH,       //按摩开关
   SEAT_BLUETOOTH_COMMAND_MASSAGE_MODE,         //按摩模式
   SEAT_BLUETOOTH_COMMAND_MASSAGE_INTENSITY,    //按摩强度

   SEAT_BLUETOOTH_COMMAND_BACK_FAN_SWITCH,             //风扇-靠背-开关
   SEAT_BLUETOOTH_COMMAND_BACK_FAN_INTENSITY,          //风扇-靠背-档位
   SEAT_BLUETOOTH_COMMAND_SEAT_FAN_SWITCH,             //风扇-座椅-开关
   SEAT_BLUETOOTH_COMMAND_SEAT_FAN_INTENSITY,          //风扇-靠背-档位

   SEAT_BLUETOOTH_COMMAND_BACK_HEAT_SWITCH,            //加热-靠背-开关
   SEAT_BLUETOOTH_COMMAND_BACK_HEAT_INTENSITY,         //加热-靠背-档位
   SEAT_BLUETOOTH_COMMAND_SEAT_HEAT_SWITCH,            //加热-座椅-开关
   SEAT_BLUETOOTH_COMMAND_SEAT_HEAT_INTENSITY,         //加热-座椅-档位

   SEAT_BLUETOOTH_COMMAND_ZERO_GRAVITY,         //一键零重力
   SEAT_BLUETOOTH_COMMAND_ALL_RESET,            //一键全部复位

   SEAT_BLUETOOTH_COMMAND_CALIBRATION,          //座椅标定

   SEAT_BLUETOOTH_COMMAND_MAX
}Bluetooth_event_t;
/// ---------- end of guard:   <header> -------------------------------------

void pod_bluetooth_on_init(tHsmGenCurr* const ev);
void pod_bluetooth_on_tm10ms(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_BLUETOOTH_H