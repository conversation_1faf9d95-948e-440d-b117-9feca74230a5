/**
 * file    Fls_Hw_Cfg.h
 * brief   Flash driver for LES14XX
 * author  huhl
 * date    2025.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*
* @section [global]
* Violates MISRA 2012 Advisory Directive 4.9, Function-like macro
* Reason: Function like macro are used to reduce code complexity.

* @section [global]
* Violates MISRA C 2012 Advisory Rule 2.5, The macro is declared but not used.
* Reason: As reserve
*/
#ifndef FLS_HW_CFG_H
#define FLS_HW_CFG_H



/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Fls_Hw_Types.h"



#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define FLS_HW_VENDOR_ID_CFG                          (110u)
#define FLS_HW_AR_RELEASE_MAJOR_VERSION_CFG           (4u)
#define FLS_HW_AR_RELEASE_MINOR_VERSION_CFG           (4u)
#define FLS_HW_AR_RELEASE_REVISION_VERSION_CFG        (0u)
#define FLS_HW_SW_MAJOR_VERSION_CFG                   (1u)
#define FLS_HW_SW_MINOR_VERSION_CFG                   (0u)
#define FLS_HW_SW_PATCH_VERSION_CFG                   (0u)


/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if current file and Fls_Hw_Types header file are of the same vendor */
#if (FLS_HW_TYPES_VENDOR_ID != FLS_HW_VENDOR_ID_CFG)
    #error "Fls_Hw_Cfg.h and Fls_Hw_Types.h have different vendor ids"
#endif
/* Check if current file and Fls_Hw_Types header file are of the same Autosar version */
#if ((FLS_HW_TYPES_AR_RELEASE_MAJOR_VERSION    != FLS_HW_AR_RELEASE_MAJOR_VERSION_CFG) || \
     (FLS_HW_TYPES_AR_RELEASE_MINOR_VERSION    != FLS_HW_AR_RELEASE_MINOR_VERSION_CFG) || \
     (FLS_HW_TYPES_AR_RELEASE_REVISION_VERSION != FLS_HW_AR_RELEASE_REVISION_VERSION_CFG) \
    )
    #error "AutoSar Version Numbers of Fls_Hw_Cfg.h and Fls_Hw_Types.h are different"
#endif
/* Check if current file and Fls_Hw_Types header file are of the same Software version */
#if ((FLS_HW_TYPES_SW_MAJOR_VERSION != FLS_HW_SW_MAJOR_VERSION_CFG) || \
     (FLS_HW_TYPES_SW_MINOR_VERSION != FLS_HW_SW_MINOR_VERSION_CFG) || \
     (FLS_HW_TYPES_SW_PATCH_VERSION != FLS_HW_SW_PATCH_VERSION_CFG) \
    )
    #error "Software Version Numbers of Fls_Hw_Cfg.h and Fls_Hw_Types.h are different"
#endif

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
                                              
/* Pre-processor switch to enable / disable the erase blank check */
#define FLS_ERASE_VERIFICATION_ENABLED       (STD_ON)

/* Pre-processor switch to enable / disable the write verify check */
#define FLS_PROGRAM_VERIFICATION_ENABLED       (STD_ON)

/* Timeout handling enabled */
#define FLS_TIMEOUT_SUPERVISION_ENABLED     (STD_ON)


#define FLS_ERASED_VALUE                    (0xFFFFFFFFU)

#define FLS_HW_ECC_CHECK                    STD_OFF

/*this lowest value must be configure as 2u otherwise this function will reture E_NOT_OK*/
#define FLASH_HW_REQ_LOCK_RETRY            2U


#define FLASH_HW_UPDATE_TIMEOUT             (uint16)0xFFFFu

/**
 * Program allignment for program
 */
#define FLS_HW_WRITE_MIN_UINT_SIZE                 (0x10U)



#define FLS_TIMEOUT_TYPE                       (OSIF_COUNTER_DUMMY)

#if (STD_ON == FLS_TIMEOUT_SUPERVISION_ENABLED)
#define FLS_HW_ASYNC_WRITE_TIMEOUT             (2147483647U)

#define FLS_HW_ASYNC_ERASE_TIMEOUT             (2147483647U)

#define FLS_HW_SYNC_WRITE_TIMEOUT              (2147483647U)

#define FLS_HW_SYNC_ERASE_TIMEOUT              (2147483647U)

#define FLS_HW_ABORT_TIMEOUT                   (32767U)

#endif  /*(STD_ON == FLS_TIMEOUT_SUPERVISION_ENABLED)*/

/* Flash memory characteristics */
#define FLS_HW_P_FLASH_BASE_ADDR               (0x00000000UL)
#define FLS_HW_P_FLASH_SIZE                    (0x200000UL)
#define FLS_HW_P_FLASH_SECTOR_SIZE             (0x2000UL)

#define FLS_HW_D_FLASH_BASE_ADDR               (0x04000000UL)
#define FLS_HW_D_FLASH_SIZE                    (0x36000UL)
#define FLS_HW_D_FLASH_SECTOR_SIZE             (0x2000UL)

/*Dflash mac start address */
#define FLS_HW_D_FLASH_MACPARTI_ADDR           (0x02010000UL)
/*Dflash mac area size */
#define FLS_HW_D_FLASH_MACPARTI_SIZE           (8192UL)
/*Dflash hardware configuration zone start address */
#define FLS_HW_D_FLASH_HWCFGPARTI_ADDRESS      (0x03000000UL)
/* Size of the Dflash user hardware configuration partition */
#define FLS_HW_D_FLASH_HWCFGPARTI_SIZE         (560UL)
/*Dflash Start address of the security key partition */
#define FLS_HW_D_FLASH_SECPARTI_ADDRESS        (0x03002010UL)
/*Dflash security key area partition */
#define FLS_HW_D_FLASH_SECPARTI_SIZE           (128UL)

/* Valid P_FLASH address */
#define FLS_HW_ADDRESS_VALID_P_FLASH(addr)     ( (addr) < (FLS_HW_P_FLASH_BASE_ADDR + FLS_HW_P_FLASH_SIZE) )

/* Valid D_FLASH address */
#define FLS_HW_ADDRESS_VALID_D_FLASH(addr)     ( (FLS_HW_D_FLASH_BASE_ADDR <= (addr)) && ((addr) < (FLS_HW_D_FLASH_BASE_ADDR + FLS_HW_D_FLASH_SIZE)) )

/* Valid P_FLASH or D_FLASH address */
#define FLS_HW_ADDRESS_VALID(addr)             ( FLS_HW_ADDRESS_VALID_D_FLASH(addr) || FLS_HW_ADDRESS_VALID_P_FLASH(addr) )

/* Valid P_FLASH AB partition address */
#define FLS_HW_ADDRESS_VALID_P_FLASH_AB(addr)     ( (addr) < ((FLS_HW_P_FLASH_BASE_ADDR + FLS_HW_P_FLASH_SIZE) / 2UL))

/* Valid MAC partition address */
#define FLS_HW_ADDRESS_VALID_MACPARTI(addr)    ((FLS_HW_D_FLASH_MACPARTI_ADDR <= (addr)) && ((addr) < (FLS_HW_D_FLASH_MACPARTI_ADDR + FLS_HW_D_FLASH_MACPARTI_SIZE)))

/* Valid HW configuration partition address */
#define FLS_HW_ADDRESS_VALID_HWCFGPARTI(addr)  ((FLS_HW_D_FLASH_HWCFGPARTI_ADDRESS <= (addr)) && ((addr) < (FLS_HW_D_FLASH_HWCFGPARTI_ADDRESS + FLS_HW_D_FLASH_HWCFGPARTI_SIZE)))

/* Valid security key partition address */
#define FLS_HW_ADDRESS_VALID_SECPARTI(addr)    ((FLS_HW_D_FLASH_SECPARTI_ADDRESS <= (addr)) && ((addr) < (FLS_HW_D_FLASH_SECPARTI_ADDRESS + FLS_HW_D_FLASH_SECPARTI_SIZE)))

#define FLS_HW_ADDRESS_VALID_PFLASH(Addr, FlsEndAddr)       ((Addr) < (FLS_HW_P_FLASH_BASE_ADDR + FlsEndAddr))


    
/* Check if the address is sector alignment or not */
#define FLS_HW_SECTOR_ALIGNED(addr)            ( ((addr) & (FLS_HW_P_FLASH_SECTOR_SIZE - 1UL)) == 0UL )


/* Code block size (flash read partition size) */
#define FLS_P_BLOCK_SIZE                     (0x20000U)


/*==================================================================================================
                                 GLOBAL CONSTANT DECLARATIONS
==================================================================================================*/
#define FLS_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"

extern const FlsHwConfig_t FlsConfigSet_InitCfg;

extern const FlsHWCfgPartition_t Fls_HwPartitionConfig;
extern const FlsMacPartitionCfg_t Fls_MacPartitionConfig;
extern const FlsSecPartitionCfg_t Fls_SecKeyPartitionConfig;

#define FLS_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Fls_MemMap.h"


#ifdef __cplusplus
}
#endif

/***/

#endif /* FTFC_FLS_IP_CFG_H */
