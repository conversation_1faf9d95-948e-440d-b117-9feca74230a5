#ifndef UART_IPW_CFG_H
#define UART_IPW_CFG_H

/*==================================================================================================
                                         INCLUDE FILES
==================================================================================================*/

#include "Uart_Ipw_PBcfg.h"
#include "Uart_Ipw_Types.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define UART_IPW_CFG_VENDOR_ID                    (110u)
#define UART_IPW_CFG_AR_RELEASE_MAJOR_VERSION     (4u)
#define UART_IPW_CFG_AR_RELEASE_MINOR_VERSION     (4u)
#define UART_IPW_CFG_AR_RELEASE_REVISION_VERSION  (0u)
#define UART_IPW_CFG_SW_MAJOR_VERSION             (1u)
#define UART_IPW_CFG_SW_MINOR_VERSION             (0u)
#define UART_IPW_CFG_SW_PATCH_VERSION             (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Uart_Ipw_PBcfg.h */
#if (UART_IPW_CFG_VENDOR_ID != UART_IPW_PBCFG_VENDOR_ID)
    #error "Uart_Ipw_Cfg.h and Uart_Ipw_PBcfg.h have different vendor ids"
#endif
#if ((UART_IPW_CFG_AR_RELEASE_MAJOR_VERSION    != UART_IPW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (UART_IPW_CFG_AR_RELEASE_MINOR_VERSION    != UART_IPW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (UART_IPW_CFG_AR_RELEASE_REVISION_VERSION != UART_IPW_PBCFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Uart_Ipw_Cfg.h and Uart_Ipw_PBcfg.h are different"
#endif
#if ((UART_IPW_CFG_SW_MAJOR_VERSION != UART_IPW_PBCFG_SW_MAJOR_VERSION) || \
     (UART_IPW_CFG_SW_MINOR_VERSION != UART_IPW_PBCFG_SW_MINOR_VERSION) || \
     (UART_IPW_CFG_SW_PATCH_VERSION != UART_IPW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Uart_Ipw_Cfg.h and Uart_Ipw_PBcfg.h are different"
#endif

/*Checks against Uart_Ipw_Types.h */
#if (UART_IPW_CFG_VENDOR_ID!= UART_IPW_TYPES_VENDOR_ID)
    #error "Uart_Ipw_Cfg.h and Uart_Ipw_Types.h have different vendor ids"
#endif
#if ((UART_IPW_CFG_AR_RELEASE_MAJOR_VERSION   != UART_IPW_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (UART_IPW_CFG_AR_RELEASE_MINOR_VERSION   != UART_IPW_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (UART_IPW_CFG_AR_RELEASE_REVISION_VERSION!= UART_IPW_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Uart_Ipw_Cfg.h and Uart_Ipw_Types.h are different"
#endif
#if ((UART_IPW_CFG_SW_MAJOR_VERSION!= UART_IPW_TYPES_SW_MAJOR_VERSION) || \
     (UART_IPW_CFG_SW_MINOR_VERSION!= UART_IPW_TYPES_SW_MINOR_VERSION) || \
     (UART_IPW_CFG_SW_PATCH_VERSION!= UART_IPW_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_Ipw_Cfg.h and Uart_Ipw_Types.h are different"
#endif
/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

#define UART_IPW_CONFIG_EXT \
    UART_IPW_CONFIG_PB
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


/**
* @brief    Declare callback functions if any
*/

#define UART_START_SEC_CODE
#include "Uart_MemMap.h"

/* Define User Receive callback function */
extern void BluetoothUartCallback(uint8 HwInstance, Uart_Event_t Event);

#define UART_STOP_SEC_CODE
#include "Uart_MemMap.h"

            

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* UART_IPW_CFG_H */
