/**
 * file    Adc_Hw_PBcfg.c
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/**
*   @file
*
*   @addtogroup adc_hw_config Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES

==================================================================================================*/
#include "Std_Types.h"
#include "Adc_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ADC_HW_VENDOR_ID_PBCFG_C                     (110u)
#define ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C      (4u)
#define ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C      (4u)
#define ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C   (0u)
#define ADC_HW_SW_MAJOR_VERSION_PBCFG_C              (1u)
#define ADC_HW_SW_MINOR_VERSION_PBCFG_C              (0u)
#define ADC_HW_SW_PATCH_VERSION_PBCFG_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Adc_Hw_PBcfg.c file and Std_Types.h file are of the same Autosar version */
#if ((ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != STD_AR_RELEASE_MAJOR_VERSION) || \
     (ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != STD_AR_RELEASE_MINOR_VERSION)    \
    )
    #error "AutoSar Version Numbers of Adc_Hw_PBcfg.c and Std_Types.h are different"
#endif
#endif

/* Check if Adc_Hw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same vendor */
#if (ADC_HW_VENDOR_ID_PBCFG_C != ADC_HW_VENDOR_ID_PBCFG)
    #error "Adc_Hw_PBcfg.c and Adc_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Adc_Hw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same Autosar version */
#if ((ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Adc_Hw_PBcfg.c and Adc_Hw_PBcfg.h are different"
#endif

/* Check if Adc_Hw_PBcfg.c file and Adc_Hw_PBcfg.h file are of the same Software version */
#if ((ADC_HW_SW_MAJOR_VERSION_PBCFG_C != ADC_HW_SW_MAJOR_VERSION_PBCFG) || \
     (ADC_HW_SW_MINOR_VERSION_PBCFG_C != ADC_HW_SW_MINOR_VERSION_PBCFG) || \
     (ADC_HW_SW_PATCH_VERSION_PBCFG_C != ADC_HW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Adc_Hw_PBcfg.c and Adc_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/**
* @brief          ADC Hw List of Channels Configuration for Logical ID 0 corresponding to the ADC0 configuration variant .
*/
static const Adc_Hw_ChanConfig_t AdcHwChansConfig_0[10U] =
{
    {
        0U, /* ChnIdx */
        ADC_INPUTCHAN_EXT9, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        1U, /* ChnIdx */
        ADC_INPUTCHAN_EXT8, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        2U, /* ChnIdx */
        ADC_INPUTCHAN_EXT15, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        3U, /* ChnIdx */
        ADC_INPUTCHAN_EXT14, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        4U, /* ChnIdx */
        ADC_INPUTCHAN_EXT13, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        5U, /* ChnIdx */
        ADC_INPUTCHAN_EXT12, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        6U, /* ChnIdx */
        ADC_INPUTCHAN_EXT7, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        7U, /* ChnIdx */
        ADC_INPUTCHAN_EXT3, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        8U, /* ChnIdx */
        ADC_INPUTCHAN_EXT2, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        9U, /* ChnIdx */
        ADC_INPUTCHAN_EXT6, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    }
};

/**
* @brief          ADC Hw List of Channels Configuration for Logical ID 1 corresponding to the ADC1 configuration variant .
*/
static const Adc_Hw_ChanConfig_t AdcHwChansConfig_1[9U] =
{
    {
        0U, /* ChnIdx */
        ADC_INPUTCHAN_EXT13, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        1U, /* ChnIdx */
        ADC_INPUTCHAN_EXT12, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        2U, /* ChnIdx */
        ADC_INPUTCHAN_EXT11, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        3U, /* ChnIdx */
        ADC_INPUTCHAN_EXT10, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        4U, /* ChnIdx */
        ADC_INPUTCHAN_EXT15, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        5U, /* ChnIdx */
        ADC_INPUTCHAN_EXT14, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        6U, /* ChnIdx */
        ADC_INPUTCHAN_EXT9, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        7U, /* ChnIdx */
        ADC_INPUTCHAN_EXT8, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    },
    {
        8U, /* ChnIdx */
        ADC_INPUTCHAN_EXT4, /* Channel */
    #if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
        (boolean)FALSE /* InterruptEnable */
    #endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    }
};


/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/


/**
* @brief          ADC Hw Config for Logical ID 0 corresponding to the ADC0 configuration variant .
*/
const Adc_Hw_Config_t AdcHwConfig_0 =
{
    100U, /* SampleTime */
    8U, /* CalHoldtime */
    150U, /* Holdtime */
    7U, /* Calibration Switch Hold Time Duration */
	(boolean)TRUE, /* Adc output enable */
    (boolean)TRUE, /* Adc power enable */
    ADC_TRIGGER_SEL_PDB, /* Adc Hw Unit Trigger Source */
    ADC_PRETRIGGER_SEL_PDB, /* Adc Hw Unit Pretrigger Source */
    ADC_SW_PRETRIGGER_DISABLED, /* Adc Hw Unit Software Trigger Source */
    (boolean)FALSE, /* Adc power monitor enable */
    ADC_SUPPLY_VCC_5V, /* Adc Hw Unit Power Monitor Source */
    (boolean)FALSE, /* AvgEn */
    ADC_HW_AVG_32_CONV, /* AvgSel */
    (boolean)FALSE, /* Adc Hardware MovingAverage Enable */
    ADC_HW_MovAVG_4_CONV, /* Adc HwUnit Conversions Num */
    ADC_HW_RESOLUTION_12BIT, /* Resolution */
    ADC_HW_TRIGGER_HARDWARE, /* TriggerMode */
    (boolean)FALSE, /* DmaEnable */
    ADC_HW_VOLTAGEREF_VREFH,  /* VoltageRef */
    (boolean)FALSE, /* ContinuousConvEnable */
    (boolean)FALSE, /* CompareEnable */
    (boolean)FALSE, /* CompareGreaterThanEnable */
    (boolean)FALSE, /* CompareRangeFuncEnable */
    0U, /* CompVal1 */
    255U, /* CompVal2 */
    0U, /* UsrGain */
    0U, /* UsrOffset */
    0U, /* NumChannels */
    AdcHwChansConfig_0, /* ChannelConfigs */
#if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
    NULL_PTR, /* ConversionCompleteNotification */
#endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    ADC_INVTERLEAVE_DISABLE	/*InterleavedMode*/
};

/**
* @brief          ADC Hw Config for Logical ID 1 corresponding to the ADC1 configuration variant .
*/
const Adc_Hw_Config_t AdcHwConfig_1 =
{
    100U, /* SampleTime */
    8U, /* CalHoldtime */
    150U, /* Holdtime */
    7U, /* Calibration Switch Hold Time Duration */
	(boolean)TRUE, /* Adc output enable */
    (boolean)TRUE, /* Adc power enable */
    ADC_TRIGGER_SEL_PDB, /* Adc Hw Unit Trigger Source */
    ADC_PRETRIGGER_SEL_PDB, /* Adc Hw Unit Pretrigger Source */
    ADC_SW_PRETRIGGER_DISABLED, /* Adc Hw Unit Software Trigger Source */
    (boolean)FALSE, /* Adc power monitor enable */
    ADC_SUPPLY_VCC_5V, /* Adc Hw Unit Power Monitor Source */
    (boolean)FALSE, /* AvgEn */
    ADC_HW_AVG_32_CONV, /* AvgSel */
    (boolean)FALSE, /* Adc Hardware MovingAverage Enable */
    ADC_HW_MovAVG_4_CONV, /* Adc HwUnit Conversions Num */
    ADC_HW_RESOLUTION_12BIT, /* Resolution */
    ADC_HW_TRIGGER_HARDWARE, /* TriggerMode */
    (boolean)FALSE, /* DmaEnable */
    ADC_HW_VOLTAGEREF_VREFH,  /* VoltageRef */
    (boolean)FALSE, /* ContinuousConvEnable */
    (boolean)FALSE, /* CompareEnable */
    (boolean)FALSE, /* CompareGreaterThanEnable */
    (boolean)FALSE, /* CompareRangeFuncEnable */
    0U, /* CompVal1 */
    255U, /* CompVal2 */
    0U, /* UsrGain */
    0U, /* UsrOffset */
    0U, /* NumChannels */
    AdcHwChansConfig_1, /* ChannelConfigs */
#if (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON)
    NULL_PTR, /* ConversionCompleteNotification */
#endif /* (ADC_HW_AIEN_INTERRUPT_ENABLE == STD_ON) .*/
    ADC_INVTERLEAVE_DISABLE	/*InterleavedMode*/
};


#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

