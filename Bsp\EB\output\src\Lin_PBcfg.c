/**
 * @file     Lin_PBcfg.c
 * @brief    Lin_PBcfg Software c files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 * 
 * section [global]
 * Violates MISRA C 2012 Advisory Rule 5.4, The macro definition name cannot be changed
 * reason: Macro definitions have similar names, version number definitions have the same prefix, but different keywords.
 * 
 * section [global]
 * Violates MISRA C 2012 Dir-1.1-C99 [L] Number of macro definitions exceeds 4095 - program does not conform strictly to ISO C99.
 * Reason: include this file is needed.
 * 
 **/


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/

#include "Std_Types.h"
#include "Lin_Cfg.h"
#include "Lin_Ipw_Cfg.h"
#include "Lin_Hw_Cfg.h"

#if (STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS)
#include "Dem.h"
#endif /* STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS */

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define LIN_PBCFG_VENDOR_ID_C                     (110u)
#define LIN_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4u)
#define LIN_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4u)
#define LIN_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0u)
#define LIN_PBCFG_SW_MAJOR_VERSION_C              (1u)
#define LIN_PBCFG_SW_MINOR_VERSION_C              (0u)
#define LIN_PBCFG_SW_PATCH_VERSION_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Checks against Lin_Cfg.h */
#if (LIN_PBCFG_VENDOR_ID_C != LIN_CFG_VENDOR_ID)
    #error "Lin_PBcfg.c and Lin_Cfg.h have different vendor ids"
#endif
#if ((LIN_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_PBcfg.c and Lin_Cfg.h are different"
#endif
#if ((LIN_PBCFG_SW_MAJOR_VERSION_C != LIN_CFG_SW_MAJOR_VERSION) || \
     (LIN_PBCFG_SW_MINOR_VERSION_C != LIN_CFG_SW_MINOR_VERSION) || \
     (LIN_PBCFG_SW_PATCH_VERSION_C != LIN_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lin_PBcfg.c and Lin_Cfg.h are different"
#endif

/* Checks against Lin_Ipw_Cfg.h */
#if (LIN_PBCFG_VENDOR_ID_C != LIN_IPW_CFG_VENDOR_ID)
    #error "Lin_PBcfg.c and Lin_Ipw_Cfg.h have different vendor ids"
#endif
#if ((LIN_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != LIN_IPW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_MINOR_VERSION_C    != LIN_IPW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (LIN_PBCFG_AR_RELEASE_REVISION_VERSION_C != LIN_IPW_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Lin_PBcfg.c and Lin_Ipw_Cfg.h are different"
#endif
#if ((LIN_PBCFG_SW_MAJOR_VERSION_C != LIN_IPW_CFG_SW_MAJOR_VERSION) || \
     (LIN_PBCFG_SW_MINOR_VERSION_C != LIN_IPW_CFG_SW_MINOR_VERSION) || \
     (LIN_PBCFG_SW_PATCH_VERSION_C != LIN_IPW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Lin_PBcfg.c and Lin_Ipw_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if header file and Std_Types header file are of the same Autosar version */
    #if ((LIN_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
        (LIN_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Lin_Hw_Types.h and Std_Types.h are different"
    #endif
    #if (STD_OFF == LIN_DISABLE_DEM_REPORT_ERROR_STATUS)
        /* Checks against Dem.h */
        #if ((LIN_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DEM_AR_RELEASE_MAJOR_VERSION) || \
             (LIN_PBCFG_AR_RELEASE_MINOR_VERSION_C != DEM_AR_RELEASE_MINOR_VERSION))
            #error "AUTOSAR Version Numbers of Lin_PBcfg.c and Dem.h are different"
        #endif
    #endif
#endif

/*==================================================================================================
*                                 GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/**
* @brief   Export IPW configurations.
*/
LIN_HW_CONFIG_EXT

#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/
/**
* @brief        All CoreIDs are supported by LIN driver.
*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/
#define LIN_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"




/**
* @brief          Configuration for Lin Channel 0 - Configuration:
*                 LinGlobalConfig.
*
* 
*/
const LinChannelConfig_t Lin_LinChannel_0_UnAllocatedPar =
{
    LIN_CHANNEL_ID_0, /* Lin Channel ID */
 
    &Lin_Ipw_pHwConfigPB_0, /* Pointer to the hardware configuration*/

    LIN_UNALLOCATEDPAR_CORE_ID,         /* Partition core id of channel */

    (boolean)TRUE
};

/**
* @brief          Configuration for Lin Channel 1 - Configuration:
*                 LinGlobalConfig.
*
* 
*/
const LinChannelConfig_t Lin_LinChannel_1_UnAllocatedPar =
{
    LIN_CHANNEL_ID_1, /* Lin Channel ID */
 
    &Lin_Ipw_pHwConfigPB_1, /* Pointer to the hardware configuration*/

    LIN_UNALLOCATEDPAR_CORE_ID,         /* Partition core id of channel */

    (boolean)TRUE
};



/**
* @brief          Lin Configuration data for LinGlobalConfig.
*
*/
const Lin_ConfigType Lin_Config =
{
    LIN_UNALLOCATEDPAR_CORE_ID,            /** Core Id */

    {
        /**
        * @brief   Configuration for Lin Channel 0.
        */
        &Lin_LinChannel_0_UnAllocatedPar,
        /**
        * @brief   Configuration for Lin Channel 1.
        */
        &Lin_LinChannel_1_UnAllocatedPar
    }
};


#define LIN_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Lin_MemMap.h"

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */
