#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "types.h"
#include "pid.h"
#include "Pwm_Types.h"
#include "Icu_Types.h"
#include "motor_stall.h"

#define OVER_RANGE 0xFFFF
#define POS_PROXIMITY 3

/// @brief 电机类型
typedef enum {
    MOTOR_1_BACK_REST, // 靠背电机
#ifdef SEAT_SIDE_RIGHT
    MOTOR_2_SEAT_LIFT, // 座椅上下电机
    MOTOR_3_SEAT_SLIDE, // 座椅侧滑电机
#endif
    MOTOR_4_FRONT_BACK, // 前后电机
    MOTOR_5_LEG_LIFT, // 腿托电机（上下）
    MOTOR_6_LEG_STRETCH, // 腿托电机（伸缩）
    MOTOR_MAX,
} tMotorType;

typedef enum {
    CALIB_IDLE,
    CALIB_CLEAR,
    CALIB_MIN,
    CALIB_EXPAND,
    CALIB_MAX,
    CALIB_SHRINK,
} tMotorCalibState;

typedef enum {
    CLEAR_IDLE,
    CLEAR_SHRINK,
    CLEAR_MIN,
} tMotorClearState;

typedef struct {
    // 最大行程
    float range;
    // 零重力位置
    // float zero_p;
    tMotorCalibState state;
} tMotorCalib;

/// @brief 电机PID标定值
typedef struct {
    tFloat kp;
    tFloat ki;
    tFloat kd;
} tConfigPid;

/// @brief 电机所在座椅
// TODO: 左右侧要区分吗？
typedef enum {
    FRONT_DRIVER, // 前排主驾
    FRONT_PASSENGER, // 前排副驾
} tMotorSeat;

/// @brief 电机控制选项
typedef enum {
    OPTION_OFF, // 关闭：不动
    OPTION_POS, // 正转：如向上、向前
    OPTION_NEG, // 反转：如向下、向后
} tMotorOption;

/// @brief 电机配置，每个电机的配置需要单独标定
typedef struct MotorConfig {
    //pwm phyical channel
    Pwm_ChannelType channel_forward;
    Pwm_ChannelType channel_backward;
    uint8 gpmc_id;

    //hall phyical channel
    Icu_ChannelType channel_hall;

    // 最大行程
    u32 range;

    // speed
    tFloat rated_speed;

    // stall voltage threshold
    tU32 stall_voltage;

    // pid
    tConfigPid speed_pid;
    tConfigPid position_pid;

    bool openloop;
    
} tMotorConfig;

/// @brief 电机运转方向
typedef enum {
    DIR_FORWARD,
    DIR_BACKWARD,
} tMotorDirection;

typedef enum {
    TARGET_DIR_NONE,
    TARGET_DIR_FORWARD,
    TARGET_DIR_BACKWARD,
} tMotorTargetDirection;

typedef enum {
    NO_STALL, // 未堵转
    FORWARD_STALL, // 正向堵转
    BACKWARD_STALL, // 反向堵转
} tStallFlag;

/// @brief 电机驱动
typedef struct MotorDriver {
    tU32 pwm;
    tMotorTargetDirection target_dir;
    tMotorDirection dir;
    tU32 current_feedback;
    tStallFlag stall_flag;
    tU32 ntc;
    tS8 temp;
    tU8 fault;
} tMotorDriver;

/// @brief 编码器状态
typedef struct MotorEncoder {
    tU32 position;
    tU32 position_last;
    tU32 period_time;
    float speed;
} tMotorEncoder;

/// @brief 电机状态历史
typedef struct {
    tU32 position;
} tMotorHistory;

typedef struct {
    i32 position;
} tMotorMem;

/// @brief 电机
typedef struct Motor {
    tMotorType type;

    // Config
    tMotorConfig config;

    // Driver
    tMotorDriver driver;

    // Encoder
    tMotorEncoder encoder;

    // PID
    tPID speed_pid;
    tPID position_pid;

    // Monitor
    tStallMonitor stall_monitor;

    // Mem
    // tMotorMem mem;

    // Calib
    tMotorCalib calib;

    // Clear
    tMotorClearState clear;

    // History
    tMotorHistory history;
} tMotor;

/// @brief 一组电机（例如一个座椅上的所有电机）
typedef struct Motors {
    tMotor back_rest;// 座椅靠背电机
    tMotor seat_lift; // 座椅上下电机
    tMotor seat_slide; // 座椅侧滑电机
    tMotor front_back; // 座椅前后电机
    tMotor leg_lift; // 腿托上下电机
    tMotor leg_stretch; // 腿托上下电机
} tMotors;

/// @brief 初始化单个电机
/// @param  TODO: 添加参数
tMotor motor_new(tMotorType type);

/// @brief 电机控制API，给按钮用
/// @param TODO: 添加参数
void motor_go(tMotor* motor, tMotorOption option);

/// @brief 电机移动到指定位置
bool motor_move_to(tMotor* motor, i32 pos);
bool motor_force(tMotor* motor, tMotorOption option);

void motor_stop(tMotor* motor);
void motor_brake(tMotor* motor);

/// @brief 初始化所有电机
void motors_init(void);

Pwm_ChannelType driver_get_channel(tMotorDriver* driver);

/// @brief 根据座椅和电机类型查找电机
tMotor* motor_find(tMotorType type);

void motor_record(tMotor* motor);
void motor_record_all(void);

/// @brief 收起座椅
void motors_shrink_fast(void);
void motors_shrink_force(void);
void motors_expand(void);
void motors_clear(void);

/// @brief 一键零重力
void motors_zero_gravity(void);
/// @brief 从一键零重力恢复原始值
void motors_zero_recover(void);

void monitor_stall_detect(tMotor* motor, tBool stall);

/// @brief 电机标定
void motors_calib_all(void);
void motors_calib_batch(void);
void motor_calib(tMotor* motor);
void motor_calib_reset(tMotor* motor);
void motor_calib_range(tMotor* motor);

void motors_calib_s1_back(void);
void motors_calib_s2_lift(void);
void motors_calib_s3_front(void);
void motors_calib_s4_legs(void);
void motors_calib_s5_slide(void);

void motor_do_clear(tMotor* motor);


// bool motor_is_active(tMotor* motor);


extern tMotor motors[MOTOR_MAX];

void motor_stall_callback(tMotor* motor);
void motor_stall_clear(tMotor* motor);

void motor_set_stall(tMotor* motor);

void motor_stall_detect(tMotor* motor, bool stall, bool stuck);

bool motor_is_idle(tMotor* motor);
bool motors_all_idle(void);
bool motor_is_stopped(tMotor* motor);

#endif