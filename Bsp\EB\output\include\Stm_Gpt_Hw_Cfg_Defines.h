/**
 * @file    Stm_Gpt_Hw_Cfg_Defines.h
 * @brief   Stm Hw Cfg defines Head file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 */

#ifndef STM_GPT_HW_CFG_DEFINES_H
#define STM_GPT_HW_CFG_DEFINES_H

#include "Std_Types.h"
#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define STM_GPT_HW_DEFINES_VENDOR_ID_CFG                    (110U)
#define STM_GPT_HW_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG     (4U)
#define STM_GPT_HW_DEFINES_AR_RELEASE_MINOR_VERSION_CFG     (4U)
#define STM_GPT_HW_DEFINES_AR_RELEASE_REVISION_VERSION_CFG  (0U)
#define STM_GPT_HW_DEFINES_SW_MAJOR_VERSION_CFG             (1U)
#define STM_GPT_HW_DEFINES_SW_MINOR_VERSION_CFG             (0U)
#define STM_GPT_HW_DEFINES_SW_PATCH_VERSION_CFG             (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Std_Types.h file are of the same Autosar version */
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    #if ((STM_GPT_HW_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG != STD_AR_RELEASE_MAJOR_VERSION) || \
         (STM_GPT_HW_DEFINES_AR_RELEASE_MINOR_VERSION_CFG != STD_AR_RELEASE_MINOR_VERSION))
    #error "AutoSar Version Numbers of Stm_Gpt_Hw_Cfg_Defines.h and Std_Types.h are different"
    #endif
#endif
/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/**
 * @brief These defines indicate that at least one channel from each module is used in all configurations.
 *
 */
#define STM_GPT_HW_USED (STD_OFF)

/**
 * @brief Stm Mask Interrupt Switch for channel used
 *
 */


/**
 * @brief IRQ Defines for each channel used
 *
 */
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** } */
#endif  /* STM_GPT_HW_CFG_DEFINES_H */
