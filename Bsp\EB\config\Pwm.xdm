<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Pwm" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Pwm" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Pwm"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:ctr name="PwmConfigSet" type="IDENTIFIABLE">
                <d:var name="PwmMaxChannel" type="INTEGER" value="16">
                  <a:a name="IMPORTER_INFO">
                    <a:v>@CALC</a:v>
                    <a:v>@DEF</a:v>
                  </a:a>
                </d:var>
                <d:lst name="PwmChannel" type="MAP">
                  <d:ctr name="PwmChannel_Motor1_BackRest_A" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="0"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_0/PwmGpcmCh0_A_PTD16_PWM0_CH1"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Falling_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor1_BackRest_B" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="1"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_0/PwmGpcmCh0_B_PTD15_PWM0_CH0"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor2_SeatLift_A" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="2"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_1/PwmGpcmCh1_A_PTE9_PWM0_CH7"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor2_SeatLift_B" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="3"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_1/PwmGpcmCh1_B_PTE8_PWM0_CH6"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor3_SeatSlide_A" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="4"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_2/PwmGpcmCh2_A_PTB5_PWM0_CH5"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor3_SeatSlide_B" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="5"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_2/PwmGpcmCh2_B_PTB4_PWM0_CH4"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor4_FrontBack_A" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="6"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_3/PwmGpcmCh8_A_PTD5_PWM2_CH3"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor4_FrontBack_B" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="7"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_3/PwmGpcmCh8_B_PTD12_PWM2_CH2"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor5_LegLift_A" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="8"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_4/PwmGpcmCh9_A_PTD11_PWM2_CH1"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor5_LegLift_B" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="9"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_4/PwmGpcmCh9_B_PTD10_PWM2_CH0"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor6_LegStretch_A" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="10"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_5/PwmGpcmCh12_A_PTD3_PWM3_CH5"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Motor6_LegStretch_B" 
                         type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="11"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_5/PwmGpcmCh12_B_PTD2_PWM3_CH4"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="false"/>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH"/>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW"/>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="Pwm_Rising_Edge_Notification">
                      <a:a name="ENABLE" value="false"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Fan1" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="12">
                      <a:a name="IMPORTER_INFO">
                        <a:v>@DEF</a:v>
                        <a:v>@CALC</a:v>
                      </a:a>
                    </d:var>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_Fan/PwmGpcmChannels_Fan_A_PTA3_PWM3_CH1"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Fan2" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="13"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_Fan/PwmGpcmChannels_Fan_B_PTA2_PWM3_CH0"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Heat1" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="14"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_Heat/PwmGpcmChannels_Heat_A_PTE11_PWM2_CH5"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                  <d:ctr name="PwmChannel_Heat2" type="IDENTIFIABLE">
                    <d:var name="PwmChannelId" type="INTEGER" value="15"/>
                    <d:ref name="PwmChannelRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmChannels_Heat/PwmGpcmChannels_Heat_B_PTE10_PWM2_CH4"/>
                    <d:var name="PwmChannelClass" type="ENUMERATION" 
                           value="PWM_VARIABLE_PERIOD">
                      <a:a name="ENABLE" value="true"/>
                    </d:var>
                    <d:var name="PwmPeriodInTicks" type="BOOLEAN" value="true">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodDefault" type="FLOAT" value="1.0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmDutycycleDefault" type="INTEGER" 
                           value="16384">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPolarity" type="ENUMERATION" 
                           value="PWM_HIGH">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmIdleState" type="ENUMERATION" 
                           value="PWM_LOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmNotification" type="FUNCTION-NAME" 
                           value="NULL_PTR">
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmMcuClockReferencePoint" type="REFERENCE" 
                           value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Pwm"/>
                  </d:ctr>
                </d:lst>
                <d:lst name="PwmChannels" type="MAP">
                  <d:ctr name="PwmChannels_0" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="0"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh0_A_PTD16_PWM0_CH1" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD16"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH1"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh0_B_PTD15_PWM0_CH0" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD15"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH0"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_1" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="1"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh1_A_PTE9_PWM0_CH7" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTE9"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH7"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh1_B_PTE8_PWM0_CH6" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTE8"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH6"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_2" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="2"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh2_A_PTB5_PWM0_CH5" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTB5"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH5"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh2_B_PTB4_PWM0_CH4" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTB4"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM0_CH4"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_3" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="8"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh8_A_PTD5_PWM2_CH3" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD5"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH3"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh8_B_PTD12_PWM2_CH2" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD12"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH2"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_4" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="9"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh9_A_PTD11_PWM2_CH1" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD11"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH1"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh9_B_PTD10_PWM2_CH0" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD10"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH0"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_5" type="IDENTIFIABLE">
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false"/>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" value="500"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0"/>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="500"/>
                    <d:var name="PwmGpcmId" type="INTEGER" value="12"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS"/>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW"/>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW"/>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL"/>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL"/>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT"/>
                    <d:ref name="PwmIrqRef" type="REFERENCE" 
                           value="ASPath:/Pwm/Pwm/PwmConfigSet/PwmHwInterruptConfigList_0">
                      <a:a name="ENABLE" value="false"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmCh12_A_PTD3_PWM3_CH5" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD3"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH5"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmCh12_B_PTD2_PWM3_CH4" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTD2"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH4"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_Fan" type="IDENTIFIABLE">
                    <d:var name="PwmGpcmId" type="INTEGER" value="13"/>
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" 
                           value="100000"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="100000"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="100000"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmChannels_Fan_A_PTA3_PWM3_CH1" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTA3"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH1"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmChannels_Fan_B_PTA2_PWM3_CH0" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTA2"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM3_CH0"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                  <d:ctr name="PwmChannels_Heat" type="IDENTIFIABLE">
                    <d:var name="PwmGpcmId" type="INTEGER" value="10"/>
                    <d:var name="PwmExternalClockSelect" type="BOOLEAN" 
                           value="false">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmExtClockSource" type="ENUMERATION" 
                           value="EXT_CLK_0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrescaler" type="ENUMERATION" 
                           value="PWM_CLK_DIV_8"/>
                    <d:var name="PwmInitCountValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPeriodCountValue" type="INTEGER" 
                           value="2000000"/>
                    <d:var name="PwmDieWidthValue" type="INTEGER" value="0">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmChnaCmpvalue" type="INTEGER" value="2000000"/>
                    <d:var name="PwmChnbCmpvalue" type="INTEGER" value="2000000"/>
                    <d:var name="PwmCountMode" type="ENUMERATION" 
                           value="UP_CONTINUOUS">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmPrdLoadMode" type="ENUMERATION" 
                           value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpLoad" type="ENUMERATION" value="SHADOW">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpChnaCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNA_ORIGINAL">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpChnbCtrl" type="ENUMERATION" 
                           value="PWM_CMP_CHNB_ORIGINAL">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:var name="PwmCmpMode" type="ENUMERATION" 
                           value="PWM_CMP_INDEPENDENT">
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:var>
                    <d:ref name="PwmIrqRef" type="REFERENCE" >
                      <a:a name="ENABLE" value="false"/>
                      <a:a name="IMPORTER_INFO" value="@DEF"/>
                    </d:ref>
                    <d:lst name="PwmGpcmChannels" type="MAP">
                      <d:ctr name="PwmGpcmChannels_Heat_A_PTE11_PWM2_CH5" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_A">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTE11"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH5"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                      <d:ctr name="PwmGpcmChannels_Heat_B_PTE10_PWM2_CH4" 
                             type="IDENTIFIABLE">
                        <d:var name="PwmGpcmChannel" type="ENUMERATION" 
                               value="CH_B"/>
                        <d:var name="PwmPad" type="ENUMERATION" value="PTE10"/>
                        <d:var name="PwmPortMux" type="ENUMERATION" 
                               value="PWM2_CH4"/>
                        <d:var name="PwmFilterWidth" type="INTEGER" value="0">
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                      </d:ctr>
                    </d:lst>
                  </d:ctr>
                </d:lst>
                <d:lst name="PwmFlexio" type="MAP"/>
                <d:lst name="PwmPowerStateConfig" type="MAP"/>
                <d:lst name="PwmHwInterruptConfigList" type="MAP"/>
              </d:ctr>
              <d:ctr name="PwmGeneral" type="IDENTIFIABLE">
                <d:var name="PwmIndex" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PwmDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="PwmNotificationSupported" type="BOOLEAN" 
                       value="true"/>
                <d:var name="PwmEnableUserModeSupport" type="BOOLEAN" 
                       value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PwmLowPowerStatesSupport" type="BOOLEAN" 
                       value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="PwmPowerStateAsynchTransitionMode" type="BOOLEAN" 
                       value="false">
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PwmEnableDualClockMode" type="BOOLEAN" value="true"/>
              </d:ctr>
              <d:ctr name="PwmOptionalApis" type="IDENTIFIABLE">
                <d:var name="PwmDeInitApi" type="BOOLEAN" value="true">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="PwmGetOutputState" type="BOOLEAN" value="true"/>
                <d:var name="PwmSetDutyCycle" type="BOOLEAN" value="true"/>
                <d:var name="PwmSetOutputToIdle" type="BOOLEAN" value="true"/>
                <d:var name="PwmSetPeriodAndDuty" type="BOOLEAN" value="true"/>
                <d:var name="PwmVersionInfoApi" type="BOOLEAN" value="true"/>
                <d:var name="PwmGetChannelStateApi" type="BOOLEAN" value="true"/>
                <d:var name="PwmSetChannelDeadTime" type="BOOLEAN" value="true"/>
                <d:var name="PwmEnableTrigger" type="BOOLEAN" value="true"/>
                <d:var name="PwmDisableTrigger" type="BOOLEAN" value="true"/>
                <d:var name="PwmResetCounter" type="BOOLEAN" value="true"/>
              </d:ctr>
              <d:ctr name="CommonPublishedInformation" type="IDENTIFIABLE">
                <d:var name="ArReleaseMajorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseMinorVersion" type="INTEGER" value="4">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ArReleaseRevisionVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="ModuleId" type="INTEGER" value="121">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMajorVersion" type="INTEGER" value="1">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwMinorVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="SwPatchVersion" type="INTEGER" value="0">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="VendorId" type="INTEGER" value="110">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
