/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Com_Callout.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-03-01 14:41:59 
*  @date               : 2024-12-23 10:04:55 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef  COM_CALLOUT_H
#define  COM_CALLOUT_H

#include "ComStack_Types.h"

boolean COM_RX_CCU_1_Callout(PduIdType PduId, const PduInfoType* PduInfoPtr);

boolean COM_RX_CCU_ZCUL_HVSPRL_1_Callout(PduIdType PduId, const PduInfoType* PduInfoPtr);

boolean COM_RX_CCU_ZCUR_HVSPRR_1_Callout(PduIdType PduId, const PduInfoType* PduInfoPtr);

boolean COM_RX_IDC_1_Callout(PduIdType PduId, const PduInfoType* PduInfoPtr);

void Rte_COMCbkInv_UMM_UsageModeSt_IPDU_COM_CCU_1(void);

void Rte_COMCbkInv_HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1(void);

void Rte_COMCbkInv_HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1(void);

void Rte_COMCbkInv_HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1(void);

void Rte_COMCbkInv_HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1(void);

void Rte_COMCbkInv_HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1(void);

void Rte_COMCbkInv_HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1(void);

void Rte_COMCbkInv_HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1(void);

void Rte_COMCbkInv_HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1(void);

void Rte_COMCbkInv_ACU_CDSwSt_IPDU_COM_IDC_1(void);

void Rte_COMCbkInv_ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1(void);

void Rte_COMCbkInv_Fun_Diag_Request_IPDU_COM_Fun_Diag_Rx(void);

void Rte_COMCbkInv_CD_Diag_Request_IPDU_COM_CD_Phys_Diag_Rx(void);

#endif
/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/
