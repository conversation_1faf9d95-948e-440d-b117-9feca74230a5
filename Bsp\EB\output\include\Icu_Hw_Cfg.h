/**
 * 
 * @file Icu_Hw_Cfg.h
 * @brief 
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>H<PERSON> IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef ICU_HW_CFG_H
#define ICU_HW_CFG_H

#include "Icu_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define ICU_HW_CFG_VENDOR_ID                   (110u)
#define ICU_HW_CFG_AR_RELEASE_MAJOR_VERSION    (4u)
#define ICU_HW_CFG_AR_RELEASE_MINOR_VERSION    (4u)
#define ICU_HW_CFG_AR_RELEASE_REVISION_VERSION (0u)
#define ICU_HW_CFG_SW_MAJOR_VERSION            (1u)
#define ICU_HW_CFG_SW_MINOR_VERSION            (0u)
#define ICU_HW_CFG_SW_PATCH_VERSION            (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if source file and Icu configuration header file are of the same vendor */
#if (ICU_HW_CFG_VENDOR_ID != ICU_HW_PBCFG_VENDOR_ID)
    #error "Icu_Hw_Cfg.h and Icu_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if source file and Icu configuration header file are of the same Autosar version */
#if ((ICU_HW_CFG_AR_RELEASE_MAJOR_VERSION != ICU_HW_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (ICU_HW_CFG_AR_RELEASE_MINOR_VERSION != ICU_HW_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (ICU_HW_CFG_AR_RELEASE_REVISION_VERSION != ICU_HW_PBCFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Icu_Hw_Cfg.h and Icu_Hw_PBcfg.h are different"
#endif

/* Check if source file and Icu configuration header file are of the same software version */
#if ((ICU_HW_CFG_SW_MAJOR_VERSION != ICU_HW_PBCFG_SW_MAJOR_VERSION) || \
     (ICU_HW_CFG_SW_MINOR_VERSION != ICU_HW_PBCFG_SW_MINOR_VERSION) || \
     (ICU_HW_CFG_SW_PATCH_VERSION != ICU_HW_PBCFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Icu_Hw_Cfg.h and Icu_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
                                             ENUMS
==================================================================================================*/

/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/


#define ICU_HW_PORT_USED                       (STD_OFF)

#define ICU_HW_PWM_USED                        (STD_ON)

/** @brief Switches the Development Error Detection and Notification on or off.  */
#define ICU_HW_PORT_DEV_ERROR_DETECT           (STD_OFF)

/** @brief Adds or removes all services related to the edge detect functionality. */
#define ICU_HW_PORT_EDGE_DETECT_API            (STD_ON)

/** @brief Adds or removes all services related to the deinitialization functionality. */
#define ICU_HW_PORT_DEINIT_API                 (STD_ON)

/** @brief Adds or removes all services related to mode set functionality. */
#define ICU_HW_PORT_SET_MODE_API               (STD_OFF)

/** @brief Add or remove all functions related to input state. */
#define ICU_HW_PORT_GET_INPUT_STATE_API        (STD_OFF)

#ifdef __cplusplus
}
#endif

#endif /* ICU_HW_CFG_H */
