#ifndef UART_CFG_DEFINES_H
#define UART_CFG_DEFINES_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"

#ifdef __cplusplus
extern "C"
{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define UART_DEFINES_VENDOR_ID_CFG                     (110u)
#define UART_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG      (4u)
#define UART_DEFINES_AR_RELEASE_MINOR_VERSION_CFG      (4u)
#define UART_DEFINES_AR_RELEASE_REVISION_VERSION_CFG   (0u)
#define UART_DEFINES_SW_MAJOR_VERSION_CFG              (1u)
#define UART_DEFINES_SW_MINOR_VERSION_CFG              (0u)
#define UART_DEFINES_SW_PATCH_VERSION_CFG              (0u)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((UART_DEFINES_AR_RELEASE_MAJOR_VERSION_CFG != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (UART_DEFINES_AR_RELEASE_MINOR_VERSION_CFG != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of CDD_Uart_Defines.h and Mcal.h are different"
    #endif
#endif
/*==================================================================================================
*                                     DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/* @brief Enables / Disables multicore support */
#define UART_MULTICORE_SUPPORT          (STD_OFF)

/* @brief Enables / Disables PreCompile support when there is a single configuration */
#define UART_PRECOMPILE_SUPPORT         (STD_OFF)

/* @brief Enables / Disables DET development errors detection and reporting */
#define UART_DEV_ERROR_DETECT           (STD_OFF)

/* @brief Enables / Disables Runtime errors detection and reporting */
#define UART_RUNTIME_ERROR_DETECT       (STD_ON)

/* @brief Number of Channels configured. */
#define UART_CH_MAX_CONFIG              (1U)

/* @brief UART Osif source counter. This parameter is used to select between different OsIf counter implementation */
#define UART_TIMEOUT_TYPE               (OSIF_COUNTER_DUMMY)

/* @brief Number of loops before returning Timeout status */
#define UART_TIMEOUT_VALUE_US           (1000U)

/* @brief Switches the Uart_GetVersionInfo() API ON or OFF. Support for version info API. */
#define UART_VERSION_INFO_API           (STD_ON)

/* @brief Uart has feature DMA enable. */
#define UART_HAS_DMA_ENABLED            (STD_ON)

/* @brief Uart has feature DMA enable. */
/* [cover SWSID=SWS_Uart_00100] Disabling functionality by configuration should always be possible in pre-compile variant only. */
#define UART_HAS_TIMEOUT_INTERRUPT_ENABLED            (STD_OFF)

/* @brief Number of available hardware instances */
#define UART_NUMBER_OF_INSTANCES             (7U)

/* @brief UART Hardware instances mapping initialize value */
#define UART_HW_CHANNELS_MAPPING_FOR_INIT       {-1, -1, -1, -1, -1, -1, -1}

/* @brief Declare callback parameters if any */
/* [cover SWSID=SWS_Uart_00100] Disabling functionality by configuration should always be possible in pre-compile variant only. */
#define UART_USE_CALLBACK            (STD_ON)

/**
* @brief        All CoreIDs are supported by Uart driver.
*/

#define UART_MAX_PARTITIONS             (1U)

/* Api to retrieve the core id */
#if (STD_ON == UART_MULTICORE_SUPPORT)
    #define Uart_GetCoreID() (OsIf_GetCoreID())
#else
    #define Uart_GetCoreID() ((uint32)0x0U)
#endif /* (STD_ON == UART_MULTICORE_SUPPORT) */

/*==================================================================================================
*                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* UART_CFG_DEFINES_H */
