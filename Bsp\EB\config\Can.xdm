<?xml version='1.0'?>
<datamodel version="7.0" 
           xmlns="http://www.tresos.de/_projects/DataModel2/16/root.xsd" 
           xmlns:a="http://www.tresos.de/_projects/DataModel2/16/attribute.xsd" 
           xmlns:v="http://www.tresos.de/_projects/DataModel2/06/schema.xsd" 
           xmlns:d="http://www.tresos.de/_projects/DataModel2/06/data.xsd">

  <d:ctr type="AUTOSAR" factory="autosar" 
         xmlns:ad="http://www.tresos.de/_projects/DataModel2/08/admindata.xsd" 
         xmlns:cd="http://www.tresos.de/_projects/DataModel2/08/customdata.xsd" 
         xmlns:f="http://www.tresos.de/_projects/DataModel2/14/formulaexpr.xsd" 
         xmlns:icc="http://www.tresos.de/_projects/DataModel2/08/implconfigclass.xsd" 
         xmlns:mt="http://www.tresos.de/_projects/DataModel2/11/multitest.xsd"  
         xmlns:variant="http://www.tresos.de/_projects/DataModel2/11/variant.xsd">
    <d:lst type="TOP-LEVEL-PACKAGES">
      <d:ctr name="Can" type="AR-PACKAGE">
        <d:lst type="ELEMENTS">
          <d:chc name="Can" type="AR-ELEMENT" value="MODULE-CONFIGURATION">
            <d:ctr type="MODULE-CONFIGURATION">
              <a:a name="DEF" value="ASPath:/LS_LSE14M01I0R0/Can"/>
              <d:var name="POST_BUILD_VARIANT_USED" type="BOOLEAN" value="false">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:var name="IMPLEMENTATION_CONFIG_VARIANT" type="ENUMERATION" 
                     value="VariantPostBuild">
                <a:a name="IMPORTER_INFO" value="@DEF"/>
              </d:var>
              <d:lst name="CanConfigSet" type="MAP">
                <d:ctr name="CanConfigSet_0" type="IDENTIFIABLE">
                  <d:lst name="CanController" type="MAP">
                    <d:ctr name="CanController_0" type="IDENTIFIABLE">
                      <d:var name="CanControllerRunningMode" type="ENUMERATION" 
                             value="CAN_CONTROLLER_NORMAL">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanBusoffProcessing" type="ENUMERATION" 
                             value="INTERRUPT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanControllerActivation" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanControllerBaseAddress" type="INTEGER" 
                             value="1342177280">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanControllerId" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanRxProcessing" type="ENUMERATION" 
                             value="INTERRUPT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTxProcessing" type="ENUMERATION" 
                             value="INTERRUPT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanAutoResendEnable" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanWakeupFunctionalityAPI" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanWakeupProcessing" type="ENUMERATION" 
                             value="INTERRUPT">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanWakeupSupport" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanTimeStampEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanDMATxFIFOEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanDMATxFIFOChannel" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="CanDMARxFIFO0Enable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanDMARxFIFO0Channel" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="CanDMARxFIFO1Enable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanDMARxFIFO1Channel" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="CanControllerInstance" type="ENUMERATION" 
                             value="0">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanControllerPhysicalChannel" 
                             type="ENUMERATION" value="CAN00">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanMessageRamBaseAddress" type="INTEGER" 
                             value="1342193664">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@CALC</a:v>
                          <a:v>@DEF</a:v>
                        </a:a>
                      </d:var>
                      <d:var name="CanMessageRamSize" type="INTEGER" 
                             value="12544">
                        <a:a name="IMPORTER_INFO">
                          <a:v>@DEF</a:v>
                          <a:v>@CALC</a:v>
                        </a:a>
                      </d:var>
                      <d:ref name="CanControllerDefaultBaudrate" 
                             type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0/CanControllerBaudrateConfig_0"/>
                      <d:ref name="CanControllerEcucPartitionRef" 
                             type="REFERENCE" >
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:ref name="CanCpuClockRef" type="REFERENCE" 
                             value="ASPath:/Mcu/Mcu/McuModuleConfiguration/McuClockSettingConfig_0/McuClockReferencePoint_Lin"/>
                      <d:ref name="CanWakeupSourceRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:lst name="CanControllerBaudrateConfig" type="MAP">
                        <d:ctr name="CanControllerBaudrateConfig_0" 
                               type="IDENTIFIABLE">
                          <d:var name="CanControllerBaudRate" type="FLOAT" 
                                 value="500.0"/>
                          <d:var name="CanControllerBaudRateConfigID" 
                                 type="INTEGER" value="0"/>
                          <d:var name="CanControllerPropSeg" type="INTEGER" 
                                 value="0"/>
                          <d:var name="CanControllerSeg1" type="INTEGER" 
                                 value="60"/>
                          <d:var name="CanControllerSeg2" type="INTEGER" 
                                 value="17"/>
                          <d:var name="CanControllerSyncJumpWidth" 
                                 type="INTEGER" value="4"/>
                          <d:ctr name="CanControllerFdBaudrateConfig" 
                                 type="IDENTIFIABLE">
                            <a:a name="ENABLE" value="true"/>
                            <d:var name="CanControllerFdBaudRate" type="FLOAT" 
                                   value="2000.0"/>
                            <d:var name="CanControllerPropSeg" type="INTEGER" 
                                   value="0"/>
                            <d:var name="CanControllerSeg1" type="INTEGER" 
                                   value="30"/>
                            <d:var name="CanControllerSeg2" type="INTEGER" 
                                   value="7"/>
                            <d:lst name="CanControllerSspOffset"/>
                            <d:var name="CanControllerSyncJumpWidth" 
                                   type="INTEGER" value="3"/>
                            <d:var name="CanControllerTxBitRateSwitch" 
                                   type="BOOLEAN" value="true">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="TransceiverDelayCompensationEnable" 
                                   type="BOOLEAN" value="false">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var name="TrcvDelayCompensationOffset" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                            <d:var 
                                   name="TrcvDelayCompensationFilterWindowLength" 
                                   type="INTEGER" value="0">
                              <a:a name="IMPORTER_INFO" value="@DEF"/>
                            </d:var>
                          </d:ctr>
                        </d:ctr>
                      </d:lst>
                      <d:ctr name="CanTTController" type="IDENTIFIABLE">
                        <a:a name="ENABLE" value="false"/>
                        <d:var name="CanTTControllerApplWatchdogLimit" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerCycleCountMax" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerExpectedTxTrigger" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var 
                               name="CanTTControllerExternalClockSynchronisation" 
                               type="BOOLEAN" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerGlobalTimeFiltering" 
                               type="BOOLEAN" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerInitialRefOffset" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerInterruptEnable" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerLevel2" type="BOOLEAN" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerNTUConfig" type="FLOAT" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerOperationMode" 
                               type="ENUMERATION" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerSyncDeviation" type="FLOAT" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerTimeMaster" type="BOOLEAN" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerTimeMasterPriority" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerTURRestore" type="BOOLEAN" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerTxEnableWindowLength" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerWatchTriggerGapTimeMark" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTControllerWatchTriggerTimeMark" 
                               type="INTEGER" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:var name="CanTTIRQProcessing" type="ENUMERATION" >
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:var>
                        <d:ref name="CanTTControllerEcucPartitionRef" 
                               type="REFERENCE" >
                          <a:a name="ENABLE" value="false"/>
                          <a:a name="IMPORTER_INFO" value="@DEF"/>
                        </d:ref>
                      </d:ctr>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="CanHardwareObject" type="MAP">
                    <d:ctr name="CanHardwareObject_Receive" type="IDENTIFIABLE">
                      <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanHandleType" type="ENUMERATION" 
                             value="BASIC"/>
                      <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                             value="false">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanHwObjectCount" type="INTEGER" value="16"/>
                      <d:var name="CanIdType" type="ENUMERATION" 
                             value="STANDARD"/>
                      <d:var name="CanObjectId" type="INTEGER" value="0"/>
                      <d:var name="CanIdValue" type="INTEGER" value="0">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanObjectType" type="ENUMERATION" 
                             value="RECEIVE"/>
                      <d:var name="CanRxBufferSelection" type="ENUMERATION" 
                             value="CAN_RX_FIFO0"/>
                      <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="TxEventStoreEnable" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanControllerRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0"/>
                      <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="CanIcomRxMessageDedicated" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="CanHwFilter" type="MAP">
                        <d:ctr name="CanHwFilter_0" type="IDENTIFIABLE">
                          <d:var name="CanHwFilterCode" type="INTEGER" 
                                 value="1024"/>
                          <d:var name="CanHwFilterMask" type="INTEGER" 
                                 value="1024"/>
                        </d:ctr>
                      </d:lst>
                      <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                    </d:ctr>
                    <d:ctr name="CanHardwareObject_Transmit" 
                           type="IDENTIFIABLE">
                      <d:var name="CanFdPaddingValue" type="INTEGER" value="0">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanHandleType" type="ENUMERATION" 
                             value="BASIC"/>
                      <d:var name="CanHardwareObjectUsesPolling" type="BOOLEAN" 
                             value="false">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanHwObjectCount" type="INTEGER" value="8"/>
                      <d:var name="CanIdType" type="ENUMERATION" 
                             value="STANDARD"/>
                      <d:var name="CanObjectId" type="INTEGER" value="1"/>
                      <d:var name="CanIdValue" type="INTEGER" value="0">
                        <a:a name="ENABLE" value="false"/>
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="CanObjectType" type="ENUMERATION" 
                             value="TRANSMIT"/>
                      <d:var name="CanRxBufferSelection" type="ENUMERATION" 
                             value="CAN_TX_FIFO"/>
                      <d:var name="CanTriggerTransmitEnable" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:var name="TxEventStoreEnable" type="BOOLEAN" 
                             value="true">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:ref name="CanControllerRef" type="REFERENCE" 
                             value="ASPath:/Can/Can/CanConfigSet_0/CanController_0"/>
                      <d:ref name="CanMainFunctionRWPeriodRef" type="REFERENCE" >
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:ref>
                      <d:var name="CanIcomRxMessageDedicated" type="BOOLEAN" 
                             value="false">
                        <a:a name="IMPORTER_INFO" value="@DEF"/>
                      </d:var>
                      <d:lst name="CanHwFilter" type="MAP"/>
                      <d:lst name="CanTTHardwareObjectTrigger" type="MAP"/>
                    </d:ctr>
                  </d:lst>
                  <d:lst name="CanIcom" type="MAP"/>
                </d:ctr>
              </d:lst>
              <d:ctr name="CanGeneral" type="IDENTIFIABLE">
                <d:var name="CanDevErrorDetect" type="BOOLEAN" value="true"/>
                <d:var name="CanIndex" type="INTEGER" value="0"/>
                <d:var name="CanLPduReceiveCalloutFunction" 
                       type="FUNCTION-NAME" >
                  <a:a name="ENABLE" value="false"/>
                </d:var>
                <d:var name="CanMainFunctionBusoffPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanMainFunctionModePeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:var name="CanMainFunctionWakeupPeriod" type="FLOAT" 
                       value="0.001">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanMultiplexedTransmission" type="BOOLEAN" 
                       value="true"/>
                <d:var name="CanPublicIcomSupport" type="BOOLEAN" value="true"/>
                <d:var name="CanSetBaudrateApi" type="BOOLEAN" value="true">
                  <a:a name="ENABLE" value="true"/>
                </d:var>
                <d:var name="CanTimeoutDuration" type="FLOAT" value="0.001"/>
                <d:var name="CanVersionInfoApi" type="BOOLEAN" value="false">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="CanEcucPartitionRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ref name="CanOsCounterRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:var name="CanTimeoutCount" type="INTEGER" value="100000">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:ref name="CanSupportTTCANRef" type="REFERENCE" >
                  <a:a name="ENABLE" value="false"/>
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:ref>
                <d:ctr name="CanIcomGeneral" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanIcomLevel" type="ENUMERATION" 
                         value="CAN_ICOM_LEVEL_ONE">
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                  <d:var name="CanIcomVariant" type="ENUMERATION" 
                         value="CAN_ICOM_VARIANT_NONE">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:ctr name="CanMainFunctionRWPeriods" type="IDENTIFIABLE">
                  <a:a name="ENABLE" value="false"/>
                  <d:var name="CanMainFunctionPeriod" type="FLOAT" >
                    <a:a name="ENABLE" value="false"/>
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:ctr>
                <d:var name="CanErrorCalloutFunction" type="FUNCTION-NAME" 
                       value="ErrorCalloutHandler">
                  <a:a name="IMPORTER_INFO" value="@DEF"/>
                </d:var>
                <d:lst name="CanIncludeFile">
                  <d:var type="STRING" value="ErrorCalloutHandler.h">
                    <a:a name="IMPORTER_INFO" value="@DEF"/>
                  </d:var>
                </d:lst>
              </d:ctr>
            </d:ctr>
          </d:chc>
        </d:lst>
      </d:ctr>
    </d:lst>
  </d:ctr>

</datamodel>
