/**
 * file    Flexio_Uart_Hw_PBcfg.c
 * brief   Configuration for Flexio 
 * author  Zhuyn
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/

#include "Flexio_Uart_Hw_Cfg.h"
#include "Std_Types.h"
#if (FLEXIO_UART_HW_HAS_DMA_ENABLED == STD_ON)
#include "Dma_Hw.h"
#endif

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define FLEXIO_UART_HW_PBCFG_VENDOR_ID_C                     (110U)
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C      (4U) 
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C      (4U) 
#define FLEXIO_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C   (0U) 
#define FLEXIO_UART_HW_PBCFG_SW_MAJOR_VERSION_C              (1U) 
#define FLEXIO_UART_HW_PBCFG_SW_MINOR_VERSION_C              (0U) 
#define FLEXIO_UART_HW_PBCFG_SW_PATCH_VERSION_C              (0U) 

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Flexio_Uart_Hw_Cfg.h */
#if (FLEXIO_UART_HW_PBCFG_VENDOR_ID_C != FLEXIO_UART_HW_CFG_VENDOR_ID)
    #error "Flexio_Uart_Hw_PBcfg.c and Flexio_Uart_Hw_Cfg.h have different vendor ids"
#endif
#if ((FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C    != FLEXIO_UART_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C    != FLEXIO_UART_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_AR_RELEASE_REVISION_VERSION_C != FLEXIO_UART_HW_CFG_AR_RELEASE_REVISION_VERSION))
     #error "AUTOSAR Version Numbers of Flexio_Uart_Hw_PBcfg.c and Flexio_Uart_Hw_Cfg.h are different"
#endif
#if ((FLEXIO_UART_HW_PBCFG_SW_MAJOR_VERSION_C != FLEXIO_UART_HW_CFG_SW_MAJOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_SW_MINOR_VERSION_C != FLEXIO_UART_HW_CFG_SW_MINOR_VERSION) || \
     (FLEXIO_UART_HW_PBCFG_SW_PATCH_VERSION_C != FLEXIO_UART_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Flexio_Uart_Hw_PBcfg.c and Flexio_Uart_Hw_Cfg.h are different"
#endif

#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Check if current file and Std_Types.h header file are of the same Autosar version */
    #if ((FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != STD_AR_RELEASE_MAJOR_VERSION) || \
         (FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != STD_AR_RELEASE_MINOR_VERSION))
        #error "AutoSar Version Numbers of Flexio_Uart_Hw_PBcfg.c and Std_Types.h are different"
    #endif
    /* Checks against Dma_Hw.h */
    #if (FLEXIO_UART_HW_HAS_DMA_ENABLED == STD_ON)
        #if ((FLEXIO_UART_HW_PBCFG_AR_RELEASE_MAJOR_VERSION_C != DMA_HW_AR_RELEASE_MAJOR_VERSION) || \
             (FLEXIO_UART_HW_PBCFG_AR_RELEASE_MINOR_VERSION_C != DMA_HW_AR_RELEASE_MINOR_VERSION))
            #error "AutoSar Version Numbers of Flexio_Uart_Hw_PBcfg.c and Dma_Hw.h are different"
        #endif
    #endif
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                   STATE STRUCTURE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/
#define UART_START_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Uart_MemMap.h"

#define UART_STOP_SEC_VAR_CLEARED_UNSPECIFIED_NO_CACHEABLE
#include "Uart_MemMap.h"

#define UART_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"


#define UART_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Uart_MemMap.h"
/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/


#ifdef __cplusplus
}

/** @} */

#endif
