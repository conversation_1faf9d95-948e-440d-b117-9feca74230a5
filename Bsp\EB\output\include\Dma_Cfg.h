/**
 * file    Dma_Cfg.h
 * brief   dma cfg header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */
 
#ifndef DMA_CFG_H
#define DMA_CFG_H

/*==================================================================================================
                                         INCLUDE FILES
 1) system and project includes
 2) needed interfaces from external units
 3) internal and external interfaces from this unit
==================================================================================================*/
#include "Dma_Types.h"
#include "Dma_Hw_Cfg.h"
#include "Dma_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_CFG_VENDOR_ID                       (110U)
#define DMA_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define DMA_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define DMA_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define DMA_CFG_SW_MAJOR_VERSION                (1U)
#define DMA_CFG_SW_MINOR_VERSION                (0U)
#define DMA_CFG_SW_PATCH_VERSION                (0U)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
/* Check if header file and Dma_Types.h file are of the same vendor */
#if (DMA_CFG_VENDOR_ID != DMA_TYPES_VENDOR_ID)
    #error "Dma_Cfg.h and Dma_Types.h have different vendor ids"
#endif

/* Check if header file and Dma_Types.h file are of the same Autosar version */
#if ((DMA_CFG_AR_RELEASE_MAJOR_VERSION != DMA_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_MINOR_VERSION != DMA_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_REVISION_VERSION != DMA_TYPES_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Cfg.h and Dma_Types.h are different"
#endif

/* Check if header file and Dma_Types.h file are of the same Software version */
#if ((DMA_CFG_SW_MAJOR_VERSION != DMA_TYPES_SW_MAJOR_VERSION) || \
     (DMA_CFG_SW_MINOR_VERSION != DMA_TYPES_SW_MINOR_VERSION) || \
     (DMA_CFG_SW_PATCH_VERSION != DMA_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Cfg.h and Dma_Types.h are different"
#endif

/* Check if header file and Dma_Hw_Cfg.h file are of the same vendor */
#if (DMA_CFG_VENDOR_ID != DMA_HW_CFG_VENDOR_ID)
    #error "Dma_Cfg.h and Dma_Hw_Cfg.h have different vendor ids"
#endif

/* Check if header file and Dma_Hw_Cfg.h file are of the same Autosar version */
#if ((DMA_CFG_AR_RELEASE_MAJOR_VERSION != DMA_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_MINOR_VERSION != DMA_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_REVISION_VERSION != DMA_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Cfg.h and Dma_Hw_Cfg.h are different"
#endif

/* Check if header file and Dma_Hw_Cfg.h file are of the same Software version */
#if ((DMA_CFG_SW_MAJOR_VERSION != DMA_HW_CFG_SW_MAJOR_VERSION) || \
     (DMA_CFG_SW_MINOR_VERSION != DMA_HW_CFG_SW_MINOR_VERSION) || \
     (DMA_CFG_SW_PATCH_VERSION != DMA_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Cfg.h and Dma_Hw_Cfg.h are different"
#endif

/* Check if header file and Dma_PBcfg.h file are of the same vendor */
#if (DMA_CFG_VENDOR_ID != DMA_PBCFG_VENDOR_ID)
    #error "Dma_Cfg.h and Dma_PBcfg.h have different vendor ids"
#endif

/* Check if header file and Dma_PBcfg.h file are of the same Autosar version */
#if ((DMA_CFG_AR_RELEASE_MAJOR_VERSION != DMA_PBCFG_AR_RELEASE_MAJOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_MINOR_VERSION != DMA_PBCFG_AR_RELEASE_MINOR_VERSION) || \
     (DMA_CFG_AR_RELEASE_REVISION_VERSION != DMA_PBCFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Dma_Cfg.h and Dma_PBcfg.h are different"
#endif

/* Check if header file and Dma_PBcfg.h file are of the same Software version */
#if ((DMA_CFG_SW_MAJOR_VERSION != DMA_PBCFG_SW_MAJOR_VERSION) || \
     (DMA_CFG_SW_MINOR_VERSION != DMA_PBCFG_SW_MINOR_VERSION) || \
     (DMA_CFG_SW_PATCH_VERSION != DMA_PBCFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Dma_Cfg.h and Dma_PBcfg.h are different"
#endif

/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/
/*-----------------------------------------------/
/  DET                                     /
/-----------------------------------------------*/
#define DMA_DET_IS_AVAILABLE                       STD_OFF

/*-----------------------------------------------/
/  PRECOMPILE                                    /
/-----------------------------------------------*/
#define DMA_PRECOMPILE_SUPPORT                     STD_OFF

/*==================================================================================================
                                             ENUMS
==================================================================================================*/

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

#endif    /* DMA_CFG_H */

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
