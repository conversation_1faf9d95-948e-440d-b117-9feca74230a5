/**
 * file    CDD_Uart_PBcfg.h
 * brief   UART driver for LES14XX
 * author  xiali
 * date    2024.5.29
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef UART_PBCFG_H
#define UART_PBCFG_H

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Uart_Types.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/

#define UART_PBCFG_VENDOR_ID                     (110u)
#define UART_PBCFG_AR_RELEASE_MAJOR_VERSION      (4u)
#define UART_PBCFG_AR_RELEASE_MINOR_VERSION      (4u)
#define UART_PBCFG_AR_RELEASE_REVISION_VERSION   (0u)
#define UART_PBCFG_SW_MAJOR_VERSION              (1u)
#define UART_PBCFG_SW_MINOR_VERSION              (0u)
#define UART_PBCFG_SW_PATCH_VERSION              (0u)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/* Checks against Uart_Types.h */
#if (UART_PBCFG_VENDOR_ID != UART_TYPES_VENDOR_ID)
    #error "Uart_PBcfg.h and Uart_Types.h have different vendor ids"
#endif
#if ((UART_PBCFG_AR_RELEASE_MAJOR_VERSION   != UART_TYPES_AR_RELEASE_MAJOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_MINOR_VERSION   != UART_TYPES_AR_RELEASE_MINOR_VERSION) || \
     (UART_PBCFG_AR_RELEASE_REVISION_VERSION!= UART_TYPES_AR_RELEASE_REVISION_VERSION))
    #error "AUTOSAR Version Numbers of Uart_PBcfg.h and Uart_Types.h are different"
#endif
#if ((UART_PBCFG_SW_MAJOR_VERSION!= UART_TYPES_SW_MAJOR_VERSION) || \
     (UART_PBCFG_SW_MINOR_VERSION!= UART_TYPES_SW_MINOR_VERSION) || \
     (UART_PBCFG_SW_PATCH_VERSION!= UART_TYPES_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Uart_PBcfg.h and Uart_Types.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
#define UART_CONFIG_PB \
extern const Uart_ConfigType Uart_xConfig;\

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* UART_PBCFG_H */
