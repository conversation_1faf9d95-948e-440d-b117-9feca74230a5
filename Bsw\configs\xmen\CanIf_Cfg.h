/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : CanIf_Cfg.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-03-01 14:41:59 
*  @date               : 2024-12-17 19:05:04 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
#ifndef CANIF_CFG_H
#define CANIF_CFG_H

/*=======[V E R S I O N  I N F O R M A T I O N]===============================*/
#define CANIF_CFG_H_AR_MAJOR_VERSION    4u
#define CANIF_CFG_H_AR_MINOR_VERSION    2u
#define CANIF_CFG_H_AR_PATCH_VERSION    2u
#define CANIF_CFG_H_SW_MAJOR_VERSION    2u
#define CANIF_CFG_H_SW_MINOR_VERSION    0u
#define CANIF_CFG_H_SW_PATCH_VERSION    2u

/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/
#define CANIF_CAN_AUTOSAR_422                0u
#define CANIF_CAN_AUTOSAR_431                1u
#define CANIF_CAN_AUTOSAR_440                2u

#define CANIF_CAN_AUTOSAR_VERSION            CANIF_CAN_AUTOSAR_422

#define CANIF_CANID_UINT16                  0u
#define CANIF_CANID_UINT32                  1u

#define CANIF_CAN_IDTYPE                    CANIF_CANID_UINT32

#define CANIF_SOFTWARE_FILTER_BINARY                0u
#define CANIF_SOFTWARE_FILTER_INDEX                 1u
#define CANIF_SOFTWARE_FILTER_LINEAR                2u
#define CANIF_SOFTWARE_FILTER_TABLE                 3u

#define CANIF_FIXED_BUFFER            STD_ON

#define CANIF_PRIVATE_DLC_CHECK       STD_OFF

#define CANIF_SOFTWARE_FILTER_TYPE    CANIF_SOFTWARE_FILTER_LINEAR

#define CANIF_SUPPORT_TTCAN           STD_OFF

#define CANIF_META_DATA_SUPPORT       STD_OFF

#define CANIF_PUBLIC_CANCEL_TRANSMIT_SUPPORT               STD_OFF

#define CANIF_PUBLIC_DEV_ERROR_DETECT                      STD_ON

#define CANIF_PUBLIC_ICOM_SUPPORT                          STD_OFF

#define CANIF_PUBLIC_MULTIPLE_DRV_SUPPORT                  STD_ON

#define CANIF_PUBLIC_PN_SUPPORT                            STD_OFF

#define CANIF_PUBLIC_READ_RX_PDU_DATA_API                  STD_OFF

#define CANIF_PUBLIC_READ_RX_PDU_NOTIFY_STATUS_API         STD_OFF

#define CANIF_PUBLIC_READ_TX_PDU_NOTIFY_STATUS_API         STD_OFF

#define CANIF_PUBLIC_SET_DYNAMIC_TX_ID_API                 STD_OFF

#define CANIF_PUBLIC_TX_BUFFERING                          STD_OFF

#define CANIF_PUBLIC_TX_CONFIRM_POLLING_SUPPORT            STD_ON

#define CANIF_PUBLIC_VERSION_INFO_API                      STD_ON

#define CANIF_PUBLIC_WAKEUP_CHECK_VALID_BY_NM              STD_OFF

#define CANIF_PUBLIC_WAKEUP_CHECK_VALID_SUPPORT            STD_OFF/* PRQA S 0791 */ /* MISRA Rule 5.4 */

#define CANIF_SET_BAUDRATE_API                             STD_OFF

#define CANIF_TRIGGER_TRANSMIT_SUPPORT            STD_OFF

#define CANIF_TX_OFFLINE_ACTIVE_SUPPORT           STD_OFF

#define CANIF_WAKE_UP_SUPPORT                     STD_ON

#define CANIF_CANDRV_WAKE_UP_SUPPORT              STD_OFF

#define CANIF_CANTRCV_WAKE_UP_SUPPORT             STD_OFF

#define CANIF_HRH_RANGE_SUPPORT     STD_OFF

#define CANIF_RXPDU_CANID_RANGE_SUPPORT     STD_OFF

#define CANIF_RX_STANDARD_CAN_SUPPORT     STD_OFF

#define CANIF_RX_STANDARD_FD_CAN_SUPPORT     STD_ON

#define CANIF_RX_STANDARD_NO_FD_CAN_SUPPORT     STD_OFF

#define CANIF_RX_EXTENDED_CAN_SUPPORT     STD_OFF

#define CANIF_RX_EXTENDED_FD_CAN_SUPPORT     STD_OFF

#define CANIF_RX_EXTENDED_NO_FD_CAN_SUPPORT     STD_OFF

#define CANIF_TX_EXTENDED_CAN_SUPPORT       STD_OFF

#define CANIF_TX_EXTENDED_FD_CAN_SUPPORT        STD_OFF

#define CANIF_TX_STANDARD_CAN_SUPPORT           STD_OFF

#define CANIF_TX_STANDARD_FD_CAN_SUPPORT        STD_ON

#define CANIF_WAKEUPSOURCE_MAX                    0x1Fu
/*******************************************************************************************/
#define PDUR_CANIF_CD_1_0X3BF CANIF_TXPDU_CD_1
#define CANIF_PDUR_IDC_1_0X0A1 CANIF_RXPDU_IDC_1
#define CANIF_PDUR_CCU_ZCUL_0X13B CANIF_RXPDU_CCU_ZCUL_HVSPRL_1
#define CANIF_PDUR_CCU_ZCUR_0X13C CANIF_RXPDU_CCU_ZCUR_HVSPRR_1
#define CANIF_PDUR_CCU_1_0X268 CANIF_RXPDU_CCU_1

// dummy PDUR IDs for DCM
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define CANIF_TXPDU_CAN0_Tx_0x351_Cyclic_PN29         0u
#define CANIF_TXPDU_CAN0_Tx_0x350_Cyclic_PN17         1u
#define CANIF_TXPDU_CAN0_Tx_0x300_Cyclic         2u
#define CANIF_TXPDU_CAN0_Tx_0x301_Event         3u
#define CANIF_TXPDU_CAN0_Tx_0x302_Mixed         4u
#define CANIF_TXPDU_CAN0_Tx_0x303_Cyclic_Counter         5u
#define CANIF_TXPDU_CAN0_Tx_0x709_Diag_Phy_Response         6u
#define CANIF_TXPDU_CAN0_Tx_0x501_NM_ECU         7u
#define CANIF_TXPDU_CAN0_Tx_0x360_E2E_P01         8u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define CANIF_RXPDU_CAN0_Rx_0x251_Cyclic_PN29         0u
#define CANIF_RXPDU_CAN0_Rx_0x250_Cyclic_PN17         1u
#define CANIF_RXPDU_CAN0_Rx_0x200_Cyclic         2u
#define CANIF_RXPDU_CAN0_Rx_0x201_Event         3u
#define CANIF_RXPDU_CAN0_Rx_0x202_Mixed         4u
#define CANIF_RXPDU_CAN0_Rx_0x203_Cyclic_Counter         5u
#define CANIF_RXPDU_CAN0_Rx_0x708_Diag_Phy_Request         6u
#define CANIF_RXPDU_CAN0_Rx_0x7df_Diag_Fun_Request         7u
#define CANIF_RXPDU_CAN0_Rx_0x5xx_NM_ECU         8u
#define CANIF_RXPDU_CAN0_Rx_0x260_E2E_P01         9u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */


#define CANIF_RXPDU_NUMBER              6u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define CANIF_RXPDU_CCU_1         0u
#define CANIF_RXPDU_CCU_ZCUL_HVSPRL_1         1u
#define CANIF_RXPDU_CCU_ZCUR_HVSPRR_1         2u
#define CANIF_RXPDU_IDC_1         3u
#define CANIF_RXPDU_Fun_Diag_Rx         4u
#define CANIF_RXPDU_CD_Phys_Diag_Rx         5u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */
#define CANIF_TXPDU_NUMBER              2u
#define CANIF_DYNAMIC_TXPDU_NUMBER      0u

/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define CANIF_TXPDU_CD_1         0u
#define CANIF_TXPDU_CD_Diag_Tx         1u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */

#define CANIF_HRH_NUMBER           6u
#define CANIF_HOH0_HRH_0           0u
#define CANIF_HOH0_HRH_1           1u
#define CANIF_HOH0_HRH_2           2u
#define CANIF_HOH0_HRH_3           3u
#define CANIF_HOH0_HRH_4           4u
#define CANIF_HOH0_HRH_5           5u

#define CANIF_HTH_NUMBER           2u
#define CANIF_HOH0_HTH_0           0u
#define CANIF_HOH0_HTH_1           1u

#define CANIF_TXBUFFER_NUMBER          0u
#define CANIF_RXBUFFER_NUMBER          0u

#define CANIF_RXNOTIFYSTATUS_BUFFER    0u
#define CANIF_TXNOTIFYSTATUS_BUFFER    0u

#define CANIF_CANDRIVER_NUMBER      1u
#define CANIF_CAN_43_FLEXCAN    0u
#define CANIF_CANCONTROLLER_NUMBER      1u
#define CANIF_CANDRV_0_CANIF_CTR_DRV_CanController_0    0u

#define CANIF_TRCVDRIVER_NUMBER      0u
#define CANIF_TRCV_NUMBER      0u

#define CANIF_RXINDICATION_FUNC_NUMBER     2u

#define CANIF_TXCONFIRMATION_FUNC_NUMBER     2u

#endif
/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/
