/**
 * *************************************************************************
 * @file   Clock_Hw_Cfg.c
 * @brief  Code template for Post-Compile(PC) configuration file generation.
 * @version V1.0.0
 * <AUTHOR>
 * @date 2024-06-07
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights reserved 
 * 
 * *************************************************************************/

/*==================================================================================================
                                         INCLUDE FILES

==================================================================================================*/
#include "Clock_Hw_Cfg.h"
#include "Std_Types.h"
#include "Clock_Hw.h"
#include "Clock_Hw_Private.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define CLOCK_HW_CFG_VENDOR_ID_C                      (110U)
#define CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION_C       (4U)
#define CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION_C       (4U)
#define CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION_C    (0U)
#define CLOCK_HW_CFG_SW_MAJOR_VERSION_C               (1U)
#define CLOCK_HW_CFG_SW_MINOR_VERSION_C               (0U)
#define CLOCK_HW_CFG_SW_PATCH_VERSION_C               (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Cfg.h file are of the same vendor */
#if (CLOCK_HW_CFG_VENDOR_ID_C != CLOCK_HW_CFG_VENDOR_ID)
    #error "Clock_Hw_Cfg.c and Clock_Hw_Cfg.h have different vendor ids"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Cfg.h file are of the same Autosar version */
#if ((CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_Cfg.c and Clock_Hw_Cfg.h are different"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Cfg.h file are of the same Software version */
#if ((CLOCK_HW_CFG_SW_MAJOR_VERSION_C != CLOCK_HW_CFG_SW_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_SW_MINOR_VERSION_C != CLOCK_HW_CFG_SW_MINOR_VERSION) || \
     (CLOCK_HW_CFG_SW_PATCH_VERSION_C != CLOCK_HW_CFG_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_Cfg.c and Clock_Hw_Cfg.h are different"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw.h file are of the same vendor */
#if (CLOCK_HW_CFG_VENDOR_ID_C != CLOCK_HW_VENDOR_ID)
    #error "Clock_Hw_Cfg.c and Clock_Hw.h have different vendor ids"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw.h file are of the same Autosar version */
#if ((CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_Cfg.c and Clock_Hw.h are different"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw.h file are of the same Software version */
#if ((CLOCK_HW_CFG_SW_MAJOR_VERSION_C != CLOCK_HW_SW_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_SW_MINOR_VERSION_C != CLOCK_HW_SW_MINOR_VERSION) || \
     (CLOCK_HW_CFG_SW_PATCH_VERSION_C != CLOCK_HW_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_Cfg.c and Clock_Hw.h are different"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Private.h file are of the same vendor */
#if (CLOCK_HW_CFG_VENDOR_ID_C != CLOCK_HW_PRIVATE_VENDOR_ID)
    #error "Clock_Hw_Cfg.c and Clock_Hw_Private.h have different vendor ids"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Private.h file are of the same Autosar version */
#if ((CLOCK_HW_CFG_AR_RELEASE_MAJOR_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_MINOR_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_MINOR_VERSION) || \
     (CLOCK_HW_CFG_AR_RELEASE_REVISION_VERSION_C != CLOCK_HW_PRIVATE_AR_RELEASE_REVISION_VERSION) \
    )
    #error "AutoSar Version Numbers of Clock_Hw_Cfg.c and Clock_Hw_Private.h are different"
#endif

/* Check if Clock_Hw_Cfg.c file and Clock_Hw_Private.h file are of the same Software version */
#if ((CLOCK_HW_CFG_SW_MAJOR_VERSION_C != CLOCK_HW_PRIVATE_SW_MAJOR_VERSION) || \
     (CLOCK_HW_CFG_SW_MINOR_VERSION_C != CLOCK_HW_PRIVATE_SW_MINOR_VERSION) || \
     (CLOCK_HW_CFG_SW_PATCH_VERSION_C != CLOCK_HW_PRIVATE_SW_PATCH_VERSION) \
    )
    #error "Software Version Numbers of Clock_Hw_Cfg.c and Clock_Hw_Private.h are different"
#endif


/*==================================================================================================
                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
                                        LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL VARIABLES
==================================================================================================*/


#define MCU_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"







#define MCU_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Mcu_MemMap.h"

/*==================================================================================================
                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

