/**
 * @file    Pit_Gpt_Hw_Cfg.h
 * @brief   Pit Gpt Cfg Head file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 */
 

#ifndef PIT_GPT_HW_CFG_H
#define PIT_GPT_HW_CFG_H


#include "Pit_Gpt_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PIT_GPT_HW_VENDOR_ID_CFG                       (110U)
#define PIT_GPT_HW_AR_RELEASE_MAJOR_VERSION_CFG        (4U)
#define PIT_GPT_HW_AR_RELEASE_MINOR_VERSION_CFG        (4U)
#define PIT_GPT_HW_AR_RELEASE_REVISION_VERSION_CFG     (0U)
#define PIT_GPT_HW_SW_MAJOR_VERSION_CFG                (1U)
#define PIT_GPT_HW_SW_MINOR_VERSION_CFG                (0U)
#define PIT_GPT_HW_SW_PATCH_VERSION_CFG                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

#if (PIT_GPT_HW_VENDOR_ID_CFG != PIT_GPT_HW_VENDOR_ID_PBCFG_H)
    #error "Pit_Gpt_Hw_Cfg.h and Pit_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((PIT_GPT_HW_AR_RELEASE_MAJOR_VERSION_CFG != PIT_GPT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (PIT_GPT_HW_AR_RELEASE_MINOR_VERSION_CFG != PIT_GPT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (PIT_GPT_HW_AR_RELEASE_REVISION_VERSION_CFG != PIT_GPT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Pit_Gpt_Hw_Cfg.h and Pit_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((PIT_GPT_HW_SW_MAJOR_VERSION_CFG != PIT_GPT_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (PIT_GPT_HW_SW_MINOR_VERSION_CFG != PIT_GPT_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (PIT_GPT_HW_SW_PATCH_VERSION_CFG != PIT_GPT_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Pit_Gpt_Hw_Cfg.h and Pit_Gpt_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
*brief Dev error detect switch
*/
#define PIT_GPT_HW_DEV_ERROR_DETECT (STD_ON)

/*==================================================================================================*/
/**
*brief    PIT_GPT_HW_CHAIN_MODE switch
*details  Enable/disable API for Chain Mode.
*/
#define PIT_GPT_HW_CHAIN_MODE (STD_ON)

/*================================================================================================*/
/**
*brief    PIT_GPT_HW_CHANGE_NEXT_TIMEOUT_VALUE switch
*details  Enable/disable support for changing timeout value during timer running
*/
#define PIT_GPT_HW_CHANGE_NEXT_TIMEOUT_VALUE     (STD_ON)

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** } */
#endif  /* PIT_GPT_HW_CFG_H */
