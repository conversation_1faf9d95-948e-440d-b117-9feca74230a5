/**
 * file    Dma_Hw_PBCfg.h
 * brief   Dma Hw PBCfg header file.
 * author  MiaoW
 * date    2025.6.16
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */

#ifndef DMA_HW_PBCFG_H
#define DMA_HW_PBCFG_H

/*==================================================================================================
*                                        INCLUDE FILES
* 1) system and project includes
* 2) needed interfaces from external units
* 3) internal and external interfaces from this unit
==================================================================================================*/
#include "mcal_stub.h" 

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define DMA_HW_PBCFG_VENDOR_ID                        (110U)
#define DMA_HW_PBCFG_AR_RELEASE_MAJOR_VERSION         (4U)
#define DMA_HW_PBCFG_AR_RELEASE_MINOR_VERSION         (4U)
#define DMA_HW_PBCFG_AR_RELEASE_REVISION_VERSION      (0U)
#define DMA_HW_PBCFG_SW_MAJOR_VERSION                 (1U)
#define DMA_HW_PBCFG_SW_MINOR_VERSION                 (0U)
#define DMA_HW_PBCFG_SW_PATCH_VERSION                 (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/
#define DMA_START_SEC_CODE
#include "Dma_MemMap.h"

extern void Dma_Adc0_CallBack(uint32 channel);
extern void Dma_Adc1_CallBack(uint32 channel);
extern void Lpuart_0_Uart_Hw_DmaTxCompleteCallback(uint32 channel);
extern void Lpuart_0_Uart_Hw_DmaRxCompleteCallback(uint32 channel);


#define DMA_STOP_SEC_CODE
#include "Dma_MemMap.h"


#ifdef __cplusplus
}
#endif

#endif /*DMA_HW_PBCFG_H*/

/*==================================================================================================
 *                                        END OF FILE
==================================================================================================*/
