/**
* 
* @file    Port_Hw_PBcfg.h
* @brief   Port registers defines
* @version V1.0.0
* <AUTHOR>
* @date 2024-06-05
* 
* @copyright Copyright (c) 2024 LANSHAN. All rights 
*
*
* THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
* IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
* OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
* IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
* INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
* (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
* SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
* HOW<PERSON><PERSON>R CAUSED AND ON ANY THEORY OF LIABILITY, WH<PERSON>H<PERSON> IN CONTRACT,
* STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
* IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
* THE POSSIBILITY OF SUCH DAMAGE.
* 
*/ 


#ifndef PORT_HW_PBCFG_H
#define PORT_HW_PBCFG_H

#ifdef __cplusplus
extern "C"{
#endif


/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
/**
* @brief        Parameters that shall be published within the Port driver header file and also in the
*               module description file
* @details      The integration of incompatible files shall be avoided.
*
*/
#define PORT_HW_VENDOR_ID_PBCFG_H                       (110U)
#define PORT_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H        (4U)
#define PORT_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H        (4U)
#define PORT_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H     (0U)
#define PORT_HW_SW_MAJOR_VERSION_PBCFG_H                (1U)
#define PORT_HW_SW_MINOR_VERSION_PBCFG_H                (0U)
#define PORT_HW_SW_PATCH_VERSION_PBCFG_H                (0U)


/*==================================================================================================
*                                      FILE VERSION CHECKS
==================================================================================================*/

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/*! @brief User number of configured pins */
#define NUM_OF_CONFIGURED_PINS 81
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
/*! @brief User configuration structure */






extern const PortPinSettingsConfig_t g_pin_mux_InitConfigArr[NUM_OF_CONFIGURED_PINS];
/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/


#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PORT_HW_PBCFG_H */
