/**
 * @file     Lin_Define.h
 * @brief    Lin_Define Software h files.
 * <AUTHOR>
 * @version  1.0.0
 * @date     2024-07-03
 *
 * @copyright  Copyright (c) 2024 LANSHAN. All rights reserved
 *
 * modify history:
 * Date        Version   Author    Description
 * 2024/7/3    1.0.0     zhangb     Initilization
 **/

#ifndef LIN_DEFINES_H
#define LIN_DEFINES_H


/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Mcal.h"

#ifdef __cplusplus
extern "C"
{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define LIN_DEFINES_VENDOR_ID                     (110u)
#define LIN_DEFINES_AR_RELEASE_MAJOR_VERSION      (4u)
#define LIN_DEFINES_AR_RELEASE_MINOR_VERSION      (4u)
#define LIN_DEFINES_AR_RELEASE_REVISION_VERSION   (0u)
#define LIN_DEFINES_SW_MAJOR_VERSION              (1u)
#define LIN_DEFINES_SW_MINOR_VERSION              (0u)
#define LIN_DEFINES_SW_PATCH_VERSION              (0u)
/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
    /* Checks against Mcal.h */
    #if ((LIN_DEFINES_AR_RELEASE_MAJOR_VERSION != MCAL_AR_RELEASE_MAJOR_VERSION) || \
         (LIN_DEFINES_AR_RELEASE_MINOR_VERSION != MCAL_AR_RELEASE_MINOR_VERSION))
        #error "AUTOSAR Version Numbers of Lin_Defines.h and Mcal.h are different"
    #endif
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/
/**
* @brief          Pre-compile Support.
*
*
*/
#define LIN_PRECOMPILE_SUPPORT  (STD_OFF) 

/**
* @brief   No of Channels configured.
*
*
*/
#define LIN_HW_NUM_OF_CHANNELS (2U)

/**
* @brief   Total number of available hardware lin channels.
*
*
*/
#define LIN_HW_MAX_AVAILABLE_MODULES (4U)

/**
* @brief   Switches the Development Error Detection and Notification ON or OFF.
*
*
*/
#define LIN_DEV_ERROR_DETECT (STD_ON)  /* Enable Development Error Detection */

/**
* @brief   Switches the Production Error Detection and Notification OFF
*
*
*/
#define LIN_DISABLE_DEM_REPORT_ERROR_STATUS  (STD_ON) /* Disable Production Error Detection */ 

/**
* @brief          Lin Master Node Used
* @details        When LinGlobalConfig/LinChannel/LinNodeType contains at least one MASTER channel.
*
*
*/
#define LIN_MASTER_NODE_USED  (STD_ON) /* Used Master Node */

/**
* @brief          Support for version info API.
* @details        Switches the Lin_GetVersionInfo() API ON or OFF.
*
*
*/
#define LIN_VERSION_INFO_API (STD_ON)  /* Enable API Lin_GetVersionInfo      */

/**
* @brief        All CoreIDs are supported by LIN driver.
*/

#define LIN_MAX_PARTITIONS     ((uint32)1U)

/**
* @brief          Multicore is enabled or not
*/
#define LIN_MULTICORE_SUPPORT   (STD_OFF) /* Multicore is disabled */

/**
* @brief          Enable Non-Autosar API for Dual-Clock support.
* @details        Enable/Disable API Lin_SetClockMode() to set the
*                 clock to be used by the LIN driver (Normal clock: default mode;
*                 Alternate clock: when the driver is in Low-Power mode).
*                 This can be set to STD_ON only if it is activated from xdm file:
*                 LinClockRef_Alternate is enable.
*
* @api
*/

#define LIN_DUAL_CLOCK_MODE  (STD_OFF) /* Disable API Lin_SetClockMode */ 

/**
* @brief          Lin channel Id
*/
#define LIN_CHANNEL_ID_0   ((uint8)0U)
#define LIN_CHANNEL_ID_1   ((uint8)1U)

        
/**
* @brief Macro which shows if at least one slave is present in the configuration
*/
#define LIN_SLAVE_NODE_USED     (STD_OFF) 

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
#define LIN_UNALLOCATEDPAR_CORE_ID  ((uint32)0x0U)

#if (STD_ON == LIN_MULTICORE_SUPPORT)
    #define Lin_GetCoreID() (OsIf_GetCoreID())
#else
    #define Lin_GetCoreID() (LIN_UNALLOCATEDPAR_CORE_ID)
#endif /* (STD_ON == LIN_MULTICORE_SUPPORT) */

/**
* @brief          LIN driver status initialization variable.
*/
#define LIN_UNINIT_ARRAY  {LIN_UNINIT}



    
    
    
    

/**
*
* @internal
* @brief          Lin Wakeup detection feature 
* @details        It is set to STD_ON when LinGlobalConfig/LinChannel/WakeupDetectionSupport is enabled.
*
*
*/
#define LIN_WAKEUP_DETECTION (STD_OFF) /* Support wakeup detection */
/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                  EXTERNAL CONSTANTS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* LIN_DEFINES_H */
