#ifndef POD_POWER_H
#define POD_POWER_H
/// ---------- begin of guard: <includes> -----------------------------------
/// ---------- end of guard:   <includes> -----------------------------------


/// ---------- begin of guard: <header> -------------------------------------
#define ACC_VOL_LIMIT      18  //V
#define DRIVER_TEMP_LIMIT  100 //℃
#define SEAT_NTC_LIMIT     50 //℃
/// ---------- end of guard:   <header> -------------------------------------

extern uint32 accVol10x;
extern uint32 seatTemp;
extern tMotor motors[MOTOR_MAX];

void pod_power_on_init(tHsmGenCurr* const ev);
void pod_power_on_tm10ms(tHsmGenCurr* const ev);

/// ---------- begin of guard: <tail> ---------------------------------------
/// ---------- end of guard:   <tail> ---------------------------------------

#endif // POD_POWER_H