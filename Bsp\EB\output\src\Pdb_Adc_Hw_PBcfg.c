/**
 * file    Pdb_Adc_Hw_PBcfg.c
 * brief   
 * author  
 * date    2024-06-06
 * version 1.0.0
 * copyright (c) 2024 LANSHAN. All rights reserved.
 */


/**
*   @file
*
*   @addtogroup pdb_adc_hw_config Pdb Adc IPL Configuration
*   @{
*/

/*==================================================================================================
*                                        INCLUDE FILES
==================================================================================================*/
#include "Std_Types.h"
#include "Pdb_Adc_Hw_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PDB_ADC_HW_VENDOR_ID_PBCFG_C                     (110u)
#define PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C      (4u)
#define PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C      (4u)
#define PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C   (0u)
#define PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG_C              (1u)
#define PDB_ADC_HW_SW_MINOR_VERSION_PBCFG_C              (0u)
#define PDB_ADC_HW_SW_PATCH_VERSION_PBCFG_C              (0u)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/
#ifndef DISABLE_MCAL_INTERMODULE_ASR_CHECK
/* Check if Adc_Hw_PBcfg.c file and Std_Types.h file are of the same Autosar version */
#if ((PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != STD_AR_RELEASE_MAJOR_VERSION) || \
     (PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != STD_AR_RELEASE_MINOR_VERSION)    \
    )
    #error "AutoSar Version Numbers of Pdb_Adc_Hw_PBcfg.c and Std_Types.h are different"
#endif
#endif

/* Check if Pdb_Adc_Hw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same vendor */
#if (PDB_ADC_HW_VENDOR_ID_PBCFG_C != PDB_ADC_HW_VENDOR_ID_PBCFG)
    #error "Pdb_Adc_Hw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h have different vendor ids"
#endif

/* Check if Pdb_Adc_Hw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same Autosar version */
#if ((PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_MINOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_C != PDB_ADC_HW_AR_RELEASE_REVISION_VERSION_PBCFG) \
    )
    #error "AutoSar Version Numbers of Pdb_Adc_Hw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h are different"
#endif

/* Check if Pdb_Adc_Hw_PBcfg.c file and Pdb_Adc_Hw_PBcfg.h file are of the same Software version */
#if ((PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG_C != PDB_ADC_HW_SW_MAJOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_SW_MINOR_VERSION_PBCFG_C != PDB_ADC_HW_SW_MINOR_VERSION_PBCFG) || \
     (PDB_ADC_HW_SW_PATCH_VERSION_PBCFG_C != PDB_ADC_HW_SW_PATCH_VERSION_PBCFG) \
    )
  #error "Software Version Numbers of Pdb_Adc_Hw_PBcfg.c and Pdb_Adc_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                          LOCAL TYPEDEFS (STRUCTURES, UNIONS, ENUMS)
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL MACROS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      LOCAL VARIABLES
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      GLOBAL VARIABLES
==================================================================================================*/

#define ADC_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"




/**
* @brief          PDB Hw channel Config for Hardware Unit ADC0 for configuration variant .
*/
const Pdb_Adc_Hw_ChanConfig_t PdbAdcHwChConfig_0[2]= 
{

    {        
        0, /* ChnIdx */
        {
            255u, /* EnableMask */
            255u, /* EnableDelayMask */
            0u, /* BackToBackEnableMask */
        },
        {
            0u, /* Pretrigger0 Delay Value */
            0u, /* Pretrigger1 Delay Value */
            0u, /* Pretrigger2 Delay Value */
            0u, /* Pretrigger3 Delay Value */
            0u, /* Pretrigger4 Delay Value */
            0u, /* Pretrigger5 Delay Value */
            0u, /* Pretrigger6 Delay Value */
            0u, /* Pretrigger7 Delay Value */
        }
    },
    {        
        1, /* ChnIdx */
        {
            3u, /* EnableMask */
            3u, /* EnableDelayMask */
            0u, /* BackToBackEnableMask */
        },
        {
            0u, /* Pretrigger0 Delay Value */
            0u, /* Pretrigger1 Delay Value */
            0u, /* Pretrigger2 Delay Value */
            0u, /* Pretrigger3 Delay Value */
            0u, /* Pretrigger4 Delay Value */
            0u, /* Pretrigger5 Delay Value */
            0u, /* Pretrigger6 Delay Value */
            0u, /* Pretrigger7 Delay Value */
        }
    }


}; 


/**
* @brief          PDB Hw Config for Hardware Unit ADC0 for configuration variant .
*/
const Pdb_Adc_Hw_Config_t PdbAdcHwConfig_0 =
{
    PDB_ADC_HW_LOAD_IMMEDIATELY, /* LoadValueMode */
    PDB_ADC_HW_CLK_PREDIV_BY_128, /* PrescalerDiv */
    PDB_ADC_HW_CLK_PREMULT_FACT_40, /* ClkPreMultFactor */
    PDB_ADC_HW_SOFTWARE_TRIGGER, /* TriggerSource */
    (boolean)TRUE, /* ContinuousModeEnable */
    (boolean)FALSE,  /* Pdb Error Interrupt Enable Status */
    {
        (boolean)TRUE, /* Pdb Interrupt Enable Status */
        PDB_SELECT_INTERRUPT /* Pdb Interrupt Trigger mode */
    },
    65534u, /* ModValue */
    0u, /* Pdb Interrupt Delay */
#if  (STD_ON == FEATURE_PDB_ENABLE_BACKTOBACK)
    (boolean)FALSE, /* PDB BackToBack Enable */
    PDB_PDB0_CH0_PDB0_CH1,	/*InstanceBackToBackMode*/
#endif /* (STD_ON == FEATURE_PDB_ENABLE_BACKTOBACK) .*/
    2u, /* NumChans */
    PdbAdcHwChConfig_0,
    NULL_PTR, /* SeqErrNotification */
    NULL_PTR, /* Adc Pdb Interrupt Notification */
	{
		(boolean)FALSE, /* Pulse output enable */
		0u,	/* Pulse high duration */
		0u	/* Pulse low duration */
	}
    
};

/**
* @brief          PDB Hw channel Config for Hardware Unit ADC1 for configuration variant .
*/
const Pdb_Adc_Hw_ChanConfig_t PdbAdcHwChConfig_1[2]= 
{

    {        
        0, /* ChnIdx */
        {
            255u, /* EnableMask */
            255u, /* EnableDelayMask */
            0u, /* BackToBackEnableMask */
        },
        {
            0u, /* Pretrigger0 Delay Value */
            0u, /* Pretrigger1 Delay Value */
            0u, /* Pretrigger2 Delay Value */
            0u, /* Pretrigger3 Delay Value */
            0u, /* Pretrigger4 Delay Value */
            0u, /* Pretrigger5 Delay Value */
            0u, /* Pretrigger6 Delay Value */
            0u, /* Pretrigger7 Delay Value */
        }
    },
    {        
        1, /* ChnIdx */
        {
            1u, /* EnableMask */
            1u, /* EnableDelayMask */
            0u, /* BackToBackEnableMask */
        },
        {
            0u, /* Pretrigger0 Delay Value */
            0u, /* Pretrigger1 Delay Value */
            0u, /* Pretrigger2 Delay Value */
            0u, /* Pretrigger3 Delay Value */
            0u, /* Pretrigger4 Delay Value */
            0u, /* Pretrigger5 Delay Value */
            0u, /* Pretrigger6 Delay Value */
            0u, /* Pretrigger7 Delay Value */
        }
    }


}; 


/**
* @brief          PDB Hw Config for Hardware Unit ADC1 for configuration variant .
*/
const Pdb_Adc_Hw_Config_t PdbAdcHwConfig_1 =
{
    PDB_ADC_HW_LOAD_IMMEDIATELY, /* LoadValueMode */
    PDB_ADC_HW_CLK_PREDIV_BY_128, /* PrescalerDiv */
    PDB_ADC_HW_CLK_PREMULT_FACT_40, /* ClkPreMultFactor */
    PDB_ADC_HW_SOFTWARE_TRIGGER, /* TriggerSource */
    (boolean)TRUE, /* ContinuousModeEnable */
    (boolean)FALSE,  /* Pdb Error Interrupt Enable Status */
    {
        (boolean)TRUE, /* Pdb Interrupt Enable Status */
        PDB_SELECT_INTERRUPT /* Pdb Interrupt Trigger mode */
    },
    65534u, /* ModValue */
    0u, /* Pdb Interrupt Delay */
#if  (STD_ON == FEATURE_PDB_ENABLE_BACKTOBACK)
    (boolean)FALSE, /* PDB BackToBack Enable */
    PDB_PDB0_CH0_PDB0_CH1,	/*InstanceBackToBackMode*/
#endif /* (STD_ON == FEATURE_PDB_ENABLE_BACKTOBACK) .*/
    2u, /* NumChans */
    PdbAdcHwChConfig_1,
    NULL_PTR, /* SeqErrNotification */
    NULL_PTR, /* Adc Pdb Interrupt Notification */
	{
		(boolean)FALSE, /* Pulse output enable */
		0u,	/* Pulse high duration */
		0u	/* Pulse low duration */
	}
    
};



#define ADC_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Adc_MemMap.h"

/*==================================================================================================
*                                   LOCAL FUNCTION PROTOTYPES
==================================================================================================*/

/*==================================================================================================
*                                       LOCAL FUNCTIONS
==================================================================================================*/

/*==================================================================================================
*                                       GLOBAL FUNCTIONS
==================================================================================================*/

#ifdef __cplusplus
}
#endif

/** @} */

