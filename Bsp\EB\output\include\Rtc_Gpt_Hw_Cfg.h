/**
 * @file    Rtc_Gpt_Hw_Cfg.h
 * @brief   Rtc Gpt Hw Cfg file
 * <AUTHOR>
 * @date    2024.6.10
 * @version 1.0.0
 * @copyright (c) 2024 LANSHAN. All rights reserved
 *
 * @section [global]
 * Violates MISRA C 2012 Required Rule 5.4, Macro identifiers shall be distinct.
 * REASON: Here is the version number information, which is similar
 */

#ifndef RTC_GPT_HW_CFG_H
#define RTC_GPT_HW_CFG_H

/* Include all variants header files. */

#include "Rtc_Gpt_Hw_PBcfg.h"


#ifdef __cplusplus
extern "C"{
#endif

/*==================================================================================================
*                              SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define RTC_HW_VENDOR_ID_CFG                       (110U)
#define RTC_HW_AR_RELEASE_MAJOR_VERSION_CFG        (4U)
#define RTC_HW_AR_RELEASE_MINOR_VERSION_CFG        (4U)
#define RTC_HW_AR_RELEASE_REVISION_VERSION_CFG     (0U)
#define RTC_HW_SW_MAJOR_VERSION_CFG                (1U)
#define RTC_HW_SW_MINOR_VERSION_CFG                (0U)
#define RTC_HW_SW_PATCH_VERSION_CFG                (0U)

/*==================================================================================================
*                                     FILE VERSION CHECKS
==================================================================================================*/

#if (RTC_HW_VENDOR_ID_CFG != RTC_HW_VENDOR_ID_PBCFG_H)
    #error "Rtc_Gpt_Hw_Cfg.h and Rtc_Gpt_Hw_PBcfg.h have different vendor ids"
#endif
/* Check if this header file and header file are of the same Autosar version */
#if ((RTC_HW_AR_RELEASE_MAJOR_VERSION_CFG != RTC_HW_AR_RELEASE_MAJOR_VERSION_PBCFG_H) || \
     (RTC_HW_AR_RELEASE_MINOR_VERSION_CFG != RTC_HW_AR_RELEASE_MINOR_VERSION_PBCFG_H) || \
     (RTC_HW_AR_RELEASE_REVISION_VERSION_CFG != RTC_HW_AR_RELEASE_REVISION_VERSION_PBCFG_H) \
    )
    #error "AutoSar Version Numbers of Rtc_Gpt_Hw_Cfg.h and Rtc_Gpt_Hw_PBcfg.h are different"
#endif
/* Check if this header file and header file are of the same Software version */
#if ((RTC_HW_SW_MAJOR_VERSION_CFG != RTC_HW_SW_MAJOR_VERSION_PBCFG_H) || \
     (RTC_HW_SW_MINOR_VERSION_CFG != RTC_HW_SW_MINOR_VERSION_PBCFG_H) || \
     (RTC_HW_SW_PATCH_VERSION_CFG != RTC_HW_SW_PATCH_VERSION_PBCFG_H) \
    )
    #error "Software Version Numbers of Rtc_Gpt_Hw_Cfg.h and Rtc_Gpt_Hw_PBcfg.h are different"
#endif

/*==================================================================================================
*                                          CONSTANTS
==================================================================================================*/

/*==================================================================================================
*                                      DEFINES AND MACROS
==================================================================================================*/
/**
 * @brief Dev error detect switch
 *
 */
#define RTC_HW_DEV_ERROR_DETECT (STD_ON)

/*================================================================================================*/
/**
 * @brief    RTC_HW_STANDBY_WAKEUP_SUPPORT
 * @details  StandBy Mode is not supported in this release
 *
 */
#define RTC_HW_STANDBY_WAKEUP_SUPPORT    (STD_OFF)

/*==================================================================================================
*                                             ENUMS
==================================================================================================*/

/*==================================================================================================
*                                STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/

/*==================================================================================================
*                                GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/

/*==================================================================================================
*                                    FUNCTION PROTOTYPES
==================================================================================================*/

#ifdef __cplusplus
}
#endif
/** } */
#endif  /* RTC_GPT_HW_CFG_H */
