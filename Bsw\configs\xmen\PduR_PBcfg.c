/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : PduR_PBcfg.c 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-06-01 13:56:06 
*  @date               : 2025-04-14 18:11:39 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#include "PduR.h"
#include "CanIf.h"
#include "CanTp.h"
#include "Com_Cbk.h"
#include "Dcm.h"
#include "Dcm_Cbk.h"
#include "LinIf.h"
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/
#define PDUR_PBCFG_C_AR_MAJOR_VERSION  4u
#define PDUR_PBCFG_C_AR_MINOR_VERSION  2u
#define PDUR_PBCFG_C_AR_PATCH_VERSION  2u

/*******************************************************************************
**                      Version Check                                         **
*******************************************************************************/
#if (PDUR_PBCFG_C_AR_MAJOR_VERSION != PDUR_PBCFG_H_AR_MAJOR_VERSION)
    #error "PduR.c : Mismatch in Specification Major Version"
#endif
#if (PDUR_PBCFG_C_AR_MINOR_VERSION != PDUR_PBCFG_H_AR_MINOR_VERSION)
    #error "PduR.c : Mismatch in Specification Major Version"
#endif
#if (PDUR_PBCFG_C_AR_PATCH_VERSION != PDUR_PBCFG_H_AR_PATCH_VERSION)
    #error "PduR.c : Mismatch in Specification Major Version"
#endif

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/

/*******************************************************************************
**                      Global Data Types                                     **
*******************************************************************************/

/*******************************************************************************
**                      Global Symbols                                        **
*******************************************************************************/

#define PDUR_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
static CONST(PduRDestPduType,PDUR_CONST)
PduR_DestPduConfigData[PDUR_DEST_PDU_SUM] =
{
    {
       /* 0 PDUR_DESTPDU_COM_CCU_1, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_CCU_1,
        COM_RXPDU_COM_CCU_1,
        PDUR_DIRECT,

    },
    {
       /* 1 PDUR_DESTPDU_COM_CCU_ZCUL_HVSPRL_1, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1,
        COM_RXPDU_COM_CCU_ZCUL_HVSPRL_1,
        PDUR_DIRECT,

    },
    {
       /* 2 PDUR_DESTPDU_COM_CCU_ZCUR_HVSPRR_1, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1,
        COM_RXPDU_COM_CCU_ZCUR_HVSPRR_1,
        PDUR_DIRECT,

    },
    {
       /* 3 PDUR_DESTPDU_COM_IDC_1, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_IDC_1,
        COM_RXPDU_COM_IDC_1,
        PDUR_DIRECT,

    },
    {
       /* 4 PDUR_DESTPDU_DCM_Func_Diag_Request, */
        PDUR_DCM,
        FALSE,

        PDUR_SRCPDU_CanTP_Func_Diag_Request,
        DCM_DCM_Func_Diag_Request,
        PDUR_DIRECT,

    },
    {
       /* 5 PDUR_DESTPDU_DCM_Phys_Diag_Request, */
        PDUR_DCM,
        FALSE,

        PDUR_SRCPDU_CanTP_Phys_Diag_Request,
        DCM_DCM_Phys_Diag_Request,
        PDUR_DIRECT,

    },
    {
       /* 6 PDUR_DESTPDU_CD_1, */
        PDUR_CANIF,
        FALSE,

        PDUR_SRCPDU_COM_CD_1,
        CANIF_TXPDU_CD_1,
        PDUR_DIRECT,

    },
    {
       /* 7 PDUR_DESTPDU_CanTP_Diag_Resp, */
        PDUR_CANTP,
        FALSE,

        PDUR_SRCPDU_DCM_Diag_Resp,
        CANTP_CanTP_Diag_Resp,
        PDUR_DIRECT,

    },
    {
       /* 8 PDUR_DESTPDU_DSM_LIN1, */
        PDUR_LINIF,
        FALSE,

        PDUR_SRCPDU_Com_DSM_LIN1,
        LINIF_TXPDU_DSM_LIN1,
        PDUR_DIRECT,

    },
    {
       /* 9 PDUR_DESTPDU_Com_MLCU_DSCU_1, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_MLCU_DSCU_1,
        COM_RXPDU_Com_MLCU_DSCU_1,
        PDUR_DIRECT,

    },
    {
       /* 10 PDUR_DESTPDU_Com_MLCU_DSCU_2, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_MLCU_DSCU_2,
        COM_RXPDU_Com_MLCU_DSCU_2,
        PDUR_DIRECT,

    },
    {
       /* 11 PDUR_DESTPDU_Com_DSS_DSCU, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_DSS_DSCU,
        COM_RXPDU_Com_DSS_DSCU,
        PDUR_DIRECT,

    },
    {
       /* 12 PDUR_DESTPDU_Com_PSS_DSCU, */
        PDUR_COM,
        FALSE,

        PDUR_SRCPDU_PSS_DSCU,
        COM_RXPDU_Com_PSS_DSCU,
        PDUR_DIRECT,

    },
};
#define PDUR_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"

#define PDUR_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
static CONST(PduRSrcPduType,PDUR_CONST)
PduR_SrcPduConfigData[PDUR_SRC_PDU_SUM] =
{
    {
        /*0 PDUR_SRCPDU_CCU_1*/ 
        TRUE,
        PDUR_CANIF,

        PDUR_UNUSED_UINT16,
        CANIF_RXPDU_CCU_1,

    },
    {
        /*1 PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1*/ 
        TRUE,
        PDUR_CANIF,

        PDUR_UNUSED_UINT16,
        CANIF_RXPDU_CCU_ZCUL_HVSPRL_1,

    },
    {
        /*2 PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1*/ 
        TRUE,
        PDUR_CANIF,

        PDUR_UNUSED_UINT16,
        CANIF_RXPDU_CCU_ZCUR_HVSPRR_1,

    },
    {
        /*3 PDUR_SRCPDU_IDC_1*/ 
        TRUE,
        PDUR_CANIF,

        PDUR_UNUSED_UINT16,
        CANIF_RXPDU_IDC_1,

    },
    {
        /*4 PDUR_SRCPDU_CanTP_Func_Diag_Request*/ 
        TRUE,
        PDUR_CANTP,

        PDUR_UNUSED_UINT16,
        CANTP_CanTP_Func_Diag_Request,

    },
    {
        /*5 PDUR_SRCPDU_CanTP_Phys_Diag_Request*/ 
        TRUE,
        PDUR_CANTP,

        PDUR_UNUSED_UINT16,
        CANTP_CanTP_Phys_Diag_Request,

    },
    {
        /*6 PDUR_SRCPDU_COM_CD_1*/ 
        TRUE,
        PDUR_COM,

        PDUR_UNUSED_UINT16,
        COM_TXPDU_COM_CD_1,

    },
    {
        /*7 PDUR_SRCPDU_DCM_Diag_Resp*/ 
        TRUE,
        PDUR_DCM,

        PDUR_UNUSED_UINT16,
        DCM_DCM_Diag_Resp,

    },
    {
        /*8 PDUR_SRCPDU_Com_DSM_LIN1*/ 
        TRUE,
        PDUR_COM,

        PDUR_UNUSED_UINT16,
        COM_TXPDU_Com_DSM_LIN1,

    },
    {
        /*9 PDUR_SRCPDU_MLCU_DSCU_1*/ 
        TRUE,
        PDUR_LINIF,

        PDUR_UNUSED_UINT16,
        0xffff,

    },
    {
        /*10 PDUR_SRCPDU_MLCU_DSCU_2*/ 
        TRUE,
        PDUR_LINIF,

        PDUR_UNUSED_UINT16,
        0xffff,

    },
    {
        /*11 PDUR_SRCPDU_DSS_DSCU*/ 
        TRUE,
        PDUR_LINIF,

        PDUR_UNUSED_UINT16,
        0xffff,

    },
    {
        /*12 PDUR_SRCPDU_PSS_DSCU*/ 
        TRUE,
        PDUR_LINIF,

        PDUR_UNUSED_UINT16,
        0xffff,

    },
};
#define PDUR_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"

/* PRQA S 0779,0779 ++ */ /* MISRA Rule 1.3,Rule 5.2 */
#define PDUR_START_SEC_PBCONFIG_DATA_16
#include "PduR_MemMap.h"
static CONST(uint16, PDUR_CONST) PduR_PduRDestPduIdRef[] =
{

    PDUR_DESTPDU_COM_CCU_1,      /* PDUR_SRCPDU_CCU_1 */

    PDUR_DESTPDU_COM_CCU_ZCUL_HVSPRL_1,      /* PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1 */

    PDUR_DESTPDU_COM_CCU_ZCUR_HVSPRR_1,      /* PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1 */

    PDUR_DESTPDU_COM_IDC_1,      /* PDUR_SRCPDU_IDC_1 */

    PDUR_DESTPDU_DCM_Func_Diag_Request,      /* PDUR_SRCPDU_CanTP_Func_Diag_Request */

    PDUR_DESTPDU_DCM_Phys_Diag_Request,      /* PDUR_SRCPDU_CanTP_Phys_Diag_Request */

    PDUR_DESTPDU_CD_1,      /* PDUR_SRCPDU_COM_CD_1 */

    PDUR_DESTPDU_CanTP_Diag_Resp,      /* PDUR_SRCPDU_DCM_Diag_Resp */

    PDUR_DESTPDU_DSM_LIN1,      /* PDUR_SRCPDU_Com_DSM_LIN1 */

    PDUR_DESTPDU_Com_MLCU_DSCU_1,      /* PDUR_SRCPDU_MLCU_DSCU_1 */

    PDUR_DESTPDU_Com_MLCU_DSCU_2,      /* PDUR_SRCPDU_MLCU_DSCU_2 */

    PDUR_DESTPDU_Com_DSS_DSCU,      /* PDUR_SRCPDU_DSS_DSCU */

    PDUR_DESTPDU_Com_PSS_DSCU,      /* PDUR_SRCPDU_PSS_DSCU */

};
#define PDUR_STOP_SEC_PBCONFIG_DATA_16
#include "PduR_MemMap.h"

/* PRQA S 0779,0779 -- */ /* MISRA Rule 1.3,Rule 5.2 */
#define PDUR_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
static CONST(PduRRoutingPathType,PDUR_CONST)
PduR_RoutingPathConfigData0[13] =
{

    {
        /*0 PDUR_SRCPDU_CCU_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[0],

    },
    {
        /*1 PDUR_SRCPDU_CCU_ZCUL_HVSPRL_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[1],

    },
    {
        /*2 PDUR_SRCPDU_CCU_ZCUR_HVSPRR_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[2],

    },
    {
        /*3 PDUR_SRCPDU_IDC_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[3],

    },
    {
        /*4 PDUR_SRCPDU_CanTP_Func_Diag_Request*/ 

        TRUE,

        1u,

        &PduR_PduRDestPduIdRef[4],

    },
    {
        /*5 PDUR_SRCPDU_CanTP_Phys_Diag_Request*/ 

        TRUE,

        1u,

        &PduR_PduRDestPduIdRef[5],

    },
    {
        /*6 PDUR_SRCPDU_COM_CD_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[6],

    },
    {
        /*7 PDUR_SRCPDU_DCM_Diag_Resp*/ 

        TRUE,

        1u,

        &PduR_PduRDestPduIdRef[7],

    },
    {
        /*8 PDUR_SRCPDU_Com_DSM_LIN1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[8],

    },
    {
        /*9 PDUR_SRCPDU_MLCU_DSCU_1*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[9],

    },
    {
        /*10 PDUR_SRCPDU_MLCU_DSCU_2*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[10],

    },
    {
        /*11 PDUR_SRCPDU_DSS_DSCU*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[11],

    },
    {
        /*12 PDUR_SRCPDU_PSS_DSCU*/ 

        FALSE,

        1u,

        &PduR_PduRDestPduIdRef[12],

    },
};
#define PDUR_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"

#define PDUR_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
static CONST(PduRRoutingTableType,PDUR_CONST)
PduR_RoutingTableConfigData[1] =
{
    {
        PduR_RoutingPathConfigData0,
    },
};
#define PDUR_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"

#define PDUR_START_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
/* PRQA S 1531 ++ */ /* MISRA Rule 8.7 */
CONST(PduR_PBConfigType, PDUR_CONST_PBCFG) PduR_PBConfigData =
/* PRQA S 1531 -- */ /* MISRA Rule 8.7 */
{
    0u,
    PDUR_ROUTING_PATH_GROUP_SUM,
    PDUR_SRC_PDU_SUM,
    PDUR_DEST_PDU_SUM,NULL_PTR,
    PduR_RoutingTableConfigData,
    PduR_SrcPduConfigData,
    PduR_DestPduConfigData,
};
#define PDUR_STOP_SEC_PBCONFIG_DATA_UNSPECIFIED
#include "PduR_MemMap.h"
/*******************************************************************************
**                      End of file                                           **
*******************************************************************************/

