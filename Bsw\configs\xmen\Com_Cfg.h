/**********************************************************************************************************************
* COPYRIGHT 
* ------------------------------------------------------------------------------------------------------------------- 
* Copyright (c) iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. This software is proprietary to 
* iSOFT INFRASTRUCTURE SOFTWARE CO., LTD., and all rights are reserved by iSOFT INFRASTRUCTURE SOFTWARE CO., LTD. 
* Without the express written permission of the company, no organization or individual may copy, install, trial, 
* distribute, or reverse engineer this software. For terms of use and further details, please refer to the End User 
* License Agreement (EULA) or <NAME_EMAIL> for more assistance. 
* 
* This file contains code from EasyXMen, which is licensed under the LGPL-2.1. However, due to a special exception, 
* you are not required to comply with the provisions of section 6a of LGPL-2.1. Specifically, you may distribute 
* your software, including this file, under terms of your choice, including proprietary licenses, without needing to 
* provide the source code or object code as specified in section 6a. For more details, please refer to the project's 
* LICENSE and EXCEPTION files and the specific exception statement.  
* ------------------------------------------------------------------------------------------------------------------- 
* FILE DESCRIPTION 
* ------------------------------------------------------------------------------------------------------------------- 
*  @MCU                : S32K148 
*  @file               : Com_Cfg.h 
*  @license            : Evaliation 
*  @licenseExpiryDate  : 2025-09-01 09:26:30 
*  @date               : 2025-06-03 11:13:57 
*  @customer           : EasyXMen User 
*  @toolVersion        : 2.0.18 
*********************************************************************************************************************/ 
/*******************************************************************************
**                      Include Section                                       **
*******************************************************************************/
#ifndef  COM_CFG_H
#define  COM_CFG_H

#include "ComStack_Types.h"
/*******************************************************************************
**                      Revision Control History                              **
*******************************************************************************/

/*******************************************************************************
**                      Version Information                                   **
*******************************************************************************/
#define COM_CFG_H_AR_MAJOR_VERSION    4u
#define COM_CFG_H_AR_MINOR_VERSION    2u
#define COM_CFG_H_AR_PATCH_VERSION    2u
#define COM_CFG_H_SW_MAJOR_VERSION    2u
#define COM_CFG_H_SW_MINOR_VERSION    0u
#define COM_CFG_H_SW_PATCH_VERSION    0u

/*******************************************************************************
**                      Macros                                                **
*******************************************************************************/

#define COM_CANCELLATION_SUPPORT                     STD_OFF
#define COM_DEV_ERROR_DETECT                         STD_OFF
#define COM_ENABLE_SIGNAL_GROUP_ARRAY_API            STD_OFF
#define COM_VERSION_INFO_API                         STD_OFF
#define COM_ENABLE_MDT_FOR_CYCLIC_TRANSMISSION       STD_ON
#define COM_METADATA_SUPPORT                         STD_OFF
#define COM_RETRY_FAILED_TRANSMIT_REQUESTS           STD_OFF

#define COM_SIGNAL_BOOLBUFF_SIZE            6u
#define COM_RXGROUPSIGNAL_BOOLBUFF_SIZE     0u
#define COM_SIGNAL_BOOL_INVALID_SIZE        0u
#define COM_SIGNAL_BOOL_SUBSTITUTE_SIZE            0u

#define COM_SIGNAL_8BITBUFF_SIZE            177u
#define COM_RXGROUPSIGNAL_8BITBUFF_SIZE     0u
#define COM_SIGNAL_8BIT_INVALID_SIZE        149u
#define COM_SIGNAL_8BIT_SUBSTITUTE_SIZE            0u

#define COM_SIGNAL_16BITBUFF_SIZE           5u
#define COM_RXGROUPSIGNAL_16BITBUFF_SIZE    0u
#define COM_SIGNAL_16BIT_INVALID_SIZE       5u
#define COM_SIGNAL_16BIT_SUBSTITUTE_SIZE           0u

#define COM_SIGNAL_32BITBUFF_SIZE           0u
#define COM_RXGROUPSIGNAL_32BITBUFF_SIZE    0u
#define COM_SIGNAL_32BIT_INVALID_SIZE       0u
#define COM_SIGNAL_32BIT_SUBSTITUTE_SIZE           0u

#define COM_SIGNAL_64BITBUFF_SIZE           2u
#define COM_RXGROUPSIGNAL_64BITBUFF_SIZE    0u
#define COM_SIGNAL_64BIT_INVALID_SIZE       2u
#define COM_SIGNAL_64BIT_SUBSTITUTE_SIZE           0u

#define COM_GWSIGNAL_BOOLBUFF_SIZE          0u
#define COM_GWSIGNAL_8BITBUFF_SIZE          0u
#define COM_GWSIGNAL_16BITBUFF_SIZE         0u
#define COM_GWSIGNAL_32BITBUFF_SIZE         0u
#define COM_GWSIGNAL_64BITBUFF_SIZE         0u

#define COM_RXIPDUBUFF_SIZE        224u
#define COM_TXIPDUBUFF_SIZE        16u

#define COM_RXIPDUGROUP_NUMBER                 8u

#define COM_TXIPDUGROUP_NUMBER                 2u

#define COM_IPDUGROUP_NUMBER                         4u
#define Com_RxPduGroup_CanController_0        0u
#define ComIPduGroup_DSCU_Rx        1u
#define Com_TxPduGroup_CanController_0        2u
#define ComIPduGroup_DSCU_Tx        3u

#define COM_RXIPDU_CALLOUT_NUMBER   4u

#define COM_RXIPDU_GW_NUMBER         0u

#define COM_RXIPDU_COUNTER_NUMBER   0u

#define COM_TXIPDU_COUNTER_NUMBER   0u

#define COM_TX_MODE_TRUE_PERIOD_NUMBER   0u

#define COM_TX_MODE_FALSE_PERIOD_NUMBER    0u

#define COM_TX_MODE_TRUE_DIRECT_NUMBER   0u

#define COM_TX_MODE_FALSE_DIRECT_NUMBER   0u

#define COM_TX_MODE_TRUE_MIXED_NUMBER    0u

#define COM_TX_MODE_FALSE_MIXED_NUMBER    0u

#define COM_TX_MODE_TRUE_DIRECT_NOREPETITION_NUMBER   0u

#define COM_TX_MODE_FALSE_DIRECT_NOREPETITION_NUMBER   0u

#define COM_TX_MODE_TRUE_MIXED_NOREPETITION_NUMBER    0u

#define COM_TX_MODE_FALSE_MIXED_NOREPETITION_NUMBER    0u

#define COM_RXIPDU_NUMBER        8u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define COM_RXPDU_COM_CCU_1        0u
#define COM_RXPDU_COM_CCU_ZCUL_HVSPRL_1        1u
#define COM_RXPDU_COM_CCU_ZCUR_HVSPRR_1        2u
#define COM_RXPDU_COM_IDC_1        3u
#define COM_RXPDU_Com_MLCU_DSCU_1        4u
#define COM_RXPDU_Com_MLCU_DSCU_2        5u
#define COM_RXPDU_Com_DSS_DSCU        6u
#define COM_RXPDU_Com_PSS_DSCU        7u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define COM_TXIPDU_NUMBER    2u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define COM_TXPDU_COM_CD_1        8u
#define COM_TXPDU_Com_DSM_LIN1        9u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define COM_SIGNALGROUPARRY_NUMBER  0u

#define COM_RXSIG_TIMEOUT_NUMBER                       0u

#define COM_RXSIGGRP_TIMEOUT_NUMBER                    0u

#define  COM_RXSIGNAL_INVALID_NUMBER        11u

#define  COM_TXSIGNAL_INVALID_NUMBER        5u

#define  COM_RXGRPSIG_INVALID_NUMBER        0u

#define  COM_TXGRPSIG_INVALID_NUMBER        0u

#define COM_RX_SIGNAL_8BIT_INVALIDVALUE_NUMBER    0u

#define COM_TX_SIGNAL_8BIT_INVALIDVALUE_NUMBER   0u

#define COM_RXSIGNAL_FILTERTYPE_MAX_NUMBER  0u

#define COM_TXSIGNAL_FILTERTYPE_MAX_NUMBER  0u

#define COM_RXGRPSIG_FILTERTYPE_MAX_NUMBER  0u

#define COM_TXGRPSIG_FILTERTYPE_MAX_NUMBER  0u

#define COM_DESTSIG_FILTERTYPE_MAX_NUMBER    0u

#define COM_TMCTXSIGNAL_NUMBER              0u

 #define COM_TXSIG_ERROR_AND_NOTIFY_NUMBER  0u

 #define COM_TXSIGGRP_ERROR_AND_NOTIFY_NUMBER  0u

#define COM_ONEEVERYNFILTERSIGNAL_NUMBER           0u
#define COM_RXMASKNEWDIFFERMASKOLD_NUMBER    0u

#define COM_RXMASKNEWDIFFERX_NUMBER   0u

#define COM_RXMASKNEWEQUALSX_NUMBER   0u

#define COM_RXNEWISOUTSIDE_NUMBER   0u

#define COM_RXNEWISWITHIN_NUMBER   0u

#define COM_TXMASKNEWDIFFERMASKOLD_NUMBER   0u

#define COM_TXMASKNEWDIFFERX_NUMBER   0u

#define COM_TXMASKNEWEQUALSX_NUMBER   0u

#define COM_TXNEWISOUTSIDE_NUMBER   0u

#define COM_TXNEWISWITHIN_NUMBER   0u

#define COM_TXSIGNAL_TIMEOUT_NUMBER    0u

#define COM_TXSIGGRP_TIMEOUT_NUMBER    0u

#define COM_RXSIGNAL_NUMBER               37u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define UMM_UsageModeSt_IPDU_COM_CCU_1             0u
#define HVSPRL_CDPwrReq_IPDU_COM_CCU_ZCUL_HVSPRL_1             1u
#define HVSPRL_CDdisplaySetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1             2u
#define HVSPRL_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1             3u
#define HVSPRL_CDbrightnessSetReq_IPDU_COM_CCU_ZCUL_HVSPRL_1             4u
#define HVSPRR_CDPwrReq_IPDU_COM_CCU_ZCUR_HVSPRR_1             5u
#define HVSPRR_CDAnglAdjSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1             6u
#define HVSPRR_CDbrightnessSetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1             7u
#define HVSPRR_CDdisplaySetReq_IPDU_COM_CCU_ZCUR_HVSPRR_1             8u
#define ACU_CDSwSt_IPDU_COM_IDC_1             9u
#define ACU_CDAnglAdjSwSt_IPDU_COM_IDC_1             10u
#define DSMDiverReqMasStFeedback_MLCU_DSCU_1             11u
#define DsmParkMassageModSts_MLCU_DSCU_1             12u
#define DsmParkMassageStrSts_MLCU_DSCU_1             13u
#define DrvPmpFailr_MLCU_DSCU_1             14u
#define DrvSysVoltagefault_MLCU_DSCU_1             15u
#define DrvMLCUResponseErr_MLCU_DSCU_1             16u
#define DrvSoftversion_MLCU_DSCU_1             17u
#define DrvLumbsts_MLCU_DSCU_1             18u
#define DSMpassReqMassFeedback_MLCU_DSCU_2             19u
#define PsmParkMassageModSts_MLCU_DSCU_2             20u
#define DsmPassMasStrSts_MLCU_DSCU_2             21u
#define PsgRPmpFailr_MLCU_DSCU_2             22u
#define PsgRSysVoltagefault_MLCU_DSCU_2             23u
#define PsgRMLCUResponseErr_MLCU_DSCU_2             24u
#define PsgRSoftversion_MLCU_DSCU_2             25u
#define PsgRLumbsts_MLCU_DSCU_2             26u
#define DSS_ForwardBack_SwitchActiveSts_DSS_DSCU             27u
#define DSS_LegRest_SwitchActiveSts_FB_DSS_DSCU             28u
#define DSS_SeatBack_SwitchActiveSts_DSS_DSCU             29u
#define DSS_LegRest_SwitchActiveSts_DSS_DSCU             30u
#define DSS_ResponseErr_DSS_DSCU             31u
#define PSS_ForwardBack_SwitchActiveSts_PSS_DSCU             32u
#define PSS_LegRest_SwitchActiveSts_FB_PSS_DSCU             33u
#define PSS_SeatBack_SwitchActiveSts_PSS_DSCU             34u
#define PSS_LegRest_SwitchActiveSts_PSS_DSCU             35u
#define PSS_ResponseErr_PSS_DSCU             36u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define COM_TXSIGNAL_NUMBER                  13u
/* PRQA S 0791 ++ */ /* MISRA Rule 5.4 */
#define CD_CellingHostMotSt_IPDU_COM_CD_1               0u
#define CD_CellingLockMotSt_IPDU_COM_CD_1               1u
#define CD_CellingSwitchCnt_IPDU_COM_CD_1               2u
#define CD_CellingDisplaySt_IPDU_COM_CD_1               3u
#define CD_CellingDisplayAngle_IPDU_COM_CD_1               4u
#define CDCDiverMassReqSet_DSM_LIN1               5u
#define CdcDsmMssgModSet_DSM_LIN1               6u
#define CdcDsmMssgStrngthSet_DSM_LIN1               7u
#define CDCpassMassReqset_DSM_LIN1               8u
#define CdcPsmMssgModSet_DSM_LIN1               9u
#define CdcPsmMssgStrngthSet_DSM_LIN1               10u
#define CdcDsmLumbSet_DSM_LIN1               11u
#define CdcPsmLumbSet_DSM_LIN1               12u
/* PRQA S 0791 -- */ /* MISRA Rule 5.4 */

#define COM_RXGRPSIG_SUBSTITUTE_NUMBER     0u

#define COM_TXGROUPSIGNAL_NUMBER        0u

#define COM_RXGROUPSIGNAL_NUMBER        0u

#define COM_RXSIGNALGROUP_NUMBER            0u

#define COM_TXSIGNALGROUP_NUMBER            0u

#define COM_GWMAPPING_NUMBER         0u

#define COM_GW_DESTINATION_NUM                    0u

#define COM_GWSOURCESIGNAL_UPDATE_NUMBER           0u

#define COM_GW_SOURCE_DESCRIPTION_NUMBER     0u
#define COM_GW_DESTINATION_DESCRIPTION_NUMBER        0u

#define COM_TXTPPDU_SUPPORT          STD_OFF
#define COM_RXTPPDU_SUPPORT          STD_OFF

/* Macro for Optimize */
#define COM_TMS_ENABLE    STD_OFF

#define COM_MDT_ENABLE  STD_OFF

#define COM_TX_IPDU_COUNTER_ENABLE  STD_OFF

#define COM_RX_IPDU_COUNTER_ENABLE  STD_OFF

#define COM_IPDU_REPLICATION_ENABLE      STD_OFF

#define COM_TX_SIGNAL_NOTIFICATION_ENABLE   STD_OFF

#define COM_RX_SIGNAL_NOTIFICATION_ENABLE   STD_OFF

#define COM_RX_SIGNAL_TIMEOUT_ENABLE    STD_OFF

#define COM_TX_SIGNAL_TIMEOUT_ENABLE    STD_OFF

#define COM_RX_SIGNAL_TIMEOUT_NOTIFICATION_ENABLE      STD_OFF

#define COM_TX_SIGNAL_TIMEOUT_NOTIFICATION_ENABLE     STD_OFF

#define COM_RX_SIGNAL_UPDATE_BIT_ENABLE     STD_OFF

#define COM_TX_SIGNAL_UPDATE_BIT_ENABLE     STD_OFF

#define COM_RX_SIGNAL_INVALID_DATA_ENABLE   STD_ON

#define COM_TX_SIGNAL_INVALID_DATA_ENABLE   STD_ON

#define COM_RX_SIGNAL_INVALID_DATA_ACTION_NOTIFY_ENABLE     STD_ON

#define COM_TX_SIG_PROP_TRIGGERED_ENABLE    STD_OFF

#define COM_TX_SIG_PROP_TRIGGERED_ON_CHANGE_ENABLE  STD_OFF

#define COM_TX_SIG_PROP_TRIG_ON_CH_NO_REPETITION_ENABLE     STD_OFF

#define COM_TX_SIG_PROP_TRIG_NO_REPETITION_ENABLE   STD_OFF

#define COM_TX_SIGNAL_ERROR_NOTIFICATION_ENABLE     STD_OFF

#define COM_RX_SIGNAL_FILTER_ENABLE     STD_OFF

#define COM_TX_SIGNAL_TYPE_UINT8_DYN_ENABLE     STD_OFF

#define COM_TX_GRP_SIGNAL_TYPE_UINT8_N_ENABLE     STD_OFF

#define COM_RX_SIGNAL_TYPE_UINT8_DYN_ENABLE     STD_OFF

#define COM_RX_SIGNAL_TYPE_UINT8_N_ENABLE   STD_OFF

#define COM_TX_SIG_GROUP_NOTIFICATION_ENABLE    STD_OFF

#define COM_RX_SIG_GROUP_NOTIFICATION_ENABLE    STD_OFF

#define COM_RX_SIG_GROUP_TIMEOUT_ENABLE     STD_OFF

#define COM_TX_SIG_GROUP_TIMEOUT_ENABLE     STD_OFF

#define COM_RX_SIG_GROUP_TIMEOUT_NOTIFICATION_ENABLE    STD_OFF

#define COM_TX_SIG_GROUP_TIMEOUT_NOTIFICATION_ENABLE    STD_OFF

#define COM_RX_SIG_GROUP_UPDATE_BIT_ENABLE  STD_OFF

#define COM_TX_SIG_GROUP_UPDATE_BIT_ENABLE  STD_OFF

#define COM_TX_SIG_GROUP_PROP_TRIGGERED_ENABLE  STD_OFF

#define COM_TX_SIG_GROUP_PROP_TRIGGERED_ON_CHANGE_ENABLE    STD_OFF

#define COM_TX_SIG_GROUP_PROP_TRIG_ON_CH_NO_REPETITION_ENABLE   STD_OFF

#define COM_TX_SIG_GROUP_PROP_TRIG_NO_REPETITION_ENABLE     STD_OFF

#define COM_TX_SIG_GROUP_ERROR_NOTIFICATION_ENABLE  STD_OFF

#define COM_TX_SIG_GROUP_INITIAL_VALUE_ONLY_ENABLE  STD_OFF

#define COM_RX_SIG_GROUP_INVALID_DATA_ACTION_NOTIFY_ENABLE  STD_OFF

#define COM_TX_GRP_SIGNAL_TYPE_UINT8_DYN_ENABLE      STD_OFF

#define COM_TX_SIGNAL_TYPE_UINT8_N_ENABLE     STD_OFF

#define COM_RX_GRP_SIGNAL_TYPE_UINT8_DYN_ENABLE     STD_OFF

#define COM_RX_GRP_SIGNAL_TYPE_UINT8_N_ENABLE   STD_OFF

#define COM_RX_GRP_SIGNAL_INVALID_DATA_ENABLE   STD_OFF

#define COM_TX_GRP_SIGNAL_INVALID_DATA_ENABLE   STD_OFF

#define COM_RX_GRP_SIGNAL_FILTER_ENABLE     STD_OFF

#define COM_GW_DEST_SIG_UPDATE_BIT_ENABLE   STD_OFF

#define COM_GW_SRC_SIG_UPDATE_BIT_ENABLE    STD_OFF

#define COM_TX_IPDU_CALLOUT_ENABLE     STD_OFF

#define COM_RX_IPDU_CALLOUT_ENABLE     STD_ON

#define COM_TX_SIG_INITIAL_VALUE_ONLY_ENABLE  STD_OFF

#define COM_RX_IPDU_SIGNAL_PROCESS_IMMEDIATE_ENABLE     STD_OFF

#define COM_TX_IPDU_SIGNAL_PROCESS_IMMEDIATE_ENABLE     STD_OFF

#define COM_RX_SIGNAL_TIMEOUT_ACTION_REPLACE_ENABLE     STD_OFF

#define COM_RX_SIGNAL_TIMEOUT_ACTION_SUBSTITUTE_ENABLE     STD_OFF

#define COM_RX_SIG_GROUP_TIMEOUT_ACTION_REPLACE_ENABLE     STD_OFF

#define COM_RX_SIG_GROUP_TIMEOUT_ACTION_SUBSTITUTE_ENABLE     STD_OFF

#define COM_GW_SRC_DSP_SIG_TYPE_UINT8_N_ENABLE     STD_OFF

#define COM_GW_SRC_DSP_SIG_TYPE_UINT8_DYN_ENABLE     STD_OFF

#define COM_SIGNAL_SIGNED_TYPE_ENABLE     STD_OFF

#define COM_TXIPDU_TIGGERTRANSMIT_CALLOUT_ENABLE   STD_OFF

#endif

