/**
 * 
 * @file Pwm_Cfg.h
 * @brief AUTOSAR Pwm - contains the configuration data of the PWM driver
 * @version V1.0.0
 * <AUTHOR> @date 2024-06-05
 * 
 * @copyright Copyright (c) 2024 LANSHAN. All rights 
 *
 *
 * THIS SOFTWARE IS PROVIDED BY LANSHAN "AS IS" AND ANY EXPRESSED OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTPWMLAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL LANSHAN OR ITS CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOW<PERSON>VER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF
 * THE POSSIBILITY OF SUCH DAMAGE.
 * 
 */ 

#ifndef PWM_CFG_H
#define PWM_CFG_H

#include "Pwm_PBcfg.h"

#ifdef __cplusplus
extern "C"{
#endif
/*==================================================================================================
                               SOURCE FILE VERSION INFORMATION
==================================================================================================*/
#define PWM_CFG_VENDOR_ID                       (110U)
#define PWM_CFG_AR_RELEASE_MAJOR_VERSION        (4U)
#define PWM_CFG_AR_RELEASE_MINOR_VERSION        (4U)
#define PWM_CFG_AR_RELEASE_REVISION_VERSION     (0U)
#define PWM_CFG_SW_MAJOR_VERSION                (1U)
#define PWM_CFG_SW_MINOR_VERSION                (0U)
#define PWM_CFG_SW_PATCH_VERSION                (0U)

/*==================================================================================================
                                      FILE VERSION CHECKS
==================================================================================================*/

/* Check if source file and Pwm configuration header file are of the same vendor */
#if (PWM_PBCFG_VENDOR_ID != PWM_CFG_VENDOR_ID)
    #error "Pwm_PBcfg.h and Pwm_Cfg.h have different vendor IDs"
#endif
    /* Check if header file and Pwm configuration header file are of the same Autosar version */
#if ((PWM_PBCFG_AR_RELEASE_MAJOR_VERSION != PWM_CFG_AR_RELEASE_MAJOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_MINOR_VERSION != PWM_CFG_AR_RELEASE_MINOR_VERSION) || \
     (PWM_PBCFG_AR_RELEASE_REVISION_VERSION != PWM_CFG_AR_RELEASE_REVISION_VERSION))
    #error "AutoSar Version Numbers of Pwm_PBcfg.h and Pwm_Cfg.h are different"
#endif
/* Check if header file and Pwm configuration header file are of the same software version */
#if ((PWM_PBCFG_SW_MAJOR_VERSION != PWM_CFG_SW_MAJOR_VERSION) || \
     (PWM_PBCFG_SW_MINOR_VERSION != PWM_CFG_SW_MINOR_VERSION) || \
     (PWM_PBCFG_SW_PATCH_VERSION != PWM_CFG_SW_PATCH_VERSION))
    #error "Software Version Numbers of Pwm_PBcfg.h and Pwm_Cfg.h are different"
#endif

/*==================================================================================================
                                           CONSTANTS
==================================================================================================*/

/*==================================================================================================
                                       DEFINES AND MACROS
==================================================================================================*/


/** @brief Maximum number of PWM channels configured. */
#define PWM_MAX_CHANNEL                 ((Pwm_ChannelType)16U)

/**
*   @brief  Switches the Development Error Detection and Notification on or off.
*        STD_ON: Enabled.
*        STD_OFF: Disabled.
*   @implements PWM_DEV_ERROR_DETECT_define
*/
#define PWM_DEV_ERROR_DETECT            (STD_ON)


/**
* @brief Support for User mode.
*        If this parameter has been configured to STD_ON, the PWM driver code can be executed from both supervisor and user mode.
*/
#define PWM_ENABLE_USER_MODE_SUPPORT    (STD_OFF)

#ifndef MCAL_ENABLE_USER_MODE_SUPPORT
 #ifdef PWM_ENABLE_USER_MODE_SUPPORT
  #if (STD_ON == PWM_ENABLE_USER_MODE_SUPPORT)
   #error MCAL_ENABLE_USER_MODE_SUPPORT is not enabled. For running Pwm in user mode the MCAL_ENABLE_USER_MODE_SUPPORT needs to be defined
  #endif /* (STD_ON == PWM_ENABLE_USER_MODE_SUPPORT) */
 #endif /* ifdef PWM_ENABLE_USER_MODE_SUPPORT*/
#endif /* ifndef MCAL_ENABLE_USER_MODE_SUPPORT */



/** @brief        Switch to indicate that platform-specific validation of the period is supported */
#define PWM_MAX_PERIOD_PLAUSABILITY             (STD_ON)

/**
* @brief        Pre-compile configuration constants
*
*/
#define PWM_PRECOMPILE_SUPPORT                  (STD_OFF)

/**
* @brief        Support for User mode.
*               If this parameter has been configured to 'TRUE' the PWM driver code can be executed from both supervisor and user mode.
*/
#define PWM_ENABLE_USER_MODE_SUPPORT            (STD_OFF)



/** @brief        Enable/Disable multicore feature for PWM driver */
#define PWM_MULTICORE_ENABLED                   (STD_OFF)

/** @brief        Switch to indicate that Pwm_DeInit API is supported */
#define PWM_DE_INIT_API                         (STD_ON)

/** @brief        Switch to indicate that Pwm_GetOutputState API is supported */
/* [cover SWSID = SWS_Pwm_20085]
The function Pwm_GetOutputState shall be configurable On/Off by the configuration parameter: PwmGetOutputState {PWM_GET_OUTPUT_STATE_API}. */
#define PWM_GET_OUTPUT_STATE_API                (STD_ON)

/** @brief        Switch to indicate that Pwm_SetDutyCycle API is supported */
/* [cover SWSID = SWS_Pwm_20082]
The function Pwm_SetDutyCycle shall be configurable On/Off by the configuration parameter: PwmSetDutyCycle {PWM_SET_DUTY_CYCLE_API}.*/
#define PWM_SET_DUTY_CYCLE_API                  (STD_ON)

/** @brief        Switch to indicate that Pwm_SetOutputToIdle API is supported */
/* [cover SWSID = SWS_Pwm_20084]
The function Pwm_SetOutputToIdle shall be configurable On/Off by the configuration parameter: PwmSetOutputToIdle {PWM_SET_OUTPUT_TO_IDLE_API}.*/
#define PWM_SET_OUTPUT_TO_IDLE_API              (STD_ON)

/** @brief        Switch to indicate that Pwm_SetClockMode API is supported */
#define PWM_VERSION_INFO_API                    (STD_ON)

/** @brief        Switch to indicate that Pwm_GetChannelState API is supported */
#define PWM_GET_CHANNEL_STATE_API               (STD_ON)

/* @brief Add/remove the service Pwm_SetChannelDeadTime() from the code.*/
#define PWM_SET_CHANNEL_DEAD_TIME_API           (STD_ON)

/** @brief        Switch to indicate that Pwm_EnableTrigger API is supported */
#define PWM_ENABLE_TRIGGER_API                  (STD_ON)

/** @brief        Switch to indicate that Pwm_DisableTrigger API is supported */
#define PWM_DISABLE_TRIGGER_API                 (STD_ON)

/** @brief        Switch to indicate that Pwm_SwResetCounter API is supported */
#define PWM_RESET_COUNTER_API                   (STD_ON)

/** @brief        Switch to indicate that platform-specific validations of the notification function are supported */
#define PWM_NOTIFICATION_PLAUSABILITY           (STD_ON)

/**
* @brief        Specifies the InstanceId of this module instance.
*               If only one instance is present it shall have the Id 0.
* @note         Not used in the current implementation
*/
#define PWM_INDEX                               (0U)

/** @brief      Total number of Pwm logic channels configured. */
#define PWM_CONFIG_LOGIC_CHANNELS               (16U)

#define PWM_HW_CHANNEL_NO                       (16U)         


/** @brief        Maximum number of partitions declared in Os. */
#define PWM_MAX_PARTITION_NO                    (1U)

/** @brief      Macro used to initialize the driver state structure */
#define PWM_DRIVER_STATE_INITIALIZATION         {\
                                                    {\
                                                        PWM_STATE_UNINIT,\
                                                        NULL_PTR,\
                                                        PWM_NODEFINE_POWER,\
                                                        PWM_NODEFINE_POWER,\
                                                        {\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR,\
                                                            NULL_PTR\
                                                        },\
                                                        {\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE,\
                                                            (boolean)FALSE\
                                                        }\
                                                    }\
                                                }

/**
*   @brief   Adds / removes the service Pwm_GetCaptureRegisterValue() from the code.
*         STD_ON:  Pwm_GetCaptureRegisterValue() can be used.
*         STD_OFF: Pwm_GetCaptureRegisterValue() can not be used.
*/

#define PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_A               ((Pwm_ChannelType)0U)
#define PwmConf_PwmChannel_PwmChannel_Motor1_BackRest_B               ((Pwm_ChannelType)1U)
#define PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_A               ((Pwm_ChannelType)2U)
#define PwmConf_PwmChannel_PwmChannel_Motor2_SeatLift_B               ((Pwm_ChannelType)3U)
#define PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_A               ((Pwm_ChannelType)4U)
#define PwmConf_PwmChannel_PwmChannel_Motor3_SeatSlide_B               ((Pwm_ChannelType)5U)
#define PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_A               ((Pwm_ChannelType)6U)
#define PwmConf_PwmChannel_PwmChannel_Motor4_FrontBack_B               ((Pwm_ChannelType)7U)
#define PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_A               ((Pwm_ChannelType)8U)
#define PwmConf_PwmChannel_PwmChannel_Motor5_LegLift_B               ((Pwm_ChannelType)9U)
#define PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_A               ((Pwm_ChannelType)10U)
#define PwmConf_PwmChannel_PwmChannel_Motor6_LegStretch_B               ((Pwm_ChannelType)11U)
#define PwmConf_PwmChannel_PwmChannel_Fan1               ((Pwm_ChannelType)12U)
#define PwmConf_PwmChannel_PwmChannel_Fan2               ((Pwm_ChannelType)13U)
#define PwmConf_PwmChannel_PwmChannel_Heat1               ((Pwm_ChannelType)14U)
#define PwmConf_PwmChannel_PwmChannel_Heat2               ((Pwm_ChannelType)15U)

/**
* @brief          Number of configured partitions.
*/
#define PWM_MAX_PARTITIONS                    (1U)

#define PWM_MULTICORE_SUPPORT            	  (STD_OFF)

/*==================================================================================================
                                             ENUMS
==================================================================================================*/


/*==================================================================================================
                                 STRUCTURES AND OTHER TYPEDEFS
==================================================================================================*/
/**
*   @brief Implementation specific. This type shall be chosen in order to have the most efficient
*       implementation on a specific micro-controller platform.
*       Range: 0  to width of the timer register.
*       Description: Width of the buffer for timestamps ticks and measured elapsed time ticks
*/

/**
* @brief        PWM Period type (the value of the period is platform dependent and thus configurable)
*/
typedef uint32 Pwm_Ipw_PeriodType;

/**
* @brief        PWM Duty type (the value of the duty is platform dependent and thus configurable)
*/
typedef uint16 Pwm_Ipw_DutyType;

/*==================================================================================================
*                                  GLOBAL VARIABLE DECLARATIONS
==================================================================================================*/
#define PWM_START_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/** @brief          Post Build configuration constants */
#define PWM_CONFIG_EXTERNAL \
                PWM_CONFIG_PB

#define PWM_STOP_SEC_CONFIG_DATA_UNSPECIFIED
#include "Pwm_MemMap.h"

/*==================================================================================================
                                     FUNCTION PROTOTYPES
==================================================================================================*/

#define PWM_START_SEC_CODE
#include "Pwm_MemMap.h"
	
#define PWM_STOP_SEC_CODE
#include "Pwm_MemMap.h"


#ifdef __cplusplus
}
#endif

#endif    /* PWM_CFG_H */


